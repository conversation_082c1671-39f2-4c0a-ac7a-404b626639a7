
INSERT INTO [dbo].[CO_TDFM] ([iOrder], [cFieldCode], [cFieldName], [cModule], [cDefine1], [cDefine2]) VALUES (1, N'001', N'����', N'GL', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TDFM] ([iOrder], [cFieldCode], [cFieldName], [cModule], [cDefine1], [cDefine2]) VALUES (2, N'001', N'����', N'CA', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TDFM] ([iOrder], [cFieldCode], [cFieldName], [cModule], [cDefine1], [cDefine2]) VALUES (3, N'001', N'����', N'WA', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TDFM] ([iOrder], [cField<PERSON>ode], [cFieldName], [cModule], [cDefine1], [cDefine2]) VALUES (4, N'001', N'����', N'FA', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TDFM] ([iOrder], [cFieldCode], [cFieldName], [cModule], [cDefine1], [cDefine2]) VALUES (14, N'001', N'����', N'RE', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TDFM] ([iOrder], [cFieldCode], [cFieldName], [cModule], [cDefine1], [cDefine2]) VALUES (20, N'002', N'��Ӧ��', N'IA', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TDFM] ([iOrder], [cFieldCode], [cFieldName], [cModule], [cDefine1], [cDefine2]) VALUES (21, N'002', N'��Ӧ��', N'ST', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TDFM] ([iOrder], [cFieldCode], [cFieldName], [cModule], [cDefine1], [cDefine2]) VALUES (22, N'002', N'��Ӧ��', N'SA', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TDFM] ([iOrder], [cFieldCode], [cFieldName], [cModule], [cDefine1], [cDefine2]) VALUES (23, N'002', N'��Ӧ��', N'PU', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TDFM] ([iOrder], [cFieldCode], [cFieldName], [cModule], [cDefine1], [cDefine2]) VALUES (24, N'002', N'��Ӧ��', N'OM', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TDFM] ([iOrder], [cFieldCode], [cFieldName], [cModule], [cDefine1], [cDefine2]) VALUES (25, N'002', N'��Ӧ��', N'QC', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TDFM] ([iOrder], [cFieldCode], [cFieldName], [cModule], [cDefine1], [cDefine2]) VALUES (26, N'002', N'��Ӧ��', N'AR', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TDFM] ([iOrder], [cFieldCode], [cFieldName], [cModule], [cDefine1], [cDefine2]) VALUES (27, N'002', N'��Ӧ��', N'AP', NULL, NULL)
GO