INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (19, NULL, N'GL_AccountBook', N'���˱�', N'IF 12={iMonth} delete from gl_accountbook where iYear={iYear} and iMonth={iMonth} ELSE update gl_accountbook set iMonth=1 where  iYear={iYear} and iMonth={iMonth}', NULL, N'GL', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (20, NULL, N'GL_AssistBook', N'�������˱�', N'IF 12={iMonth} delete from gl_assistbook where iYear={iYear} and iMonth={iMonth} ELSE update gl_assistbook set iMonth=1 where  iYear={iYear} and iMonth={iMonth}', NULL, N'GL', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (13, NULL, N'GL_AccountBook', N'���˱�', N'delete from gl_accountbook where iYear*12+iMonth<{iYear}*12+{iMonth}', N'delete from gl_accountbook where iYear*12+iMonth>{iYear}*12+{iMonth}', N'GL', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (17, NULL, N'GL_VoucherLineCashFlow', N'ƾ֤���ֽ�������', N'delete from gl_voucherlinecashflow where cvouguid in  ( select cguid from gl_voucher where iYear*12+iMonth<={iYear}*12+{iMonth})', N'delete from gl_voucherlinecashflow where cvouguid in ( select cguid from gl_voucher where iYear*12+iMonth>{iYear}*12+{iMonth})', N'GL', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (16, NULL, N'GL_VoucherLine', N'ƾ֤�ӱ�', N'delete from gl_voucherline where cvouguid in ( select cguid from gl_voucher where iYear*12+iMonth<={iYear}*12+{iMonth})', N'delete from gl_voucherline where cvouguid in ( select cguid from gl_voucher where iYear*12+iMonth>{iYear}*12+{iMonth})', N'GL', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (18, NULL, N'GL_Voucher', N'ƾ֤����', N'delete from gl_voucher where iYear*12+iMonth<={iYear}*12+{iMonth}', N'delete from gl_voucher where iYear*12+iMonth>{iYear}*12+{iMonth}', N'GL', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (14, NULL, N'GL_AssistBook', N'�������˱�', N'delete from gl_assistbook where iYear*12+iMonth<{iYear}*12+{iMonth}
', N'delete from gl_assistbook where iYear*12+iMonth>{iYear}*12+{iMonth}', N'GL', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (13, NULL, N'CA_BankBalance', N'��������', N'delete from CA_BankBalance where iYear*12+iPeriod<={iYear}*12+{iMonth}', N'delete from CA_BankBalance where iYear*12+iPeriod>{iYear}*12+{iMonth}', N'CA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (10, NULL, N'CA_CashBalance', N'�ֽ�����', N'IF 12={iMonth} update  CA_CashBalance  set dStartDate=dateadd(day,1,''{dDate}'') ,iInitFlag=1 where iYear={iYear}+1 and iPeriod=1 ELSE update  CA_CashBalance  set dStartDate=dateadd(day,1,''{dDate}'') ,iInitFlag=1 where iYear={iYear} and iPeriod={iMonth}+1

IF 12={iMonth} update  CA_CashBalance  set iYDebitAMT_F=(select cb.iYDebitAMT_F from CA_CashBalance cb where cb.iYear={iYear} and cb.iPeriod={iMonth} and cb.cAccountGUID=CA_CashBalance.cAccountGUID), iYDebitAMT=(select cb.iYDebitAMT from CA_CashBalance cb where cb.iYear={iYear} and cb.iPeriod={iMonth} and cb.cAccountGUID=CA_CashBalance.cAccountGUID), iYCreditAMT_F=(select cb.iYCreditAMT_F from CA_CashBalance cb where cb.iYear={iYear} and cb.iPeriod={iMonth} and cb.cAccountGUID=CA_CashBalance.cAccountGUID),iYCreditAMT=(select cb.iYCreditAMT from CA_CashBalance cb where cb.iYear={iYear} and cb.iPeriod={iMonth} and cb.cAccountGUID=CA_CashBalance.cAccountGUID) where iYear={iYear}+1 and iPeriod=1 and (iYDebitAMT_F is null and iYDebitAMT is null and iYCreditAMT_F is null and iYCreditAMT is null) ELSE update  CA_CashBalance  set iYDebitAMT_F=(select cb.iYDebitAMT_F from CA_CashBalance cb where cb.iYear={iYear} and cb.iPeriod={iMonth} and cb.cAccountGUID=CA_CashBalance.cAccountGUID), iYDebitAMT=(select cb.iYDebitAMT from CA_CashBalance cb where cb.iYear={iYear} and cb.iPeriod={iMonth} and cb.cAccountGUID=CA_CashBalance.cAccountGUID), iYCreditAMT_F=(select cb.iYCreditAMT_F from CA_CashBalance cb where cb.iYear={iYear} and cb.iPeriod={iMonth} and cb.cAccountGUID=CA_CashBalance.cAccountGUID),iYCreditAMT=(select cb.iYCreditAMT from CA_CashBalance cb where cb.iYear={iYear} and cb.iPeriod={iMonth} and cb.cAccountGUID=CA_CashBalance.cAccountGUID) where iYear={iYear} and iPeriod={iMonth}+1 and (iYDebitAMT_F is null and iYDebitAMT is null and iYCreditAMT_F is null and iYCreditAMT is null)
', NULL, N'CA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (16, NULL, N'CA_CashStatement', N'�ֽ��ռ���', N'insert into CA_CashStatement (cGUID,iYear,iPeriod,dDate,cAccountGUID,cCurGUID,iDCDir,iExchRate,iAmount_F,iAmount,iInitFlag) select cGUID,iYear,iPeriod,dStartDate, cAccountGUID,cCurGUID,1 iDCDir,(case when ''*''=(select cOperator from gl_currency where gl_currency.cguid=ca_cashbalance.ccurguid) then case when (iInitAMT=0 and iInitAMT_F=0) then 1 else (iInitAMT_F / case iInitAMT when 0 then 1 else iInitAMT end) end ELSE	case when (iInitAMT=0 and iInitAMT_F=0) then 1 else (iInitAMT / case iInitAMT_F when 0 then 1 else iInitAMT_F end) end end) iExchRate,iInitAMT_F,iInitAMT,0 iInitFlag FROM CA_CashBalance where iInitFlag=1', NULL, N'CA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (9, NULL, N'CA_CashCheck', N'�ֽ��̵㵥����', N'delete from CA_CashCheck where dDate<={dDate}', N'delete from CA_CashCheck where dDate>{dDate}', N'CA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (7, NULL, N'CA_InitBankBookPara', N'�ֽ�������������Ϣ��', N'update  CA_InitBankBookPara set dStartDate=dateadd(day,1,''{dDate}'')', NULL, N'CA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (8, NULL, N'CA_CashCheckLine', N'�ֽ��̵㵥�ӱ�', N'delete from CA_CashCheckLine where cHeadGUID in ( select cGUID from CA_CashCheck where dDate<={dDate})', N'delete from CA_CashCheckLine where cHeadGUID in ( select cGUID from CA_CashCheck where dDate>{iYear})', N'CA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (11, NULL, N'CA_CashBalance', N'�ֽ�����', N'delete from CA_CashBalance where iYear*12+iPeriod<={iYear}*12+{iMonth}', N'delete from CA_CashBalance where iYear*12+iPeriod>{iYear}*12+{iMonth}', N'CA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (15, NULL, N'CA_CashStatement', N'�ֽ��ռ���', N'delete from CA_CashStatement where iYear*12+iPeriod<={iYear}*12+{iMonth}', N'delete from CA_CashStatement where iYear*12+iPeriod>{iYear}*12+{iMonth}', N'CA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (14, NULL, N'CA_BankBalance', N'��������', N'IF 12={iMonth} update  CA_BankBalance  set iInitFlag=1 ,dStartDate=dateadd(day,1,''{dDate}'')  where iYear={iYear}+1 and iPeriod=1 ELSE update  CA_BankBalance  set iInitFlag=1 ,dStartDate=dateadd(day,1,''{dDate}'')  where iYear={iYear} and iPeriod={iMonth}+1', NULL, N'CA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (10, 9, N'FA_CardDevice', N'��Ƭ-�����豸��', N'delete from FA_CardDevice where cHeadGUID not in (select cGUID from FA_Cards)', N'delete from FA_CardDevice where cHeadGUID not in (select cGUID from FA_Cards)', N'FA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (11, 9, N'FA_CardDept', N'��Ƭ-ʹ�ò��ű�', N'delete from FA_CardDept where cHeadGUID not in (select cGUID from FA_Cards)', N'delete from FA_CardDept where cHeadGUID not in (select cGUID from FA_Cards)', N'FA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (12, 9, N'FA_CardFundAcct', N'��Ƭ-�޹�������ñ�', N'delete from FA_CardFundAcct where cHeadGUID not in (select cGUID from FA_Cards)', N'delete from FA_CardFundAcct where cHeadGUID not in (select cGUID from FA_Cards)', N'FA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (13, 9, N'FA_FixAccount', N'�̶��ʲ���ϸ�˱�', N'delete from FA_FixAccount where iYear < {iYear} or iYear = {iYear} and iPeriod <= {iMonth}

update FA_FixAccount set iInitdepr = isnull(iAccudepr,0)-isnull(iCreditdepr,0)+isnull(iDebitdepr,0) where iYear = {iYear} and iPeriod = {iMonth}+1 and {iMonth}<12 or iYear = {iYear}+1 and iPeriod = 1 and {iMonth}=12', N'delete from FA_FixAccount where iYear > {iYear} or iYear = {iYear} and iPeriod > {iMonth}', N'FA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (15, 14, N'FA_CChangeLine', N'�̶��ʲ������-����', N'delete from FA_CChangeLine where cHeadGUID not in (select cGUID from FA_CardChange)', N'delete from FA_CChangeLine where cHeadGUID not in (select cGUID from FA_CardChange)', N'FA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (14, 9, N'FA_CardChange', N'�̶��ʲ������-��ͷ', N'delete from FA_CardChange where iYear < {iYear} or iYear = {iYear} and iPeriod <= {iMonth}', N'delete from FA_CardChange where iYear > {iYear} or iYear = {iYear} and iPeriod > {iMonth}', N'FA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (16, 9, N'FA_CardClear', N'�̶��ʲ������-��ͷ', N'delete from FA_CardClear where iYear < {iYear} or iYear = {iYear} and iPeriod <= {iMonth}', N'delete from FA_CardClear where iYear > {iYear} or iYear = {iYear} and iPeriod > {iMonth}', N'FA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (18, 9, N'FA_WorkLoad', N'�̶��ʲ���������-��ͷ', N'delete from FA_WorkLoad where iYear < {iYear} or iYear = {iYear} and iPeriod <= {iMonth}', N'delete from FA_WorkLoad where iYear > {iYear} or iYear = {iYear} and iPeriod > {iMonth}', N'FA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (17, 16, N'FA_ClearLine', N'�̶��ʲ������-����', N'delete from FA_ClearLine where cHeadGUID not in (select cGUID from FA_CardClear)', N'delete from FA_ClearLine where cHeadGUID not in (select cGUID from FA_CardClear)', N'FA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (9, NULL, N'FA_Cards', N'�̶��ʲ���Ƭ��', N'delete FA_Cards from FA_Cards C inner join (select C.cCardID,C.iNumber from FA_CardClear H inner join FA_ClearLine I on H.cGUID=I.cHeadGUID inner join FA_Cards C on C.cGUID=I.cAfterGUID where H.iStatus=1 and iClearStatus = 1 and iAlterStatus = 3 and (H.iYear < {iYear} or H.iYear = {iYear} and H.iPeriod <= {iMonth})) H on H.cCardID=C.cCardID and C.iNumber<=H.iNumber

delete from FA_Cards where cGUID in (select cBeforeGUID cardguid from FA_CardClear join FA_ClearLine on FA_CardClear.cguid=FA_ClearLine.cHeadGUID where FA_CardClear.iYear < {iYear} or FA_CardClear.iYear = {iYear} and FA_CardClear.iPeriod <={iMonth} union all select cAfterPartGUID from FA_CardClear join FA_ClearLine on FA_CardClear.cguid=FA_ClearLine.cHeadGUID where FA_CardClear.iYear < {iYear} or FA_CardClear.iYear = {iYear} and FA_CardClear.iPeriod <= {iMonth})

delete FA_Cards from FA_Cards C inner join FA_FixAccount F on F.cCardID=C.cCardID and iYear={iYear} and iPeriod={iMonth} where C.iNumber<F.iRelNumber

update FA_Cards set iInitFlag=0 where cNewYear < {iYear} or cNewYear = {iYear} and cNewPeriod <= {iMonth}

update FA_Cards set iAlterStatus=0 where inumber in (select irelnumber from fa_fixaccount where iyear = {iYear} and iperiod = {iMonth}) and iAlterStatus in (1,2,4,5)

update FA_Cards set iDeprBuyAmt = isnull(F.iAccudepr,0) from FA_Cards C inner join FA_FixAccount F on F.cCardID=C.cCardID and (F.iYear = {iYear} and F.iPeriod = {iMonth}+1 and {iMonth}<12 or F.iYear = {iYear}+1 and F.iPeriod = 1 and {iMonth}=12)', N'delete from FA_Cards where cNewYear > {iYear} or cNewYear = {iYear} and cNewPeriod > {iMonth}

delete FA_Cards from FA_Cards C inner join FA_FixAccount F on F.cCardID=C.cCardID and iYear={iYear} and iPeriod={iMonth} where C.iNumber>F.iRelNumber

update FA_Cards set iIsCurrently=1,iAccudepr=isnull(F.iAccudepr,0),iPeriodsUsed=isnull(F.iPeriodsUsed,0) from FA_Cards C inner join FA_FixAccount F on F.cCardID=C.cCardID and C.iNumber=F.iRelNumber and iYear={iYear} and iPeriod={iMonth}', N'FA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (22, 9, N'FA_DeprLineDept', N'�۾ɷ��÷����', N'delete from FA_DeprLineDept where cHeadGUID not in (select cGUID from FA_DeprLine)', N'delete from FA_DeprLineDept where cHeadGUID not in (select cGUID from FA_DeprLine)', N'FA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (20, 9, N'FA_Depreciation', N'�̶��ʲ��۾ɱ�-��ͷ', N'delete from FA_Depreciation where iYear < {iYear} or iYear = {iYear} and iPeriod <= {iMonth}', N'delete from FA_Depreciation where iYear > {iYear} or iYear = {iYear} and iPeriod > {iMonth}', N'FA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (21, 20, N'WA_Data', N'���������ӱ�', N'delete from WA_Data where cGroupGUID not in (select cGUID from WA_DataGroup)', N'delete from WA_Data where cGroupGUID not in (select cGUID from WA_DataGroup)', N'WA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (20, NULL, N'WA_DataGroup', N'������������', N'delete from WA_DataGroup where iYear < {iYear} or iYear = {iYear} and iMonth <= {iMonth}', N'delete from WA_DataGroup where iYear > {iYear} or iYear = {iYear} and iMonth > {iMonth}', N'WA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (16, NULL, N'WA_Class', N'�������', N'update WA_Class set iStartYear=year(dateadd(month,1,convert(smalldatetime,cast({iYear} as varchar(4))+''-''+cast({iMonth} as varchar(2))+''-1''))),iStartMonth=month(dateadd(month,1,convert(smalldatetime,cast({iYear} as varchar(4))+''-''+cast({iMonth} as varchar(2))+''-1''))) where iIsSumClass=0', N'update WA_Class set iCurYear={iYear},iCurMonth={iMonth} where iIsSumClass=0', N'WA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (1, NULL, N're_cellhistory', N'����ϵͳ', N'delete from  re_cellhistory where cReportId in (select  cGUID  FROM  RE_ReportHistory where  iRptYear*12+iEndPeriod <={iYear}*12+{iMonth})', N'delete from  re_cellhistory where  cReportId in (select  cGUID  FROM RE_ReportHistory where iRptYear*12+iEndPeriod  > {iYear}*12+{iMonth})', N'RE', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (2, NULL, N'RE_Formula', N'����ϵͳ', N'delete from RE_Formula where cReportId in (select  cGUID  FROM  RE_ReportHistory where  iRptYear*12+iEndPeriod <={iYear}*12+{iMonth})', N'delete from  RE_Formula where cReportId in (select  cGUID  FROM   RE_ReportHistory where iRptYear*12+iEndPeriod  > {iYear}*12+{iMonth})', N'RE', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (3, 2, N'RE_Report', N'����ϵͳ', N'delete from RE_ReportHistory where   iRptYear*12+iEndPeriod <={iYear}*12+{iMonth}', N'delete from RE_ReportHistory where  iRptYear*12+iEndPeriod  > {iYear}*12+{iMonth}', N'RE', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (302, NULL, N'sa_order', N'���۶���', N'delete from sa_order where ddate <= {ddate}', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (303, 302, N'sa_orderline', N'���۶����ӱ�', N'delete l from sa_orderline l where not exists (select 1 from sa_order s where s.cguid = l.cheadguid)', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (306, NULL, N'sa_dispatch', N'���۷�����', N'delete from sa_dispatch where ddate <= {ddate}', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (307, 306, N'sa_dispatchline', N'���۷������ӱ�', N'delete l from sa_dispatchline l where not exists (select 1 from sa_dispatch s where s.cguid = l.cheadguid)', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (310, NULL, N'st_stkrecord', N'���۳���', N'delete i from st_stkrecord i where ISNULL(i.iInventoryFlag,0)!=1 and ({IAsys} != 1 AND i.ddate <= {ddate} OR {IAsys} = 1 AND ( SELECT isnull(MAX(dl.dAccountDate), i.ddate) FROM st_stkrecordline dl WHERE dl.cheadguid = i.cguid GROUP BY dl.cheadguid) <= {ddate})', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (311, 310, N'st_stkrecordline', N'���۳����ӱ�', N'delete l from st_stkrecordline l where not exists (select 1 from st_stkrecord s where s.cguid = l.cheadguid)', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (315, 314, N'sa_invoiceline', N'���۷�Ʊ�ӱ�', N'delete l from sa_invoiceline l where not exists (select 1 from sa_invoice s where s.cguid = l.cheadguid)', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (314, NULL, N'sa_invoice', N'���۷�Ʊ', N'delete i from sa_invoice i where ({IAsys} != 1 AND i.ddate <= {ddate} OR {IAsys} = 1 AND (SELECT isnull(MAX(dl.dAccountDate), i.ddate) FROM sa_invoiceline dl WHERE dl.cheadguid = i.cguid GROUP BY dl.cheadguid) <= {ddate}) AND isnull(i.iInventoryFlag, 0) != 1', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (19, 18, N'FA_WorkLoadLine', N'�̶��ʲ���������-����', N'delete from FA_WorkLoadLine where cHeadGUID not in (select cGUID from FA_WorkLoad)', N'delete from FA_WorkLoadLine where cHeadGUID not in (select cGUID from FA_WorkLoad)', N'FA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (21, 20, N'FA_DeprLine', N'�̶��ʲ��۾ɱ�-����', N'delete from FA_DeprLine where cHeadGUID not in (select cGUID from FA_Depreciation)', N'delete from FA_DeprLine where cHeadGUID not in (select cGUID from FA_Depreciation)', N'FA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (318, NULL, N'SA_ConsignSettle', N'ί�д������㵥', N'delete i from SA_ConsignSettle i where ({IAsys} != 1 AND i.ddate <= {ddate} OR {IAsys} = 1 AND (SELECT isnull(MAX(dl.dAccountDate), i.ddate) FROM SA_ConsignSettleline dl WHERE dl.cheadguid = i.cguid GROUP BY dl.cheadguid) <= {ddate})', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (319, 318, N'SA_ConsignSettleline', N'ί�д������㵥�ӱ�', N'delete l from SA_ConsignSettleline l where not exists (select 1 from SA_ConsignSettle s where s.cguid = l.cheadguid)', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (322, NULL, N'SA_CarriageInvoice', N'���۷��÷�Ʊ', N'delete from SA_CarriageInvoice where ddate <= {ddate} AND isnull(iInventoryFlag, 0) != 1', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (323, 322, N'SA_CarriageInvoiceline', N'���۷��÷�Ʊ�ӱ�', N'delete l from SA_CarriageInvoiceline l where not exists (select 1 from SA_CarriageInvoice s where s.cguid = l.cheadguid)', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (324, 322, N'BILL_GEN_RELATION_MAIN', N'���ɹ�ϵ�������ۣ�', N'delete from BILL_GEN_RELATION_MAIN where (cSMainEntity = ''sa_order'' and not exists (select 1 from sa_order s where s.cguid = cSMainID)) or (cDMainEntity = ''sa_order'' and not exists (select 1 from sa_order s where s.cguid = cDMainID)) or (cSMainEntity = ''sa_dispatch'' and not exists (select 1 from sa_dispatch s where s.cguid = cSMainID)) or (cDMainEntity = ''sa_dispatch'' and not exists (select 1 from sa_dispatch s where s.cguid = cDMainID)) or (cSMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord s where s.cguid = cSMainID)) or (cDMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord s where s.cguid = cDMainID)) or (cSMainEntity = ''sa_invoice'' and not exists (select 1 from sa_invoice s where s.cguid = cSMainID)) or (cDMainEntity = ''sa_invoice'' and not exists (select 1 from sa_invoice s where s.cguid = cDMainID)) or (cSMainEntity = ''SA_ConsignSettle'' and not exists (select 1 from SA_ConsignSettle s where s.cguid = cSMainID)) or (cDMainEntity = ''SA_ConsignSettle'' and not exists (select 1 from SA_ConsignSettle s where s.cguid = cDMainID)) or (cSMainEntity = ''SA_CarriageInvoice'' and not exists (select 1 from SA_CarriageInvoice s where s.cguid = cSMainID))', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (325, 322, N'BILL_GEN_RELATION_DETAIL', N'���ɹ�ϵ�ӱ����ۣ�', N'delete from BILL_GEN_RELATION_DETAIL where (cSMainEntity = ''sa_order'' and not exists (select 1 from sa_order s where s.cguid = cSMainID)) or (cDMainEntity = ''sa_order'' and not exists (select 1 from sa_order s where s.cguid = cDMainID)) or (cSMainEntity = ''sa_dispatch'' and not exists (select 1 from sa_dispatch s where s.cguid = cSMainID)) or (cDMainEntity = ''sa_dispatch'' and not exists (select 1 from sa_dispatch s where s.cguid = cDMainID)) or (cSMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord s where s.cguid = cSMainID)) or (cDMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord s where s.cguid = cDMainID)) or (cSMainEntity = ''sa_invoice'' and not exists (select 1 from sa_invoice s where s.cguid = cSMainID)) or (cDMainEntity = ''sa_invoice'' and not exists (select 1 from sa_invoice s where s.cguid = cDMainID)) or (cSMainEntity = ''SA_ConsignSettle'' and not exists (select 1 from SA_ConsignSettle s where s.cguid = cSMainID)) or (cDMainEntity = ''SA_ConsignSettle'' and not exists (select 1 from SA_ConsignSettle s where s.cguid = cDMainID)) or (cSMainEntity = ''SA_CarriageInvoice'' and not exists (select 1 from SA_CarriageInvoice s where s.cguid = cSMainID))', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (207, NULL, N'ST_StkCheck', N'����̵㵥����', N'delete from st_stkcheck where ddate <= {ddate}', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (208, 207, N'ST_StkCheckLine', N'����̵㵥�ӱ�', N'delete l from st_stkcheckline l where not exists (select 1 from st_stkcheck s where s.cguid = l.cheadguid)', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (204, 200, N'ST_MatPosition', N'��λ��', N'delete l from ST_MatPosition l where not exists (select 1 from ST_StkRecordLine s where s.cguid = l.cStkLineID) AND  cAdjustID is null', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (205, 200, N'ST_SerialNumber', N'���кű�1', N'delete  ST_SerialNumber from ST_SerialNumber se   where not EXISTS  (select 1 from st_stkrecordline st  left join cm_material cm on cm.cguid=st.cmatguid  where cm.isnstart=''1''  and st.cguid=se.cStkInGUID) and se.iInOutFlag=''1'' and se.iOutFlag=''1'' and se.iauditstatus=''zk''', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (206, 200, N'ST_SerialNumber', N'���кű�2', N'delete  ST_SerialNumber from ST_SerialNumber se where not EXISTS  (select 1 from st_stkrecordline st  left join cm_material cm on cm.cguid=st.cmatguid  where cm.isnstart=''1''and st.cguid=se.cStkOutGUID)   and se.iInOutFlag=''0'' and se.iauditstatus=''yc''  and se.ioutguid is not null', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (500, NULL, N'PU_Planhead', N'�ɹ��ƻ�', N'delete from PU_Planhead where dCreatdate <= {ddate}', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (501, 500, N'PU_Planline', N'�ɹ��ƻ��ӱ�', N'delete from PU_Planline where not EXISTS (select 1 from PU_Planhead WHERE PU_Planhead.cGUID = PU_Planline.cHeadGUID)', NULL, N'PU', N'1', NULL, NULL)
GO


INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (502, NULL, N'PU_ROPhead', N'ROPִ�м�¼��ͷ', N'delete from PU_ROPhead where DROPTIME <= {ddate}', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (503, 502, N'PU_ROPline', N'ROPִ�м�¼����', N'delete from PU_ROPline where not EXISTS (select 1 from PU_ROPhead WHERE PU_ROPhead.cGUID = PU_ROPline.CPU_ROPHEADGUID)', NULL, N'PU', N'1', NULL, NULL)
GO


INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (504, NULL, N'PU_Order', N'�ɹ�����', N'delete from PU_Order where dPODate<= {ddate}', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (505, 504, N'PU_OrderLine', N'�ɹ������ӱ�', N'delete from PU_OrderLine where not EXISTS (select 1 from PU_Order where PU_Order.cGUID = PU_OrderLine.cHeadGUID)', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (508, NULL, N'PU_Receive', N'�ɹ�������', N'delete from PU_Receive where ddate <= {ddate}', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (509, 508, N'PU_ReceiveLine', N'�ɹ��������ӱ�', N'delete from PU_ReceiveLine where not EXISTS (select 1 from PU_Receive where PU_Receive.cGUID = PU_ReceiveLine.cHeadGUID)', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (600, NULL, N'QC_Checklist', N'���鵥', N'delete from QC_Checklist where dBillDate <= {ddate}', NULL, N'QC', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (601, 600, N'QC_ChecklistLine', N'���鵥�ӱ�', N'delete l from QC_ChecklistLine l where not exists (select 1 from QC_Checklist s where s.cguid = l.cheadguid)', NULL, N'QC', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (211, NULL, N'ST_StkTrans', N'������������', N'delete from ST_StkTrans where ddate <= {ddate}', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (212, 211, N'ST_StkTransLine', N'���������ӱ�', N'delete l from ST_StkTransLine l where not exists (select 1 from ST_StkTrans s where s.cguid = l.cheadguid)', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (605, 604, N'QC_DefectiveLine', N'���ϸ����ӱ�', N'delete l from QC_DefectiveLine l where not exists (select 1 from QC_Defective s where s.cguid = l.cheadguid)', NULL, N'QC', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (650, NULL, N'OM_Order', N'ί�ⶩ��', N'delete from OM_Order where ddate <= {ddate}', NULL, N'OM', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (651, 650, N'OM_OrderLine', N'ί�ⶩ���ӱ�', N'delete l from OM_OrderLine l where not exists (select 1 from OM_Order s where s.cguid = l.cheadguid)', NULL, N'OM', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (654, NULL, N'OM_Invoice', N'ί�ⷢƱ', N'delete from OM_Invoice where dInvDate <= {ddate} AND isnull(iInventoryFlag, 0) != 1', NULL, N'OM', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (655, 654, N'OM_InvoiceLine', N'ί�ⷢƱ�ӱ�', N'delete l from OM_InvoiceLine l where not exists (select 1 from OM_Invoice s where s.cguid = l.cheadguid)', NULL, N'OM', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (656, 654, N'BILL_GEN_RELATION_MAIN', N'���ɹ�ϵ����ί�⣩', N'delete from BILL_GEN_RELATION_MAIN where (cSMainEntity = ''OM_Order'' and not exists (select 1 from OM_Order s where s.cguid=cSMainID)) or (cDMainEntity = ''OM_Order'' and not exists (select 1 from OM_Order s where s.cguid=cDMainID)) or (cSMainEntity = ''OM_Invoice'' and not exists (select 1 from OM_Invoice s where s.cguid=cSMainID)) or (cDMainEntity = ''OM_Invoice'' and not exists (select 1 from OM_Invoice s where s.cguid=cDMainID))', NULL, N'OM', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (657, 654, N'BILL_GEN_RELATION_DETAIL', N'���ɹ�ϵ�ӱ�ί�⣩', N'delete from BILL_GEN_RELATION_DETAIL where (cSMainEntity = ''OM_Order'' and not exists (select 1 from OM_Order s where s.cguid=cSMainID)) or (cDMainEntity = ''OM_Order'' and not exists (select 1 from OM_Order s where s.cguid=cDMainID)) or (cSMainEntity = ''OM_Invoice'' and not exists (select 1 from OM_Invoice s where s.cguid=cSMainID)) or (cDMainEntity = ''OM_Invoice'' and not exists (select 1 from OM_Invoice s where s.cguid=cDMainID))', NULL, N'OM', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (658, NULL, N'OM_Settle', N'ί����㵥', N'delete from OM_Settle where dSettleDate <= {ddate}', NULL, N'OM', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (659, 658, N'OM_SettleLine', N'ί����㵥�ӱ�', N'delete l from OM_SettleLine l where not exists (select 1 from OM_Settle s where s.cguid = l.cheadguid)', NULL, N'OM', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (660, NULL, N'ST_StkRecord', N'�ڳ�ί��ӹ���ⵥ', N'DELETE st from ST_StkRecord st WHERE st.cBillType = ''101'' AND st.ddate<={ddate}', NULL, N'OM', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (661, 660, N'ST_StkRecordLine', N'�ڳ�ί��ӹ���ⵥ�ӱ�', N'delete l from ST_StkRecordLine l where not exists (select 1 from ST_StkRecord s where s.cguid = l.cheadguid)', NULL, N'OM', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (662, NULL, N'ST_StkRecord', N'�ڳ�ί����ϳ��ⵥ', N'DELETE st from ST_StkRecord st WHERE st.cBillType = ''102'' AND st.ddate<={ddate}', NULL, N'OM', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (663, 662, N'ST_StkRecordLine', N'�ڳ�ί����ϳ��ⵥ�ӱ�', N'delete l from ST_StkRecordLine l where not exists (select 1 from ST_StkRecord s where s.cguid = l.cheadguid)', NULL, N'OM', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (552, NULL, N'ST_StkRecord', N'�ڳ��ɹ���ⵥ', N'DELETE st from ST_StkRecord st WHERE st.cBillType = ''091'' AND st.ddate<={ddate}', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (553, 552, N'ST_StkRecordLine', N'�ڳ��ɹ���ⵥ�ӱ�', N'delete from ST_StkRecordLine where not EXISTS (select 1 from ST_StkRecord where ST_StkRecord.cGUID = ST_StkRecordLine.cHeadGUID)', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (400, NULL, N'IA_Adjust', N'�������������', N'delete i from IA_Adjust i where (SELECT isnull(MAX(dl.dAccountDate), i.ddate) FROM IA_AdjustLine dl WHERE dl.cheadguid = i.cguid GROUP BY dl.cheadguid )<={ddate}', NULL, N'IA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (401, 400, N'IA_AdjustLine', N'����������ӱ�', N'delete from IA_AdjustLine where not exists (select 1 from IA_Adjust s where s.cguid = cheadguid)', NULL, N'IA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (404, NULL, N'IA_EnAdjust', N'�������������', N'delete i from IA_EnAdjust i where (SELECT isnull(MAX(dl.dAccountDate), i.ddate) FROM IA_EnAdjustLine dl WHERE dl.cheadguid = i.cguid GROUP BY dl.cheadguid )<={ddate}', NULL, N'IA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (405, 404, N'IA_EnAdjustLine', N'����������ӱ�', N'delete from IA_EnAdjustLine where not exists (select 1 from IA_EnAdjust s where s.cguid = cheadguid)', NULL, N'IA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (408, NULL, N'IA_GeneralLedger', N'�������', N'DELETE gl FROM IA_GeneralLedger gl WHERE ISNULL(gl.iInventoryFlag, 0) != 1 AND ( gl.iYear < {iYear} OR ( gl.iYear = {iYear} AND gl.iMonth <= {iMonth} ))', NULL, N'IA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (409, NULL, N'IA_SubsidiaryLedger', N'�����ϸ��', N'DELETE s FROM IA_SubsidiaryLedger s WHERE ISNULL(s.iInventoryFlag, 0) != 1 AND ( s.iYear < {iYear} OR ( s.iYear = {iYear} AND s.iMonth <= {iMonth}  ))', NULL, N'IA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (410, NULL, N'IA_EnSubsidiaryLedger', N'������Ʒ��ϸ��', N'DELETE s FROM IA_EnSubsidiaryLedger s WHERE s.daccountdate <= {ddate}', NULL, N'IA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (411, NULL, N'ST_StkRecord', N'���������', N'delete i from st_stkrecord i where ISNULL(i.iInventoryFlag,0)!=1 and ({IAsys} != 1 AND i.ddate <= {ddate} OR {IAsys} = 1 AND ( SELECT isnull(MAX(dl.dAccountDate), i.ddate) FROM st_stkrecordline dl WHERE dl.cheadguid = i.cguid GROUP BY dl.cheadguid) <= {ddate})', NULL, N'IA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (412, 411, N'ST_StkRecordLine', N'������ӱ�', N'delete from st_stkrecordline where not exists (select 1 from st_stkrecord s where s.cguid = cheadguid)', NULL, N'IA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (415, NULL, N'IA_CosDis', N'����Ʒ���������', N'DELETE  FROM IA_CosDis WHERE ISNULL(iDisperFlag,0) = 1  AND  ddate<={ddate}', NULL, N'IA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (416, 415, N'IA_CosDisLine', N'����Ʒ������ӱ�', N'delete from IA_CosDisLine where not exists (select 1 from IA_CosDis s where s.cguid = cheadguid)', NULL, N'IA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (417, 415, N'BILL_GEN_RELATION_MAIN', N'���ɹ�ϵ���������', N'delete from BILL_GEN_RELATION_MAIN where (cSMainEntity = ''IA_Adjust'' and not exists (select 1 from IA_Adjust s where s.cguid = cSMainID)) or (cDMainEntity = ''IA_Adjust'' and not exists (select 1 from IA_Adjust s where s.cguid = cDMainID)) or (cSMainEntity = ''IA_EnAdjust'' and not exists (select 1 from IA_EnAdjust s where s.cguid = cSMainID)) or (cDMainEntity = ''IA_EnAdjust'' and not exists (select 1 from IA_EnAdjust s where s.cguid = cDMainID)) or (cSMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord s where s.cguid = cSMainID)) or (cDMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord s where s.cguid = cDMainID)) or (cSMainEntity = ''IA_CosDis'' and not exists (select 1 from IA_CosDis s where s.cguid = cSMainID)) or (cDMainEntity = ''IA_CosDis'' and not exists (select 1 from IA_CosDis s where s.cguid = cDMainID))', NULL, N'IA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (418, 415, N'BILL_GEN_RELATION_DETAIL', N'���ɹ�ϵ�ӱ������', N'delete from BILL_GEN_RELATION_DETAIL where (cSMainEntity = ''IA_Adjust'' and not exists (select 1 from IA_Adjust s where s.cguid = cSMainID)) or (cDMainEntity = ''IA_Adjust'' and not exists (select 1 from IA_Adjust s where s.cguid = cDMainID)) or (cSMainEntity = ''IA_EnAdjust'' and not exists (select 1 from IA_EnAdjust s where s.cguid = cSMainID)) or (cDMainEntity = ''IA_EnAdjust'' and not exists (select 1 from IA_EnAdjust s where s.cguid = cDMainID)) or (cSMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord s where s.cguid = cSMainID)) or (cDMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord s where s.cguid = cDMainID)) or (cSMainEntity = ''IA_CosDis'' and not exists (select 1 from IA_CosDis s where s.cguid = cSMainID)) or (cDMainEntity = ''IA_CosDis'' and not exists (select 1 from IA_CosDis s where s.cguid = cDMainID))', NULL, N'IA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (150, NULL, N'AP_Payment', N'���', N'delete from AP_Payment where cFlag = ''AP'' and dVouDate <= {ddate} AND isnull(iInventoryFlag, 0) != 1', NULL, N'AP', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (151, 150, N'AP_PaymentDetail', N'�����ϸ', N'delete from AP_PaymentDetail where not exists (select 1 from AP_Payment s where s.cguid = cPaymentGUID)', NULL, N'AP', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (154, NULL, N'AP_APVoucher', N'����Ӧ����', N'delete from AP_APVoucher where cFlag = ''AP'' and dVouDate <= {ddate} AND isnull(iInventoryFlag, 0) != 1', NULL, N'AP', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (155, NULL, N'AP_APCheck', N'��������', N'delete from AP_APCheck where cFlag = ''AP'' and dVouDate <= {ddate}', NULL, N'AP', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (156, 155, N'AP_APCheckLine', N'�����ӱ�', N'delete from AP_APCheckLine where not exists (select 1 from AP_APCheck s where s.cguid = cHeadGUID)', NULL, N'AP', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (157, NULL, N'PU_Invoice', N'�ɹ���Ʊ����', N'delete from PU_Invoice where cSysType = ''AP'' and dInvDate <= {ddate} AND isnull(iInventoryFlag, 0) != 1', NULL, N'AP', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (215, NULL, N'ST_StkPosCheck', N'��λ�̵㵥��������', N'delete from ST_StkPosCheck where ddate <= {ddate}', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (216, 215, N'ST_StkPosCheckLine', N'��λ�̵㵥�ӱ�', N'delete l from ST_StkPosCheckLine l where not exists (select 1 from ST_StkPosCheck s where s.cguid = l.cheadguid)', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (219, NULL, N'ST_Assembly', N'��װ��ж������', N'delete from ST_Assembly where ddate <= {ddate}', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (220, 219, N'ST_AssemblyLine', N'��װ��ж���ӱ�', N'delete l from ST_StkPosCheckLine l where not exists (select 1 from ST_Assembly s where s.cguid = l.cheadguid)', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (223, NULL, N'ST_ShapeChange', N'��̬ת��������', N'delete from ST_ShapeChange where ddate <= {ddate}', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (224, 223, N'ST_ShapeChangeLine', N'��̬ת�����ӱ�', N'delete l from ST_StkPosCheckLine l where not exists (select 1 from ST_ShapeChange s where s.cguid = l.cheadguid)', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (225, 223, N'BILL_GEN_RELATION_MAIN', N'���ɹ�ϵ������棩', N'delete from BILL_GEN_RELATION_MAIN where (cSMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord s where s.cguid=cSMainID)) or (cDMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord s where s.cguid=cDMainID)) or (cSMainEntity = ''st_stkcheck'' and not exists (select 1 from st_stkcheck s where s.cguid=cSMainID)) or (cSMainEntity = ''ST_StkTrans'' and not exists (select 1 from ST_StkTrans s where s.cguid=cSMainID)) or (cSMainEntity = ''ST_StkPosCheck'' and not exists (select 1 from ST_StkPosCheck s where s.cguid=cSMainID)) or (cSMainEntity = ''ST_Assembly'' and not exists (select 1 from ST_Assembly s where s.cguid=cSMainID)) or (cSMainEntity = ''ST_ShapeChange'' and not exists (select 1 from ST_ShapeChange s where s.cguid=cSMainID))', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (226, 223, N'BILL_GEN_RELATION_DETAIL', N'���ɹ�ϵ�ӱ���棩', N'delete from BILL_GEN_RELATION_DETAIL where (cSMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord s where s.cguid=cSMainID)) or (cDMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord s where s.cguid=cDMainID)) or (cSMainEntity = ''st_stkcheck'' and not exists (select 1 from st_stkcheck s where s.cguid=cSMainID)) or (cSMainEntity = ''ST_StkTrans'' and not exists (select 1 from ST_StkTrans s where s.cguid=cSMainID)) or (cSMainEntity = ''ST_StkPosCheck'' and not exists (select 1 from ST_StkPosCheck s where s.cguid=cSMainID)) or (cSMainEntity = ''ST_Assembly'' and not exists (select 1 from ST_Assembly s where s.cguid=cSMainID)) or (cSMainEntity = ''ST_ShapeChange'' and not exists (select 1 from ST_ShapeChange s where s.cguid=cSMainID))', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (200, NULL, N'st_stkrecord', N'���ⵥ����', N'delete i from st_stkrecord i where ISNULL(i.iInventoryFlag,0)!=1 and ({IAsys} != 1 AND i.ddate <= {ddate} OR {IAsys} = 1 AND ( SELECT isnull(MAX(dl.dAccountDate), i.ddate) FROM st_stkrecordline dl WHERE dl.cheadguid = i.cguid GROUP BY dl.cheadguid) <= {ddate})', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (201, 200, N'st_stkrecordline', N'���ⵥ�ӱ��ӱ�', N'delete l from st_stkrecordline l where not exists (select 1 from st_stkrecord s where s.cguid = l.cheadguid)', NULL, N'ST', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (522, NULL, N'SA_Rebate', N'������', N'delete from SA_Rebate where ddate <= {ddate}', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (523, 522, N'SA_RebateLine', N'�������ӱ�', N'delete from SA_RebateLine where not EXISTS (select 1 from SA_Rebate where SA_Rebate.cGUID = SA_RebateLine.cHeadGUID)', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (524, NULL, N'st_stkrecord', N'�ɹ���ⵥ', N'delete i from st_stkrecord i where ISNULL(i.iInventoryFlag,0)!=1 and ({IAsys} != 1 AND i.ddate <= {ddate} OR {IAsys} = 1 AND ( SELECT isnull(MAX(dl.dAccountDate), i.ddate) FROM st_stkrecordline dl WHERE dl.cheadguid = i.cguid GROUP BY dl.cheadguid) <= {ddate})', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (525, 524, N'st_stkrecordline', N'�ɹ���ⵥ�ӱ�', N'delete from st_stkrecordline where not EXISTS (select 1 from st_stkrecord where ST_StkRecord.cGUID = ST_StkRecordLine.cHeadGUID) 
', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (300, NULL, N'SA_Rebate', N'���۷�����', N'delete from SA_Rebate where ddate <= {ddate}', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (301, 300, N'SA_RebateLine', N'���۷������ӱ�', N'delete l from SA_RebateLine l where not exists (select 1 from SA_Rebate s where s.cguid = l.cheadguid)', NULL, N'SA', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (606, 604, N'BILL_GEN_RELATION_MAIN', N'���ɹ�ϵ�����ʼ죩', N'delete from BILL_GEN_RELATION_MAIN where (cSMainEntity = ''QC_Checklist'' and not exists (select 1 from QC_Checklist s where s.cguid=cSMainID)) or (cDMainEntity = ''QC_Checklist'' and not exists (select 1 from QC_Checklist s where s.cguid=cDMainID)) or (cSMainEntity = ''QC_Defective'' and not exists (select 1 from QC_Defective s where s.cguid=cSMainID)) or (cDMainEntity = ''QC_Defective'' and not exists (select 1 from QC_Defective s where s.cguid=cDMainID))', NULL, N'QC', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (608, NULL, N'QC_PuReturn', N'�ɹ��˻ص�', N'delete from QC_PuReturn where dBillDate <= {ddate}', NULL, N'QC', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (609, 608, N'QC_PuReturnline', N'�ɹ��˻ص��ӱ�', N'delete l from QC_PuReturnline l where not exists (select 1 from QC_PuReturn s where s.cguid = l.cheadguid)', NULL, N'QC', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (520, NULL, N'PU_Settle', N'�ɹ����㵥', N'delete from PU_Settle where dSettleDate <= {ddate}', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (521, 520, N'PU_SettleLine', N'�ɹ����㵥�ӱ�', N'delete from PU_SettleLine where not EXISTS (select 1 from PU_Settle where PU_Settle.cGUID = PU_SettleLine.cHeadGUID)', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (528, NULL, N'PU_Invoice', N'�ɹ���Ʊ', N'delete from PU_Invoice where dInvDate <= {ddate} AND isnull(iInventoryFlag, 0) != 1', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (529, 528, N'PU_InvoiceLine', N'�ɹ���Ʊ�ӱ�', N'delete from PU_InvoiceLine where not EXISTS (select 1 from PU_Invoice where PU_Invoice.cGUID = PU_InvoiceLine.cHeadGUID) 
', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (530, NULL, N'PU_CarriageShare', N'�ɹ����÷�Ʊ���㵥', N'delete from PU_CarriageShare where ddate <= {ddate}', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (531, 530, N'PU_CarriageShareLine', N'�ɹ����÷�Ʊ���㵥�ӱ�', N'delete from PU_CarriageShareLine where not EXISTS (select 1 from PU_CarriageShare where PU_CarriageShare.cGUID = PU_CarriageShareLine.cHeadGUID)', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (532, NULL, N'PU_CarriageInvoice', N'�ɹ����÷�Ʊ', N'delete from PU_CarriageInvoice where ddate <= {ddate} AND isnull(iInventoryFlag, 0) != 1', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (533, 532, N'PU_CarriageInvoiceLine', N'�ɹ����÷�Ʊ�ӱ�', N'
delete from PU_CarriageInvoiceLine where not EXISTS (select 1 from PU_CarriageInvoice WHERE PU_CarriageInvoice.cGUID = PU_CarriageInvoiceLine.cHeadGUID)', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (534, 532, N'BILL_GEN_RELATION_MAIN', N'���ɹ�ϵ�����ɹ���', N'delete from BILL_GEN_RELATION_MAIN where (cSMainEntity = ''PU_Planhead'' and not exists (select 1 from PU_Planhead p where  p.cguid=cSMainID)) or (cDMainEntity = ''PU_Planhead'' and not exists (select 1 from PU_Planhead p where p.cguid=cDMainID)) or (cSMainEntity = ''PU_Order'' and not exists (select 1 from PU_Order p where p.cguid=cSMainID)) or (cDMainEntity = ''PU_Order'' and not exists (select 1 from PU_Order p where p.cguid=cDMainID)) or (cSMainEntity = ''PU_Receive'' and not exists (select 1 from PU_Receive p where p.cguid=cSMainID)) or (cDMainEntity = ''PU_Receive'' and not exists (select 1 from PU_Receive p where p.cguid=cDMainID)) or (cSMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord p where p.cguid=cSMainID)) or (cDMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord p where p.cguid=cDMainID)) or (cSMainEntity = ''PU_Invoice'' and not exists (select 1 from PU_Invoice p where p.cguid=cSMainID)) or (cDMainEntity = ''PU_Invoice'' and not exists (select 1 from PU_Invoice p where p.cguid=cDMainID)) or (cSMainEntity = ''PU_CarriageInvoice'' and not exists (select 1 from PU_CarriageInvoice p where p.cguid=cSMainID))', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (535, 532, N'BILL_GEN_RELATION_DETAIL', N'���ɹ�ϵ�ӱ��ɹ���', N'delete from BILL_GEN_RELATION_DETAIL where (cSMainEntity = ''PU_Planhead'' and not exists (select 1 from PU_Planhead p where  p.cguid=cSMainID)) or (cDMainEntity = ''PU_Planhead'' and not exists (select 1 from PU_Planhead p where p.cguid=cDMainID)) or (cSMainEntity = ''PU_Order'' and not exists (select 1 from PU_Order p where p.cguid=cSMainID)) or (cDMainEntity = ''PU_Order'' and not exists (select 1 from PU_Order p where p.cguid=cDMainID)) or (cSMainEntity = ''PU_Receive'' and not exists (select 1 from PU_Receive p where p.cguid=cSMainID)) or (cDMainEntity = ''PU_Receive'' and not exists (select 1 from PU_Receive p where p.cguid=cDMainID)) or (cSMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord p where p.cguid=cSMainID)) or (cDMainEntity = ''st_stkrecord'' and not exists (select 1 from st_stkrecord p where p.cguid=cDMainID)) or (cSMainEntity = ''PU_Invoice'' and not exists (select 1 from PU_Invoice p where p.cguid=cSMainID)) or (cDMainEntity = ''PU_Invoice'' and not exists (select 1 from PU_Invoice p where p.cguid=cDMainID)) or (cSMainEntity = ''PU_CarriageInvoice'' and not exists (select 1 from PU_CarriageInvoice p where p.cguid=cSMainID))', NULL, N'PU', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (607, 604, N'BILL_GEN_RELATION_DETAIL', N'���ɹ�ϵ�ӱ��ʼ죩', N'delete from BILL_GEN_RELATION_DETAIL where (cSMainEntity = ''QC_Checklist'' and not exists (select 1 from QC_Checklist s where s.cguid=cSMainID)) or (cDMainEntity = ''QC_Checklist'' and not exists (select 1 from QC_Checklist s where s.cguid=cDMainID)) or (cSMainEntity = ''QC_Defective'' and not exists (select 1 from QC_Defective s where s.cguid=cSMainID)) or (cDMainEntity = ''QC_Defective'' and not exists (select 1 from QC_Defective s where s.cguid=cDMainID))', NULL, N'QC', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (604, NULL, N'QC_Defective', N'���ϸ���', N'delete from QC_Defective where ddate <= {ddate}', NULL, N'QC', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (100, NULL, N'AP_Payment', N'�տ', N'delete from AP_Payment where cFlag = ''AR'' and dVouDate <= {ddate} AND isnull(iInventoryFlag, 0) != 1', NULL, N'AR', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (101, 100, N'AP_PaymentDetail', N'�տ��ϸ', N'delete from AP_PaymentDetail where not exists (select 1 from AP_Payment s where s.cguid = cPaymentGUID)', NULL, N'AR', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (104, NULL, N'AP_APVoucher', N'����Ӧ�յ�', N'delete from AP_APVoucher where cFlag = ''AR'' and dVouDate <= {ddate} AND isnull(iInventoryFlag, 0) != 1', NULL, N'AR', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (105, NULL, N'AP_APCheck', N'��������', N'delete from AP_APCheck where cFlag = ''AR'' and dVouDate <= {ddate}', NULL, N'AR', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (106, 105, N'AP_APCheckLine', N'�����ӱ�', N'delete from AP_APCheckLine where not exists (select 1 from AP_APCheck s where s.cguid = cHeadGUID)', NULL, N'AR', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (107, NULL, N'SA_Invoice', N'���۷�Ʊ����', N'delete from SA_Invoice where cSysType = ''AR'' and ddate <= {ddate} AND isnull(iInventoryFlag, 0) != 1', NULL, N'AR', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (108, 107, N'SA_Invoiceline', N'���۷�Ʊ�ӱ�', N'delete from SA_Invoiceline where not exists (select 1 from SA_Invoice s where s.cguid = cheadguid)', NULL, N'AR', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (109, 107, N'BILL_GEN_RELATION_MAIN', N'���ɹ�ϵ����Ӧ�գ�', N'delete from BILL_GEN_RELATION_MAIN where (cSMainEntity = ''ap_payment'' and not exists (select 1 from ap_payment s where s.cguid = cSMainID)) or (cDMainEntity = ''ap_payment'' and not exists (select 1 from ap_payment s where s.cguid = cDMainID)) or (cSMainEntity = ''sa_invoice'' and not exists (select 1 from sa_invoice s where s.cguid = cSMainID)) or (cDMainEntity = ''sa_invoice'' and not exists (select 1 from sa_invoice s where s.cguid = cDMainID))', NULL, N'AR', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (110, 107, N'BILL_GEN_RELATION_DETAIL', N'���ɹ�ϵ�ӱ�Ӧ�գ�', N'delete from BILL_GEN_RELATION_DETAIL where (cSMainEntity = ''ap_payment'' and not exists (select 1 from ap_payment s where s.cguid = cSMainID)) or (cDMainEntity = ''ap_payment'' and not exists (select 1 from ap_payment s where s.cguid = cDMainID)) or (cSMainEntity = ''sa_invoice'' and not exists (select 1 from sa_invoice s where s.cguid = cSMainID)) or (cDMainEntity = ''sa_invoice'' and not exists (select 1 from sa_invoice s where s.cguid = cDMainID))', NULL, N'AR', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (111, NULL, N'AP_Account', N'Ӧ�����˱�', N'delete from AP_Account where (iYear < {iYear} OR iYear = {iYear} AND iPeriod <= {iMonth} ) and cFlag = ''AR'' AND isnull(iInventoryFlag, 0) != 1', NULL, N'AR', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (112, NULL, N'AP_DetailAccount', N'Ӧ����ϸ��', N'delete from AP_DetailAccount where (iYear < {iYear} OR iYear = {iYear} AND iPeriod <= {iMonth} ) and cFlag = ''AR'' AND isnull(iInventoryFlag, 0) != 1', NULL, N'AR', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (113, NULL, N'AP_Exchange', N'��������', N'delete from AP_Exchange where iYear < {iYear} OR ( iYear = {iYear} AND iPeriod <= {iMonth} ) and cFlag = ''AR''', NULL, N'AR', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (158, 157, N'PU_Invoiceline', N'�ɹ���Ʊ�ӱ�', N'delete from PU_Invoiceline where not exists (select 1 from PU_Invoice s where s.cguid = cheadguid)', NULL, N'AP', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (159, 157, N'BILL_GEN_RELATION_MAIN', N'���ɹ�ϵ����Ӧ����', N'delete from BILL_GEN_RELATION_MAIN where (cSMainEntity = ''ap_payment'' and not exists (select 1 from ap_payment s where s.cguid = cSMainID)) or (cDMainEntity = ''ap_payment'' and not exists (select 1 from ap_payment s where s.cguid = cDMainID)) or (cSMainEntity = ''pu_invoice'' and not exists (select 1 from pu_invoice s where s.cguid = cSMainID)) or (cDMainEntity = ''pu_invoice'' and not exists (select 1 from pu_invoice s where s.cguid = cDMainID))', NULL, N'AP', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (160, 157, N'BILL_GEN_RELATION_DETAIL', N'���ɹ�ϵ�ӱ�Ӧ����', N'delete from BILL_GEN_RELATION_DETAIL where (cSMainEntity = ''ap_payment'' and not exists (select 1 from ap_payment s where s.cguid = cSMainID)) or (cDMainEntity = ''ap_payment'' and not exists (select 1 from ap_payment s where s.cguid = cDMainID)) or (cSMainEntity = ''pu_invoice'' and not exists (select 1 from pu_invoice s where s.cguid = cSMainID)) or (cDMainEntity = ''pu_invoice'' and not exists (select 1 from pu_invoice s where s.cguid = cDMainID))', NULL, N'AP', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (161, NULL, N'AP_Account', N'Ӧ�����˱�', N'delete from AP_Account where (iYear < {iYear} OR iYear = {iYear} AND iPeriod <= {iMonth} ) and cFlag = ''AP'' AND isnull(iInventoryFlag, 0) != 1', NULL, N'AP', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (162, NULL, N'AP_DetailAccount', N'Ӧ����ϸ��', N'delete from AP_DetailAccount where (iYear < {iYear} OR iYear = {iYear} AND iPeriod <= {iMonth} ) and cFlag = ''AP'' AND isnull(iInventoryFlag, 0) != 1', NULL, N'AP', N'1', NULL, NULL)
GO

INSERT INTO [dbo].[CO_TransferData] ([iOrder], [iRefOrder], [cTable], [cTableName], [cSrcSQLScript], [cHstrSQLScript], [cModule], [iStatus], [cDefine1], [cDefine2])
VALUES (163, NULL, N'AP_Exchange', N'��������', N'delete from AP_Exchange where iYear < {iYear} OR ( iYear = {iYear} AND iPeriod <= {iMonth} ) and cFlag = ''AP''', NULL, N'AP', N'1', NULL, NULL)
GO