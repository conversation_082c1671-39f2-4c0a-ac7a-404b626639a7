
INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (1, 'CA', '3', 'com.aisino.a6.finance.util.CorpBankStatementTransService', '1', NULL, NULL)
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (2, 'GL', '2', 'com.aisino.a6.finance.util.CashFlowService', '1', NULL, NULL)
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (1, 'SA', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', '���۾���У��', '2', 'scm_sa_trans.warndate')
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (2, 'SA', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', '���۴���У��', '1', 'scm_sa_trans.vaildate')
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (1, 'PU', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', '�ɹ�����У��', '2', 'scm_pu_trans.warndate')
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (2, 'PU', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', '�ɹ�����У��', '1', 'scm_pu_trans.vaildate')
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (1, 'QC', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', '�ʼ쾯��У��', '2', 'scm_qc_trans.warndate')
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (2, 'QC', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', '�ʼ����У��', '1', 'scm_qc_trans.vaildate')
GO


INSERT INTO [CO_RuleCfg] ([iOrder], [cModule], [cType], [cClassName], [cDesc], [cDefine1], [cDefine2]) VALUES (1, N'ST', N'2', N'com.aisino.a6.scm.util.STQCPlugin', N'����ת', N'1', NULL)
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (1, 'ST', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', '��澯��У��', '2', 'scm_st_trans.warndate')
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (2, 'ST', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', '������У��', '1', 'scm_st_trans.vaildate')
GO

INSERT INTO [CO_RuleCfg] ([iOrder], [cModule], [cType], [cClassName], [cDesc], [cDefine1], [cDefine2]) VALUES (1, N'ST', N'3', N'com.aisino.a6.scm.util.STSerinumberPlugin', N'������кŽ�ת', N'1', N'')
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (1, 'OM', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', 'ί�⾯��У��', '2', 'scm_om_trans.warndate')
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (2, 'OM', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', 'ί�����У��', '1', 'scm_om_trans.vaildate')
GO

INSERT INTO [CO_RuleCfg] ([iOrder], [cModule], [cType], [cClassName], [cDesc], [cDefine1], [cDefine2]) VALUES (1, N'IA', N'2', N'com.aisino.a6.scm.util.IAQCPlugin', N'�����ת', N'1', NULL)
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (1, 'IA', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', '�������У��', '2', 'scm_ia_trans.warndate')
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (2, 'IA', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', '�������У��', '1', 'scm_ia_trans.vaildate')
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (1, 'AR', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', 'Ӧ�վ���У��', '2', 'scm_ar_trans.warndate')
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (2, 'AR', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', 'Ӧ�մ���У��', '1', 'scm_ar_trans.vaildate')
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (1, 'AP', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', 'Ӧ������У��', '2', 'scm_ap_trans.warndate')
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (2, 'AP', '1', 'com.aisino.a6.scm.util.ScmValidateUtilService', 'Ӧ������У��', '1', 'scm_ap_trans.vaildate')
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (1, 'AR', '2', 'com.aisino.a6.scm.util.ARPQCPlugin', 'Ӧ��Ӧ����ת�ڳ�', NULL, NULL)
GO

INSERT INTO CO_RuleCfg (iOrder, cModule, cType, cClassName, cDesc, cDefine1, cDefine2) VALUES (1, 'AP', '2', 'com.aisino.a6.scm.util.ARPQCPlugin', 'Ӧ��Ӧ����ת�ڳ�', NULL, NULL)
GO