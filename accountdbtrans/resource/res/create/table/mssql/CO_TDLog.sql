
if object_id('CO_TDLog') is null 
begin
CREATE TABLE [CO_TDLog](
	[cGUID] [varchar](18) NOT NULL,
	[cType] [char](1) NOT NULL,
	[dDate] [datetime] NOT NULL,
	[cModule] [varchar](50) NULL,
	[cItem] [varchar](100) NOT NULL,
	[cResult] [char](1) NULL,
	[cDespt] [varchar](200) NOT NULL,
	[cExpt] [varchar](4000) NULL,
	[cDefine1] [varchar](50) NULL,
	[cDefine2] [varchar](100) NULL
) ON [PRIMARY]

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'��־����' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDLog', @level2type=N'COLUMN',@level2name=N'cType'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'��¼��ǰ�����ϵͳ����ʱ��' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDLog', @level2type=N'COLUMN',@level2name=N'dDate'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ģ��' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDLog', @level2type=N'COLUMN',@level2name=N'cModule'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'����������ҵ��������ݿ���' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDLog', @level2type=N'COLUMN',@level2name=N'cItem'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'������' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDLog', @level2type=N'COLUMN',@level2name=N'cResult'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'��ϸ����' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDLog', @level2type=N'COLUMN',@level2name=N'cDespt'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ԭʼ��' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDLog', @level2type=N'COLUMN',@level2name=N'cExpt'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�Զ����ֶ�1' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDLog', @level2type=N'COLUMN',@level2name=N'cDefine1'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�Զ����ֶ�2' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDLog', @level2type=N'COLUMN',@level2name=N'cDefine2'
end
GO

