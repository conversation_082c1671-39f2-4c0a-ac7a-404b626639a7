if not exists( select * from syscolumns where id=object_id('ST_StkRecord') and name='iInventoryFlag')
begin
ALTER TABLE [dbo].[ST_StkRecord] ADD [iInventoryFlag] smallint NULL
end
GO
if not exists( select * from syscolumns where id=object_id('IA_SubsidiaryLedger') and name='iInventoryFlag')
begin
ALTER TABLE [dbo].[IA_SubsidiaryLedger] ADD [iInventoryFlag] smallint NULL
end
GO
if not exists( select * from syscolumns where id=object_id('IA_GeneralLedger') and name='iInventoryFlag')
begin
ALTER TABLE [dbo].[IA_GeneralLedger] ADD [iInventoryFlag] smallint NULL
end
GO
if not exists( select * from syscolumns where id=object_id('AP_Payment') and name='iInventoryFlag')
begin
ALTER TABLE [dbo].[AP_Payment] ADD [iInventoryFlag] smallint NULL
end
GO
if not exists( select * from syscolumns where id=object_id('AP_APVoucher') and name='iInventoryFlag')
begin
ALTER TABLE [dbo].[AP_APVoucher] ADD [iInventoryFlag] smallint NULL
end
GO
if not exists( select * from syscolumns where id=object_id('sa_invoice') and name='iInventoryFlag')
begin
ALTER TABLE [dbo].[sa_invoice] ADD [iInventoryFlag] smallint NULL
end
GO
if not exists( select * from syscolumns where id=object_id('SA_CarriageInvoice') and name='iInventoryFlag')
begin
ALTER TABLE [dbo].[SA_CarriageInvoice] ADD [iInventoryFlag] smallint NULL
end
GO
if not exists( select * from syscolumns where id=object_id('PU_Invoice') and name='iInventoryFlag')
begin
ALTER TABLE [dbo].[PU_Invoice] ADD [iInventoryFlag] smallint NULL
end
GO
if not exists( select * from syscolumns where id=object_id('PU_CarriageInvoice') and name='iInventoryFlag')
begin
ALTER TABLE [dbo].[PU_CarriageInvoice] ADD [iInventoryFlag] smallint NULL
end
GO
if not exists( select * from syscolumns where id=object_id('OM_Invoice') and name='iInventoryFlag')
begin
ALTER TABLE [dbo].[OM_Invoice] ADD [iInventoryFlag] smallint NULL
end
GO
if not exists( select * from syscolumns where id=object_id('AP_Account') and name='iInventoryFlag')
begin
ALTER TABLE [dbo].[AP_Account] ADD [iInventoryFlag] smallint NULL
end
GO
if not exists( select * from syscolumns where id=object_id('AP_DetailAccount') and name='iInventoryFlag')
begin
ALTER TABLE [dbo].[AP_DetailAccount] ADD [iInventoryFlag] smallint NULL
end
GO