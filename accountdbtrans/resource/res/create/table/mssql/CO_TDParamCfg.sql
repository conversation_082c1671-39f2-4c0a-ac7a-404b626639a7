
if object_id('CO_TDParamCfg') is null 
begin
CREATE TABLE [CO_TDParamCfg](
	[code] [varchar](50) NOT NULL,
	[notelabel] [varchar](8000) NOT NULL,
	[parentfdm] [varchar](48) NOT NULL,
	[defvalue] [varchar](400) NOT NULL,
	[realvalue] [varchar](400) NOT NULL,
	[isvalid] [char](1) NOT NULL,
	[isshow] [char](1) NOT NULL,
	[CsOrd] [int] NOT NULL,
	[cDefine1] [varchar](50) NULL,
	[cDefine2] [varchar](50) NULL,
 CONSTRAINT [PK__CO_TDParamCfg__2D288360] PRIMARY KEY CLUSTERED 
(
	[code] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'��������' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDParamCfg', @level2type=N'COLUMN',@level2name=N'code'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'����������Ϣ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDParamCfg', @level2type=N'COLUMN',@level2name=N'notelabel'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'��������ģ��' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDParamCfg', @level2type=N'COLUMN',@level2name=N'parentfdm'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Ĭ��ֵ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDParamCfg', @level2type=N'COLUMN',@level2name=N'defvalue'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�û�ֵ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDParamCfg', @level2type=N'COLUMN',@level2name=N'realvalue'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�Ƿ���ñ�־' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDParamCfg', @level2type=N'COLUMN',@level2name=N'isvalid'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'������ʾ��־,y����ʾ��n������' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDParamCfg', @level2type=N'COLUMN',@level2name=N'isshow'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'���������򣨴Σ�' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDParamCfg', @level2type=N'COLUMN',@level2name=N'CsOrd'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�Զ����ֶ�1' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDParamCfg', @level2type=N'COLUMN',@level2name=N'cDefine1'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�Զ����ֶ�2' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDParamCfg', @level2type=N'COLUMN',@level2name=N'cDefine2'
end
GO

