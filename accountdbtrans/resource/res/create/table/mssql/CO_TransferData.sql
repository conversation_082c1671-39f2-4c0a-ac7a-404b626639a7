
if object_id('CO_TransferData') is null 
begin
CREATE TABLE [CO_TransferData](
	[iOrder] [int] NOT NULL,
	[iRefOrder] [int] NULL,
	[cTable] [varchar](50) NULL,
	[cTableName] [varchar](50) NULL,
	[cSrcSQLScript] [varchar](4000) NULL,
	[cHstrSQLScript] [varchar](4000) NULL,
	[cModule] [varchar](50) NOT NULL,
	[iStatus] [char](1) NOT NULL,
	[cDefine1] [varchar](50) NULL,
	[cDefine2] [varchar](100) NULL
) ON [PRIMARY]

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'˳���' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TransferData', @level2type=N'COLUMN',@level2name=N'iOrder'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ǰ����������ǰһ��ִ����ſ���ִ�и���' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TransferData', @level2type=N'COLUMN',@level2name=N'iRefOrder'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'A6���׿����ݿ�' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TransferData', @level2type=N'COLUMN',@level2name=N'cTable'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'���׿����ݿ��' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TransferData', @level2type=N'COLUMN',@level2name=N'cTableName'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'��ԭ������ִ�е����ݿ�ű�' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TransferData', @level2type=N'COLUMN',@level2name=N'cSrcSQLScript'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'����ʷ����������ִ�е����ݿ�ű�' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TransferData', @level2type=N'COLUMN',@level2name=N'cHstrSQLScript'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'���ݱ��ű�������ģ��' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TransferData', @level2type=N'COLUMN',@level2name=N'cModule'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�Ƿ���Ч��0��1' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TransferData', @level2type=N'COLUMN',@level2name=N'iStatus'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�Զ����ֶ�1' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TransferData', @level2type=N'COLUMN',@level2name=N'cDefine1'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�Զ����ֶ�2' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TransferData', @level2type=N'COLUMN',@level2name=N'cDefine2'
end
GO

