
if object_id('CO_TDLogMsg') is null 
begin
CREATE TABLE [dbo].[CO_TDLogMsg] (
  [cguid] varchar(18) COLLATE Chinese_PRC_CI_AS NOT NULL,
  [clogguid] varchar(18) COLLATE Chinese_PRC_CI_AS NULL,
  [type] smallint NULL,
  [cbillcode] varchar(50) COLLATE Chinese_PRC_CI_AS NULL,
  [ddate] datetime NULL,
  [cbilltype] varchar(50) COLLATE Chinese_PRC_CI_AS NULL,
  [iaccountflag] varchar(50) COLLATE Chinese_PRC_CI_AS NULL,
  [iauditstatus] varchar(50) COLLATE Chinese_PRC_CI_AS NULL,
  [vouchflag] varchar(50) COLLATE Chinese_PRC_CI_AS NULL,
  [overflag] varchar(50) COLLATE Chinese_PRC_CI_AS NULL,
  [iSettleFlag] varchar(50) COLLATE Chinese_PRC_CI_AS NULL,
  [iExeFlag] varchar(50) COLLATE Chinese_PRC_CI_AS NULL,
  [iCheckStatus] varchar(50) COLLATE Chinese_PRC_CI_AS NULL,
  CONSTRAINT [PK__CO_TDLog__46E81D5E7F7780C3] PRIMARY KEY CLUSTERED ([cguid])
) ON [PRIMARY]

EXEC sp_addextendedproperty 'MS_Description', N'��־��ϸ��Ϣ', 'schema', 'dbo', 'table', 'CO_TDLogMsg'

EXEC sp_addextendedproperty 'MS_Description', N'����', 'schema', 'dbo', 'table', 'CO_TDLogMsg', 'column', 'cguid'

EXEC sp_addextendedproperty 'MS_Description', N'��־id', 'schema', 'dbo', 'table', 'CO_TDLogMsg', 'column', 'clogguid'

EXEC sp_addextendedproperty 'MS_Description', N'1ʧ��;2����', 'schema', 'dbo', 'table', 'CO_TDLogMsg', 'column', 'type'

EXEC sp_addextendedproperty 'MS_Description', N'���ݱ���', 'schema', 'dbo', 'table', 'CO_TDLogMsg', 'column', 'cbillcode'

EXEC sp_addextendedproperty 'MS_Description', N'��������', 'schema', 'dbo', 'table', 'CO_TDLogMsg', 'column', 'ddate'

EXEC sp_addextendedproperty 'MS_Description', N'��������', 'schema', 'dbo', 'table', 'CO_TDLogMsg', 'column', 'cbilltype'

EXEC sp_addextendedproperty 'MS_Description', N'����״̬', 'schema', 'dbo', 'table', 'CO_TDLogMsg', 'column', 'iaccountflag'

EXEC sp_addextendedproperty 'MS_Description', N'���״̬', 'schema', 'dbo', 'table', 'CO_TDLogMsg', 'column', 'iauditstatus'

EXEC sp_addextendedproperty 'MS_Description', N'ƾ֤״̬', 'schema', 'dbo', 'table', 'CO_TDLogMsg', 'column', 'vouchflag'

EXEC sp_addextendedproperty 'MS_Description', N'���ת״̬', 'schema', 'dbo', 'table', 'CO_TDLogMsg', 'column', 'overflag'

EXEC sp_addextendedproperty 'MS_Description', N'����״̬', 'schema', 'dbo', 'table', 'CO_TDLogMsg', 'column', 'iSettleFlag'

EXEC sp_addextendedproperty 'MS_Description', N'����ִ��״̬', 'schema', 'dbo', 'table', 'CO_TDLogMsg', 'column', 'iExeFlag'

EXEC sp_addextendedproperty 'MS_Description', N'�ո������״̬', 'schema', 'dbo', 'table', 'CO_TDLogMsg', 'column', 'iCheckStatus'
end
GO

