
if object_id('CO_RuleCfg') is null 
begin
CREATE TABLE [CO_RuleCfg](
	[iOrder] [int] NOT NULL,
	[cModule] [varchar](50) NOT NULL,
	[cType] [varchar](1) NOT NULL,
	[cClassName] [varchar](100) NOT NULL,
	[cDesc] [varchar](50) NOT NULL,
	[cDefine1] [varchar](50) NULL,
	[cDefine2] [varchar](100) NULL
) ON [PRIMARY]

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'���' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_RuleCfg', @level2type=N'COLUMN',@level2name=N'iOrder'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ģ��' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_RuleCfg', @level2type=N'COLUMN',@level2name=N'cModule'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'���:1��ҵ�����2���ű�ǰ���ã�3���ű������' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_RuleCfg', @level2type=N'COLUMN',@level2name=N'cType'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'��' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_RuleCfg', @level2type=N'COLUMN',@level2name=N'cClassName'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'����' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_RuleCfg', @level2type=N'COLUMN',@level2name=N'cDesc'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�Զ����ֶ�1' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_RuleCfg', @level2type=N'COLUMN',@level2name=N'cDefine1'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�Զ����ֶ�2' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_RuleCfg', @level2type=N'COLUMN',@level2name=N'cDefine2'
end
GO

