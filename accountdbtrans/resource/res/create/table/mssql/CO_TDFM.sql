
if object_id('CO_TDFM') is null 
begin
CREATE TABLE [CO_TDFM](
	[iOrder] [int] NOT NULL,
	[cFieldCode] [varchar](50) NOT NULL,
	[cFieldName] [varchar](50) NOT NULL,
	[cModule] [varchar](50) NOT NULL,
	[cDefine1] [varchar](50) NULL,
	[cDefine2] [varchar](100) NULL
) ON [PRIMARY]

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�ڽ�����չʾ��˳��' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDFM', @level2type=N'COLUMN',@level2name=N'iOrder'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'FA��SC��MF' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDFM', @level2type=N'COLUMN',@level2name=N'cFieldCode'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'���񣬹�Ӧ������������' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDFM', @level2type=N'COLUMN',@level2name=N'cFieldName'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ģ��' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDFM', @level2type=N'COLUMN',@level2name=N'cModule'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�Զ����ֶ�1' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDFM', @level2type=N'COLUMN',@level2name=N'cDefine1'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�Զ����ֶ�2' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'CO_TDFM', @level2type=N'COLUMN',@level2name=N'cDefine2'
end
GO



