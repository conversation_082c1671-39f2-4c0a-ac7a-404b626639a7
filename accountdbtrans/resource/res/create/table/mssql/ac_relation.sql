
if object_id('ac_relation') is null 
begin
CREATE TABLE [ac_relation](
	[cAcctId] [varchar](50) NOT NULL,
	[cParentId] [varchar](50) NULL,
	[iLeaf] [smallint] NOT NULL,
	[iLevel] [smallint] NOT NULL,
	[cFullCode] [varchar](100) NOT NULL,
	[dDate] [datetime] NOT NULL,
	[iYear] [int] NULL,
	[iMonth] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[cAcctId] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'���׺�' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ac_relation', @level2type=N'COLUMN',@level2name=N'cAcctId'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�����׺�' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ac_relation', @level2type=N'COLUMN',@level2name=N'cParentId'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'�Ƿ񱻽�ת' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ac_relation', @level2type=N'COLUMN',@level2name=N'iLeaf'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'��ת���' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ac_relation', @level2type=N'COLUMN',@level2name=N'iLevel'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'��α���' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ac_relation', @level2type=N'COLUMN',@level2name=N'cFullCode'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'��תʱ��' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ac_relation', @level2type=N'COLUMN',@level2name=N'dDate'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'��ת��ֹ���' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ac_relation', @level2type=N'COLUMN',@level2name=N'iYear'

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'��ת��ֹ�ڼ�' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ac_relation', @level2type=N'COLUMN',@level2name=N'iMonth'
end
GO

