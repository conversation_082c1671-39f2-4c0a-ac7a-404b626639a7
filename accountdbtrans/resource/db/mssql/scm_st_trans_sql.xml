<?xml version="1.0" encoding="UTF-8"?>
<sqls>
	<sql group="scm_st_trans">

		<i id="warndate" desc="库存警告提示信息"><![CDATA[
          SELECT i.cbillcode
				,i.ddate
				,cb.cBillTypeName cbilltype
				,isnull(kjz.overflag, '') overflag
                ,isnull(vouchIA.vouchflag, '') vouchflag
                ,null as iSettleFlag
                ,'' iExeFlag
			FROM (
                  SELECT 
	                   cguid,
	                   cbillcode,
	                   cbilltype,
					   ddate,
					   iAuditStatus
			           FROM st_stkrecord 
			           
                      union all
                     
                   SELECT  
	                  cguid,
	                  cCode as cbillcode,
	                  cbilltype,
					  ddate,
					  iAuditStatus
			          FROM ST_Assembly )i
			LEFT JOIN(
                    SELECT          
                    l.csmainid
					,'存在目标单据跨结转期' overflag
				FROM (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'st_stkrecord'
						AND ii.ddate >{ddate}     )l
                    GROUP BY l.csmainid
				) kjz ON kjz.csmainid = i.cguid
          left join CO_BillType cb on cb.cBillTypeCode=i.cbilltype
          LEFT JOIN (
				SELECT l.cheadguid
					,'未生成凭证' vouchflag
				FROM ST_StkRecordLine l
				LEFT JOIN ST_StkRecord ii ON ii.cguid = l.cheadguid
				LEFT JOIN IA_SubsidiaryLedger ia ON l.cGUID = ia.cStkLineGUID
				LEFT JOIN FAP_BILL_VOU_REL vouchrela ON vouchrela.cBillGuid = ia.cGUID
				LEFT JOIN GL_Voucher vouch ON vouch.cGUID = vouchrela.cVouGuid
				WHERE ia.cbilltype IN ('070','050','040','060')
					AND ia.dAccountDate IS NOT NULL
					AND ia.iOutAMT <> 0
					AND {IAsys:0} = 1
					AND {GLsys:0} = 1
					AND vouch.cCode IS NULL
				GROUP BY l.cheadguid
				) vouchIA ON vouchIA.cheadguid = i.cguid
              WHERE 
				i.ddate <= {ddate}
				and i.iAuditStatus != 'tsaved'
				AND i.cbilltype in ('070','050','040','060','074','075','030')
				AND (
					kjz.overflag IS NOT NULL
					OR vouchIA.vouchflag IS NOT NULL
					)    
					
			/**单独处理一下委外加工入库单的校验*/
				UNION ALL
				
			SELECT * FROM (
				SELECT i.cbillcode
				,i.ddate
				,'委外加工入库单' cbilltype
				,isnull(kjz.overflag, '') overflag
				,isnull(vouchIA.vouchflag, '') vouchflag
				,case when setl.SUMisettleQTY/SUMiUnitQTY=1 then ''
               				else '未完全结算'
               		end as iSettleFlag
               	,'' iExeFlag
			FROM st_stkrecord i
			LEFT JOIN (
				SELECT l.csmainid
					,'存在目标单据跨结转期' overflag
				FROM (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'st_stkrecord'
						AND ii.ddate > {ddate}
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN OM_Invoice ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'om_invoice'
						AND ii.dInvDate > {ddate}
				
					) l
				GROUP BY l.csmainid
				) kjz ON kjz.csmainid = i.cguid
			LEFT JOIN (
				SELECT l.cheadguid
					,'未生成凭证' vouchflag
				FROM ST_StkRecordLine l
				LEFT JOIN ST_StkRecord ii ON ii.cguid = l.cheadguid
				LEFT JOIN IA_SubsidiaryLedger ia ON l.cGUID = ia.cStkLineGUID
				LEFT JOIN FAP_BILL_VOU_REL vouchrela ON vouchrela.cBillGuid = ia.cGUID
				LEFT JOIN GL_Voucher vouch ON vouch.cGUID = vouchrela.cVouGuid
				WHERE ia.cbilltype = '073'
					AND ia.dAccountDate IS NOT NULL
					AND {IAsys:0} = 1
					AND {GLsys:0} = 1
					AND vouch.cCode IS NULL
				GROUP BY l.cheadguid
				) vouchIA ON vouchIA.cheadguid = i.cguid
				
		   	LEFT JOIN (
					SELECT cHeadGUID,
							 Sum(isettleQTY) AS SUMisettleQTY,
							 case when Sum(iUnitQTY) = 0 then 1 else  Sum(iUnitQTY) end AS SUMiUnitQTY
					FROM   ST_StkRecordLine b
					GROUP  BY cHeadGUID
				) setl ON setl.cHeadGUID = i.cguid
			WHERE 
				i.ddate <= {ddate}
				and i.iAuditStatus != 'tsaved'
				AND i.cbilltype IN ('073') 
				) omstk
		WHERE (omstk.iSettleFlag != '' and {IAsys} = 1) 
			OR omstk.overflag != ''
			OR omstk.vouchflag != ''
					
			 /**单独处理组装拆卸未完全生单的校验*/
		UNION ALL
			SELECT i.cCode cbillcode
				,i.ddate
				,'组装拆卸单' cbilltype
				,'' overflag
				,'' vouchflag
				,'' iSettleFlag
				,'未完全生单' iExeFlag
			FROM ST_Assembly i
			WHERE i.ddate <= {ddate}
				AND iPullGenFlag = '1'
				AND EXISTS( SELECT cguid FROM ST_AssemblyLine s where s.iunitqty != s.iunitoutqty AND s.cheadguid = i.CGUID)		
		]]>
		</i>

		<i id="vaildate" desc="库存错误提示信息"><![CDATA[
			SELECT 
		    s.cbillcode,
				s.dDate,
				CASE  WHEN s.iAccountflag = '1' THEN ''
					ELSE '未记账' END AS iAccountflag,
				CASE  WHEN s.iAuditStatus != 'checked' THEN '未审核'
					ELSE '' END AS iAuditStatus,
				CASE WHEN s.cbilltype = '040' THEN '其它入库单'
						 WHEN s.cbilltype = '050' THEN '其它出库单' 
						 WHEN s.cbilltype = '060' THEN '成品入库单' 
						 WHEN s.cbilltype = '070' THEN '材料出库单' 
						 WHEN s.cbilltype = '073' THEN '委外入库单' 
						 WHEN s.cbilltype = '010' THEN '采购入库单' 
						 WHEN s.cbilltype = '020' THEN '销售出库单' 
					END AS cbilltype
			FROM st_stkrecord s
			LEFT JOIN (
				SELECT l.cheadguid
					,'未记账' iAccountflag
				FROM st_stkrecordline l
				LEFT JOIN CM_Storehouse sh ON l.cStoreGUID = sh.cGUID
				WHERE isnull(sh.iNoCalCost, 0) = 0
					AND isnull(l.iAccountflag, 0) = 0
					AND 1 = (
						SELECT 1
						FROM CO_SysInit
						WHERE cSubSysCode = 'IA'
							AND iInitYear IS NOT NULL
							AND iInitMonth IS NOT NULL
						)
				GROUP BY l.cheadguid
				) iacc ON iacc.cheadguid = s.cguid
			WHERE 
			  s.cbilltype IN ('040','050','060','070','073','010','020' )
			  AND ( SELECT cvalue from AOS_PREF_VALUE WHERE ccode = 'A6_IA_TransBillNoAcc') = 'y' 
			  AND s.cSysType = 'ST'
			   AND ( iacc.iAccountflag IS NOT NULL AND s.cbilltype != '020' OR iacc.iAccountflag IS NOT NULL AND (s.iIaFlag != 0 OR s.iAccDeliverGoods != 0) AND s.cbilltype = '020' OR s.iAuditStatus != 'checked' )
			   and s.ddate<= {ddate}
			   and ISNULL(s.iInitFlag, 0)= 0
			   and s.iAuditStatus != 'tsaved'

UNION ALL 


	SELECT 
		    s.cbillcode,
				s.dDate,
				CASE  WHEN s.iAccountflag != '1' THEN '未记账'
					ELSE '' END AS iAccountflag,
				CASE  WHEN s.iAuditStatus != 'checked' THEN '未审核'
					ELSE '' END AS iAuditStatus,
				CASE WHEN s.cbilltype = '040' THEN '其它入库单'
						 WHEN s.cbilltype = '050' THEN '其它出库单' 
						 WHEN s.cbilltype = '060' THEN '成品入库单' 
						 WHEN s.cbilltype = '070' THEN '材料出库单' 
						 WHEN s.cbilltype = '073' THEN '委外入库单' 
						 WHEN s.cbilltype = '010' THEN '采购入库单' 
						 WHEN s.cbilltype = '020' THEN '销售出库单' 
					END AS cbilltype
			FROM st_stkrecord s
			LEFT JOIN (
				SELECT l.cheadguid
					,'未记账' iAccountflag
				FROM st_stkrecordline l
				LEFT JOIN CM_Storehouse sh ON l.cStoreGUID = sh.cGUID
				WHERE isnull(sh.iNoCalCost, 0) = 0
					AND isnull(l.iAccountflag, 0) = 0
					AND 1 = (
						SELECT 1
						FROM CO_SysInit
						WHERE cSubSysCode = 'IA'
							AND iInitYear IS NOT NULL
							AND iInitMonth IS NOT NULL
						)
				GROUP BY l.cheadguid
				) iacc ON iacc.cheadguid = s.cguid
			WHERE 
			  ( s.cbilltype IN ('060','070','073','010','020' )
			  OR s.cBusType IN ( '04001','04002','04004','04005','04006','04007','04008','04009','05001','05002','05004','05005','05006','05007','05008','05009','05010') )
				AND ( SELECT cvalue from AOS_PREF_VALUE WHERE ccode = 'A6_IA_TransBillNoAcc') = 'n' 
				AND s.cSysType = 'ST'
			   AND ( iacc.iAccountflag IS NOT NULL OR s.iAuditStatus != 'checked' )
			   and s.ddate<= {ddate}
			   and ISNULL(s.iInitFlag, 0)= 0
			   and s.iAuditStatus != 'tsaved'
						
			UNION ALL
			SELECT 
			    s.cbillcode,
					s.dDate,
				  '' AS iAccountflag,
					CASE  WHEN s.iAuditStatus != 'checked' THEN '未审核'
						ELSE '' END AS iAuditStatus,
					'库存调拨单' AS cbilltype
				FROM ST_StkTrans s
				WHERE 
				  s.iAuditStatus != 'checked'
				  and  s.ddate<= {ddate}
				  and s.iAuditStatus != 'tsaved'
	
			UNION ALL
			
			SELECT 
			    s.cbillcode,
					s.dDate,
				  '' AS iAccountflag,
					CASE  WHEN s.iAuditStatus != 'checked' THEN '未审核'
						ELSE '' END AS iAuditStatus,
					'库存盘点单' AS cbilltype
				FROM ST_StkCheck s
				WHERE 
				  s.iAuditStatus != 'checked'
				   and  s.ddate<= {ddate}
				   and s.iAuditStatus != 'tsaved'
	
			UNION ALL
			
			SELECT 
			    s.ccode as cbillcode,
					s.dDate,
				  '' AS iAccountflag,
					CASE  WHEN s.iAuditStatus != 'checked' THEN '未审核'
						ELSE '' END AS iAuditStatus,
					CASE WHEN s.cbilltype = '074' THEN '组装单'
							 WHEN s.cbilltype = '075' THEN '拆卸单' 
						END AS cbilltype
				FROM ST_Assembly s
				WHERE 
				  s.iAuditStatus != 'checked'
				  and  s.ddate<= {ddate}
				  and s.iAuditStatus != 'tsaved'
	
			UNION ALL
			
			SELECT 
			    s.ccode as cbillcode,
					s.dDate,
				  '' AS iAccountflag,
					CASE  WHEN s.iAuditStatus != 'checked' THEN '未审核'
						ELSE '' END AS iAuditStatus,
					'形态转换单' AS cbilltype
				FROM ST_ShapeChange s
				WHERE 
				  s.iAuditStatus != 'checked'
				  and  s.ddate<= {ddate}
				  and s.iAuditStatus != 'tsaved'
		
		]]></i>

		<i id="deletestbill" desc="库存单据删除"><![CDATA[
			DELETE FROM 
			ST_StkCheckLine l
			LEFT JOIN ST_StkCheck s ON l.cHeadGUID = s.cguid
			WHERE iAuditStatus = 'checked'
			GO
			DELETE FROM 
			ST_StkCheck
			WHERE iAuditStatus = 'checked'
			GO
			
			DELETE FROM 
			ST_StkTransLine l
			LEFT JOIN ST_StkTrans s ON l.cHeadGUID = s.cguid
			WHERE iAuditStatus = 'checked'
			GO
			DELETE FROM 
			ST_StkTrans
			WHERE iAuditStatus = 'checked'
			GO
			
			DELETE FROM 
			ST_StkPosCheckLine l
			LEFT JOIN ST_StkPosCheck s ON l.cHeadGUID = s.cguid
			WHERE iAuditStatus = 'checked'
			GO
			DELETE FROM 
			ST_StkPosCheck
			WHERE iAuditStatus = 'checked'
			GO
			
			DELETE FROM 
			ST_AssemblyLine l
			LEFT JOIN ST_Assembly s ON l.cHeadGUID = s.cguid
			WHERE iAuditStatus = 'checked'
			GO
			DELETE FROM 
			ST_Assembly
			WHERE iAuditStatus = 'checked'
			GO
			
			DELETE FROM 
			ST_ShapeChangeLine l
			LEFT JOIN ST_ShapeChange s ON l.cHeadGUID = s.cguid
			WHERE iAuditStatus = 'checked'
			GO
			DELETE FROM 
			ST_ShapeChange
			WHERE iAuditStatus = 'checked'
			GO
			
			DELETE
			FROM ST_StkRecordLine stl
			LEFT JOIN CM_Storehouse sh on sh.cguid = stl.cstoreguid
			LEFT JOIN ST_StkRecord s on s.cguid = stl.cheadguid
			WHERE 
			(ISNULL(stl.iAccountflag, 0)=1
			OR ISNULL(sh.iNoCalCost, 0) = 1 OR ISNULL(s.iInitFlag, 0)= 1)
			AND iAuditStatus = 'checked'
			GO
			
			DELETE 
			FROM ST_StkRecord st
			WHERE
			( ISNULL(s.iInitFlag, 0)= 1
			OR 
			ISNULL(iAccountflag, 0)=1
			OR NOT EXISTS(
			SELECT FROM ST_StkRecordLine l 
			LEFT JOIN CM_Storehouse sh on sh.cguid = l.cstoreguid
			WHERE ISNULL(l.iaccountflag, 0) = 0 
			 AND ISNULL(sh.iNoCalCost, 0) = 0
			) )
			AND iAuditStatus = 'checked'
		]]></i>
		
		<i id="stkrecord_TEM" desc="结转库存期初单据_新建临时表"><![CDATA[
		if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ST_StkRecord_TEM]') and OBJECTPROPERTY(id, N'IsUserTable')=1) 
		drop table   ST_StkRecord_TEM;
		CREATE TABLE [dbo].[ST_StkRecord_TEM] (
				[cGUID] varchar(18) NOT NULL,
				[cHeadGUID] varchar(18) NULL,
				[iRSFlag] smallint NULL,
				[iRedFlag] smallint NULL,
				[cEmpGUID] varchar(18) NULL,
				[cDeptGUID] varchar(18) NULL,
				[cSupGUID] varchar(18) NULL,
				[cCustGUID] varchar(18) NULL,
				[cStoreGUID] varchar(18) NULL,
				[cMatGUID] varchar(18) NULL,
				[cBatchGUID] varchar(100) NULL,
				[cFree1] varchar(30) NULL,
				[cFree2] varchar(30) NULL,
				[cFree3] varchar(30) NULL,
				[cFree4] varchar(30) NULL,
				[cFree5] varchar(30) NULL,
				[cFree6] varchar(30) NULL,
				[cFree7] varchar(30) NULL,
				[cFree8] varchar(30) NULL,
				[cFree9] varchar(30) NULL,
				[cFree10] varchar(30) NULL,
				[cFreeValue] varchar(500) NULL,
				[cFreeName] varchar(4000) NULL,
				[dOverDate] datetime NULL,
				[dProduction] datetime NULL,
				[iGuaranteeDays] numeric(21,9) NULL,
				[cItemGUID] varchar(18) NULL,
				[cMUnitGUID] varchar(18) NULL,
				[cStkUnitGUID] varchar(18) NULL,
				[iUnitQTY] numeric(21,9) NULL DEFAULT ((0)),
				[iQTY] numeric(21,9) NULL DEFAULT ((0)),
				[iChangeRate] numeric(21,9) NULL DEFAULT ((1)),
				[iAMT] numeric(21,9) NULL,
				[iTotal] numeric(21,9) NULL,
				[iTax] numeric(21,9) NULL,
				[iAMT_F] numeric(21,9) NULL,
				[iTax_F] numeric(21,9) NULL,
				[iTotal_F] numeric(21,9) NULL,
				[iMUnitPrice] numeric(21,9) NULL,
				[iMUnitPrice_F] numeric(21,9) NULL,
				[iUnitPrice_F] numeric(21,9) NULL,
				[iUnitPrice] numeric(21,9) NULL,
				[iTaxPrice] numeric(21,9) NULL,
				[iTaxPrice_F] numeric(21,9) NULL,
				[iMTaxPrice] numeric(21,9) NULL,
				[iMUnitTaxPrice_F] numeric(21,9) NULL,
				[cPositionGUID] varchar(18) NULL,
				[cRowPosInfo] varchar(500) NULL
			)
		]]></i>
		
		<i id="insertstinitbill_stkrecord_TEM" desc="结转库存期初单据_临时表"><![CDATA[
		INSERT INTO [ST_StkRecord_TEM] (
				[cGUID],
				[cHEADGUID],
				[iRSFlag],
				[iRedFlag],
				[cEmpGUID],
				[cDeptGUID],
				[cSupGUID],
				[cStoreGUID],
				[cMatGUID],
				[cBatchGUID],
				[cFree1],
				[cFree2],
				[cFree3],
				[cFree4],
				[cFree5],
				[cFree6],
				[cFree7],
				[cFree8],
				[cFree9],
				[cFree10],
				[cfreevalue],
				[cfreename],
				[dOverDate],
				[dProduction],
				[iGuaranteeDays],
				[cItemGUID],
				[cMUnitGUID],
				[cStkUnitGUID],
				[iUnitQTY],
				[iQTY],
				[ichangerate],
				[iAMT],
				[iTotal],
				[iTax],
				[iAMT_F],
				[iTax_F],
				[iTotal_F],
				[iMUnitPrice],
				[iMUnitPrice_F],
				[iUnitPrice_F],
				[iUnitPrice],
				[iTaxPrice],
				[iTaxPrice_F],
				[iMTaxPrice],
				[iMUnitTaxPrice_F],
				[cPositionGUID],
				[cRowPosInfo]

		)
				SELECT
								LEFT (newID(), 18) AS cGUID,
								LEFT (newID(), 18) AS cHEADGUID,
								'1' AS iRSFlag,
								CASE WHEN SUM(iUnitQTY) > 0 THEN '0' 
										ELSE '1' 
								END AS iRedflag,
								cEmpGUID,
								cDeptGUID,
								cSupGUID,
								cStoreGUID,
								cMatGUID,
								cBatchGUID,
								cFree1,
								cFree2,
								cFree3,
								cFree4,
								cFree5,
								cFree6,
								cFree7,
								cFree8,
								cFree9,
								cFree10,
								cfreevalue,
								cfreename,
								dOverDate,
								dProduction,
								iGuaranteeDays,
								cItemGUID,
								cMUnitGUID,
								cStkUnitGUID,
								SUM(iUnitQTY) AS iUnitQTY,
								SUM(iQTY) AS iQTY,
								CASE WHEN SUM(iQTY) = 0 THEN 0 ELSE SUM(iUnitQTY)/SUM(iQTY) END  AS ichangerate,
								SUM(iamt) AS iAMT,
								SUM(iTotal) AS iTotal,
								SUM(iTax) AS iTax,
								SUM(iAMT_F) AS iAMT_F,
								SUM(iTax_F) AS iTax_F,
								SUM(iTotal_F) AS iTotal_F,
								CASE WHEN SUM(iUnitQTY) = 0 THEN 0 ELSE SUM(ISNULL(iamt, 0))/SUM(iUnitQTY) END AS iMUnitPrice,
								CASE WHEN SUM(iUnitQTY) = 0 THEN 0 ELSE SUM(ISNULL(iAMT_F, 0))/SUM(iUnitQTY) END AS iMUnitPrice_F,
								CASE WHEN SUM(iQTY) = 0 THEN 0 ELSE SUM(ISNULL(iAMT_F, 0))/SUM(iQTY) END AS iUnitPrice_F,
								CASE WHEN SUM(iQTY) = 0 THEN 0 ELSE SUM(ISNULL(iAMT, 0))/SUM(iQTY) END AS iUnitPrice,
								CASE WHEN SUM(iQTY) = 0 THEN 0 ELSE SUM(ISNULL(iTax, 0))/SUM(iQTY) END AS iTaxPrice,
								CASE WHEN SUM(iQTY) = 0 THEN 0 ELSE SUM(ISNULL(iTax_F, 0))/SUM(iQTY) END AS iTaxPrice_F,
								CASE WHEN SUM(iUnitQTY) = 0 THEN 0 ELSE SUM(ISNULL(iTax, 0))/SUM(iUnitQTY) END AS iMTaxPrice,
								CASE WHEN SUM(iUnitQTY) = 0 THEN 0 ELSE SUM(ISNULL(iTax_F, 0))/SUM(iUnitQTY) END AS iMUnitTaxPrice_F,
								cPositionGUID,
								cRowPosInfo
							FROM
								(
									SELECT
								Isnull(stp.cGUID, stl.cGUID) AS cGUID,
								st.cEmpGUID,
								st.cDeptGUID,
								'' AS cSupGUID,
								stl.cMatGUID,
								stl.cBatchGUID,
								stl.cFree1,
								stl.cFree2,
								stl.cFree3,
								stl.cFree4,
								stl.cFree5,
								stl.cFree6,
								stl.cFree7,
								stl.cFree8,
								stl.cFree9,
								stl.cFree10,
								stl.cfreevalue,
								stl.cfreename,
								stl.dOverDate,
								stl.dProduction,
								stl.iGuaranteeDays,
								stl.cItemGUID,
								stl.cMUnitGUID,
								cm.cStkUnitID AS cStkUnitGUID,
								CASE WHEN stp.iRSFlag=0 THEN -1*Isnull(stp.iUnitQTY, stl.iUnitQTY)
								ELSE Isnull(stp.iUnitQTY, stl.iUnitQTY)
								END AS iUnitQTY,
								CASE WHEN stp.iRSFlag=0 THEN -1*(  CASE WHEN cuc.iRateFlag = 2 THEN Isnull(stp.iQTY, stl.iQty) ELSE Isnull(stp.iUnitQTY, stl.iUnitQTY) / cmr.iChangeRate END)
								ELSE ( CASE WHEN cuc.iRateFlag = 2 THEN Isnull(stp.iQTY, stl.iQty) ELSE Isnull(stp.iUnitQTY, stl.iUnitQTY) / cmr.iChangeRate END)
								END AS iQTY,
								0 AS iamt,
								0 AS iTotal,
								0 AS iTax,
								0 AS iAMT_F,
								0 AS iTax_F,
								0 AS iTotal_F,
								stp.cStoreGUID,
								stp.cPositionGUID,
								p.cCode AS cRowPosInfo,
								stp.iRSFlag
							FROM
								ST_PosAdjust st
							LEFT JOIN ST_PosAdjustLine stl ON st.cGUID = stl.cHeadGUID
							LEFT JOIN ST_MatPosition stp ON (stp.cAdjustLineID = stl.cGUID)
							LEFT JOIN CM_Position p ON p.cguid = stp.cPositionGUID
							LEFT JOIN CM_Material cm ON cm.cGUID = stl.cMatGUID
							LEFT JOIN CM_MatUnitRate cmr ON (
								cmr.cMatGUID = cm.cGUID
								AND cmr.cUnitGUID = cm.cStkUnitID
							)
							LEFT JOIN CM_Unit cmu ON stl.cMUnitGUID = cmu.cGUID
							LEFT JOIN CM_UnitClass cuc ON cmu.cClassGUID = cuc.cGUID
							WHERE
								st.iAuditStatus = 'checked'
								and st.ddate <= {ddate}
							UNION ALL
								SELECT
									Isnull(stp.cGUID, stl.cGUID) AS cGUID,
									st.cEmpGUID,
									st.cDeptGUID,
									st.cSupGUID,
									stl.cMatGUID,
									stl.cBatchGUID,
									stl.cFree1,
									stl.cFree2,
									stl.cFree3,
									stl.cFree4,
									stl.cFree5,
									stl.cFree6,
									stl.cFree7,
									stl.cFree8,
									stl.cFree9,
									stl.cFree10,
									stl.cfreevalue,
									stl.cfreename,
									stl.dOverDate,
									stl.dProduction,
									stl.iGuaranteeDays,
									stl.cItemGUID,
									stl.cMUnitGUID,
									cm.cStkUnitID AS cStkUnitGUID,
									case WHEN st.iRSFlag = 1 then Isnull(stp.iUnitQTY, stl.iUnitQTY) else -1*Isnull(stp.iUnitQTY, stl.iUnitQTY) end AS iUnitQTY,
									case when st.iRSFlag = 1 THEN Isnull(stp.iQTY, stl.iQty) else -1*Isnull(stp.iQTY, stl.iQty)  END AS iQTY,
								case when st.iRSFlag = 1 THEN ( case when (st.cBillType='020' or st.cBillType='212') then stl.iCostAMT else stl.iAMT end ) else -1*( case when (st.cBillType='020' or st.cBillType='212') then stl.iCostAMT else stl.iAMT end ) end as iamt,
								case when st.iRSFlag = 1 THEN stl.iTotal else -1*stl.iTotal end as iTotal,
								case when st.iRSFlag = 1 THEN stl.iTax else -1*stl.iTax end as iTax,
								case when st.iRSFlag = 1 THEN ( case when (st.cBillType='020' or st.cBillType='212') then stl.iCostAMT else stl.iAMT end ) else -1*( case when (st.cBillType='020' or st.cBillType='212') then stl.iCostAMT else stl.iAMT end ) end as iAMT_F,
								case when st.iRSFlag = 1 THEN stl.iTax_F else -1*stl.iTax_F end as iTax_F,
								case when st.iRSFlag = 1 THEN stl.iTotal_F else -1*stl.iTotal_F end as iTotal_F,
								stl.cStoreGUID,
								stp.cPositionGUID,
								p.cCode AS cRowPosInfo,
								st.iRSFlag
							FROM
								ST_StkRecord st
							LEFT JOIN ST_StkRecordLine stl ON st.cGUID = stl.cHeadGUID
							LEFT JOIN CM_STOREHOUSE sh ON sh.cguid = stl.cstoreguid
							LEFT JOIN ST_MatPosition stp ON (stp.cStkLineID = stl.cGUID)
							LEFT JOIN CM_Position p ON p.cguid = stp.cPositionGUID
							LEFT JOIN CM_Material cm ON cm.cGUID = stl.cMatGUID
							LEFT JOIN CM_MatUnitRate cmr ON (
								cmr.cMatGUID = cm.cGUID
								AND cmr.cUnitGUID = cm.cStkUnitID
							)
							LEFT JOIN CM_Unit cmu ON stl.cMUnitGUID = cmu.cGUID
							LEFT JOIN CM_UnitClass cuc ON cmu.cClassGUID = cuc.cGUID
							WHERE
								st.iAuditStatus = 'checked'
								AND ( (ISNULL(stl.iAccountflag, 0)!=0 AND ISNULL(sh.iNoCalCost, 0) != 1 ) 
									OR st.cBillType = '030'
									OR ( ISNULL(stl.iAccountflag, 0)= 0 AND ISNULL(sh.iNoCalCost, 0) = 1 )
									OR {IAsys:0} = 0 )
							AND st.cBillType NOT IN( '091','101','021')
							and st.ddate <= {ddate}
							) AS storeinfo
						GROUP BY
							cEmpGUID,
							cDeptGUID,
							cSupGUID,
							cMatGUID,
							cBatchGUID,
							cFree1,
							cFree2,
							cFree3,
							cFree4,
							cFree5,
							cFree6,
							cFree7,
							cFree8,
							cFree9,
							cFree10,
							cfreevalue,
							cfreename,
							dOverDate,
							dProduction,
							iGuaranteeDays,
							cItemGUID,
							cMUnitGUID,
							cStkUnitGUID,
							cStoreGUID,
							cPositionGUID,
							cRowPosInfo
						
		]]></i>
		<i id="insertstinitbill_stkrecord" desc="结转库存期初单据(主表)"><![CDATA[
				INSERT INTO [ST_StkRecord] (
				[iNumber],
				[cGUID],
				[cBillCode],
				[dDate],
				[cSupGUID],
				[cCustGUID],
				[cStoreGUID],
				[cDeptGUID],
				[cEmpGUID],
				[cCreatorGUID],
				[cAuditorGUID],
				[iAuditStatus],
				[iRSFlag],
				[iRedFlag],
				[cBillType],
				[cBusType],
				[cIOTypeID],
				[cSourceType],
				[cPosterGUID],
				[iAccountflag],
				[dAccountDate],
				[iCurAccountFlag],
				[cInvCode],
				[cHandler],
				[cPurTypeGUID],
				[cSalTypeGUID],
				[cSysType],
				[iInitFlag],
				[cCurGUID],
				[iCurRate],
				[cDefine1],
				[cDefine2],
				[cDefine3],
				[cDefine4],
				[cDefine5],
				[cDefine6],
				[cDefine7],
				[cDefine8],
				[cDefine9],
				[cDefine10],
				[cDefine11],
				[cDefine12],
				[cDefine13],
				[cDefine14],
				[cDefine15],
				[cDefine16],
				[cDefine17],
				[cDefine18],
				[cDefine19],
				[cDefine20],
				[cTimeStamp],
				[cProductID],
				[cOrdID],
				[iSum],
				[iPrintCount],
				[cCustAddress],
				[cModifyName],
				[dCreatorTime],
				[dModifyTime],
				[cAcctIItemGuid],
				[cFreightGUID],
				[iWFcheckstatus],
				[cCheckWay],
				[dCheckTime],
				[cBillgen],
				[cMergedGen],
				[cExeFields],
				[cAntiWFields],
				[UDEF_STK_SupAddr],
				[UDEF_STK_SupTel],
				[cSettleTypeGUID],
				[cPayGUID],
				[cPayTime],
				[cKPCustomer],
				[cRegCode],
				[cTransBillCode],
				[cTransGUID],
				[cTemplateId],
				[iReturnFlag],
				[iIsExchange],
				[cReceivingEnt],
				[iMolingAMT],
				[iReceivableAMT],
				[iPaidAMT],
				[iChangeAMT],
				[cBankAccount],
				[cReceivableBillCode],
				[cKPCusPhone],
				[cCustBank],
				[cAccount],
				[invtype],
				[cTaxType],
				[cRemark],
				[dDueDate],
				[iIaFlag],
				[iAccDeliverGoods],
				[cBusProcess],
				[iSettleflag],
				[cConsigCompany],
				[cConsignee],
				[cConsigTel],
				[cCloserGUID],
				[cCloseReason],
				[iCloseFlag],
				[cDispCode],
				[dCloseTime],
				[cInvCust],
				[cZNJGUID],
				[iPrintNum],
				[cCustCode],
				[cAddress],
				[cFax],
				[cPrinter],
				[cSubsidiaryLedgerId],
				[cBusCode],
				[ineedreturnflag],
				[cSpuBank],
				[iInventoryFlag]
			)
			SELECT 
				ROW_NUMBER() OVER (ORDER BY CMATGUID DESC) as iNumber,
				cHeadguid as cGUID,
				cHeadguid as cBillCode,
				 {dDate} as dDate,
				cSupGUID,
				NULL AS cCustGUID,
				cStoreGUID,
				cDeptGUID,
				cEmpGUID,
				'1' AS cCreatorGUID,
				'1' AS cAuditorGUID,
				'checked' AS iAuditStatus,
				'1' as iRSFlag,
				iRedFlag,
				'030' cBillType,
				'03001' AS cBusType,
				NULL AS cIOTypeID,
				NULL AS cSourceType,
				NULL AS cPosterGUID,
				'0' AS iAccountflag,
				NULL AS dAccountDate,
				NULL AS iCurAccountFlag,
				NULL AS cInvCode,
				NULL AS cHandler,
				NULL AS cPurTypeGUID,
				NULL AS cSalTypeGUID,
				'ST' AS cSysType,
				'1' AS iInitFlag,
				NULL AS cCurGUID,
				NULL AS iCurRate,
				NULL AS cDefine1,
				NULL AS cDefine2,
				NULL AS cDefine3,
				NULL AS cDefine4,
				NULL AS cDefine5,
				NULL AS cDefine6,
				NULL AS cDefine7,
				NULL AS cDefine8,
				NULL AS cDefine9,
				NULL AS cDefine10,
				NULL AS cDefine11,
				NULL AS cDefine12,
				NULL AS cDefine13,
				NULL AS cDefine14,
				NULL AS cDefine15,
				NULL AS cDefine16,
				NULL AS cDefine17,
				NULL AS cDefine18,
				NULL AS cDefine19,
				NULL AS cDefine20,
				cguid as cTimeStamp,
				NULL AS cProductID,
				NULL AS cOrdID,
				NULL AS iSum,
				NULL AS iPrintCount,
				NULL AS cCustAddress,
				NULL AS cModifyName,
				NULL AS dCreatorTime,
				NULL AS dModifyTime,
				NULL AS cAcctIItemGuid,
				NULL AS cFreightGUID,
				'0' AS iWFcheckstatus,
				NULL AS cCheckWay,
				NULL AS dCheckTime,
				NULL AS cBillgen,
				NULL AS cMergedGen,
				NULL AS cExeFields,
				NULL AS cAntiWFields,
				NULL AS UDEF_STK_SupAddr,
				NULL AS UDEF_STK_SupTel,
				NULL AS cSettleTypeGUID,
				NULL AS cPayGUID,
				NULL AS cPayTime,
				NULL AS cKPCustomer,
				NULL AS cRegCode,
				NULL AS cTransBillCode,
				NULL AS cTransGUID,
				NULL AS cTemplateId,
				NULL AS iReturnFlag,
				NULL AS iIsExchange,
				NULL AS cReceivingEnt,
				NULL AS iMolingAMT,
				NULL AS iReceivableAMT,
				NULL AS iPaidAMT,
				NULL AS iChangeAMT,
				NULL AS cBankAccount,
				NULL AS cReceivableBillCode,
				NULL AS cKPCusPhone,
				NULL AS cCustBank,
				NULL AS cAccount,
				NULL AS invtype,
				NULL AS cTaxType,
				NULL AS cRemark,
				NULL AS dDueDate,
				'0' AS iIaFlag,
				'0' AS iAccDeliverGoods,
				NULL AS cBusProcess,
				NULL AS iSettleflag,
				NULL AS cConsigCompany,
				NULL AS cConsignee,
				NULL AS cConsigTel,
				NULL AS cCloserGUID,
				NULL AS cCloseReason,
				'0' AS iCloseFlag,
				NULL AS cDispCode,
				NULL AS dCloseTime,
				NULL AS cInvCust,
				NULL AS cZNJGUID,
				'0' AS iPrintNum,
				NULL AS cCustCode,
				NULL AS cAddress,
				NULL AS cFax,
				NULL AS cPrinter,
				'0' AS cSubsidiaryLedgerId,
				NULL AS cBusCode,
				NULL AS ineedreturnflag,
				NULL AS cSpuBank,
				'1' as iInventoryFlag
			FROM ST_StkRecord_tem s
			where s.iqty != 0 or s.iunitqty != 0 
			
			
	]]></i>
	
		<i id="insertstinitbill_stkrecordline" desc="结转库存期初单据(子表)"><![CDATA[
		
		     INSERT INTO [ST_StkRecordLine] (
				[cGUID],
				[cHeadGUID],
				[iNumber],
				[cMatGUID],
				[cUnitGUID],
				[cBatchGUID],
				[iQTY],
				[iUnitPrice],
				[iAMT],
				[iTaxPrice],
				[iTotal],
				[iTax],
				[dSettleDate],
				[iSettleQTY],
				[iSettleAMT],
				[iOutQTY],
				[iInvoiceQTY],
				[iQuotedPrice],
				[iTaxRate],
				[cMUnitGUID],
				[iUnitQTY],
				[dProduction],
				[iGuaranteeDays],
				[dOverDate],
				[iTempPrice],
				[iTempAMT],
				[iSettleFlag],
				[cItemGUID],
				[cItemClassGUID],
				[iMUnitPrice_F],
				[iMUnitPrice],
				[iChangeRate],
				[iSaleUnitPrice],
				[iSaleAMT],
				[iUnitPrice_F],
				[iAMT_F],
				[iTax_F],
				[iTotal_F],
				[iAccountflag],
				[cDefine1],
				[cDefine2],
				[cDefine3],
				[cDefine4],
				[cDefine5],
				[cDefine6],
				[cDefine7],
				[cDefine8],
				[cDefine9],
				[cDefine10],
				[cDefine11],
				[cDefine12],
				[cDefine13],
				[cDefine14],
				[cDefine15],
				[cDefine16],
				[cDefine17],
				[cDefine18],
				[cDefine19],
				[cDefine20],
				[cFree1],
				[cFree2],
				[cFree3],
				[cFree4],
				[cFree5],
				[cFree6],
				[cFree7],
				[cFree8],
				[cFree9],
				[cFree10],
				[cTimeStamp],
				[iInvoiceAMT],
				[iCarriage],
				[cOmOrdGUID],
				[iOmUnitPrice],
				[iOmAMT],
				[iMtlUnitPrice],
				[iMtlAMT],
				[iCheckFlag],
				[dCheckDate],
				[iCheckAMT],
				[iCheckQTY],
				[PlanQTY],
				[iReplaceMark],
				[iReplacePart],
				[iTaxPrice_F],
				[iTaxQuotedPrice],
				[cOrdCode],
				[cMOOrderCode],
				[cPUOrderCode],
				[cPUStkInCode],
				[cPUInvCode],
				[iInvoiceAMT_F],
				[cSAOrderCode],
				[cSAStkOutCode],
				[cSAInvCode],
				[cSADisCode],
				[cOMOrderCode],
				[cOMRecordCode],
				[iTMPMUnitPrice],
				[ctempPositionGUID],
				[cBillgen],
				[cMergedGen],
				[cExeFields],
				[cAntiWFields],
				[cFreeValue],
				[cFreeName],
				[iTransGUID],
				[iDisRate],
				[iDisRateCon],
				[iDisAMT_F],
				[iDisAMT],
				[iCostMUnitPrice],
				[iCostAMT],
				[cStoreGUID],
				[iOutAMT],
				[iOutAMT_F],
				[csarefepricemethod],
				[cSaRefePriceUnitGUID],
				[iRefePriceQTY],
				[cPURefePriceMethod],
				[cPURefePriceUnitGUID],
				[cProductID],
				[iConsignSettleQTY],
				[iConsignSettleAMT],
				[iConsignSettleAMT_F],
				[cRowPosInfo],
				[irowposnum],
				[iPositionQty],
				[iReOutQTY],
				[iReOutAMT],
				[iReOutAMT_F],
				[iReturnQTY],
				[iReturnTotal],
				[iReturnTotal_F],
				[iExchangeQTY],
				[iExchangeTotal],
				[iExchangeTotal_F],
				[iReturnAMT_F],
				[iReturnAMT],
				[cSalQty],
				[iCostPrice],
				[iMinPrice],
				[iMaxPrice],
				[izp],
				[iReceiveTotal_F],
				[iReceiveTotal],
				[iSettleAMT_F],
				[iIsGift],
				[iUnitOutQTY],
				[cRemark],
				[cSupMatGUID],
				[iMolingAMT],
				[iPayAMT],
				[iAccDeliverGoods],
				[iIaFlag],
				[iCurAccountFlag],
				[cPosterGUID],
				[dAccountDate],
				[cCloserGUID],
				[cCloseReason],
				[iCloseFlag],
				[CusMatGUID],
				[cQCDefectiveCode],
				[cPUReceiveCode],
				[cRowSnInfo],
				[dCloseTime],
				[cPriceInfo],
				[iMTaxPrice],
				[iMUnitTaxPrice_F],
				[iSInQTY],
				[CMOBATCH],
				[iQuotedTotal_F],
				[iOutTotal],
				[iOutTotal_F],
				[cRowSnInfoNew],
				[iHighPrice],
				[iLowPrice],
				[cSrcBillType],
				[iBatchOutQTY],
				[iUnitBatchOutQTY],
				[cPUPlanCode],
				[iSTFetch],
				[iPcCalFlag],
				[isiamtnull],
				[cSupMatCode],
				[cSupMatName],
				[cCusMatCode],
				[cCusMatName],
				[iReturnFlag],
				[ctranslineguid],
				[cDispatchCode],
				[cStdoperationGUID],
				[iRecPuPrice]
			)
			
			SELECT 
				s.cGUID,
				s.cHeadGUID,
				ROW_NUMBER() OVER (
						ORDER BY s.CMATGUID DESC
						) as iNumber,
				s.cMatGUID,
				m.cStkUnitID AS cUnitGUID,
				s.cBatchGUID,
				s.iQTY,
				s.iUnitPrice,
				s.iAMT,
				s.iTaxPrice,
				s.iTotal,
				s.iTax,
				NULL AS dSettleDate,
				NULL AS iSettleQTY,
				NULL AS iSettleAMT,
				NULL AS iOutQTY,
				NULL AS iInvoiceQTY,
				NULL AS iQuotedPrice,
				m.iTaxRate,
				s.cMUnitGUID,
				s.iUnitQTY,
				s.dProduction,
				s.iGuaranteeDays,
				s.dOverDate,
				NULL AS iTempPrice,
				NULL AS iTempAMT,
				NULL AS iSettleFlag,
				s.cItemGUID,
				NULL AS cItemClassGUID,
				s.iMUnitPrice_F,
				s.iMUnitPrice,
				s.iChangeRate,
				NULL AS iSaleUnitPrice,
				NULL AS iSaleAMT,
				s.iUnitPrice_F,
				s.iAMT_F,
				s.iTax_F,
				s.iTotal_F,
				0 AS iAccountflag,
				NULL AS cDefine1,
				NULL AS cDefine2,
				NULL AS cDefine3,
				NULL AS cDefine4,
				NULL AS cDefine5,
				NULL AS cDefine6,
				NULL AS cDefine7,
				NULL AS cDefine8,
				NULL AS cDefine9,
				NULL AS cDefine10,
				NULL AS cDefine11,
				NULL AS cDefine12,
				NULL AS cDefine13,
				NULL AS cDefine14,
				NULL AS cDefine15,
				NULL AS cDefine16,
				NULL AS cDefine17,
				NULL AS cDefine18,
				NULL AS cDefine19,
				NULL AS cDefine20,
				s.cFree1,
				s.cFree2,
				s.cFree3,
				s.cFree4,
				s.cFree5,
				s.cFree6,
				s.cFree7,
				s.cFree8,
				s.cFree9,
				s.cFree10,
				s.cGUID AS cTimeStamp,
				NULL AS iInvoiceAMT,
				NULL AS iCarriage,
				NULL AS cOmOrdGUID,
				NULL AS iOmUnitPrice,
				NULL AS iOmAMT,
				NULL AS iMtlUnitPrice,
				NULL AS iMtlAMT,
				NULL AS iCheckFlag,
				NULL AS dCheckDate,
				NULL AS iCheckAMT,
				NULL AS iCheckQTY,
				NULL AS PlanQTY,
				NULL AS iReplaceMark,
				NULL AS iReplacePart,
				s.iTaxPrice_F,
				NULL AS iTaxQuotedPrice,
				NULL AS cOrdCode,
				NULL AS cMOOrderCode,
				NULL AS cPUOrderCode,
				NULL AS cPUStkInCode,
				NULL AS cPUInvCode,
				NULL AS iInvoiceAMT_F,
				NULL AS cSAOrderCode,
				NULL AS cSAStkOutCode,
				NULL AS cSAInvCode,
				NULL AS cSADisCode,
				NULL AS cOMOrderCode,
				NULL AS cOMRecordCode,
				NULL AS iTMPMUnitPrice,
				s.cpositionguid AS ctempPositionGUID,
				NULL AS cBillgen,
				NULL AS cMergedGen,
				NULL AS cExeFields,
				NULL AS cAntiWFields,
				s.cFreeValue,
				s.cFreeName,
				NULL AS iTransGUID,
				NULL AS iDisRate,
				NULL AS iDisRateCon,
				NULL AS iDisAMT_F,
				NULL AS iDisAMT,
				NULL AS iCostMUnitPrice,
				NULL AS iCostAMT,
				s.cStoreGUID,
				NULL AS iOutAMT,
				NULL AS iOutAMT_F,
				NULL AS csarefepricemethod,
				NULL AS cSaRefePriceUnitGUID,
				NULL AS iRefePriceQTY,
				NULL AS cPURefePriceMethod,
				NULL AS cPURefePriceUnitGUID,
				NULL AS cProductID,
				NULL AS iConsignSettleQTY,
				NULL AS iConsignSettleAMT,
				NULL AS iConsignSettleAMT_F,
				s.cRowPosInfo,
				case when sh.iPositionFlag = 1 then 1 ELSE NULL END AS irowposnum,
				CASE WHEN sh.iPositionFlag = 1 then s.iUnitQTY else NULL end AS iPositionQty,
				NULL AS iReOutQTY,
				NULL AS iReOutAMT,
				NULL AS iReOutAMT_F,
				NULL AS iReturnQTY,
				NULL AS iReturnTotal,
				NULL AS iReturnTotal_F,
				NULL AS iExchangeQTY,
				NULL AS iExchangeTotal,
				NULL AS iExchangeTotal_F,
				NULL AS iReturnAMT_F,
				NULL AS iReturnAMT,
				NULL AS cSalQty,
				NULL AS iCostPrice,
				NULL AS iMinPrice,
				NULL AS iMaxPrice,
				NULL AS izp,
				NULL AS iReceiveTotal_F,
				NULL AS iReceiveTotal,
				NULL AS iSettleAMT_F,
				NULL AS iIsGift,
				NULL AS iUnitOutQTY,
				NULL AS cRemark,
				NULL AS cSupMatGUID,
				NULL AS iMolingAMT,
				NULL AS iPayAMT,
				NULL AS iAccDeliverGoods,
				'0' AS iIaFlag,
				NULL AS iCurAccountFlag,
				NULL AS cPosterGUID,
				NULL AS dAccountDate,
				NULL AS cCloserGUID,
				NULL AS cCloseReason,
				NULL AS iCloseFlag,
				NULL AS CusMatGUID,
				NULL AS cQCDefectiveCode,
				NULL AS cPUReceiveCode,
				NULL AS cRowSnInfo,
				NULL AS dCloseTime,
				NULL AS cPriceInfo,
				s.iMTaxPrice,
				s.iMUnitTaxPrice_F,
				NULL AS iSInQTY,
				NULL AS CMOBATCH,
				NULL AS iQuotedTotal_F,
				NULL AS iOutTotal,
				NULL AS iOutTotal_F,
				NULL AS cRowSnInfoNew,
				NULL AS iHighPrice,
				NULL AS iLowPrice,
				NULL AS cSrcBillType,
				NULL AS iBatchOutQTY,
				NULL AS iUnitBatchOutQTY,
				NULL AS cPUPlanCode,
				NULL AS iSTFetch,
				NULL AS iPcCalFlag,
				NULL AS isiamtnull,
				NULL AS cSupMatCode,
				NULL AS cSupMatName,
				NULL AS cCusMatCode,
				NULL AS cCusMatName,
				NULL AS iReturnFlag,
				NULL AS ctranslineguid,
				NULL AS cDispatchCode,
				NULL AS cStdoperationGUID,
				NULL AS iRecPuPrice
			FROM ST_StkRecord_tem s
				LEFT JOIN CM_Material m ON m.cguid = s.cmatguid
				LEFT JOIN cm_storehouse sh ON sh.cguid = s.cstoreguid
			where s.iqty != 0 or s.iunitqty != 0 
		]]></i>

		<i id="dealposbill" desc="处理货位数据"><![CDATA[
		
		     /**DELETE
					FROM
					ST_MatPosition  WHERE  cStkLineID NOT  IN(SELECT CGUID FROM ST_StkRecordLine)
					AND  cAdjustID is null 
				DELETE
					FROM
					ST_MatPosition p
					LEFT JOIN ST_StkRecordLine stl ON stl.cguid = p.cStkLineID
					LEFT JOIN ST_StkRecord st ON st.cguid = stl.cheadguid
					WHERE
					st.iAuditStatus = 'checked'
					and p.cAdjustID is null 
					AND (ISNULL(stl.iAccountflag, 0)=1
								OR ISNULL(sh.iNoCalCost, 0) = 0 )
				GO8**/
				
				INSERT INTO [ST_MatPosition] (
					[cGUID],
					[cMatGUID],
					[cStoreGUID],
					[cBatchGUID],
					[cPositionGUID],
					[cStkGuid],
					[cStkLineID],
					[cAdjustLineID],
					[cUnitGUID],
					[iQTY],
					[cMUnitGUID],
					[iUnitQTY],
					[dDate],
					[iRSFlag],
					[iUnitPrice],
					[iAMT],
					[iTaxPrice],
					[cFree1],
					[cFree2],
					[cFree3],
					[cFree4],
					[cFree5],
					[cFree6],
					[cFree7],
					[cFree8],
					[cFree9],
					[cFree10],
					[cAdjustID],
					[iAuditStatus],
					[cBillType],
					[cAcctIItemGuid],
					[dProduction],
					[iGuaranteeDays],
					[dCheckTime],
					[cBusType],
					[dOverDate],
					[cFreeName],
					[iInitFlag],
					[cRemark],
					[cCreatorGUID],
					[cAuditorGUID],
					[cBillCode],
					[iBatchOutQTY],
					[iUnitBatchOutQTY]
				) SELECT
					LEFT (newID(), 18) AS cGUID,
					stl.cMatGUID,
					stl.cStoreGUID,
					stl.cBatchGUID,
					stl.cPositionGUID,
					stl.cHeadGUID AS cStkGuid,
					stl.cGUID AS cStkLineID,
					NULL AS cAdjustLineID,
					stl.cstkUnitGUID,
					stl.iQTY,
					stl.cMUnitGUID,
					stl.iUnitQTY,
					{dDate} as dDate,
					stl.iRSFlag,
					stl.iUnitPrice,
					stl.iAMT,
					stl.iTaxPrice,
					stl.cFree1,
					stl.cFree2,
					stl.cFree3,
					stl.cFree4,
					stl.cFree5,
					stl.cFree6,
					stl.cFree7,
					stl.cFree8,
					stl.cFree9,
					stl.cFree10,
					NULL AS cAdjustID,
					'checked' AS iAuditStatus,
					'030' AS cBillType,
					stl.citemguid,
					stl.dProduction,
					stl.iGuaranteeDays,
					NULL AS dCheckTime,
					'03001' AS cBusType,
					stl.dOverDate,
					stl.cFreeName,
					'1' AS iInitFlag,
					null as cRemark,
					null as cCreatorGUID,
					null as cAuditorGUID,
					stl.cHeadguid as cBillCode,
					CASE
				WHEN stl.iRSFlag = '0' THEN
					stl.iQTY
				ELSE
					NULL
				END AS iBatchOutQTY,
				 CASE
				WHEN stl.iRSFlag = '0' THEN
					stl.iUnitQTY
				ELSE
					NULL
				END AS iUnitBatchOutQTY
				FROM ST_StkRecord_tem stl
				LEFT JOIN CM_Storehouse sh on sh.cguid = stl.cstoreguid
				WHERE
					sh.ipositionflag = 1
		]]></i>


		<i id="serialnumdatabeforetrans" desc="结转单据对应的结存不为0的序列号"><![CDATA[
			SELECT
				a.cSerialNumber,
				a.cMatGUID,
				a.cMatGUID,
				a.cSerialNumber,
				a.cStoreGUID,
				a.cBatchGUID,
				a.cItemGUID,
				a.cPositionGUID,
				a.cFree1,a.cFree2,a.cFree3,a.cFree4,a.cFree5,a.cFree6,a.cFree7,a.cFree8,a.cFree9,a.cFree10,
				SUM(a.iAuditStatus) AS iauditstatus
			FROM
				(
					SELECT
						st.cMatGUID,
						st.cSerialNumber,
						st.cStoreGUID,
						st.cBatchGUID,
						st.cItemGUID,
						st.cPositionGUID,
						st.cFree1,st.cFree2,st.cFree3,st.cFree4,st.cFree5,st.cFree6,st.cFree7,st.cFree8,st.cFree9,st.cFree10,
						CASE st.iAuditStatus WHEN 'zk' THEN 1 
							WHEN 'yc' THEN - 1
							ELSE 0
						END AS iAuditStatus
					FROM
						ST_SerialNumber st
					LEFT JOIN ST_StkRecord s on ( st.cStkInHeadGUID = s.cGUID OR st.cStkOutHeadGUID = s.cGUID )
					WHERE
						( ISNULL(s.iAccountflag, 0)=1
						OR NOT EXISTS(
						SELECT 1 FROM ST_StkRecordLine l 
						LEFT JOIN CM_Storehouse sh on sh.cguid = l.cstoreguid
						WHERE ISNULL(l.iaccountflag, 0) = 0 
						 AND ISNULL(sh.iNoCalCost, 0) = 0
						) )
						AND s.iAuditStatus = 'checked'
				) a
			GROUP BY
				a.cSerialNumber,
				a.cMatGUID,
				a.cMatGUID,
				a.cSerialNumber,
				a.cStoreGUID,
				a.cBatchGUID,
				a.cItemGUID,
				a.cPositionGUID,
				a.cFree1,a.cFree2,a.cFree3,a.cFree4,a.cFree5,a.cFree6,a.cFree7,a.cFree8,a.cFree9,a.cFree10
			HAVING
				SUM (a.iAuditStatus) != 0
	
	]]></i>

		<i id="deleteserialnumdata" desc="结转单据对应的可删除的序列号"><![CDATA[
			SELECT
				a.cSerialNumber,
				a.cMatGUID,
				a.cSerialNumber,
				a.cStoreGUID,
				a.cBatchGUID,
				a.cItemGUID,
				a.cPositionGUID,
				a.cFree1,a.cFree2,a.cFree3,a.cFree4,a.cFree5,a.cFree6,a.cFree7,a.cFree8,a.cFree9,a.cFree10,
				SUM(a.iAuditStatus) AS iauditstatus
			FROM
				(
					SELECT
						st.cMatGUID,
						st.cSerialNumber,
						st.cStoreGUID,
						st.cBatchGUID,
						st.cItemGUID,
						st.cPositionGUID,
						st.cFree1,st.cFree2,st.cFree3,st.cFree4,st.cFree5,st.cFree6,st.cFree7,st.cFree8,st.cFree9,st.cFree10,
						CASE st.iAuditStatus WHEN 'zk' THEN 1 
							WHEN 'yc' THEN - 1
							ELSE 0
						END AS iAuditStatus
					FROM
						ST_SerialNumber st
					LEFT JOIN ST_StkRecord s on ( st.cStkInHeadGUID = s.cGUID OR st.cStkOutHeadGUID = s.cGUID )
					WHERE
						( ISNULL(s.iAccountflag, 0)=1
						OR NOT EXISTS(
						SELECT 1 FROM ST_StkRecordLine l 
						LEFT JOIN CM_Storehouse sh on sh.cguid = l.cstoreguid
						WHERE ISNULL(l.iaccountflag, 0) = 0 
						 AND ISNULL(sh.iNoCalCost, 0) = 0
						) )
						AND s.iAuditStatus = 'checked'
				) a
			GROUP BY
				a.cSerialNumber,
				a.cMatGUID,
				a.cMatGUID,
				a.cSerialNumber,
				a.cStoreGUID,
				a.cBatchGUID,
				a.cItemGUID,
				a.cPositionGUID,
				a.cFree1,a.cFree2,a.cFree3,a.cFree4,a.cFree5,a.cFree6,a.cFree7,a.cFree8,a.cFree9,a.cFree10
			HAVING
				SUM (a.iAuditStatus) = 0
		]]></i>

		<i id="aftertransinitserialsnumdata" desc="结转的期初单据-序列号物品"><![CDATA[
			SELECT 
				stl.cMatGUID+stl.cStoreGUID+stl.cBatchGUID+stl.cItemGUID+stl.ctempPositionGUID+stl.cFree1+stl.cFree2+stl.cFree3+stl.cFree4+stl.cFree5+stl.cFree6+stl.cFree7+stl.cFree8+stl.cFree9+stl.cFree10
					AS serialsmat,
				case when s.iRSFlag = '1' THEN stl.cguid
						ELSE NULL END AS cStkInGUID,
				case when s.iRSFlag = '1' THEN s.cguid
						ELSE NULL END AS cStkInHeadGUID,
				case when s.iRSFlag = '0' THEN stl.cguid
						ELSE NULL END AS cStkOutGUID,
				case when s.iRSFlag = '0' THEN s.cguid
						ELSE NULL END AS cStkOutHeadGUID
			FROM ST_StkRecord s
				LEFT JOIN ST_StkRecordLine stl on stl.cHeadGUID = s.cguid
				LEFT JOIN CM_Material cm on stl.cMatGUID = cm.cGUID
			WHERE s.cbustype = '03002'
				AND ISNULL(cm.iSNStart, 0) =  1
		]]></i>


		<i id="delSerinumberZK" desc="删去截至期间前面的已经出库的对应的入库的序列号表的数据">
	   <![CDATA[
	
           delete  ST_SerialNumber from ST_SerialNumber se
                 where not EXISTS  (select 1 from st_stkrecordline st 
                          left join cm_material cm on cm.cguid=st.cmatguid
                           where cm.isnstart='1'
                           and st.cguid=se.cStkInGUID
                          )
	              and se.iInOutFlag='1'
	              and se.iOutFlag='1'
	              and se.iauditstatus='zk' 
		]]>
		</i>


		<i id="delSerinumberYC" desc="删去截至期间前面的已经出库的序列号表的数据">
	   <![CDATA[
		
			delete  ST_SerialNumber from ST_SerialNumber se
                where not EXISTS  (select 1 from st_stkrecordline st 
                          left join cm_material cm on cm.cguid=st.cmatguid
                           where cm.isnstart='1'
                           and st.cguid=se.cStkOutGUID
                          )
	              and se.iInOutFlag='0'
	              and se.iauditstatus='yc' 
	              and se.ioutguid is not null
		]]>
		</i>

		<i id="getTransSerinumberZK" desc="获取截至日期前仍然在库的序列号的各个维度信息，此部分序列号需要结转">
	   <![CDATA[
		select 
			  ss.stklineID, 
			  ss.stkheadID, 
			  ss.iqty as 'stk_iqty',
			  p.*  
		      from 
	              (  select  
		                s1.cguid SerID,
		                s1.cMatGUID,
		                s1.cStoreGUID,
		                s1.cSerialNumber,
		                s1.cItemGUID,
						s1.cPositionGUID,
						s1.cBatchGUID,
						s1.iOutFlag,
						cFree1,
						cFree2,
						cFree3,
						cFree4,
						cFree5,
						cFree6,
						cFree7,
						cFree8,
						cFree9,
						cFree10
				from  ST_SerialNumber s1
				left join ST_StkRecord s on s.cguid=s1.cStkInHeadGUID 
				where s.ddate<{ddate}    
				and (s1.iAuditStatus='zk' or (s1.iAuditStatus='yc' and  s1.ioutguid is null))
				
			)p
         inner join (
                     select 
                         
		                 sst.cGUID as stklineID,
		                 sst.cheadguid stkheadID,
		                 sst.iqty,
		                 sst.cMatGUID,
		                 sst.cStoreGUID,
		                 sst.cItemGUID,
						 stp.cPositionGUID,
						 sst.cBatchGUID,
						sst.cFree1,
						sst.cFree2,
						sst.cFree3,
						sst.cFree4,
						sst.cFree5,
						sst.cFree6,
						sst.cFree7,
						sst.cFree8,
						sst.cFree9,
						sst.cFree10
		                from  ST_StkRecordLine  sst
		                left join st_stkrecord s on (s.cguid=sst.cheadguid )
		                left JOIN ST_MatPosition stp ON stp.cStkLineID = sst.cGUID
		                left join cm_material cm on cm.cguid=sst.cmatguid
		                where s.ddate<{ddate}   
		                and s.cbilltype='030'
		                and  cm.isnstart='1'
		                and  s.iInventoryFlag='1'
                )ss   on(
		                 p.cMatGUID=ss.cMatGUID and 
		                 p.cStoreGUID=ss.cStoreGUID and 
		                 ISNULL(p.cItemGUID,'')= ISNULL(ss.cItemGUID,'') and 
		                 ISNULL(p.cBatchGUID,'')=ISNULL(ss.cBatchGUID,'') and 
		                 ISNULL(p.cPositionGUID,'')=ISNULL(ss.cPositionGUID,'') and
					     ISNULL(p.cFree1,'')=ISNULL(ss.cFree1,'')  and
		                 ISNULL(p.cFree2,'')=ISNULL(ss.cFree2,'')  and
		                 ISNULL( p.cFree3,'')=ISNULL(ss.cFree3,'') and
		                 ISNULL(p.cFree4,'')=ISNULL(ss.cFree4,'')  and
		                 ISNULL(p.cFree5,'')=ISNULL(ss.cFree5,'')  and
		                 ISNULL( p.cFree6,'')=ISNULL(ss.cFree6,'') and
		                 ISNULL(p.cFree7,'')=ISNULL(ss.cFree7,'')  and
		                 ISNULL(p.cFree8,'')=ISNULL(ss.cFree8,'')  and
		                 ISNULL(p.cFree9,'')=ISNULL( ss.cFree9,'') and
		                 ISNULL(p.cFree10,'')=ISNULL(ss.cFree10 ,'')    
		                 )
		                  order  by ss.stklineID,p.cSerialNumber
		]]>
		</i>
	</sql>
</sqls>