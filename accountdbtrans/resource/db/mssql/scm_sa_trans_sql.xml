<?xml version="1.0" encoding="UTF-8"?>
<sqls>
	<sql group="scm_sa_trans">
		<i id="vaildate" desc="失败数据校验"><![CDATA[
			SELECT i.cbillcode
				,i.ddate
				,'销售订单' cbilltype
				,'' iAccountflag
				,CASE 
					WHEN i.iAuditStatus != 'checked'
						THEN '未审核'
					ELSE ''
					END AS iAuditStatus
			FROM sa_order i
			WHERE i.iAuditStatus != 'checked'
				AND i.ddate <= {ddate}
				and i.iAuditStatus != 'tsaved'
			
			UNION ALL
			
			SELECT i.cbillcode
				,i.ddate
				,'销售发货单' cbilltype
				,'' iAccountflag
				,CASE 
					WHEN i.iAuditStatus != 'checked'
						THEN '未审核'
					ELSE ''
					END AS iAuditStatus
			FROM sa_dispatch i
			WHERE i.iAuditStatus != 'checked'
				AND i.ddate <= {ddate}
				and i.iAuditStatus != 'tsaved'
			
			UNION ALL
			
			SELECT i.cbillcode
				,i.ddate
				,'零售单' cbilltype
				,isnull(iacc.iAccountflag, '') iAccountflag
				,CASE 
					WHEN i.iAuditStatus != 'checked'
						THEN '未审核'
					ELSE ''
					END AS iAuditStatus
			FROM st_stkrecord i
			LEFT JOIN (
				SELECT l.cheadguid
					,'未记账' iAccountflag
				FROM st_stkrecordline l
				LEFT JOIN CM_Material m ON l.cMatGUID = m.cGUID
				LEFT JOIN CM_Storehouse sh ON l.cStoreGUID = sh.cGUID
				WHERE isnull(sh.iNoCalCost, 0) = 0
					AND isnull(l.iAccountflag, 0) = 0
					AND {IAsys} = 1
				GROUP BY l.cheadguid
				) iacc ON iacc.cheadguid = i.cguid
			WHERE i.cbilltype = '212'
				AND i.ddate <= {ddate}
				AND (
					iacc.iAccountflag IS NOT NULL
					OR i.iAuditStatus != 'checked'
					)
				and i.iAuditStatus != 'tsaved'
				 
			UNION ALL
			
			SELECT i.cbillcode
				,i.ddate
				,case when i.iInitFlag = 1 then '期初销售出库单' else '销售出库单' end as cbilltype
				,isnull(iacc.iAccountflag, '') iAccountflag
				,CASE 
					WHEN i.iAuditStatus != 'checked'
						THEN '未审核'
					ELSE ''
					END AS iAuditStatus
			FROM st_stkrecord i
			LEFT JOIN (
				SELECT l.cheadguid
					,'未记账' iAccountflag
				FROM st_stkrecordline l
				LEFT JOIN CM_Material m ON l.cMatGUID = m.cGUID
				LEFT JOIN CM_Storehouse sh ON l.cStoreGUID = sh.cGUID
				WHERE isnull(sh.iNoCalCost, 0) = 0
					AND isnull(l.iAccountflag, 0) = 0
					AND {IAsys} = 1
				GROUP BY l.cheadguid
				) iacc ON iacc.cheadguid = i.cguid
			WHERE (
					(
						iacc.iAccountflag IS NOT NULL
						AND (
							i.iIaFlag != 0
							OR i.iAccDeliverGoods != 0
							)
						AND i.cbilltype = '020'
						)
					OR (
						i.cbilltype IN (
							'020'
							,'021'
							)
						AND i.iAuditStatus != 'checked'
						)
					)
				AND i.ddate <= {ddate}
				and i.iAuditStatus != 'tsaved'
			
			UNION ALL
			
			SELECT i.cinvcode cbillcode
				,i.ddate
				,'销售费用发票' cbilltype
				,'' iAccountflag
				,CASE 
					WHEN i.iAuditStatus != 'checked'
						THEN '未审核'
					ELSE ''
					END AS iAuditStatus
			FROM SA_CarriageInvoice i
			WHERE i.iAuditStatus != 'checked'
				AND i.ddate <= {ddate}
				and i.iAuditStatus != 'tsaved'
			
			UNION ALL
			
			SELECT i.cinvcode cbillcode
				,i.ddate
				,case when i.iInitFlag = 0 then '销售发票' when i.iInitFlag = 1 then '期初应收发票' else '期初销售发票' end as cbilltype
				,isnull(iacc.iAccountflag, '') iAccountflag
				,CASE 
					WHEN i.iAuditStatus != 'checked'
						THEN '未审核'
					ELSE ''
					END AS iAuditStatus
			FROM sa_invoice i
			LEFT JOIN (
				SELECT l.cheadguid
					,'未记账' iAccountflag
				FROM sa_invoiceline l
				LEFT JOIN CM_Material m ON l.cMatGUID = m.cGUID
				LEFT JOIN CM_Storehouse sh ON l.cStoreGUID = sh.cGUID
				WHERE l.iIaFlag = 1
					AND isnull(m.iServisFlag, 0) = 0
					AND isnull(sh.iNoCalCost, 0) = 0
					AND isnull(l.iAccountflag, 0) = 0
					AND {IAsys} = 1
				GROUP BY l.cheadguid
				) iacc ON iacc.cheadguid = i.cguid
			WHERE (
					(
						iacc.iAccountflag IS NOT NULL
						AND i.iInitFlag = 0
						)
					OR i.iAuditStatus != 'checked'
					)
				AND i.ddate <= {ddate}
				and i.iAuditStatus != 'tsaved'
			
			UNION ALL
			
			SELECT i.cbillcode
				,i.ddate
				,'委托代销结算单' cbilltype
				,isnull(iacc.iAccountflag, '') iAccountflag
				,CASE 
					WHEN i.iAuditStatus != 'checked'
						THEN '未审核'
					ELSE ''
					END AS iAuditStatus
			FROM SA_ConsignSettle i
			LEFT JOIN (
				SELECT l.cheadguid
					,'未记账' iAccountflag
				FROM SA_ConsignSettleline l
				LEFT JOIN CM_Material m ON l.cMatGUID = m.cGUID
				LEFT JOIN CM_Storehouse sh ON l.cStoreGUID = sh.cGUID
				WHERE l.iIaFlag = 1
					AND isnull(m.iServisFlag, 0) = 0
					AND isnull(sh.iNoCalCost, 0) = 0
					AND isnull(l.iAccountflag, 0) = 0
					AND {IAsys} = 1
				GROUP BY l.cheadguid
				) iacc ON iacc.cheadguid = i.cguid
			WHERE (
					iacc.iAccountflag IS NOT NULL
					OR i.iAuditStatus != 'checked'
					)
				AND i.ddate <= {ddate}
				and i.iAuditStatus != 'tsaved'
			
			UNION ALL
			
			SELECT i.cbillcode
				,i.ddate
				,'销售返利单' cbilltype
				,'' iAccountflag
				,CASE 
					WHEN i.iAuditStatus != 'checked'
						THEN '未审核'
					ELSE ''
					END AS iAuditStatus
			FROM SA_Rebate i
			WHERE i.iAuditStatus != 'checked'
				AND i.ddate <= {ddate}
				and i.iAuditStatus != 'tsaved'
			
		]]></i>
		<i id="warndate" desc="警告数据校验"><![CDATA[
			SELECT i.cbillcode
				,i.ddate
				,'销售订单' cbilltype
				,'存在目标单据跨结转期' overflag
				,'' vouchflag
				,'' iCheckStatus
				,'' iExeFlag
			FROM sa_order i
			WHERE i.ddate <= {ddate}
				AND EXISTS (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN pu_order ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'pu_order'
						AND m.csmainid = i.cguid
						AND ii.dPODate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN sa_order ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'sa_order'
						AND m.csmainid = i.cguid
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN sa_dispatch ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'sa_dispatch'
						AND m.csmainid = i.cguid
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'st_stkrecord'
						AND m.csmainid = i.cguid
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN sa_invoice ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'sa_invoice'
						AND m.csmainid = i.cguid
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'ap_payment'
						AND m.csmainid = i.cguid
						AND ii.dVouDate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					)
				and i.iAuditStatus != 'tsaved'
			
			UNION ALL
			
			SELECT i.cbillcode
				,i.ddate
				,'销售发货单' cbilltype
				,'存在目标单据跨结转期' overflag
				,'' vouchflag
				,'' iCheckStatus
				,'' iExeFlag
			FROM sa_dispatch i
			WHERE i.ddate <= {ddate}
				AND EXISTS (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN sa_dispatch ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'sa_dispatch'
						AND m.csmainid = i.cguid
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'st_stkrecord'
						AND m.csmainid = i.cguid
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN sa_invoice ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'sa_invoice'
						AND m.csmainid = i.cguid
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'ap_payment'
						AND m.csmainid = i.cguid
						AND ii.dVouDate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					)
				and i.iAuditStatus != 'tsaved'
			
			UNION ALL
			
			SELECT i.cinvcode cbillcode
				,i.ddate
				,case when i.iInitFlag = 0 then '销售发票' when i.iInitFlag = 1 then '期初应收发票' else '期初销售发票' end as cbilltype
				,isnull(kjz.overflag, '') overflag
				,CASE 
					WHEN {GLsys} = 1
						AND (
							isnull(vouch.cVouGuid, '') = ''
							OR isnull(vouchIA.vouchflag, '') != ''
							)
						and i.iInitFlag = 0
						THEN '未生成凭证'
					ELSE ''
					END AS vouchflag
				,isnull(iCheck.iCheckStatus, '') iCheckStatus
				,isnull(iStatus_Entire.iStkStatus, '') iExeFlag
			FROM sa_invoice i
			LEFT JOIN (
				SELECT l.csmainid
					,'存在目标单据跨结转期' overflag
				FROM (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN sa_dispatch ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'sa_dispatch'
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'st_stkrecord'
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN sa_invoice ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'sa_invoice'
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'ap_payment'
						AND ii.dVouDate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					) l
				GROUP BY l.csmainid
				) kjz ON kjz.csmainid = i.cguid
			LEFT JOIN v_getVoucherinfo vouch ON vouch.cBillGuid = i.cGUID
			LEFT JOIN (
				SELECT l.cheadguid
					,'未生成凭证' vouchflag
				FROM sa_invoiceLine l
				left join sa_invoice i on i.cguid = l.cheadguid
				LEFT JOIN CM_Material m ON l.cMatGUID = m.cGUID
				LEFT JOIN CM_Storehouse sh ON l.cStoreGUID = sh.cGUID
				LEFT JOIN (
					SELECT ia.cGUID
						,ia.cStkLineGUID lineguid
					FROM IA_SubsidiaryLedger ia
					WHERE ia.cBillType = '085'
						AND ia.dAccountDate IS NOT NULL
						AND ia.iOutAMT <> 0
					
					UNION ALL
					
					SELECT ia.cGUID
						,ia.cInvoicelineGUID lineguid
					FROM IA_EnSubsidiaryLedger ia
					WHERE ia.cBillType = '085'
						AND ia.dAccountDate IS NOT NULL
						AND ia.iOutAMT <> 0
					) ia ON ia.lineguid = l.cGUID
				LEFT JOIN FAP_BILL_VOU_REL vouchrela ON vouchrela.cBillGuid = ia.cGUID
				LEFT JOIN GL_Voucher vouch ON vouch.cGUID = vouchrela.cVouGuid
				WHERE l.iIaFlag = 1
					AND isnull(m.iServisFlag, 0) = 0
					AND isnull(sh.iNoCalCost, 0) = 0
					AND {IAsys} = 1
					AND {GLsys} = 1
					and (isnull(l.iAccountflag, 0) = 0 AND i.iInitFlag = 0
					or vouch.cCode IS NULL)
				GROUP BY l.cheadguid
				) vouchIA ON vouchIA.cheadguid = i.cguid
			left join (
				select l.cheadguid as cheadguid,
					   case when sum(isnull(l.itotal_f,0)) = sum(isnull(l.iReceiveTotal_F,0)) then null
					   else '未完全收款核销' end as iCheckStatus
				from SA_InvoiceLine l
				left join sa_invoice i on i.cguid = l.cheadguid
				where i.iInitFlag = 0
				and {ARsys} = 1
				GROUP BY l.cheadguid
			) iCheck on iCheck.cheadguid = i.cguid
			left JOIN (
            	SELECT i.cguid cHeadGUID,
				       CASE WHEN Count(istatus.cInvLineGUID) = Sum(isnull(istatus.iSingleStkStatus,-1)) THEN null
				        ELSE '未完全出库' END AS iStkStatus
				from sa_invoice i 
				left join v_business_sa_inv2billStatus istatus on i.cguid = istatus.cInvGUID
				where i.iInitFlag = 2
				GROUP BY i.cguid
            ) AS iStatus_Entire ON iStatus_Entire.cHeadGUID = i.cguid
			WHERE i.ddate <= {ddate}
				AND (
					kjz.overflag IS NOT NULL
					OR (vouch.cVouGuid IS NULL and {GLsys} = 1 and i.iInitFlag = 0)
					OR (vouchIA.vouchflag IS NOT NULL and i.iInitFlag = 0)
					OR iCheck.iCheckStatus IS NOT NULL
					OR iStatus_Entire.iStkStatus IS NOT NULL
					)
				and i.iAuditStatus != 'tsaved'
			
			UNION ALL
			
			SELECT i.cinvcode cbillcode
				,i.ddate
				,'销售费用发票' cbilltype
				,isnull(kjz.overflag, '') overflag
				,CASE 
					WHEN {GLsys} = 1
						AND isnull(vouch.cVouGuid, '') = ''
						THEN '未生成凭证'
					ELSE ''
					END AS vouchflag
				,isnull(iCheck.iCheckStatus, '') iCheckStatus
				,'' iExeFlag
			FROM SA_CarriageInvoice i
			LEFT JOIN (
				SELECT m.csmainid
					,'存在目标单据跨结转期' overflag
				FROM BILL_GEN_RELATION_MAIN m
				INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
				WHERE m.cDMainEntity = 'ap_payment'
					AND ii.dVouDate > {ddate}
					and ii.iAuditStatus != 'tsaved'
				GROUP BY m.csmainid
				) kjz ON kjz.csmainid = i.cguid
			LEFT JOIN v_getVoucherinfo vouch ON vouch.cBillGuid = i.cGUID
			left join (
				select l.cheadguid as cheadguid,
					   case when sum(isnull(l.itotal_f,0)) = sum(isnull(l.iPayTotal_F,0)) then null
					   else '未完全收款核销' end as iCheckStatus
				from SA_CarriageInvoiceline l
				left join SA_CarriageInvoice i on i.cguid = l.cheadguid
				where {ARsys} = 1
				GROUP BY l.cheadguid
			) iCheck on iCheck.cheadguid = i.cguid
			WHERE i.ddate <= {ddate}
				AND (
					kjz.overflag IS NOT NULL
					OR (vouch.cVouGuid IS NULL and {GLsys} = 1)
					OR iCheck.iCheckStatus IS NOT NULL
					)
				and i.iAuditStatus != 'tsaved'
			
			UNION ALL
			
			SELECT i.cbillcode
				,i.ddate
				,'零售单' cbilltype
				,isnull(kjz.overflag, '') overflag
				,isnull(vouchIA.vouchflag, '') vouchflag
				,'' iCheckStatus
				,'' iExeFlag
			FROM st_stkrecord i
			LEFT JOIN (
				SELECT l.csmainid
					,'存在目标单据跨结转期' overflag
				FROM (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN sa_invoice ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'sa_invoice'
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'st_stkrecord'
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
				) l
			GROUP BY l.csmainid
			) kjz ON kjz.csmainid = i.cguid
			LEFT JOIN (
				SELECT l.cheadguid
					,'未生成凭证' vouchflag
				FROM ST_StkRecordLine l
				LEFT JOIN CM_Storehouse sh ON l.cStoreGUID = sh.cGUID
				LEFT JOIN IA_SubsidiaryLedger ia ON l.cGUID = ia.cStkLineGUID
				LEFT JOIN FAP_BILL_VOU_REL vouchrela ON vouchrela.cBillGuid = ia.cGUID
				LEFT JOIN GL_Voucher vouch ON vouch.cGUID = vouchrela.cVouGuid
				WHERE (isnull(l.iAccountflag, 0) = 0 and isnull(sh.iNoCalCost, 0) = 0
					or ia.cbilltype = '212'
					AND ia.dAccountDate IS NOT NULL
					AND ia.iOutAMT <> 0
					AND vouch.cCode IS NULL)
					AND {IAsys} = 1
					AND {GLsys} = 1
				GROUP BY l.cheadguid
				) vouchIA ON vouchIA.cheadguid = i.cguid
			WHERE i.ddate <= {ddate}
				AND i.cbilltype = '212'
				AND (
					kjz.overflag IS NOT NULL
					OR vouchIA.vouchflag IS NOT NULL
					)
				and i.iAuditStatus != 'tsaved'
			
			UNION ALL
			
			SELECT i.cbillcode
				,i.ddate
				,case when i.iInitFlag = 1 then '期初销售出库单' else '销售出库单' end as cbilltype
				,isnull(kjz.overflag, '') overflag
				,isnull(vouchIA.vouchflag, '') vouchflag
				,'' iCheckStatus
				,isnull(iStatus_Entire.iInvStatus, '') iExeFlag
			FROM st_stkrecord i
			LEFT JOIN (
				SELECT l.csmainid
					,'存在目标单据跨结转期' overflag
				FROM (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'st_stkrecord'
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN sa_invoice ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'sa_invoice'
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN SA_ConsignSettle ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'SA_ConsignSettle'
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'ap_payment'
						AND ii.dVouDate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					) l
				GROUP BY l.csmainid
				) kjz ON kjz.csmainid = i.cguid
			LEFT JOIN (
				SELECT l.cheadguid
					,'未生成凭证' vouchflag
				FROM ST_StkRecordLine l
				LEFT JOIN CM_Storehouse sh ON l.cStoreGUID = sh.cGUID
				LEFT JOIN ST_StkRecord ii ON ii.cguid = l.cheadguid
				LEFT JOIN IA_SubsidiaryLedger ia ON l.cGUID = ia.cStkLineGUID
				LEFT JOIN FAP_BILL_VOU_REL vouchrela ON vouchrela.cBillGuid = ia.cGUID
				LEFT JOIN GL_Voucher vouch ON vouch.cGUID = vouchrela.cVouGuid
				WHERE (isnull(sh.iNoCalCost, 0) = 0
					AND isnull(l.iAccountflag, 0) = 0
					and ii.cbilltype = '020'
					or ia.cbilltype = '020'
					AND ia.dAccountDate IS NOT NULL
					AND ia.iOutAMT <> 0
					AND vouch.cCode IS NULL)
					AND (
						ii.iIaFlag != 0
						OR ii.iAccDeliverGoods != 0
						)
					
					AND {IAsys} = 1
					AND {GLsys} = 1
				GROUP BY l.cheadguid
				) vouchIA ON vouchIA.cheadguid = i.cguid
			LEFT JOIN (
				select i.cguid cHeadGUID,
  				 	   CASE WHEN Count(i.cguid) = Sum(isnull(istatus.iSingleInvStatus,-1)) THEN null
			      		ELSE '未完全开票' END AS iInvStatus
			    from st_stkrecord i
  				left join v_business_sa_stk2invStatus istatus on i.cguid = istatus.cStkGUID
  				where i.iCloseFlag = 0 
  				and ((i.iAccDeliverGoods = 1 and {IAsys} = 1) or i.cbilltype = '021')
  				group by i.cguid
  				) as iStatus_Entire ON iStatus_Entire.cHeadGUID=i.cguid 
			WHERE i.ddate <= {ddate}
				AND i.cbilltype IN (
					'020'
					,'021'
					)
				AND (
					kjz.overflag IS NOT NULL
					OR vouchIA.vouchflag IS NOT NULL
					OR iStatus_Entire.iInvStatus IS NOT NULL
					)
				and i.iAuditStatus != 'tsaved'
			
			UNION ALL
			
			SELECT i.cbillcode
				,i.ddate
				,'委托代销结算单' cbilltype
				,isnull(kjz.overflag, '') overflag
				,isnull(vouchIA.vouchflag, '') vouchflag
				,'' iCheckStatus
				,'' iExeFlag
			FROM SA_ConsignSettle i
			LEFT JOIN (
				SELECT m.csmainid
					,'存在目标单据跨结转期' overflag
				FROM BILL_GEN_RELATION_MAIN m
				INNER JOIN sa_invoice ii ON ii.cguid = m.cdmainid
				WHERE m.cDMainEntity = 'sa_invoice'
					AND ii.ddate > {ddate}
					and ii.iAuditStatus != 'tsaved'
				GROUP BY m.csmainid
				) kjz ON kjz.csmainid = i.cguid
			LEFT JOIN (
				SELECT l.cheadguid
					,'未生成凭证' vouchflag
				FROM SA_ConsignSettleLine l
				LEFT JOIN CM_Material m ON l.cMatGUID = m.cGUID
				LEFT JOIN CM_Storehouse sh ON l.cStoreGUID = sh.cGUID
				LEFT JOIN (
					SELECT ia.cGUID
						,ia.cStkLineGUID lineguid
					FROM IA_SubsidiaryLedger ia
					WHERE ia.cBillType = '208'
						AND ia.dAccountDate IS NOT NULL
						AND ia.iOutAMT <> 0
					
					UNION ALL
					
					SELECT ia.cGUID
						,ia.cSettleLineGUID lineguid
					FROM IA_EnSubsidiaryLedger ia
					WHERE ia.cBillType = '208'
						AND ia.dAccountDate IS NOT NULL
						AND ia.iOutAMT <> 0
					) ia ON ia.lineguid = l.cGUID
				LEFT JOIN FAP_BILL_VOU_REL vouchrela ON vouchrela.cBillGuid = ia.cGUID
				LEFT JOIN GL_Voucher vouch ON vouch.cGUID = vouchrela.cVouGuid
				WHERE l.iIaFlag = 1
					AND isnull(m.iServisFlag, 0) = 0
					AND isnull(sh.iNoCalCost, 0) = 0
					AND {IAsys} = 1
					AND {GLsys} = 1
					AND (vouch.cCode IS NULL
					or isnull(l.iAccountflag, 0) = 0)
				GROUP BY l.cheadguid
				) vouchIA ON vouchIA.cheadguid = i.cguid
			WHERE i.ddate <= {ddate}
				AND (
					kjz.overflag IS NOT NULL
					OR vouchIA.vouchflag IS NOT NULL
					)
				and i.iAuditStatus != 'tsaved'
		]]></i>
	</sql>
</sqls>