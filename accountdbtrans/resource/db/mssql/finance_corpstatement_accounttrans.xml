<?xml version="1.0" encoding="UTF-8"?>
<sqls>
	<sql group="finance_corpstatement_trans">
		<i id="calNewInit" desc=""><![CDATA[
		      select sum(iDCDir*iAmount) iAmount, sum(iDCDir*iAmount_f) iAmount_f, cBankAcctGUID  
			  from CA_CorpStatement where ((iYear<{iYear} or (iYear={iYear} and iPeriod<={iMonth})) or (iInitFlag=0))
			  and iInitFlag<>1
			  group by cBankAcctGUID
		]]></i>
	</sql>
	<sql group="finance_bankstatement_trans">
		<i id="calNewInit" desc=""><![CDATA[
		      select sum(iDCDir*iAmount*iBalanceDir) iAmount, sum(iDCDir*iAmount_f*iBalanceDir) iAmount_f, CA_initBankBookPara.cBankAcctGUID  
			  from CA_BankStatement 
			  join CA_initBankBookPara on CA_initBankBookPara.cBankAcctGUID=CA_BankStatement.cBankAcctGUID
			  where ((iYear<{iYear} or (iYear={iYear} and iPeriod<={iMonth})) or (iInitFlag=0)) and iInitFlag<>1
			  group by CA_initBankBookPara.cBankAcctGUID
		]]></i>
	</sql>
</sqls>