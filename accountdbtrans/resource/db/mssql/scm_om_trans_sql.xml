<?xml version="1.0" encoding="UTF-8"?>
<sqls>
	<sql group="scm_om_trans">
		<i id="vaildate" desc="失败单据校验"><![CDATA[
			SELECT	i.cBillCode,
					i.dDate,
					'期初委外加工入库单' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM ST_StkRecord i
			WHERE i.iAuditStatus != 'checked'
				AND i.cBillType = '101'
				AND i.dDate <= {ddate}
			
			UNION ALL
			
			SELECT	i.cBillCode,
					i.dDate,
					'期初委外材料出库单' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM ST_StkRecord i
			WHERE i.iAuditStatus != 'checked'
				AND i.cBillType = '102'
				AND i.dDate <= {ddate}
				
			UNION ALL
			
			SELECT	i.cInvCode cbillcode,
					i.dInvDate ddate,
					'委外发票' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM OM_Invoice i
			WHERE i.iAuditStatus != 'checked'
				AND i.dInvDate <= {ddate}
				
			UNION ALL
			
			SELECT	i.cBillCode,
					i.dDate,
					'委外订单' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM OM_Order i
			WHERE i.iAuditStatus != 'checked'
				AND i.dDate <= {ddate}
				
			UNION ALL
			
			SELECT	i.cBillCode,
					i.dSettleDate ddate,
					'委外结算单' cbilltype,
					isnull(iacc.iAccountflag, '') iAccountflag,
					'' iAuditStatus
			FROM OM_Settle i
			LEFT JOIN (
				SELECT l.cheadguid,
					'未记账' iAccountflag
				FROM OM_SettleLine l
				WHERE isnull(l.iAccount, 0) = 0
					AND {IAsys} = 1
				GROUP BY l.cheadguid
				) iacc ON iacc.cheadguid = i.cguid
			WHERE {IAsys} = 1
				AND i.cGUID IN (
					SELECT l.cheadguid
					FROM OM_SettleLine l
					WHERE isnull(l.iAccount, 0) = 0
				)
				AND i.dSettleDate <= {ddate}
		]]></i>
			
		<i id="warndate" desc="警告单据校验"><![CDATA[
			SELECT * FROM (
				SELECT	i.cbillcode,
						i.ddate,
						'期初委外加工入库单' cbilltype,
						isnull(kjz.overflag, '') overflag,
						'' vouchflag,
						case when (a.SUMisettleQTY*1.0/a.SUMiUnitQTY)=1 then '' 
								else '未完全结算' 
							end as iSettleFlag,
						case when a.SUMiInvQTY/SUMiUnitQTY =1 then '' 
								else '未完全开票' 
							end as iExeFlag
				FROM st_stkrecord i
				LEFT JOIN (
					SELECT l.csmainid,
						'存在目标单据跨结转期' overflag
					FROM (	
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'st_stkrecord'
							AND ii.ddate > {ddate}
							
						UNION ALL
							
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN om_invoice ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'om_invoice'
							AND ii.dInvDate > {ddate}
						) l GROUP BY l.csmainid
					) kjz ON kjz.csmainid = i.cguid
				LEFT JOIN (SELECT st.cHeadGUID,
							       Sum(st.isettleQTY) AS SUMisettleQTY,
							       Sum(iInvoiceQTY) As SUMiInvQTY,
							       case when Sum(iUnitQTY) = 0 then 1 else  Sum(iUnitQTY) end AS SUMiUnitQTY
							FROM   ST_StkRecordLine st
							GROUP  BY st.cHeadGUID 
					) a on a.cHeadGUID = i.cGUID
				WHERE i.cbilltype = '101' ) qcomstk
			WHERE qcomstk.overflag !=''
				OR qcomstk.iSettleFlag !=''
				OR qcomstk.iExeFlag !=''
			
			UNION ALL
			
			SELECT * FROM (
				SELECT	i.cInvCode cbillcode,
						i.dInvDate ddate,
						'期初委外发票' cbilltype,
						isnull(kjz.overflag, '') overflag,
						'' vouchflag,
						case a.linesum when 1 then '' 
								else '未完全结算' 
						end as iSettleFlag,
						'' iExeFlag
				FROM OM_Invoice i
				LEFT JOIN (
					SELECT	l.csmainid,
							'存在目标单据跨结转期' overflag
					FROM (
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN om_invoice ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'om_invoice'
							AND ii.dInvDate > {ddate}
						
						UNION ALL
						
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'ap_payment'
							AND ii.dVouDate > {ddate}
						) l GROUP BY l.csmainid
					) kjz ON kjz.csmainid = i.cguid
				LEFT JOIN (              		
						SELECT ol.cHeadGUID,
		                		sum(isettleflag)*1.0/sum(1) as linesum  
	                    FROM om_invoiceline ol
	                    GROUP BY ol.cHeadGUID
					) a on a.cHeadGUID = i.cGUID
				WHERE i.iInitFlag = 1 ) qcomfp
			WHERE qcomfp.overflag != ''
				OR qcomfp.iSettleFlag != ''
			
			UNION ALL
					
			SELECT	i.cBillCode,
					i.dDate,
					'委外订单' cbilltype,
					'存在目标单据跨结转期' overflag,
					'' vouchflag,
					'' iSettleFlag,
					'' iExeFlag
			FROM OM_Order i
			WHERE i.dDate <= {ddate}
				AND EXISTS (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'st_stkrecord'
						AND m.csmainid = i.cguid
						AND ii.ddate > {ddate}
						
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN om_invoice ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'om_invoice'
						AND m.csmainid = i.cguid
						AND ii.dInvDate > {ddate}
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'ap_payment'
						AND m.csmainid = i.cguid
						AND ii.dVouDate > {ddate}
				)
			
				UNION ALL
				
				SELECT * FROM (
					SELECT	i.cInvCode cbillcode,
							i.dInvDate ddate,
							'委外发票' cbilltype,
							isnull(kjz.overflag, '') overflag,
							'' vouchflag,
							CASE
								WHEN {IAsys} = 1
									AND a.linesum != 1 then '未完全结算'
									ELSE ''
							END AS iSettleFlag,
							'' iExeFlag
					FROM OM_Invoice i
					LEFT JOIN (
						SELECT	l.csmainid,
								'存在目标单据跨结转期' overflag
						FROM (					
							SELECT m.csmainid
							FROM BILL_GEN_RELATION_MAIN m
							INNER JOIN om_invoice ii ON ii.cguid = m.cdmainid
							WHERE m.cDMainEntity = 'om_invoice'
								AND ii.dInvDate > {ddate}
							
							UNION ALL
							
							SELECT m.csmainid
							FROM BILL_GEN_RELATION_MAIN m
							INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
							WHERE m.cDMainEntity = 'ap_payment'
								AND ii.dVouDate > {ddate}
							) l GROUP BY l.csmainid
						) kjz ON kjz.csmainid = i.cguid
					LEFT JOIN (
							SELECT	DISTINCT 
									ol.cHeadGUID,
			                		sum(isettleflag)*1.0/sum(1) as linesum  
		                    FROM om_invoiceline ol
		                    GROUP BY ol.cHeadGUID
						) a on a.cHeadGUID = i.cguid
					WHERE i.dInvDate <= {ddate}
						AND i.iInitFlag = 0 ) omfp
				WHERE omfp.iSettleFlag !=''
					OR omfp.overflag !=''
		]]></i>
	</sql>
</sqls>