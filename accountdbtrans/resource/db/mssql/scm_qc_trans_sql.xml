<?xml version="1.0" encoding="UTF-8"?>
<sqls>
	<sql group="scm_qc_trans">
		<i id="vaildate" desc="失败单据校验"><![CDATA[
			SELECT	i.cBillCode	cbillcode,
					i.dBillDate	ddate,
					'采购检验单' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM QC_Checklist i
			WHERE i.iAuditStatus != 'checked' AND i.iAuditStatus != 'tsaved'
				AND i.cBillType = 'QC001'
				AND i.dBillDate <= {ddate}
			
			UNION ALL
			
			SELECT	i.cBillCode	cbillcode,
					i.dBillDate	ddate,
					'完工检验单' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM QC_Checklist i
			WHERE i.iAuditStatus != 'checked' AND i.iAuditStatus != 'tsaved'
				AND i.cBillType = 'QC003'
				AND i.dBillDate <= {ddate}
				
			UNION ALL
			
			SELECT	i.cBillCode	cbillcode,
					i.dDate,
					'采购不合格处理单' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM QC_Defective i
			WHERE i.iAuditStatus != 'checked' AND i.iAuditStatus != 'tsaved' 
				AND i.cBillType = 'QC002'
				AND i.dDate <= {ddate}
			
			UNION ALL
			
			SELECT	i.cBillCode	cbillcode,
					i.dDate,
					'完工不合格处理单' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM QC_Defective i
			WHERE i.iAuditStatus != 'checked' AND i.iAuditStatus != 'tsaved' 
				AND i.cBillType = 'QC004'
				AND i.dDate <= {ddate}
			
			UNION ALL
			
			SELECT	i.cBillCode,
					i.dBillDate	ddate,
					'采购退回单' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM QC_PuReturn i
			WHERE i.iAuditStatus != 'checked' AND i.iAuditStatus != 'tsaved'
				AND i.dBillDate <= {ddate}
		]]></i>
		
		<i id="warndate" desc="警告单据校验"><![CDATA[
			SELECT	i.cBillCode,
					i.dBillDate	ddate,
					'采购检验单' cbilltype,
					'存在目标单据跨结转期' overflag,
					'' vouchflag,
					'' iSettleFlag,
					'' iCheckStatus,
					''  iExeFlag
			FROM QC_Checklist i
			WHERE i.dBillDate <= {ddate}
				AND EXISTS (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN QC_Defective ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'QC_Defective'
						AND m.csmainid = i.cguid
						AND ii.dDate > {ddate}
				)
			
			UNION ALL
				
			SELECT	i.cBillCode,
					i.dDate,
					'采购不合格处理单' cbilltype,
					'存在目标单据跨结转期' overflag,
					'' vouchflag,
					'' iSettleFlag,
					'' iCheckStatus,
					''  iExeFlag
			FROM QC_Defective i
			WHERE i.dDate <= {ddate}
				AND EXISTS (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN QC_PuReturn ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'QC_PuReturn'
						AND m.csmainid = i.cguid
						AND ii.dBillDate > {ddate}
				)
		]]></i>
	</sql>
</sqls>