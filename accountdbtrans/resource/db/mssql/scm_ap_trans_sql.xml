<?xml version="1.0" encoding="UTF-8"?>
<sqls>
	<sql group="scm_ap_trans">
		<i id="vaildate" desc="失败数据校验"><![CDATA[
			SELECT i.cVouCode cbillcode
				,i.dVouDate ddate
				,case when i.iInitFlag = 0 then '期初付款单' else '付款单' end as cbilltype
				,CASE 
					WHEN i.iAuditStatus != 'checked'
						THEN '未审核'
					ELSE ''
					END AS iAuditStatus
			FROM AP_Payment i
			WHERE i.iAuditStatus != 'checked'
				AND i.dVouDate <= {ddate}
				and i.iAuditStatus != 'tsaved'
				and cFlag = 'AP'
			
			UNION ALL
			
			SELECT i.cInvCode cbillcode
				,i.dInvDate ddate
				,case when i.iInitFlag = 0 then '采购发票' else '期初应付发票' end as cbilltype
				,CASE 
					WHEN i.iAuditStatus != 'checked'
						THEN '未审核'
					ELSE ''
					END AS iAuditStatus
			FROM PU_Invoice i
			WHERE i.iAuditStatus != 'checked'
				AND i.dInvDate <= {ddate}
				and i.iAuditStatus != 'tsaved'
				and cSysType = 'AP'
			
			UNION ALL
			
			SELECT i.cVouCode cbillcode
				,i.dVouDate ddate
				,case when i.iInitFlag = 0 then '期初其他应付单' else '其他应付单' end as cbilltype
				,CASE 
					WHEN i.iAuditStatus != 'checked'
						THEN '未审核'
					ELSE ''
					END AS iAuditStatus
			FROM AP_APVoucher i
			WHERE i.iAuditStatus != 'checked'
				AND i.dVouDate <= {ddate}
				and i.iAuditStatus != 'tsaved'
				and cFlag = 'AP'
		]]></i>
		<i id="warndate" desc="警告数据校验"><![CDATA[
			SELECT i.cVouCode cbillcode
				,i.dVouDate ddate
				,case when i.iInitFlag = 0 then '期初付款单' else '付款单' end as cbilltype
				,isnull(kjz.overflag, '') overflag
				,CASE WHEN i.iPayAMT_F + isnull(i.ignoreAMT_F, 0) != i.iCheckAMT_F then '未完全核销'
				else '' end as iCheckStatus
			FROM AP_Payment i
			LEFT JOIN (
				SELECT m.csmainid
					   ,'存在目标单据跨结转期' overflag
				FROM BILL_GEN_RELATION_MAIN m
				INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
				WHERE m.cDMainEntity = 'ap_payment'
					AND ii.dVouDate > {ddate}
					and ii.iAuditStatus != 'tsaved'
				GROUP BY m.csmainid
				) kjz ON kjz.csmainid = i.cguid
			WHERE i.dVouDate <= {ddate}
				and i.iAuditStatus != 'tsaved'
				and i.cFlag = 'AP'
				AND (
					kjz.overflag IS NOT NULL
					OR i.iPayAMT_F + isnull(i.ignoreAMT_F, 0) != i.iCheckAMT_F
					)
			
			UNION ALL
			
			SELECT i.cVouCode cbillcode
				,i.dVouDate ddate
				,case when i.iInitFlag = 0 then '期初其他应付单' else '其他应付单' end as cbilltype
				,isnull(kjz.overflag, '') overflag
				,CASE WHEN i.iPayableAMT_F != i.iCheckAMT_F then '未完全核销'
				else '' end as iCheckStatus
			FROM AP_APVoucher i
			LEFT JOIN (
				SELECT l.credguid
					   ,'存在目标单据跨结转期' overflag
				FROM AP_APVoucher l
				WHERE l.credguid is not null
					AND l.dVouDate > {ddate}
					and l.iAuditStatus != 'tsaved'
				GROUP BY l.credguid
				) kjz ON kjz.credguid = i.cguid
			WHERE i.dVouDate <= {ddate}
				and i.iAuditStatus != 'tsaved'
				and i.cFlag = 'AP'
				AND (
					kjz.overflag IS NOT NULL
					OR i.iPayableAMT_F != i.iCheckAMT_F
					)
			
			UNION ALL
			
			SELECT i.cInvCode cbillcode
				,i.dInvDate	ddate
				,case when i.iInitFlag = 0 then '采购发票' else '期初应付发票' end as cbilltype
				,isnull(kjz.overflag, '') overflag
				,isnull(iCheck.iCheckStatus, '') iCheckStatus
			FROM PU_Invoice i
			LEFT JOIN (
				SELECT	l.csmainid,
						'存在目标单据跨结转期' overflag
				FROM (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN PU_Receive ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'PU_Receive'
						AND ii.dDate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'st_stkrecord'
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN PU_Invoice ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'PU_Invoice'
						AND ii.dInvDate > {ddate}
						and ii.iAuditStatus != 'tsaved'
						
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'ap_payment'
						AND ii.dVouDate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					) l
				GROUP BY l.csmainid
				) kjz ON kjz.csmainid = i.cguid
			LEFT JOIN (
				SELECT l.cheadguid as cheadguid,
					   case when sum(isnull(l.itotal_f,0)) = sum(isnull(l.iPayTotal_F,0)) then null
					   else '未完全核销' end as iCheckStatus
				FROM PU_InvoiceLine l
				LEFT JOIN PU_invoice i on i.cguid = l.cheadguid
				GROUP BY l.cheadguid
			) iCheck on iCheck.cheadguid = i.cguid
			WHERE i.dInvDate <= {ddate}
				and i.cSysType = 'AP'
				AND (
					kjz.overflag IS NOT NULL
					OR iCheck.iCheckStatus IS NOT NULL
					)
				and i.iAuditStatus != 'tsaved'
		]]></i>
	</sql>
</sqls>