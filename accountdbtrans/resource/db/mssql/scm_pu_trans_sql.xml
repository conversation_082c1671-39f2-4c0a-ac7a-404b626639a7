<?xml version="1.0" encoding="UTF-8"?>
<sqls>
	<sql group="scm_pu_trans">
		<i id="vaildate" desc="失败单据校验"><![CDATA[
			SELECT	i.cPlanCode	cbillcode,
					i.dCreatdate ddate,
					'采购计划' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM PU_Planhead i
			WHERE i.iAuditStatus != 'checked' AND i.iAuditStatus != 'tsaved'
				AND i.dCreatdate <= {ddate}
			
			UNION ALL
			
			SELECT	i.cCode	cbillcode,
					i.dPODate	ddate,
					'采购订单' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM PU_Order i
			WHERE i.iAuditStatus != 'checked' AND i.iAuditStatus != 'tsaved'
				AND i.dPODate <= {ddate}
				
			UNION ALL
			
			SELECT	i.cCode	cbillcode,
					i.dDate,
					'采购到货单' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM PU_Receive i
			WHERE i.iAuditStatus != 'checked' AND i.iAuditStatus != 'tsaved'
				AND i.dDate <= {ddate}
			
			
			UNION ALL
			
			SELECT	i.cBillCode	cbillcode,
					i.dDate,
					'采购返利单' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM SA_Rebate i
			WHERE i.iAuditStatus != 'checked' AND i.iAuditStatus != 'tsaved'
				AND i.dDate <= {ddate}
			
			UNION ALL
			
			SELECT	i.cInvCode	cbillcode,
					i.dInvDate	ddate,
					'采购发票' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM PU_Invoice i
			WHERE i.iInitFlag = '0'
				AND i.iAuditStatus != 'checked' AND i.iAuditStatus != ''  AND i.iAuditStatus != 'tsaved'
				AND i.cSysType = 'PU'
				AND i.dInvDate <= {ddate}
				
			UNION ALL
			
			SELECT	i.cInvCode	cbillcode,
					i.dInvDate	ddate,
					'期初采购发票' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM PU_Invoice i
			WHERE i.iInitFlag = '1'
				AND i.iAuditStatus != 'checked' AND i.iAuditStatus != ''  AND i.iAuditStatus != 'tsaved'
				AND i.cSysType = 'PU'
				AND i.dInvDate <= {ddate}
			
			UNION ALL
			
			SELECT	i.cInvCode	cbillcode,
					i.dDate,
					'采购费用发票' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM PU_CarriageInvoice i
			WHERE i.iAuditStatus != 'checked' AND i.iAuditStatus != 'tsaved'
				AND i.dDate <= {ddate}	
		
			UNION ALL
			
			SELECT	i.cbillcode,
					i.ddate,
					'采购入库单' cbilltype,
					isnull(iacc.iAccountflag, '') iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked' AND i.iAuditStatus != 'tsaved'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM st_stkrecord i
			LEFT JOIN (
				SELECT l.cheadguid
					,'未记账' iAccountflag
				FROM st_stkrecordline l
				LEFT JOIN CM_Material m ON l.cMatGUID = m.cGUID
				LEFT JOIN CM_Storehouse sh ON l.cStoreGUID = sh.cGUID
				WHERE isnull(sh.iNoCalCost, 0) = 0
					AND isnull(l.iAccountflag, 0) = 0
					AND {IAsys} = 1
				GROUP BY l.cheadguid
				) iacc ON iacc.cheadguid = i.cguid
			WHERE iacc.iAccountflag IS NOT NULL
				AND i.cbilltype = '010'
				AND i.ddate <= {ddate}
				
			UNION ALL
			
			SELECT	i.cbillcode,
					i.ddate,
					'期初采购入库单' cbilltype,
					'' iAccountflag,
					CASE 
						WHEN i.iAuditStatus != 'checked'
							THEN '未审核'
						ELSE ''
					END AS iAuditStatus
			FROM st_stkrecord i
			WHERE i.cbilltype = '091'
				AND i.iAuditStatus != 'checked' AND i.iAuditStatus != 'tsaved'
				AND i.ddate <= {ddate}
			
			UNION ALL
			
			SELECT	i.cBillCode,
					i.dSettleDate ddate,
					'采购结算单' cbilltype,
					isnull(iacc.iAccountflag, '') iAccountflag,
					'' iAuditStatus
			FROM PU_Settle i
			LEFT JOIN (
				SELECT l.cheadguid,
					'未记账' iAccountflag
				FROM PU_SettleLine l
				WHERE isnull(l.iAccount, 0) = 0
					AND	l.cInBillCode IS NOT NULL
					AND {IAsys} = 1
				GROUP BY l.cheadguid
				) iacc ON iacc.cheadguid = i.cguid
			WHERE {IAsys} = 1
				AND i.cGUID IN (
					SELECT l.cheadguid
					FROM PU_SettleLine l
					WHERE isnull(l.iAccount, 0) = 0
				)
				AND iAccountflag != ''
				AND i.dSettleDate <= {ddate} 
				
			UNION ALL
			
			SELECT	i.cBillCode,
					i.dDate ddate,
					'采购费用结算单' cbilltype,
					isnull(iacc.iAccountflag, '') iAccountflag,
					'' iAuditStatus
			FROM PU_CarriageShare i
			LEFT JOIN (
				SELECT l.cheadguid,
					'未记账' iAccountflag
				FROM PU_CarriageShareLine l
				WHERE isnull(l.iAccountflag, 0) = 0
					AND	isnull(l.cPustkinGUID, '0') != '0'
					AND {IAsys} = 1
				GROUP BY l.cheadguid
				) iacc ON iacc.cheadguid = i.cguid
			WHERE {IAsys} = 1
				AND i.cGUID IN (
					SELECT l.cheadguid
					FROM PU_CarriageShareLine l
					WHERE isnull(l.iAccountflag, 0) = 0
				)
				AND iAccountflag != ''
				AND i.dDate <= {ddate} 
		]]></i>
		
		<i id="warndate" desc="警告单据校验"><![CDATA[
			SELECT	i.cPlanCode	cbillcode,
					i.dCreatdate	ddate,
					'采购计划' cbilltype,
					'存在目标单据跨结转期' overflag,
					'' vouchflag,
					'' iSettleFlag,
					'' iCheckStatus,
					''  iExeFlag
			FROM PU_Planhead i
			WHERE i.dCreatdate <= {ddate}
				AND EXISTS (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN PU_Order ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'PU_Order'
						AND m.csmainid = i.cguid
						AND ii.dPODate > {ddate}
				)
			
			UNION ALL
			
			SELECT	i.cCode	cbillcode,
					i.dPODate	ddate,
					'采购订单' cbilltype,
					'存在目标单据跨结转期' overflag,
					'' vouchflag,
					'' iSettleFlag,
					'' iCheckStatus,
					''  iExeFlag
			FROM PU_Order i
			WHERE i.dPODate <= {ddate}
				AND EXISTS (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN PU_Receive ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'PU_Receive'
						AND m.csmainid = i.cguid
						AND ii.dDate > {ddate}
						
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'st_stkrecord'
						AND m.csmainid = i.cguid
						AND ii.ddate > {ddate}
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN PU_Invoice ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'PU_Invoice'
						AND m.csmainid = i.cguid
						AND ii.dInvDate > {ddate}
						
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'ap_payment'
						AND m.csmainid = i.cguid
						AND ii.dVouDate > {ddate}
				)
			
			UNION ALL
			
			SELECT	i.cCode	cbillcode,
					i.dDate,
					'采购到货单' cbilltype,
					'存在目标单据跨结转期' overflag,
					'' vouchflag,
					'' iSettleFlag,
					'' iCheckStatus,
					''  iExeFlag
			FROM PU_Receive i
			WHERE i.dDate <= {ddate}
				AND EXISTS (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN PU_Receive ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'PU_Receive'
						AND m.csmainid = i.cguid
						AND ii.dDate > {ddate}
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'st_stkrecord'
						AND m.csmainid = i.cguid
						AND ii.ddate > {ddate}
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN QC_Checklist ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'QC_Checklist'
						AND m.csmainid = i.cguid
						AND ii.dBillDate > {ddate}
				)
			
			UNION ALL
			
			SELECT	
					i.cInvCode	cbillcode,
					i.dDate,
					'采购费用发票' cbilltype,
					isnull(kjz.overflag, '') overflag,
					CASE 
						WHEN {GLsys} = 1
							AND isnull(vouch.cVouGuid, '') = ''
							THEN '未生成凭证'
						ELSE ''
					END AS vouchflag,
					isnull(setl.iSettleFlag, '') iSettleFlag,
					isnull(iCheck.iCheckStatus, '') iCheckStatus,
					''  iExeFlag
			FROM PU_CarriageInvoice i
			LEFT JOIN (
				SELECT	m.csmainid,
						'存在目标单据跨结转期' overflag
				FROM BILL_GEN_RELATION_MAIN m
				INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
				WHERE m.cDMainEntity = 'ap_payment'
					AND ii.dVouDate > {ddate}
				GROUP BY m.csmainid
				) kjz ON kjz.csmainid = i.cguid
			LEFT JOIN v_getVoucherinfo vouch ON vouch.cBillGuid = i.cGUID
			LEFT JOIN (
				SELECT DISTINCT b.cheadguid, 
					'未完全结算'  iSettleFlag
				FROM PU_CarriageInvoiceline b
				WHERE (b.iShareFlag = 0 or b.iShareFlag is null)
					AND {IAsys} = 1
				) setl on setl.cheadguid = i.cGUID
			LEFT JOIN (
				SELECT l.cheadguid as cheadguid,
					   case when sum(isnull(l.itotal_f,0)) = sum(isnull(l.iPayTotal_F,0)) then null
					   else '未完全付款核销' end as iCheckStatus
				FROM PU_CarriageInvoiceLine l
				LEFT JOIN PU_CarriageInvoice i on i.cguid = l.cheadguid
				WHERE {APsys} = 1
				GROUP BY l.cheadguid
			) iCheck on iCheck.cheadguid = i.cguid
			WHERE i.dDate <= {ddate}
				AND (
					kjz.overflag IS NOT NULL
					OR isnull(vouch.cVouGuid, '') = ''
					OR iCheck.iCheckStatus IS NOT NULL
					)
			
			UNION ALL
			
			SELECT * FROM (
				SELECT	i.cInvCode	cbillcode,
						i.dInvDate	ddate,
						'采购发票' cbilltype,
						isnull(kjz.overflag, '') overflag,
						CASE 
							WHEN {GLsys} = 1
								AND isnull(vouch.cVouGuid, '') = ''
								THEN '未生成凭证'
							ELSE ''
						END AS vouchflag,
						CASE
							WHEN {IAsys} = 1
								AND (a.iSettleFlag*1.0/a.sFlag)!=1 then '未完全结算'
								ELSE ''
						END AS iSettleFlag,
						isnull(iCheck.iCheckStatus, '') iCheckStatus,
						''  iExeFlag
				FROM PU_Invoice i
				LEFT JOIN (
					SELECT	l.csmainid,
							'存在目标单据跨结转期' overflag
					FROM (
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN PU_Receive ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'PU_Receive'
							AND ii.dDate > {ddate}
						
						UNION ALL
						
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'st_stkrecord'
							AND ii.ddate > {ddate}
						
						UNION ALL
						
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN PU_Invoice ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'PU_Invoice'
							AND ii.dInvDate > {ddate}
							
						UNION ALL
						
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'ap_payment'
							AND ii.dVouDate > {ddate}
						) l
					GROUP BY l.csmainid
					) kjz ON kjz.csmainid = i.cguid
				LEFT JOIN (
						SELECT ol.cHeadGUID,
								sum(ol.iSettleFlag) as iSettleFlag,
								sum(1) as sFlag
						FROM pu_invoiceline ol 
						GROUP BY ol.cHeadGUID
					) a on a.cHeadGUID = i.cguid
				LEFT JOIN v_getVoucherinfo vouch ON vouch.cBillGuid = i.cGUID
				LEFT JOIN (
					SELECT l.cheadguid as cheadguid,
						   case when sum(isnull(l.itotal_f,0)) = sum(isnull(l.iPayTotal_F,0)) then null
						   else '未完全付款核销' end as iCheckStatus
					FROM PU_InvoiceLine l
					LEFT JOIN PU_invoice i on i.cguid = l.cheadguid
					WHERE i.iInitFlag = 0
					AND {APsys} = 1
					GROUP BY l.cheadguid
				) iCheck on iCheck.cheadguid = i.cguid
				WHERE	i.dInvDate <= {ddate}
					AND (
						kjz.overflag IS NOT NULL
						OR isnull(vouch.cVouGuid, '') != ''
						OR iCheck.iCheckStatus IS NOT NULL
						)
					AND i.iInitFlag = 0	) cgfp
			WHERE (cgfp.iSettleFlag != '' and {IAsys} = 1)
				OR cgfp.overflag != ''
				OR cgfp.vouchflag != ''
				OR cgfp.iCheckStatus != ''
			UNION ALL
			
			SELECT	* FROM (
				SELECT	i.cbillcode,
						i.ddate,
						'采购入库单' cbilltype,
						isnull(kjz.overflag, '') overflag,
						isnull(vouchIA.vouchflag, '') vouchflag,
						CASE
							WHEN {IAsys} = 1
								AND (setl.SUMisettleQTY/SUMiUnitQTY)=1 then ''
								ELSE '未完全结算' 
						END AS iSettleFlag,
	               		'' iCheckStatus,
	               		''  iExeFlag
				FROM st_stkrecord i
				LEFT JOIN (
					SELECT l.csmainid,
						'存在目标单据跨结转期' overflag
					FROM (	
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'st_stkrecord'
							AND ii.ddate > {ddate}
							
						UNION ALL
							
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN PU_Invoice ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'PU_Invoice'
							AND ii.dInvDate > {ddate}
						
						UNION ALL
						
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'ap_payment'
							AND ii.dVouDate > {ddate}
						) l GROUP BY l.csmainid
					) kjz ON kjz.csmainid = i.cguid
				LEFT JOIN (
					SELECT l.cheadguid,
						'未生成凭证' vouchflag
					FROM ST_StkRecordLine l
					LEFT JOIN IA_SubsidiaryLedger ia ON l.cGUID = ia.cStkLineGUID
					LEFT JOIN FAP_BILL_VOU_REL vouchrela ON vouchrela.cBillGuid = ia.cGUID
					LEFT JOIN GL_Voucher vouch ON vouch.cGUID = vouchrela.cVouGuid
					WHERE ia.cbilltype = '010'
						AND ia.dAccountDate IS NOT NULL
						AND ia.iOutAMT <> 0
						AND {IAsys} = 1
						AND {GLsys} = 1
						AND vouch.cCode IS NULL
					GROUP BY l.cheadguid
					) vouchIA ON vouchIA.cheadguid = i.cguid
				LEFT JOIN (
						SELECT cHeadGUID,
								 Sum(isettleQTY) AS SUMisettleQTY,
								 case when Sum(iUnitQTY) = 0 then 1 else  Sum(iUnitQTY) end AS SUMiUnitQTY
						FROM   ST_StkRecordLine b
						GROUP  BY cHeadGUID
					) setl ON setl.cHeadGUID = i.cguid
				WHERE i.ddate <= {ddate}
					AND i.cbilltype = '010'
			) cgstk 
			WHERE (cgstk.iSettleFlag !='' and {IAsys} = 1)
			OR isnull(cgstk.overflag, '') !='' 
			OR isnull(cgstk.vouchflag, '') !='' 
					
			UNION ALL
			
			SELECT	i.cbillcode,
					i.ddate,
					'期初采购入库单' cbilltype,
					isnull(kjz.overflag, '') overflag,
					'' vouchflag,
					CASE
						WHEN (a.SUMisettleQTY*1.0/a.SUMiUnitQTY)=1 then ''
							ELSE '未完全结算' 
					END AS iSettleFlag,
					case when a.SUMiInvQTY/SUMiUnitQTY =1 then '' 
							else '未完全开票' 
						end as iExeFlag,
					'' iCheckStatus
			FROM st_stkrecord i
			LEFT JOIN (
				SELECT l.csmainid,
					'存在目标单据跨结转期' overflag
				FROM (	
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'st_stkrecord'
						AND ii.ddate > {ddate}
						
					UNION ALL
						
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN PU_Invoice ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'PU_Invoice'
						AND ii.dInvDate > {ddate}
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'ap_payment'
						AND ii.dVouDate > {ddate}
					) l GROUP BY l.csmainid
				) kjz ON kjz.csmainid = i.cguid
			LEFT JOIN (SELECT st.cHeadGUID,
						       Sum(st.isettleQTY) AS SUMisettleQTY,
						       Sum(iInvoiceQTY) As SUMiInvQTY,
						       case when Sum(iUnitQTY) = 0 then 1 else  Sum(iUnitQTY) end AS SUMiUnitQTY
						FROM   ST_StkRecordLine st
						GROUP  BY st.cHeadGUID 
				) a on a.cHeadGUID = i.cGUID
			WHERE i.cbilltype = '091'
				AND i.ddate <= {ddate}
				AND (overflag != '' 
					OR iSettleFlag != '')
					
			UNION ALL
			
			SELECT * FROM (
				SELECT	i.cInvCode	cbillcode,
						i.dInvDate	ddate,
						'期初采购发票' cbilltype,
						isnull(kjz.overflag, '') overflag,
						'' vouchflag,
						CASE
							WHEN (a.iSettleFlag*1.0/a.sFlag)=1 then ''
								ELSE '未完全结算' 
						END AS iSettleFlag,
						isnull(exe.iExeFlag, '') iExeFlag,
						'' iCheckStatus
				FROM PU_Invoice i
				LEFT JOIN (
					SELECT	l.csmainid,
							'存在目标单据跨结转期' overflag
					FROM (
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN PU_Receive ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'PU_Receive'
							AND ii.dDate > {ddate}
						
						UNION ALL
						
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'st_stkrecord'
							AND ii.ddate > {ddate}
						
						UNION ALL
						
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN PU_Invoice ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'PU_Invoice'
							AND ii.dInvDate > {ddate}
							
						UNION ALL
						
						SELECT m.csmainid
						FROM BILL_GEN_RELATION_MAIN m
						INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
						WHERE m.cDMainEntity = 'ap_payment'
							AND ii.dVouDate > {ddate}
						) l
					GROUP BY l.csmainid
					) kjz ON kjz.csmainid = i.cguid
				LEFT JOIN (
						SELECT ol.cHeadGUID,
				 			sum(ol.iSettleFlag) as iSettleFlag,
				 			sum(1) as sFlag
	              		FROM pu_invoiceline ol 
	              		GROUP BY ol.cHeadGUID
					) a on a.cHeadGUID = i.cGUID
				LEFT JOIN (
						SELECT m.cHeadGUID,
							'未完全入库'  iExeFlag
						FROM (
							SELECT l.cHeadGUID
							FROM  PU_InvoiceLine l
							LEFT JOIN v_business_pu_invoice2bill v ON v.invLineID = l.cGUID
							WHERE v.iInvToBillQTY <> l.iUnitQTY
								AND v.cDMainEntity = 'st_stkrecord'
							
							UNION ALL
						
							SELECT l.cHeadGUID
							FROM  PU_InvoiceLine l
							WHERE l.cGUID NOT IN 
								(SELECT invLineID FROM v_business_pu_invoice2bill WHERE cDMainEntity = 'st_stkrecord')
						) m GROUP BY m.cHeadGUID
					) exe on exe.cHeadGUID = i.cGUID
				WHERE i.iInitFlag = 1 
				AND i.dInvDate <= {ddate}  ) qccgfp
			WHERE qccgfp.iSettleFlag != ''
		
			UNION ALL
		
			SELECT	''	cbillcode,
					i.DROPTIME	ddate,
					'ROP采购' cbilltype,
					'存在目标单据跨结转期' overflag,
					'' vouchflag,
					'' iSettleFlag,
					'' iCheckStatus,
					'' iExeFlag
			FROM PU_ROPhead i
			WHERE i.DROPTIME <= {ddate}
				AND EXISTS (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN PU_Order ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'PU_Order'
						AND m.csmainid = i.cguid
						AND ii.dPODate > {ddate}
				)
			
		]]></i>
	</sql>
</sqls>