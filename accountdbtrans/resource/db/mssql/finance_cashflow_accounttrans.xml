<?xml version="1.0" encoding="UTF-8"?>
<sqls>
	<sql group="finance_cashflow_trans">
		<i id="deleteCashFlow">
			delete from gl_cashflowinitamt
		</i>
		<i id="insertCashFlow">
			insert into gl_cashflowinitamt (cguid, ccashflowitemguid, ccurguid, ifamount, iyear, imonth) values({cguid},{item},{curr},{amt},{iyear},{imonth})
		</i>
		<i id="queryGLInit">
			select iinityear, iinitmonth from co_sysinit where csubsyscode = 'GL' 
		</i>
		<i id="queryCashFlowInitData">
			select cl.ccashflowitemguid, cl.ccurguid, cl.ifamount, ci.cparentid, ci.iflowdirect dir 
			from gl_cashflowinitamt cl 
			join gl_cashflowitem ci on cl.ccashflowitemguid = ci.cGUID
		</i>
		<i id="queryCashFlowInit">
			select cf.cguid ccashflowitemguid,cfi.ccurguid ccurguid,0 ifamount, cf.cparentid, cf.iflowdirect dir 
			from gl_cashflowitem cf
			join gl_cashflowinitamt cfi on cfi.ccashflowitemguid = cf.cGUID
			where cf.iappend = 0 and cf.icashflow = 1
		</i>
		<!-- 
		<i id="queryCashFlowInit">
			select c1.cguid ccashflowitemguid,c2.cguid ccurguid,0 ifamount, c1.cparentid, c1.iflowdirect dir 
			from gl_cashflowitem c1,GL_Currency c2 where c1.iappend = 0 and c1.icashflow = 1
		</i>
		 -->
		<i id="queryCashFlow">
		<![CDATA[
			select 1 dir, cl.cCashItemGUID ccashflowitemguid, vl.cCurGUID ccurguid
			, sum(cl.iCashFlowAMT*(case when ci1.iflowDirect is not null then ci1.iflowDirect 
			when ci1.iflowDirect is null and ci2.iflowDirect is not null then ci2.iflowDirect else 1 end 
			)*(CASE WHEN vl.iDebitAMT<>0 then 1 else -1 END)) ifamount 
			from GL_VoucherLineCashflow cl 
			join GL_VoucherLine vl on cl.cVouLineGUID = vl.cGUID 
			left join GL_CashflowItem ci1 on cl.cCashItemGUID = ci1.cGUID
            left join GL_CashflowItem ci2 on cl.cCashSubItemGUID = ci2.cGUID
			left join GL_Voucher v on v.cGUID = vl.cVouGUID 
			where v.iYear = {iNextYear} and v.iMonth < {iNextMonth} and v.iStatus=1 
			group by ci1.iflowdirect, cl.cCashItemGUID, vl.cCurGUID  
		]]>
		</i>
	</sql>
</sqls>