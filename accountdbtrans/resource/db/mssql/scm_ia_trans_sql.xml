<?xml version="1.0" encoding="UTF-8"?>
<sqls>
	<sql group="scm_ia_trans">
	
	
		<i id="warndate" desc="存货警告提示信息"><![CDATA[
        SELECT i.cbillcode
				,i.ddate,
				CASE WHEN i.cbilltype = '010' THEN '采购入库单'
						 WHEN i.cbilltype = '020' THEN '销售出库单'
						 WHEN i.cbilltype = '040' THEN '其它入库单'
						 WHEN i.cbilltype = '050' THEN '其它出库单' 
						 WHEN i.cbilltype = '060' THEN '成品入库单' 
						 WHEN i.cbilltype = '070' THEN '材料出库单' 
					END AS cbilltype
				,isnull(kjz.overflag, '') overflag
				,isnull(vouchIA.vouchflag, '') vouchflag
			FROM st_stkrecord i
			LEFT JOIN (
				SELECT l.csmainid
					,'存在目标单据跨结转期' overflag
				FROM (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'st_stkrecord'
						AND ii.ddate > {ddate}
					) l
				GROUP BY l.csmainid
				) kjz ON kjz.csmainid = i.cguid
			LEFT JOIN (
				SELECT l.cheadguid
					,'未生成凭证' vouchflag
				FROM ST_StkRecordLine l
				LEFT JOIN ST_StkRecord ii ON ii.cguid = l.cheadguid
				LEFT JOIN IA_SubsidiaryLedger ia ON l.cGUID = ia.cStkLineGUID
				LEFT JOIN FAP_BILL_VOU_REL vouchrela ON vouchrela.cBillGuid = ia.cGUID
				LEFT JOIN GL_Voucher vouch ON vouch.cGUID = vouchrela.cVouGuid
				WHERE 
				 ( ia.cbilltype = '020' AND  (ii.iIaFlag != 0 OR ii.iAccDeliverGoods != 0)
				   or ia.cbilltype in ('010','040','050','060','070' ) )
					AND ia.dAccountDate IS NOT NULL
					AND ( ia.iOutAMT <> 0 OR ia.iInAMT <> 0 )
					AND {IAsys} = 1
					AND {GLsys} = 1
					AND vouch.cCode IS NULL
				GROUP BY l.cheadguid
				) vouchIA ON vouchIA.cheadguid = i.cguid
			WHERE i.ddate <= {ddate}
				AND i.cbilltype IN ('010','020','040','050','060','070' ) 
				AND (
					kjz.overflag IS NOT NULL
					OR vouchIA.vouchflag IS NOT NULL
					)
								
		union all
			SELECT i.cbillcode
				,i.ddate,
				CASE WHEN i.cbilltype = '092' THEN '入库调整单'
						 WHEN i.cbilltype = '093' THEN '出库调整单'
					END AS cbilltype
				,'' AS overflag
				,isnull(vouchIA.vouchflag, '') vouchflag
			FROM IA_Adjust i
			LEFT JOIN (
				SELECT
					l.cheadguid,
					'未生成凭证' vouchflag
				FROM
					IA_AdjustLine l
				LEFT JOIN IA_Adjust ii ON ii.cguid = l.cheadguid
				LEFT JOIN IA_SubsidiaryLedger ia ON l.cGUID = ia.cJustGUID
				LEFT JOIN FAP_BILL_VOU_REL vouchrela ON vouchrela.cBillGuid = ia.cGUID
				LEFT JOIN GL_Voucher vouch ON vouch.cGUID = vouchrela.cVouGuid
				WHERE
					ia.dAccountDate IS NOT NULL
					AND ( ia.iOutAMT <> 0 OR ia.iInAMT <> 0 )
					AND {IAsys} = 1
					AND {GLsys} = 1
					AND vouch.cCode IS NULL
				GROUP BY l.cheadguid
				) vouchIA ON vouchIA.cheadguid = i.cguid
			WHERE i.ddate <= {ddate}
				AND vouchIA.vouchflag IS NOT NULL
			    
		union all
			SELECT i.cbillcode
				,i.ddate,
				'发出商品贷方调整单' AS cbilltype
				,'' AS overflag
				,isnull(vouchIA.vouchflag, '') vouchflag
			FROM IA_EnAdjust i
			LEFT JOIN (
				SELECT
					l.cheadguid,
					'未生成凭证' vouchflag
				FROM
					IA_EnAdjustLine l
				LEFT JOIN IA_EnAdjust ii ON ii.cguid = l.cheadguid
				LEFT JOIN IA_EnSubsidiaryLedger ia ON l.cGUID = ia.cStkLineGUID
				LEFT JOIN FAP_BILL_VOU_REL vouchrela ON vouchrela.cBillGuid = ia.cGUID
				LEFT JOIN GL_Voucher vouch ON vouch.cGUID = vouchrela.cVouGuid
				WHERE
					ia.dAccountDate IS NOT NULL
					AND ( ia.iOutAMT <> 0 OR ia.iInAMT <> 0 )
					AND {IAsys} = 1
					AND {GLsys} = 1
					AND vouch.cCode IS NULL
				GROUP BY l.cheadguid
				) vouchIA ON vouchIA.cheadguid = i.cguid
			WHERE i.ddate <= {ddate}
				AND vouchIA.vouchflag IS NOT NULL
		
		/*蓝字结算单，红字回冲单未生成凭证给出警告提示*/	
		UNION ALL
			SELECT i.cbillcode
				,i.ddate,
				'蓝字结算单'  AS cbilltype
				,'' AS overflag
				,'未生成凭证' AS vouchflag
			FROM IA_SubsidiaryLedger i	
			LEFT JOIN FAP_BILL_VOU_REL vouchrela ON vouchrela.cBillGuid = i.cGUID
			LEFT JOIN GL_Voucher vouch ON vouch.cGUID = vouchrela.cVouGuid	
			WHERE i.dAccountDate IS NOT NULL
				AND vouch.cCode IS NULL
				AND i.cbilltype = '094'
				
		UNION ALL
			SELECT i.cbillcode
				,i.ddate,
				'红字回冲单'  AS cbilltype
				,'' AS overflag
				,'未生成凭证' AS vouchflag
			FROM IA_SubsidiaryLedger i	
			LEFT JOIN FAP_BILL_VOU_REL vouchrela ON vouchrela.cBillGuid = i.cGUID
			LEFT JOIN GL_Voucher vouch ON vouch.cGUID = vouchrela.cVouGuid	
			WHERE i.dAccountDate IS NOT NULL
				AND vouch.cCode IS NULL
				AND i.cbilltype = '095'
				
		/*2018-09-13 明细中跨结转期记账给出警告提示*/			
		UNION ALL
			SELECT
				ia.cBillCode,
				ia.dAccountDate,
				CASE WHEN ia.cBillType = '010' THEN
						'采购入库单'
					WHEN ia.cBillType = '020' THEN
						'销售出库单'
					WHEN ia.cBillType = '060' THEN
						'成品入库单'
					WHEN ia.cBillType = '070' THEN
						'材料出库单'
					WHEN ia.cBillType = '092' THEN
						'入库调整单'
					WHEN ia.cBillType = '093' THEN
						'出库调整单'
					WHEN ia.cBillType = '094' THEN
						'蓝字结算单'
					WHEN ia.cBillType = '095' THEN
						'红字回冲单'
					WHEN ia.cBillType = '077' THEN
						'货位调整单'
					WHEN ia.cBillType = '092' THEN
						'入库调整单'
					WHEN ia.cBillType = '093' THEN
						'入库调整单'
					WHEN ia.cBillType = '212' THEN
						'入库调整单'
				END AS cBillType,
			 	ISNULL(s.overflag, '') overflag,
			 	'' vouchflag
			FROM
				IA_SubsidiaryLedger ia
			LEFT JOIN (
				SELECT
					iaa.cBillcode,
					iaa.cBIllType,
					'存在明细记账跨结转期' overflag
				FROM
					IA_SubsidiaryLedger iaa
				WHERE
					iaa.dAccountDate >= '2018-09-30'
				GROUP BY
					iaa.cbillcode,
					iaa.cBilltype,
			iaa.cBillType
			) s ON s.cBillcode = ia.cbillcode
			AND s.cbilltype = ia.cBillType
			WHERE
				ia.dAccountDate <= '2018-09-30' 
			AND s.overflag !=''			
						
		/*2018-09-12 tss 校验先入先出计价物品是否出完*/		
		UNION ALL
			SELECT assis.cinvoucode as cbillcode,
				   assis.dindate as ddate,
                   CASE WHEN sr.cbilltype = '010' THEN '采购入库单'
						WHEN sr.cbilltype = '040' THEN '其它入库单' 
						WHEN sr.cbilltype = '060' THEN '成品入库单'  
				   END AS cbilltype,
				   '先入先出计价物品未出完' AS overflag,
				   null AS vouchflag
			FROM IA_assistantLedger assis
            left join cm_material mat on assis.cmatguid=mat.cguid
            left join ST_StkRecordLine srl on assis.cinguid=srl.cguid
            inner join ST_StkRecord sr on sr.cguid=srl.cHeadGUID
			WHERE assis.ioutqty is null 
            and mat.icalway=3
            and assis.dindate <= {ddate}
            
        /*2018-09-12 tss 校验个别计价物品是否出完*/		
		UNION ALL
			SELECT assis.cinvoucode as cbillcode,
				   assis.dindate as ddate,
                   CASE WHEN sr.cbilltype = '010' THEN '采购入库单'
						WHEN sr.cbilltype = '040' THEN '其它入库单' 
						WHEN sr.cbilltype = '060' THEN '成品入库单'  
				   END AS cbilltype,
				   '个别计价物品未出完' AS overflag,
				   null AS vouchflag
			FROM IA_assistantLedger assis
            left join cm_material mat on assis.cmatguid=mat.cguid
            left join ST_StkRecordLine srl on assis.cinguid=srl.cguid
            inner join ST_StkRecord sr on sr.cguid=srl.cHeadGUID
			WHERE assis.ioutqty is null 
            and mat.icalway=4
            and assis.dindate <= {ddate}
		]]>
		</i>
		<i id="vaildate" desc="存货错误提示信息"><![CDATA[
		    SELECT 
		    	ad.cbillcode,
				ad.dDate,
				CASE  WHEN ad.iAccountflag != '1' THEN '未记账'
					ELSE '' END AS iAccountflag,
				CASE  WHEN ad.iAuditStatus != 'checked' THEN '未审核'
					ELSE '' END AS iAuditStatus,
				CASE WHEN cbilltype = '093' THEN '出库调整单'
						 WHEN cbilltype = '092' THEN '入库调整单' 
					END AS cbilltype
				from 
				IA_Adjust ad 
				WHERE ( ISNULL(ad.iAccountflag, 0)=0
							OR ad.iAuditStatus != 'checked' )
				AND ad.dDate <= {ddate}
		
			UNION ALL
			  
			SELECT
				ad.cbillcode, 
				ad.dDate,
				CASE  WHEN ad.iAccountflag != '1' THEN '未记账'
					ELSE '' END AS iAccountflag,
				CASE  WHEN ad.iAuditStatus != 'checked' THEN '未审核'
					ELSE '' END AS iAuditStatus,
				'发出商品贷方调整单' AS cbilltype
				from 
				IA_EnAdjust ad 
				WHERE ( ISNULL(ad.iAccountflag, 0)=0
					OR ad.iAuditStatus != 'checked' )
				and ad.dDate <= {ddate}
				
			UNION ALL
			
			SELECT 
		    s.cbillcode,
				s.dDate,
				CASE  WHEN s.iAccountflag != '1' THEN '未记账'
					ELSE '' END AS iAccountflag,
				CASE  WHEN s.iAuditStatus != 'checked' THEN '未审核'
					ELSE '' END AS iAuditStatus,
				CASE WHEN s.cbilltype = '010' THEN '采购入库单'
						 WHEN s.cbilltype = '020' THEN '销售出库单'
						 WHEN s.cbilltype = '040' THEN '其它入库单'
						 WHEN s.cbilltype = '050' THEN '其它出库单' 
						 WHEN s.cbilltype = '060' THEN '成品入库单' 
						 WHEN s.cbilltype = '070' THEN '材料出库单' 
					END AS cbilltype
			FROM st_stkrecord s
			LEFT JOIN (
				SELECT l.cheadguid
					,'未记账' iAccountflag
				FROM st_stkrecordline l
				LEFT JOIN CM_Storehouse sh ON l.cStoreGUID = sh.cGUID
				WHERE isnull(sh.iNoCalCost, 0) = 0
					AND isnull(l.iAccountflag, 0) = 0
				GROUP BY l.cheadguid
				) iacc ON iacc.cheadguid = s.cguid
			WHERE 
			 (	( s.cbilltype IN ('010','040','050','060','070' )
			 	  AND ( iacc.iAccountflag IS NOT NULL OR s.iAuditStatus != 'checked' ) )
			 	OR ( ( iacc.iAccountflag IS NOT NULL
						AND (
							S.iIaFlag != 0
							OR S.iAccDeliverGoods != 0
							) OR s.iAuditStatus != 'checked' )
						AND S.cbilltype = '020'
					) )
				AND not exists(
							SELECT 1
								FROM CO_SysInit
								WHERE cSubSysCode IN('ST','SA','PU') )
				AND s.dDate <= {ddate}				
			UNION ALL					
			SELECT
				cBillCode,
				dDate,
				'未分配' AS iAccountflag,
				'' AS iAuditStatus,
				'产成品分配单' AS cBillType
			FROM IA_CosDis
			WHERE
				ISNULL(iDisperFlag, 0) = 0
				AND dDate <= {ddate}
		]]></i>
		<i id="deleteIAbill" desc="存货单据删除"><![CDATA[
			DELETE 
			FROM IA_AdjustLine l
			LEFT JOIN IA_Adjust s on s.cguid = l.cheadguid
			WHERE ISNULL(s.iAccountflag, 0)=1
						AND s.iAuditStatus = 'checked'
			GO
			
			
			DELETE 
			FROM IA_Adjust 
			WHERE ISNULL(iAccountflag, 0)=1
						AND iAuditStatus = 'checked'
			GO
			
			DELETE 
			FROM IA_EnAdjustLine l
			LEFT JOIN IA_EnAdjust s on s.cguid = l.cheadguid
			WHERE ISNULL(s.iAccountflag, 0)=1
						AND s.iAuditStatus = 'checked'
			GO
			DELETE 
			FROM IA_EnAdjust
			WHERE ISNULL(iAccountflag, 0)=1
						AND iAuditStatus = 'checked'
			GO
			
			DELETE
			FROM
				IA_GeneralLedger gl
			WHERE
				gl.iYear < {iYear}
				AND gl.iMonth < {iMonth}
			GO	
				
			DELETE 
			FROM
				IA_SubsidiaryLedger s
			LEFT JOIN ST_StkRecordLine stl ON stl.cguid = s.cStkLineGUID
			LEFT JOIN ST_StkRecord st ON st.cguid = stl.cheadguid
			WHERE
				st.iAuditStatus = 'checked'
				AND ( NOT EXISTS( ISNULL(stl.iAccountflag, 0)=0 AND ISNULL(sh.iNoCalCost, 0) = 1) OR st.cbilltype = '030' and csystype = 'ST' ) 
				AND st.cBillType NOT IN( '091','101','021')
			GO
			
			
			DELETE
			FROM ST_StkRecordLine stl
			LEFT JOIN CM_Storehouse sh on sh.cguid = stl.cstoreguid
			LEFT JOIN ST_StkRecord s on s.cguid = stl.cheadguid
			WHERE 
			(ISNULL(stl.iAccountflag, 0)=1
			OR ISNULL(sh.iNoCalCost, 0) = 0 )
			AND iAuditStatus = 'checked'
			AND not exists(
							SELECT 1
								FROM CO_SysInit
								WHERE cSubSysCode IN('ST','SA','PU') )
			GO
			DELETE 
			FROM ST_StkRecord st
			WHERE
			ISNULL(iAccountflag, 0)=1
			OR NOT EXISTS(
			SELECT FROM ST_StkRecordLine l 
			LEFT JOIN CM_Storehouse sh on sh.cguid = l.cstoreguid
			WHERE ISNULL(l.iaccountflag, 0) = 0 
			 AND ISNULL(sh.iNoCalCost, 0) = 0
			)
			AND iAuditStatus = 'checked'
			AND not exists(
							SELECT 1
								FROM CO_SysInit
								WHERE cSubSysCode IN('ST','SA','PU') )
			GO
			
			
			
			DELETE
			FROM IA_CosDisLine l
			LEFT JOIN IA_CosDis s ON l.cHeadGUID = s.cguid
			WHERE ISNULL(iDisperFlag, 0) = 1
			GO
			DELETE
			FROM IA_CosDis s ON l.cHeadGUID = s.cguid
			WHERE ISNULL(iDisperFlag, 0) = 1
			GO
		]]></i>
		<i id="transIAGeninitbill" desc="存货结转期初总账"><![CDATA[
			update IA_GeneralLedger set iYear = (case when iMonth = 12 then iYear+1 else iYear end), iMonth = 0, iInQTY = 0, iInAMT = 0, iOutQTY = 0, iOutAMT = 0, iInventoryFlag = 1 where iYear = {iYear} and iMonth = {iMonth} 
		]]></i>
		<i id="transIAinitbill" desc="存货结转期初"><![CDATA[
		
		set IDENTITY_INSERT IA_SubsidiaryLedger ON  
				INSERT INTO [IA_SubsidiaryLedger] (
					[cGUID],
					[iNumber],
					[iRSFlag],
					[cBusType],
					[cBillCode],
					[cStkLineGUID],
					[cSettleLineGUID],
					[cJustGUID],
					[dDate],
					[dAccountDate],
					[iYear],
					[iMonth],
					[iPZNo],
					[cPZtype],
					[cPZID],
					[cBillType],
					[cPTGUID],
					[cSTGUID],
					[cStoreGUID],
					[cMatGUID],
					[cIOTypeGUID],
					[cSupGUID],
					[cCustGUID],
					[cPurOrdLineGUID],
					[cSaOrdLineGUID],
					[cEmpGUID],
					[cHandlerGUID],
					[iInQTY],
					[iOutQTY],
					[iInUnitPrice],
					[iOutUnitPrice],
					[iInAMT],
					[iOutAMT],
					[cDeptGUID],
					[cBatchGUID],
					[cPosterGUID],
					[cCreatorGUID],
					[bSVAFlag],
					[iSelfPriceFlag],
					[bIsSale],
					[cRemark],
					[cDefine1],
					[cDefine2],
					[cDefine3],
					[cDefine4],
					[cDefine5],
					[cDefine6],
					[cDefine7],
					[cDefine8],
					[cDefine9],
					[cDefine10],
					[cDefine11],
					[cDefine12],
					[cDefine13],
					[cDefine14],
					[cDefine15],
					[cDefine16],
					[cDefine17],
					[cDefine18],
					[cDefine19],
					[cDefine20],
					[cFree1],
					[cFree2],
					[cFree3],
					[cFree4],
					[cFree5],
					[cFree6],
					[cFree7],
					[cFree8],
					[cFree9],
					[cFree10],
					[cLineDefine1],
					[cLineDefine2],
					[cLineDefine3],
					[cLineDefine4],
					[cLineDefine5],
					[cLineDefine6],
					[cLineDefine7],
					[cLineDefine8],
					[cLineDefine9],
					[cLineDefine10],
					[cLineDefine11],
					[cLineDefine12],
					[cLineDefine13],
					[cLineDefine14],
					[cLineDefine15],
					[cLineDefine16],
					[cLineDefine17],
					[cLineDefine18],
					[cLineDefine19],
					[cLineDefine20],
					[cItemClassGUID],
					[cItemGUID],
					[cStoreGroupGUID],
					[cTimeStamp],
					[iSpecialAcct],
					[cHeadRemark],
					[cInvCode],
					[cProductID],
					[cBlueSetGUID],
					[cAcctIItemGuid],
					[iSTFetch],
					[dOverDate],
					[dProduction],
					[cFreeValue],
					[cFreeName],
					[iTOSTFlag],
					[cPriceSource],
					[iInventoryFlag]
					
				)
				SELECT
					LEFT (newID(), 18) AS cGUID,
					ROW_NUMBER () OVER (ORDER BY gl.CMATGUID DESC) AS iNumber,
					'1' AS iRSFlag,
					'09001' AS cBusType,
					NULL AS cBillCode,
					NULL AS cStkLineGUID,
					NULL AS cSettleLineGUID,
					NULL AS cJustGUID,
					{dDate} AS dDate,
					{dDate} AS dAccountDate,
					{iYear} AS iYear,
					'0' AS iMonth,
					NULL AS iPZNo,
					NULL AS cPZtype,
					NULL AS cPZID,
					'090' AS cBillType,
					NULL AS cPTGUID,
					NULL AS cSTGUID,
					gl.cStoreGUID,
					gl.cMatGUID,
					NULL AS cIOTypeGUID,
					gl.cSupGUID,
					NULL AS cCustGUID,
					NULL AS cPurOrdLineGUID,
					NULL AS cSaOrdLineGUID,
					gl.cEmpGUID,
					NULL AS cHandlerGUID,
					SUM (isnull(gl.iInQTY,0)-isnull(gl.iOutQTY,0)) AS iInQTY,
					NULL AS iOutQTY,
					CASE
				WHEN SUM (isnull(gl.iInQTY,0)-isnull(gl.iOutQTY,0)) = 0 THEN
					NULL
				ELSE
					SUM (isnull(gl.iInAMT,0)-isnull(gl.iOutAMT,0)) / SUM (isnull(gl.iInQTY,0)-isnull(gl.iOutQTY,0))
				END AS iInUnitPrice,
				 NULL AS iOutUnitPrice,
				 SUM (isnull(gl.iInAMT,0)-isnull(gl.iOutAMT,0)) AS iInAMT,
				 NULL AS iOutAMT,
				 gl.cDeptGUID,
				 gl.cBatchGUID,
				 NULL AS cPosterGUID,
				 '1' AS cCreatorGUID,
				 NULL AS bSVAFlag,
				 NULL AS iSelfPriceFlag,
				 NULL AS bIsSale,
				 NULL AS cRemark,
				 NULL AS cDefine1,
				 NULL AS cDefine2,
				 NULL AS cDefine3,
				 NULL AS cDefine4,
				 NULL AS cDefine5,
				 NULL AS cDefine6,
				 NULL AS cDefine7,
				 NULL AS cDefine8,
				 NULL AS cDefine9,
				 NULL AS cDefine10,
				 NULL AS cDefine11,
				 NULL AS cDefine12,
				 NULL AS cDefine13,
				 NULL AS cDefine14,
				 NULL AS cDefine15,
				 NULL AS cDefine16,
				 NULL AS cDefine17,
				 NULL AS cDefine18,
				 NULL AS cDefine19,
				 NULL AS cDefine20,
				 gl.cFree1,
				 gl.cFree2,
				 gl.cFree3,
				 gl.cFree4,
				 gl.cFree5,
				 gl.cFree6,
				 gl.cFree7,
				 gl.cFree8,
				 gl.cFree9,
				 gl.cFree10,
				 NULL AS cLineDefine1,
				 NULL AS cLineDefine2,
				 NULL AS cLineDefine3,
				 NULL AS cLineDefine4,
				 NULL AS cLineDefine5,
				 NULL AS cLineDefine6,
				 NULL AS cLineDefine7,
				 NULL AS cLineDefine8,
				 NULL AS cLineDefine9,
				 NULL AS cLineDefine10,
				 NULL AS cLineDefine11,
				 NULL AS cLineDefine12,
				 NULL AS cLineDefine13,
				 NULL AS cLineDefine14,
				 NULL AS cLineDefine15,
				 NULL AS cLineDefine16,
				 NULL AS cLineDefine17,
				 NULL AS cLineDefine18,
				 NULL AS cLineDefine19,
				 NULL AS cLineDefine20,
				 gl.cItemClassGUID,
				 gl.cItemGUID,
				 NULL AS cStoreGroupGUID,
				 gl.cMatGUID AS cTimeStamp,
				 '0' AS iSpecialAcct,
				 NULL AS cHeadRemark,
				 NULL AS cInvCode,
				 NULL AS cProductID,
				 NULL AS cBlueSetGUID,
				 NULL AS cAcctIItemGuid,
				 '0' AS iSTFetch,
				 max(gl.dOverDate),
				 max(gl.dProduction),
				 NULL AS cFreeValue,
				 NULL AS cFreeName,
				 NULL AS iTOSTFlag,
				 NULL AS cPriceSource,
				 '1' AS iInventoryFlag
				FROM
					IA_SubsidiaryLedger gl
				WHERE gl.iYear < {iYear} or gl.iYear = {iYear} and gl.iMonth <= {iMonth}
					/*add by leiziyang 20180914 reasion:解决结转后物品TT.07.022结存数据错误 start*/
				and gl.cBillType != '091' 
						and gl.cBillType != '101' 
						and gl.cBillType != '102' 
				/*add by leiziyang 20180914 reasion:解决结转后物品TT.07.022结存数据错误 end*/
				GROUP BY
					gl.cMatGUID,
					gl.cStoreGUID,
					gl.cSupGUID,
					gl.cEmpGUID,
					gl.cDeptGUID,
					gl.cBatchGUID,
				  gl.cFree1,
					gl.cFree2,
					gl.cFree3,
					gl.cFree4,
					gl.cFree5,
					gl.cFree6,
					gl.cFree7,
					gl.cFree8,
					gl.cFree9,
					gl.cFree10,
				  gl.cItemClassGUID,
				  gl.cItemGUID
			set IDENTITY_INSERT IA_SubsidiaryLedger OFF  
			
	
	]]></i>
	</sql>
</sqls>
