<?xml version="1.0" encoding="UTF-8"?>
<sqls>
	<sql group="scm_ar_trans">
		<i id="vaildate" desc="失败数据校验"><![CDATA[
			SELECT i.cVouCode cbillcode
				,i.dVouDate ddate
				,case when i.iInitFlag = 0 then '期初收款单' else '收款单' end as cbilltype
				,CASE 
					WHEN i.iAuditStatus != 'checked'
						THEN '未审核'
					ELSE ''
					END AS iAuditStatus
			FROM AP_Payment i
			WHERE i.iAuditStatus != 'checked'
				AND i.dVouDate <= {ddate}
				and i.iAuditStatus != 'tsaved'
				and cFlag = 'AR'
			
			UNION ALL
			
			SELECT i.cInvCode cbillcode
				,i.ddate
				,case when i.iInitFlag = 0 then '销售发票' else '期初应收发票' end as cbilltype
				,CASE 
					WHEN i.iAuditStatus != 'checked'
						THEN '未审核'
					ELSE ''
					END AS iAuditStatus
			FROM SA_Invoice i
			WHERE i.iAuditStatus != 'checked'
				AND i.ddate <= {ddate}
				and i.iAuditStatus != 'tsaved'
				and i.cSysType = 'AR'
			
			UNION ALL
			
			SELECT i.cVouCode cbillcode
				,i.dVouDate ddate
				,case when i.iInitFlag = 0 then '期初其他应收单' else '其他应收单' end as cbilltype
				,CASE 
					WHEN i.iAuditStatus != 'checked'
						THEN '未审核'
					ELSE ''
					END AS iAuditStatus
			FROM AP_APVoucher i
			WHERE i.iAuditStatus != 'checked'
				AND i.dVouDate <= {ddate}
				and i.iAuditStatus != 'tsaved'
				and cFlag = 'AR'
		]]></i>
		<i id="warndate" desc="警告数据校验"><![CDATA[
			SELECT i.cVouCode cbillcode
				,i.dVouDate ddate
				,case when i.iInitFlag = 0 then '期初收款单' else '收款单' end as cbilltype
				,isnull(kjz.overflag, '') overflag
				,CASE WHEN i.iPayAMT_F + isnull(i.ignoreAMT_F, 0) != i.iCheckAMT_F then '未完全核销'
				else '' end as iCheckStatus
			FROM AP_Payment i
			LEFT JOIN (
				SELECT m.csmainid
					   ,'存在目标单据跨结转期' overflag
				FROM BILL_GEN_RELATION_MAIN m
				INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
				WHERE m.cDMainEntity = 'ap_payment'
					AND ii.dVouDate > {ddate}
					and ii.iAuditStatus != 'tsaved'
				GROUP BY m.csmainid
				) kjz ON kjz.csmainid = i.cguid
			WHERE i.dVouDate <= {ddate}
				and i.iAuditStatus != 'tsaved'
				and i.cFlag = 'AR'
				AND (
					kjz.overflag IS NOT NULL
					OR i.iPayAMT_F + isnull(i.ignoreAMT_F, 0) != i.iCheckAMT_F
					)
			
			UNION ALL
			
			SELECT i.cVouCode cbillcode
				,i.dVouDate ddate
				,case when i.iInitFlag = 0 then '期初其他应收单' else '其他应收单' end as cbilltype
				,isnull(kjz.overflag, '') overflag
				,CASE WHEN i.iPayableAMT_F != i.iCheckAMT_F then '未完全核销'
				else '' end as iCheckStatus
			FROM AP_APVoucher i
			LEFT JOIN (
				SELECT l.credguid
					   ,'存在目标单据跨结转期' overflag
				FROM AP_APVoucher l
				WHERE l.credguid is not null
					and l.iAuditStatus != 'tsaved'
					AND l.dVouDate > {ddate}
				GROUP BY l.credguid
				) kjz ON kjz.credguid = i.cguid
			WHERE i.dVouDate <= {ddate}
				and i.iAuditStatus != 'tsaved'
				and i.cFlag = 'AR'
				AND (
					kjz.overflag IS NOT NULL
					OR i.iPayableAMT_F != i.iCheckAMT_F
					)
			
			UNION ALL
			
			SELECT i.cInvCode cbillcode
				,i.ddate
				,case when i.iInitFlag = 0 then '销售发票' else '期初应收发票' end as cbilltype
				,isnull(kjz.overflag, '') overflag
				,isnull(iCheck.iCheckStatus, '') iCheckStatus
			FROM sa_invoice i
			LEFT JOIN (
				SELECT l.csmainid
					,'存在目标单据跨结转期' overflag
				FROM (
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN sa_dispatch ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'sa_dispatch'
						AND ii.ddate > {ddate}
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN st_stkrecord ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'st_stkrecord'
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN sa_invoice ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'sa_invoice'
						AND ii.ddate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					
					UNION ALL
					
					SELECT m.csmainid
					FROM BILL_GEN_RELATION_MAIN m
					INNER JOIN ap_payment ii ON ii.cguid = m.cdmainid
					WHERE m.cDMainEntity = 'ap_payment'
						AND ii.dVouDate > {ddate}
						and ii.iAuditStatus != 'tsaved'
					) l
				GROUP BY l.csmainid
				) kjz ON kjz.csmainid = i.cguid
			left join (
				select l.cheadguid as cheadguid,
					   case when sum(isnull(l.itotal_f,0)) = sum(isnull(l.iReceiveTotal_F,0)) then null
					   else '未完全核销' end as iCheckStatus
				from SA_InvoiceLine l
				left join sa_invoice i on i.cguid = l.cheadguid
				GROUP BY l.cheadguid
			) iCheck on iCheck.cheadguid = i.cguid
			WHERE i.ddate <= {ddate}
				and i.cSysType = 'AR'
				AND (
					kjz.overflag IS NOT NULL
					OR iCheck.iCheckStatus IS NOT NULL
					)
				and i.iAuditStatus != 'tsaved'
		]]></i>
		<i id="updatebg" desc="处理并账"><![CDATA[
			UPDATE AP_DetailAccount
			SET iInventoryFlag = 1,dVouDate = {dDate}
			WHERE cRemark IN (
					'应付款并账'
					,'预付款并账'
					,'应收款并账'
					,'预收款并账'
					)
				AND iCheckDir = 2
		]]></i>
		<i id="updateDetailAccount" desc="更新明细账"><![CDATA[
			insert into AP_DetailAccount (cguid,iYear,iPeriod,cVouKind,cVouGUID,cVouCode,dVouDate,cVouTypeGUID,cDeptGUID,cEmpGUID,cSupGUID,cMatGUID,cVocLGUID,iRedFlag,iPrePay,cCurGUID,iExchRate,iDebitAMT_F,iDebitAMT,iCreditAMT_F,iCreditAMT,cRemark,cFlag,iCheckDir,cTimeStamp,dTallykDate,iInventoryFlag)
			select LEFT (newID(), 18) cguid,
				   {iYear} iYear,
				   0 iPeriod,
				   i.cVouKind,
				   i.cVouGUID,
				   i.cVouCode,
				   {dDate} dVouDate,
				   i.cVouTypeGUID,
				   i.cDeptGUID,
				   i.cEmpGUID,
				   i.cSupGUID,
				   i.cMatGUID,
				   i.cVocLGUID,
				   i.iRedFlag,
				   i.iPrePay,
				   i.cCurGUID,
				   i.iExchRate,
				   case when i.cVouKind = '087' then 0
				    when i.cVouKind = '082' then sum(iDebitAMT_F) - sum(iCreditAMT_F)
					when i.cVouKind = '086' then sum(iDebitAMT_F) - sum(iCreditAMT_F)
				    when i.cVouKind = '081' then 0
				    when i.cVouKind = '085' then sum(iDebitAMT_F) - sum(iCreditAMT_F) 
					when i.cVouKind = '099' then sum(iDebitAMT_F) - sum(iCreditAMT_F)
					when i.cVouKind = '098' then 0
					when i.cVouKind = '209' then 0
					when i.cVouKind = '080' then 0
				    when i.cVouKind = '097' then 0	
				   end as iDebitAMT_F,
				   case when i.cVouKind = '087' then 0
				    when i.cVouKind = '082' then sum(iDebitAMT) - sum(iCreditAMT)
					when i.cVouKind = '086' then sum(iDebitAMT) - sum(iCreditAMT)
				    when i.cVouKind = '081' then 0
				    when i.cVouKind = '085' then sum(iDebitAMT) - sum(iCreditAMT) 
					when i.cVouKind = '099' then sum(iDebitAMT) - sum(iCreditAMT)
					when i.cVouKind = '098' then 0
					when i.cVouKind = '209' then 0
					when i.cVouKind = '080' then 0
				    when i.cVouKind = '097' then 0	
				   end as iDebitAMT,
				   case when i.cVouKind = '087' then sum(iCreditAMT_F) - sum(iDebitAMT_F) 
				    when i.cVouKind = '082' then 0
					when i.cVouKind = '086' then 0
					when i.cVouKind = '081' then sum(iCreditAMT_F) - sum(iDebitAMT_F) 
					when i.cVouKind = '085' then 0
					when i.cVouKind = '099' then 0
					when i.cVouKind = '098' then sum(iCreditAMT_F) - sum(iDebitAMT_F) 
					when i.cVouKind = '209' then sum(iCreditAMT_F) - sum(iDebitAMT_F) 
					when i.cVouKind = '080' then sum(iCreditAMT_F) - sum(iDebitAMT_F) 
					when i.cVouKind = '097' then sum(iCreditAMT_F) - sum(iDebitAMT_F) 
				   end as iCreditAMT_F,
				   case when i.cVouKind = '087' then sum(iCreditAMT) - sum(iDebitAMT) 
				    when i.cVouKind = '082' then 0
					when i.cVouKind = '086' then 0
					when i.cVouKind = '081' then sum(iCreditAMT) - sum(iDebitAMT) 
					when i.cVouKind = '085' then 0
					when i.cVouKind = '099' then 0
					when i.cVouKind = '098' then sum(iCreditAMT) - sum(iDebitAMT) 
					when i.cVouKind = '209' then sum(iCreditAMT) - sum(iDebitAMT) 
					when i.cVouKind = '080' then sum(iCreditAMT) - sum(iDebitAMT) 
					when i.cVouKind = '097' then sum(iCreditAMT) - sum(iDebitAMT) 
				   end as iCreditAMT,
				   case when i.cVouKind = '087' then '收款单' 
				    when i.cVouKind = '082' then '付款单'
					when i.cVouKind = '086' then '应收单'
					when i.cVouKind = '081' then '应付单'
					when i.cVouKind = '085' then '销售发票'
					when i.cVouKind = '099' then '销售费用发票'
					when i.cVouKind = '098' then '委外发票'
					when i.cVouKind = '209' then '工序委外发票'
					when i.cVouKind = '080' then '采购发票'
					when i.cVouKind = '097' then '采购费用发票'
				   end as cRemark,
				   i.cFlag,
				   0 iCheckDir,
				   max(cTimeStamp) cTimeStamp,
				   {dDate} dTallykDate,
				   1 iInventoryFlag
			from AP_DetailAccount i
			where (i.iYear < {iYear} or i.iYear = {iYear} and i.iPeriod <= {iMonth})
			and isnull(i.iInventoryFlag, 0) = 0
			group by i.cVouKind,
				     i.cVouGUID,
					 i.cVouCode,
					 i.dVouDate,
					 i.cVouTypeGUID,
					 i.cDeptGUID,
					 i.cEmpGUID,
					 i.cSupGUID,
					 i.cMatGUID,
					 i.cVocLGUID,
					 i.iRedFlag,
					 i.iPrePay,
					 i.cCurGUID,
					 i.iExchRate,
					 i.cFlag
		]]></i>
		<i id="updateAccount" desc="更新总账"><![CDATA[
			insert into AP_Account (cSupGUID,cDeptGUID,cEmpGUID,cMatGUID,cItemClassGUID,cItemGUID,cCurGUID,iPrePay,cFlag,iYear,iPeriod,iInitAMT_F,iInitAMT,iInventoryFlag)
			SELECT i.cSupGUID
				,i.cDeptGUID
				,i.cEmpGUID
				,i.cMatGUID
				,i.cItemClassGUID
				,i.cItemGUID
				,i.cCurGUID
				,i.iPrePay
				,i.cFlag
				,i.iYear
				,i.iPeriod
				,CASE 
					WHEN i.cFlag = 'AR'
						THEN sum(i.iDebitAMT_F) - sum(i.iCreditAMT_F)
					ELSE sum(i.iCreditAMT_F) - sum(i.iDebitAMT_F)
					END iInitAMT_F
				,CASE 
					WHEN i.cFlag = 'AR'
						THEN sum(i.iDebitAMT) - sum(i.iCreditAMT)
					ELSE sum(i.iCreditAMT) - sum(i.iDebitAMT)
					END iInitAMT
				,1 iInventoryFlag
			FROM AP_DetailAccount i
			WHERE isnull(i.iInventoryFlag, 0) = 1
			GROUP BY i.cSupGUID
				,i.cDeptGUID
				,i.cEmpGUID
				,i.cMatGUID
				,i.cItemClassGUID
				,i.cItemGUID
				,i.cCurGUID
				,i.iPrePay
				,i.iYear
				,i.iPeriod
				,i.cFlag
		]]></i>
		<i id="updatePayment" desc="更新收（付）款单"><![CDATA[
			update AP_Payment set dVouDate = {dDate}, iInitFlag = 0, iInventoryFlag = 1 where dVouDate <= {ddate} and iAuditStatus = 'checked' and iPayAMT_F + isnull(ignoreAMT_F, 0) != iCheckAMT_F
		]]></i>
		<i id="updateAPVoucher" desc="更新其他应收（付）款单"><![CDATA[
			update AP_APVoucher set dVouDate = {dDate}, iInitFlag = 0, iInventoryFlag = 1 where dVouDate <= {ddate} and iAuditStatus = 'checked' and iPayableAMT_F != iCheckAMT_F
		]]></i>
		<i id="updateSAinvoice" desc="更新销售发票"><![CDATA[
			update i set i.ddate = {dDate}, i.iInitFlag = 1, i.cSysType = 'AR', i.iInventoryFlag = 1 from sa_invoice i where i.ddate <= {ddate} and i.iInitFlag in ('0','1') and i.iAuditStatus = 'checked' and exists (select 1 from SA_InvoiceLine l where isnull(l.itotal_f,0) != isnull(l.iReceiveTotal_F,0) and isnull(l.itotal_f,0) != 0 and l.cheadguid = i.cguid)
		]]></i>
		<i id="updateSACainvoice" desc="更新销售费用发票"><![CDATA[
			update i set i.ddate = {dDate}, i.iInventoryFlag = 1 from SA_CarriageInvoice i where i.ddate <= {ddate} and i.iAuditStatus = 'checked' and exists (select 1 from SA_CarriageInvoiceline l where isnull(l.itotal_f,0) != isnull(l.iPayTotal_F,0) and isnull(l.itotal_f,0) != 0 and l.cheadguid = i.cguid)
		]]></i>
		<i id="updatePUinvoice" desc="更新采购发票"><![CDATA[
			update i set i.dInvDate = {dDate}, i.iInitFlag = 2, i.cSysType = 'AP', i.iInventoryFlag = 1 from PU_Invoice i where i.dInvDate <= {ddate} and (i.iInitFlag = 0 or i.iInitFlag = 2 and i.cSysType = 'AP') and i.iAuditStatus = 'checked' and exists (select 1 from PU_InvoiceLine l where isnull(l.itotal_f,0) != isnull(l.iPayTotal_F,0) and isnull(l.itotal_f,0) != 0 and l.cheadguid = i.cguid)
		]]></i>
		<i id="updatePUCainvoice" desc="更新采购费用发票"><![CDATA[
			update i set i.dDate = {dDate}, i.iInventoryFlag = 1 from PU_CarriageInvoice i where i.dDate <= {ddate} and i.iAuditStatus = 'checked' and exists (select 1 from PU_CarriageInvoiceLine l where isnull(l.itotal_f,0) != isnull(l.iPayTotal_F,0) and isnull(l.itotal_f,0) != 0 and l.cheadguid = i.cguid)
		]]></i>
		<i id="updateOMinvoice" desc="更新委外发票"><![CDATA[
			update i set i.iInventoryFlag = 1 from OM_Invoice i where i.dInvDate <= {ddate} and i.iInitFlag = 0 and i.iAuditStatus = 'checked' and exists (select 1 from OM_InvoiceLine l where isnull(l.itotal_f,0) != isnull(l.iPayTotal_F,0) and isnull(l.itotal_f,0) != 0 and l.cheadguid = i.cguid)
		]]></i>
	</sql>
</sqls>