<?xml version="1.0" encoding="UTF-8"?>
<sqls>
	<sql group="account_trans">
		<i id="loadPrefer" desc="加载模块参数设置"><![CDATA[
		      select '通用' cDomainName, '通用' cModuleName, cfg.*, fm.cFieldCode, fm.iOrder from CO_TDParamCfg cfg left join CO_TDFM fm on cfg.parentfdm=fm.cModule 
			  where cfg.parentfdm='CO' and cfg.isshow='1'
			  union all
			  select fm.cFieldName cDomainName, st.cSysName cModuleName, cfg.*, fm.cFieldCode, fm.iOrder from CO_TDParamCfg cfg 
			  join CO_TDFM fm on cfg.parentfdm=fm.cModule
			  join CO_SystemType st on st.cAbbreviate=cfg.parentfdm
			  join CO_SysInit si on si.cSubSysCode=st.cAbbreviate
			  where cfg.parentfdm in (select cModule from CO_TDFM where $in(cFieldCode, cModules))
			  and cfg.isshow='1' 
			  order by fm.cFieldCode, fm.iOrder, cfg.CsOrd
		]]></i>
		<i id="loadPeriod"><![CDATA[
			select distinct iYear code, iYear name from GL_Period
			where iYear>=
			(
			select min(iInitYear) from CO_SysInit 
			where cSubSysCode in (select cModule from CO_TDFM where $in(cFieldCode, cModules))
			)
			and iYear<=
			(
			select max(iYear) from CO_SysInit 
			where cSubSysCode in (select cModule from CO_TDFM where $in(cFieldCode, cModules))
			) order by iYear desc
		]]></i>
		<i id="loadDefaultPeriod"><![CDATA[
			select min(iYearMonth)/1000 iYear,	min(iYearMonth)%1000 iMonth
			from(
				select max(iYear*1000+iMonth) iYearMonth, cSubSysCode from CO_SysClosing
				where cSubSysCode in (select cModule from CO_TDFM where $in(cFieldCode, cModules))
				group by cSubSysCode
			)t
		]]></i>
		<i id="getErverNotClose"><![CDATA[
			select cSysName from CO_SystemType cst
			join CO_SysInit csi on csi.cSubSysCode=cst.cAbbreviate
			where cSubSysCode not in (select cSubSysCode from CO_SysClosing)
			and csi.cSubSysCode in (select cModule from CO_TDFM where $in(cFieldCode, cModules) AND cModule != 'QC')
		]]></i>
		<i id="loadCheckGrid"><![CDATA[
			select top 999999 ct.*,st.cSysName cModuleName, CAST(si.iInitYear as varchar)+'.'+cast(si.iInitMonth as varchar) iInitPeriod
			, cast(si.iYear as varchar)+'.'+cast(si.iMonth as varchar) iPeriod from CO_TDFM ct 
			join CO_SystemType st on st.cAbbreviate=ct.cModule
			join CO_SysInit si on si.cSubSysCode=ct.cModule
			where ct.cModule in (select cModule from CO_TDFM where $in(cFieldCode, cModules))
			order by ct.cFieldCode, ct.iOrder
		]]></i>
		<i id="loadValidation"><![CDATA[
			select mod.*,rc.cClassName, '成功' cResult,rc.cDefine1,rc.cDefine2 from CO_RuleCfg rc
			JOIN
			(
			select ct.*,st.cSysName cModuleName, CAST(si.iInitYear as varchar)+'.'+cast(si.iInitMonth as varchar) iInitPeriod
			, cast(si.iYear as varchar)+'.'+cast(si.iMonth as varchar) iPeriod from CO_TDFM ct 
			join CO_SystemType st on st.cAbbreviate=ct.cModule
			join CO_SysInit si on si.cSubSysCode=ct.cModule
			where ct.cModule in (select cModule from CO_TDFM where $in(cFieldCode, cModules))
			) mod on mod.cModule=rc.cModule
			where rc.cType=1 
			order by  mod.cFieldCode, mod.iOrder, rc.iOrder 
		]]></i>
		<i id="checkPeriod"><![CDATA[
			select stuff((select ','+ltrim(CO_SystemType.cSysName) from CO_SysInit 
			join CO_SystemType on CO_SystemType.cAbbreviate=CO_SysInit.cSubSysCode
			where (iYear<{iYear} or (iYear={iYear} and iMonth<={iMonth}))
			and cSubSysCode in (select cModule from CO_TDFM where $in(cFieldCode, cModules) AND cModule != 'QC')
			for xml path('')),1,1,'')
		]]></i>
		<i id="log"><![CDATA[
			insert into CO_TDLog(cGUID, cType, dDate, cModule, cItem, cResult, cDespt, cExpt) values(?,?,?,?,?,?,?,?)
		]]></i>
		<i id="getLastInPeriod"><![CDATA[
			select dEndDate from GL_Period where #equal(iYear, iYear) and #equal(iMonth, iMonth) 
		]]></i>
		<i id="getTransData"><![CDATA[
			select * from CO_TransferData where iStatus=1 
			and cModule in (select cModule from CO_TDFM where $in(cFieldCode, cModules) and (cModule in (select cSubSysCode from CO_SysInit) or cModule='RE')) 
			order by iOrder, iRefOrder,cModule
		]]></i>
		<i id="getCustomerProcess"><![CDATA[
			select * from CO_RuleCfg where cModule=? and cType=? order by iOrder 
		]]></i>
		<i id="getMinInitYearMonth"><![CDATA[
			select min(iInitYear*1000+iInitMonth) from CO_SysInit 
        	where cSubSysCode in (select cModule from CO_TDFM where $in(cFieldCode, cModules))
		]]></i>
		<i id="loadlogmsg"><![CDATA[
			select t.cguid,
				   t.clogguid,
				   case t.type when 1 then '失败' when 2 then '警告' else '' end as type,
				   case t.type when 1 then 'color:red' end as _style,
				   t.cbillcode,
				   t.ddate,
				   t.cbilltype,
				   t.iaccountflag,
				   t.iauditstatus,
				   t.vouchflag,
				   t.overflag,
				   t.iSettleFlag,
				   t.iExeFlag,
				   t.iCheckStatus
			from CO_TDLogMsg t
			where charindex(t.clogguid,'{clogguids:*}') != 0
		]]></i>
	</sql>
</sqls>