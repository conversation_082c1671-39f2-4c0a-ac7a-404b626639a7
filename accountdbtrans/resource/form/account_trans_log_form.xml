<?xml version="1.0" encoding="UTF-8"?>
<Forms>
	<form id="common_account_trans_log" desp="账套数据转移日志" >
		<toolbar>
			<button id="btnFilter" text="过滤" width="50px" img="img/Filter.gif"/>
			<button id="btnDelete" text="删除" width="50px" img="img/Delete.gif"/>
			<button id="btnexportExcelFileAll" text="导出" width="50px" img="img/export.gif"/>
			<button id="btnMsg" text="详细" width="50px" img="img/file.gif"/>
			<button id="btnExit" text="退出" width="50px" onclick=" PT.closeWin();" img="img/Exit.gif"/>
		</toolbar>
		<widgets>
		<layout name="content" type="TableLayout" width="100%" height="100%">
			<basic widget="Hidden" name="account"/>
			<basic name="xprint" label="打印组件" widget="XPrint"></basic>
			<basic widget="XListGrid" name="list" width="100%" height="100%" attr="page:true;pageaction:load;idField:cguid;mselect:true;sort:false">
				<col id="cguid" label="cGUID" ignore="true"/>
				<col id="ddate" label="日期时间" />
				<col id="ctype" label="日志类型" />
				<col id="cmodulename" label="模块"/>
				<col id="citem" label="项目" />
				<col id="cresult" label="结果" />
				<col id="cDespt" label="详细描述"/>
				<col id="cexpt" label="原始报错信息" />
			</basic>
		</layout>
		</widgets>
		<extendPoint addglobe="false">
			<plugin type="com.aisino.platform.query.QuerySchemePlugin" desp="加载日志" onEvent="load" attr="grid:list;hasPage:true">
				<value name="sql"><![CDATA[
					select top ********* tlog.cGUID, convert(varchar(19),dDate,120) dDate, st.cSysName cModuleName, cItem, case cResult when 'n' then '失败' when 'y' then '成功' when 'w' then '警告' else '' end cResult 
					,cDespt, cExpt ,
					case tlog.cType when '1' then '操作日志' 
							   when '2' then '业务规则日志'
							   when '3' then '原账套数据处理日志'
							   when '4' then '历史账套处理日志'
							   when '5' then '其他类日志'
							   else '' end cType
					from CO_TDLog tlog
					left join CO_SystemType st on tlog.cModule=st.cAbbreviate
					where ('*' = {beginDate:'*'} and '*' = {endDate:'*'} or convert(varchar(10),dDate,120) between {beginDate:convert(varchar(10),dDate,120)} and {endDate:convert(varchar(10),dDate,120)})
					and $equal(tlog.cModule, cModule)
					and $equal(tlog.cType, cType)
					and $like(tlog.cItem, cItem)
					and $equal(tlog.cResult, cResult)
					and $like(tlog.cDespt, cDespt) 
					order by dDate desc
				]]></value>
			</plugin>
			<plugin type="com.aisino.account.db.trans.plugin.DeleteLogPlugin" desp="删除日志" onEvent="delete" />
		</extendPoint>
		<bind element="btnMsg" event="click" >
		<![CDATA[
			var clogguids=w('list').getSelecteds('cguid');
			if(clogguids==null||clogguids==''||clogguids.length==0){
				alert('请选择列表数据');
				return false;
			}
		    PT.showModalForm('common_account_trans_logmsg','clogguids='+clogguids);
		]]>
		</bind>	
		<bind element="btnFilter" event="click"><![CDATA[
			PT.showModal('common_account_trans_log_filter');
			var	param = PT.getBusValue('common_account_trans_log_filter');
			if(param == null) return;
			PT.ns('load','',param);
		]]></bind>
		<bind element="btnDelete" event="click"><![CDATA[
			var selField = w('list').getSelecteds('cguid');
			if (selField.length==0){
				alert('请选择要删除的列表数据');
				return;
			}
			if (confirm('是否删除所选日志？')){
				PT.ns('delete','',{ids:selField});
				PT.f().refresh();
			}
		]]></bind>
		
		<bind element="this" event="refresh"><![CDATA[
			var param = PT.getBusValue('common_account_trans_log_filter');
			PT.ns('load','',param);
		]]></bind>
		
		<bind element="btnexportExcelFileAll" event="click"><![CDATA[
			var	param = PT.getBusValue('common_account_trans_log_filter');
            w('xprint').exportFile('list',param,null,'load');
		]]></bind>
	</form>
	
	<form id="common_account_trans_log_filter" extend="ProjectFilter_Template" desp="账套数据转移日志过滤">
		<widgets>
		<layout name="qc" type="TableLayout">  
			<basic widget="DateChooser" name="beginDate" layout="qc" label="起始日期" />
			<basic widget="DateChooser" name="endDate" layout="qc" label="截止日期" />
			<basic widget="Combox" name="cModule" label="模块" layout="qc" topSpace="15px">
				<valueFetcher type="com.aisino.platform.view.basicWidget.fetcher.SqlValueGetter">
					<value name="sql">select cSysName name, cAbbreviate code from CO_SystemType where iStatus=1 order by cGUID</value>
				</valueFetcher>
			</basic>
			<basic name="cType" label="日志类型" layout="qc" widget="Combox" topSpace="15px">
				<valueFetcher type="com.aisino.platform.view.basicWidget.fetcher.StaticFetcher" >
					<value name="1">操作日志</value>
					<value name="2">业务规则日志</value>
					<value name="3">原账套数据处理日志</value>
					<value name="4">历史账套处理日志</value>
					<value name="5">其他类日志</value>
				</valueFetcher>
			</basic>
			<basic name="cItem" label="项目" layout="qc" topSpace="15px"/>
			<basic name="cResult" label="处理结果" layout="qc" topSpace="15px" widget="Combox">
				<valueFetcher type="com.aisino.platform.view.basicWidget.fetcher.StaticFetcher" >
					<value name="y">成功</value>
					<value name="n">失败</value>
					<value name="w">警告</value>
				</valueFetcher>
			</basic>
			<basic name="cDespt" label="详细描述" layout="qc" topSpace="15px"/>
		</layout>
		</widgets>
	</form>
	
	<form id="common_account_trans_logmsg" desp="校验详细信息列表" >
		<toolbar template="" attr="">
			<button id="btnPrint" text="打印" width="50px" img="img/Print.gif" />
			<button id="btnExport" text="导出" width="50px" img="img/export.gif" />		
			<button id="btnExit" text="退出" width="50px" img="img/Exit.gif" onclick="PT.closeModal();"/>				
		</toolbar>
		<widgets>
			<basic name="myPrint" label="" widget="XPrint"></basic>
			<basic name="clogguids" widget="Hidden" />
			<basic name="list" widget="XListGrid" width="942px">
				<col id="cguid" label="cGUID" ignore="true"/>
				<col id="clogguid" label="clogguid" ignore="true"/>
				<col id="type" label="日志类型" sort="str"/>
				<col id="cbillcode" label="单据编号" width="120px" sort="str"/>
				<col id="ddate" label="单据日期" sort="str"/>
				<col id="cbilltype" label="单据类型" sort="str"/>
				<col id="iaccountflag" label="记账状态" sort="str"/>
				<col id="iauditstatus" label="审核状态" sort="str"/>
				<col id="vouchflag" label="凭证状态" sort="str"/>
				<col id="overflag" label="跨结转状态" sort="str"/>
				<col id="iSettleFlag" label="结算状态" sort="str"/>
				<col id="iExeFlag" label="单据执行状态" sort="str"/>
				<col id="iCheckStatus" label="收付款核销状态" sort="str"/>
			</basic>
		</widgets>
		<extendPoint>
			<plugin type="com.aisino.platform.query.CommonGridQueryPlugin" onEvent="updateGrid" attr="grid:list;hasPage:false;">
			    <value name="sqlid">account_trans.loadlogmsg</value>
		    </plugin>
		</extendPoint>
		<bind element="this"  event="onCreate"><![CDATA[
			PT.as('updateGrid','clogguids');
		]]></bind>
		<bind element="btnPrint" event="click"><![CDATA[
       		w('myPrint').print('list',null,'clogguids','updateGrid');
		]]></bind>
		<bind element="btnExport" event="click"><![CDATA[
       		w('myPrint').exportFile('list',null,'clogguids','updateGrid');
		]]></bind>
	</form>
</Forms>

