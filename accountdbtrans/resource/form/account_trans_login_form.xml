<?xml version="1.0" encoding="UTF-8"?>
<Forms>
	<form id="common_account_trans_login" extend="logintemplate" desp="账套数据转移">
		<value name="url">pt/canvas?formid=common_account_trans</value>
		<widgets>
			<layout name="bkground" type="TableLayout" backgroundImg="img/a6loginbg.jpg" />
		    <layout name="content" type="TableLayout" attr="groupSize:2;cellspacing:10" itemextway="override">														
				<basic name="user" label="用户"  width="160px"  colspan="2"/>
				<basic name="pwd" label="密码" widget="Password"  width="160px"  colspan="2" />
				<basic name="account" label="账套" widget="Combox" width="160px"  attr="addnull:false;" colspan="2">
					 <valueFetcher id="dataCenter"></valueFetcher>
				</basic>
				<basic name="labelRemark" leftSpace="85px" widget="Label" default="(该登录账套为数据转移的源账套)" colspan="2" attr="fontSize:11px;color:red;" width="200px"/>
				<basic name="ok" leftSpace="90px" height="27px" width="93px" widget="OButton" attr="buttonClass:picbutton;" backgroundImg="img/loginbtn.gif"/>
				<basic name="AuthService" widget="Hidden" layout="content" default="ACS.UserPwdAuth"></basic>
				<basic name="date" label="日期"  width="140px" widget="DateChooser" default="today"  ishide="true"/>
			</layout>
		</widgets>
		<extendPoint>
			<plugin type="com.aisino.aos.a6mainui.plugin.A6LoginCheckPlugin" onEvent="login"/>
			<plugin type="com.aisino.aos.a6mainui.plugin.GetDataCenterPlugin" onEvent="getdatacenter"/>
		</extendPoint>
		
		<bind element="this" event="onCreate" extendway="after"><![CDATA[
			PT.css('user.label','letter-spacing','15px');
			PT.css('pwd.label','letter-spacing','15px'); 
			PT.css('account.label','letter-spacing','15px');
			if(wg('user'))PT.as('getdatacenter','user,pwd');
		]]></bind>
		<bind element="user" event="onblur"><![CDATA[
			PT.as('getdatacenter','user,pwd');
		]]></bind>
		<bind element="pwd" event="onblur"><![CDATA[
			PT.as('getdatacenter','user,pwd');
		]]></bind>
		<bind element="ok" event="click" extendway="override"><![CDATA[	       		       
		       var user=wg('user');
		       if(!user){
		          PT.alert('用户名不能为空！');
		          PT.foucs('user');
		          return;
		       }
		       if(!wg('account')){
			       PT.alert('账套不能为空！');
				   return;
			   }
		       setCookie('_u',user);
		       PT.s('login',null);
		]]></bind>
	</form>
</Forms>
