<?xml version="1.0" encoding="UTF-8"?>
<Forms>
	<form id="common_account_trans" extend="Widzard" implement="pt_list_colwidths_interface" desp="账套数据转移">
		<widgets>
			<basic name="title" label="账套数据转移"></basic>
			<basic name="path" default="welcome,step1,step2,step3,step4,step5"></basic>
			<basic name="tip" label="" default="sssss"></basic>
			<layout name="panel" type="BandLayout" backgroundColor="white" width="100%" height="100%">
				<layout type="BandLayout" attr="groupSize:1" name="welcome" >		
					<basic widget="Label" label="欢迎使用本向导操作，该向导将指导您将历史数据进行结转。" topSpace="100px" leftSpace="50px" width="100%" inline="single" attr="align:left"/>
					<basic widget="Label" label="数据转移前请备份账套数据。" leftSpace="50px" width="100%" inline="single" attr="align:left"/>
					<basic widget="Label" label="业务数据清除工具：系统运行多年，企业数据越来越多，数据库文件也越来越大，可以使用本工具，将账套中的业务数据删除从而减小数据库。" topSpace="50px" leftSpace="50px" width="100%" inline="single" attr="align:left"/>
					<basic widget="Label" label="使用本工具时，可以选择任意期间作为截止期间，系统将删除所选模块截止期间及之前期间的业务数据，同时生成下一期间的总账、出纳、工资、固定资产、报表、库存、存货、应收、应付模块期初数据。" leftSpace="50px" width="100%" inline="single" attr="align:left"/>
					<basic widget="Label" label="注：采购、销售无法生成下一期间的期初数据，使用工具前请告知客户。" leftSpace="50px" width="100%" inline="single" attr="align:left"/>
					<layout type="BandLayout" desp="" name="checkEnviroment" attr="groupSize:1" topSpace="50px" leftSpace="50px" width="650px">
						 <basic name="processEnvBar" widget="ProgressBar" height="10px" width="500" labelWidth="500" label="正在检测工具环境,请稍候..."  />
					</layout>
					<basic widget="Hidden" label="当前账套id" name="account"></basic>
					<basic widget="Hidden" label="当前账套name" name="accountName"></basic>
				</layout>		    
				<layout type="BandLayout" attr="groupSize:1" width="100%" height="100%" name="step1">		
					 <basic name="" widget="Label" width="100%" label="第一步:请选择您本次需要历史数据转移的业务领域"/>
					 <layout type="BandLayout" desp="对账提示" name="coner" attr="groupSize:1" topSpace="10px" width="100%">
						 <basic name=""  widget="HSeperator" width="100%"/>
					 </layout>
					 <layout type="BandLayout" width="100%" height="100%" leftSapce="150px">
						 <basic name="list" widget="XListGrid" width="100%" height="250px" attr="mselect:true;seq:false;page:false;sort:false;">
						 	<col label="领域编码" id="cFieldCode" hidden="true" sort="false"/>
						 	<col label="领域" id="cFieldName" width="80px" sort="false"/>
						 	<col label="包含模块" id="cModules" width="300px" sort="false"/>
						 </basic>
						 <basic name="oldSelField" widget="Hidden"/>
					 </layout>
				 </layout>
				<layout type="BandLayout" attr="groupSize:1" width="100%" height="100%" name="step2">		
					 <basic name="" widget="Label" width="100%" label="第二步:请设置业务处理相关参数"/>
					 <basic name="preferCode" widget="Hidden"/>
					 <layout type="BandLayout" desp="分隔线" name="tishi21" attr="groupSize:1" topSpace="10px" width="100%">
						 <basic name=""  widget="HSeperator" width="100%"/>
					 </layout>
					 <layout type="BandLayout" width="650px" leftSapce="150px">
					 <layout type="BandLayout" desp="参数" name="layout.config" attr="groupSize:1" height="100%" width="100%">
					     <basic name="TD_CheckState" linkToState="1,2" label="对业务状态进行检测，通过后才结转" attr="truevalue:y;falsevalue:n" widget="TrueFalseBox" default="y" disabled="true"/>
					 </layout>
					 </layout>
				 </layout>	
				 <layout type="BandLayout" attr="groupSize:1" width="100%" height="100%" name="step3">		
					 <basic name="" widget="Label" width="100%" label="第三步:请选择历史数据转移的截止期间"/>
					 <layout type="BandLayout" desp="分隔线" name="tishi21" attr="groupSize:1" topSpace="10px" width="100%">
						 <basic name=""  widget="HSeperator" width="100%"/>
					 </layout>
					 <layout type="TableLayout" desp="历史数据账套" attr="groupSize:2" width="650px" topSpace="50px" leftSapce="150px">
					 	 <layout type="BandLayout">
					 	 	 <basic name="oldSelFieldFlag" widget="Hidden"/>
							 <basic name="iYear" label="截止期间    年" width="40px" require="true" widget="Combox"/>
							 <basic name="iMonth" label="月" width="40px" labelWidth="20px" require="true" widget="Combox">
							 	<valueFetcher type="com.aisino.platform.view.basicWidget.fetcher.StaticFetcher" >
							 		<value name="1">1</value>
							 		<value name="2">2</value>
							 		<value name="3">3</value>
							 		<value name="4">4</value>
							 		<value name="5">5</value>
							 		<value name="6">6</value>
							 		<value name="7">7</value>
							 		<value name="8">8</value>
							 		<value name="9">9</value>
							 		<value name="10">10</value>
							 		<value name="11">11</value>
							 		<value name="12">12</value>
							 	</valueFetcher>
							 </basic>
						 </layout>
						 <basic name="cDestDbGUID" widget="Hidden" label="历史数据账套号" labelWidth="100px" width="150px" colspan="2" require="true" topSpace="8px" validator="dataType:Custom;regexp:^\d{1,5}$;msg:账套号只能是5位以内的数字组合"/>
						 <basic name="cDestDbName" widget="Hidden" label="历史数据账套名称" labelWidth="110px" width="150px" colspan="2" require="true" topSpace="8px"/>
						 <basic name="dbname" widget="Hidden"/>
					 </layout>
				 </layout>	
				 <layout type="BandLayout" attr="groupSize:1" name="step4">		
					 <basic name="" widget="Label" width="100%" label="第四步:业务规则检测"/>
					 <basic name="oldSelFieldFlagInCheck" widget="Hidden"/>
					 <layout type="BandLayout" desp="" name="checkprocess" attr="groupSize:1" topSpace="10px" width="650px">
						 <basic name="validateBar" widget="ProgressBar" height="10px" width="500" labelWidth="500" label="正在处理中,请稍候..."  />
					 </layout>
					 <layout type="BandLayout" desp="" name="coner" attr="groupSize:1" topSpace="10px" width="650px">
						 <basic name=""  widget="HSeperator" width="650px"/>
					 </layout>
					 <layout type="BandLayout" width="650px" leftSapce="150px">
						 <basic name="list1" widget="XListGrid" width="100%" height="150px" attr="mselect:false;seq:false;page:false;sort:false;">
						 	<col label="领域" id="cFieldName" width="50px" sort="false"/>
						 	<col label="模块" id="cModuleName" width="50px" sort="false"/>
						 	<col label="模块编码" id="cModule" width="50px" sort="false" hidden="true"/>
						 	<col label="启用期间" id="iInitPeriod" width="100px" sort="false"/>
						 	<col label="当前期间" id="iPeriod" width="100px" sort="false"/>
						 	<col label="检测结果" id="cResult" width="100px" sort="false"/>
						 </basic>
					 </layout>
					 <layout type="BandLayout" width="650px" leftSapce="150px">
					 	<basic widget="Button" label="查看日志" name="validateLog"/>
					 	<basic widget="Hidden" label="校验状态" name="validateStatus"/>
					 </layout>
				 </layout>
				 <layout type="BandLayout" attr="groupSize:1" name="step5">		
					 <basic name="" widget="Label" width="100%" label="第五步:数据处理"/>
					 <layout type="BandLayout" desp="分隔线" name="tishi21" attr="groupSize:1" topSpace="10px" width="650px">
						 <basic name=""  widget="HSeperator" width="650px"/>
					 </layout>
					 <layout type="BandLayout" desp="" name="checkprocess" attr="groupSize:1" topSpace="10px" width="650px">
						 <basic name="processBar" widget="ProgressBar" height="10px" width="500" labelWidth="500" label="数据正在处理中,请稍候..."  />
					 </layout>
					 <layout type="BandLayout" width="650px" leftSapce="150px">
					 	<basic widget="Button" label="查看日志" name="processLog"/>
					 	<basic widget="Hidden" label="正在处理中" name="processStatus" default="0"/>
					 </layout>
				 </layout>		
			</layout>
		</widgets>
		<extendPoint>
			<plugin type="com.aisino.account.db.trans.plugin.LoadAccountInfoPlugin" desp="加载账套信息" onEvent="loadAccountInfo"/>
			<plugin type="com.aisino.platform.query.CommonGridQueryPlugin" desp="加载领域列表" onEvent="loadField" attr="grid:list">
				<value name="sql">
					select distinct cFieldCode cGUID, cFieldCode,cFieldName, stuff((select distinct ','+ltrim(cSysName) from CO_TDFM join CO_SystemType on cAbbreviate=cModule where cFieldCode=b.cFieldCode and (cModule = 'RE' or exists (select 1 from co_sysinit where cSubSysCode = cModule))
	  				for xml path('')),1,1,'') cModules from CO_TDFM b
	  				where b.cModule = 'RE' or exists (select 1 from co_sysinit where cSubSysCode = b.cModule)
	  			</value>
			</plugin>
			<plugin type="com.aisino.account.db.trans.plugin.LoadPreferPlugin" desp="加载校验参数配置" onEvent="loadPrefer"/>
			<plugin type="com.aisino.account.db.trans.plugin.SavePreferPlugin" desp="保存校验参数配置" onEvent="savePrefer"/>
			<plugin type="com.aisino.account.db.trans.plugin.LoadTransPeriodPlugin" desp="加载可选截止期间" onEvent="loadPeriod,checkPeriod"/>
			<plugin type="com.aisino.platform.query.CommonGridQueryPlugin" desp="加载业务校验" onEvent="loadValidateGrid" attr="grid:list1">
				<value name="sqlid">account_trans.loadCheckGrid</value>
			</plugin>
			<plugin type="com.aisino.account.db.trans.plugin.ValidatePlugin" desp="处理业务校验流程" onEvent="validate">
				<value name="grid">list1</value>
			</plugin>
			<plugin type="com.aisino.account.db.trans.plugin.TransDbDataPlugin" onEvent="backupAccount,transAccount"/>
			<plugin type="com.aisino.platform.view.login.LogoutPlugin" onEvent="logout"/>
			<plugin type="com.aisino.account.db.trans.plugin.CheckEnviromentPlugin" onEvent="checkEnviroment"/>
		</extendPoint>
		<!-- 上一步 before-->
		<bind element="last" event="click" extendway="before"><![CDATA[
		
		]]></bind>
	
		<!-- 下一步 before -->
		<bind element="next" event="click" extendway="before"><![CDATA[
			var step = PT.f().step();
			if (step==1){
	    		if (!PT.f().step1()) return;
	    	}else if (step==2){
	    		if (!PT.f().step2()) return;
	    	}else if (step==3){
	    		if (!PT.f().step3()) return;
	    	}else if (step==4){
	    		if (!PT.f().step4()) return;
	    	}else if (step==5){
	    		if (!PT.f().step5()) return;
	    	}else if(step==6){
	    		return true;
	    	}
		]]>
		</bind>
		<!-- 下一步 after -->
		<bind element="next" event="click" extendway="after"><![CDATA[
			var step = PT.f().step();
			if (step==1){
				
	    	}else if (step==2){
	    		if (!PT.f().beforeStep2()) return;
	    	}else if (step==3){
	    		if (!PT.f().beforeStep3()) return;
	    	}else if (step==4){
	    		if (!PT.f().beforeStep4()) return;
	    	}else if (step==5){
	    		if (!PT.f().beforeStep5()) return;
	    	}else if (step==6){
	    		if (!PT.f().beforeStep6()) return;
	    	}
		]]></bind>
			
		<bind element="this" event="step1"><![CDATA[
			/*校验是否选择业务领域*/
			var selField = w('list').getSelecteds('cfieldcode');
			if (selField==null || selField.length==0){
				alert('请至少选择一个领域操作');
				return false;
			}else{
				if(selField.length==1&&selField[0]=='002'&&w('list').size()!=1){
					alert('供应链不允许单独结转，请勾选财务后继续');
					return false;
				}
				if (selField.length!=w('list').size()){
					if (!confirm('部分领域未选择，数据转移后可能会影响到数据完整。确认继续吗？')){
						return false;
					}
				}
			}
			return true;
		]]></bind>
		
		<bind element="list" event="onSelect" param="row,flag,isAll"><![CDATA[
			var list = w('list');
			if(row.cfieldcode=='002'&&list.size()!=1){
				if(flag&&!isAll){
					list.selectByField(['001'],'cfieldcode');
				}
			}
		]]></bind>
		
		<bind element="this" event="beforeStep2"><![CDATA[
			/*加载相关业务处理参数
			var selField = w('list').getSelecteds('cfieldcode');
			if (selField.toString()!=wg('oldSelField')){
				ws('oldSelField',selField.toString());
				PT.clearSimpleLayout('layout.config');
				PT.ns('loadPrefer','account',{cModules:selField,cFieldName:w('list').getSelecteds('cFieldName')});
			}
			暂时屏蔽参数功能*/
			return true;
		]]></bind>
		<bind element="this" event="step2"><![CDATA[
			/*保存相关业务处理参数
			PT.ns('savePrefer',null);
			暂时屏蔽参数功能*/
			return true;
		]]></bind>
		<bind element="this" event="beforeStep3"><![CDATA[
			/*加载期间和历史源数据帐套信息*/
			var selField = w('list').getSelecteds('cFieldCode');
			if (selField.toString()!=wg('oldSelFieldFlag')){
				PT.ns('loadPeriod', '', {cModules:selField});
				ws('oldSelFieldFlag',selField.toString());
				PT.f().genAccountName();
			}
			return turn;
		]]></bind>
		<bind element="this" event="step3"><![CDATA[
			/*校验帐套在所选期间内是否结账,校验帐套号和帐套名是否重复*/
			PT.checkAll();
			ws('validateStatus',0);
			var selField = w('list').getSelecteds('cFieldCode');
			try{
				PT.ns('checkPeriod','iYear,iMonth,cDestDbGUID,cDestDbName',{cModules:selField});
			}catch(e){
				return false;
			}
			w('validateBar').start();
			/*校验*/
			var selField = w('list').getSelecteds('cfieldcode');
			PT.ns('loadValidateGrid', '',{cModules:selField});
			PT.setDisabled('validateLog,last,next,cancel',true);
			PT.as('validate','cDestDbGUID,cDestDbName,iYear,iMonth',{cModules:selField},function(result){
				if (result.hasFail){
					alert('存在不通过的校验项，无法继续，详细信息请查看日志');
				}else if (result.hasWarn){
					alert('存在警告的校验项，详细信息请查看日志，自行选择是否忽略');
					ws('validateStatus',1);
				}else{
					ws('validateStatus',1);
				}
				w('validateBar').finish();
				PT.hideLayout('checkprocess');
				PT.setDisabled('validateLog,next,last,cancel',false);
			});
			return true;
		]]></bind>
		<bind element="this" event="beforeStep4"><![CDATA[
			return true;
		]]></bind>
		<bind element="this" event="step4"><![CDATA[
			if (wg('validateStatus')!=1){
				alert('存在不通过的校验项，无法继续，详细信息请查看日志');
				return false;
			}
			return true;
		]]></bind>
		<bind element="this" event="beforeStep5"><![CDATA[
			/*备份帐套并且复制帐套*/
			ws('processStatus', 1);
			PT.setDisabled('processLog,last,finish,cancel',true);
			w('processBar').start();
			w('processBar').previousSibling.previousSibling.innerText = '正在备份账套......';
			PT.as('backupAccount','',{accid:wg('account')},function(result){
				PT.f().afterBackupAccount(result);
			});
			return true;
		]]></bind>
		
		<bind element="this" event="afterBackupAccount" param="result"><![CDATA[
			/*复制帐套*/
			w('processBar').previousSibling.previousSibling.innerText = '正在转移数据......';
			var selField = w('list').getSelecteds('cfieldcode');
			PT.as('transAccount', 'iYear,iMonth', {accid:wg('cDestDbGUID').trim(),accname:wg('cDestDbName').trim(),account:wg('account'),curYear:(new Date()).getYear(),cModules:selField},function(result){
				if (result==null) {w('processBar').previousSibling.previousSibling.innerText = '转移成功，当前账套为数据卸载后的账套，原始账套备份在服务器backup目录下';PT.setDisabled('last',true);PT.setDisabled('finish',false);}
				else {w('processBar').previousSibling.previousSibling.innerText = '转移失败，详细情况请查看日志，请使用备份重新恢复账套进行处理';PT.setDisabled('last',false);}
				w('processBar').finish();
				PT.setDisabled('processLog,cancel',false);
				ws('processStatus', 0);
			})
		]]></bind>
		
		<bind element="this" event="genAccountName"><![CDATA[
			var oldAccountName = wg('accountName');
			ws('cDestDbName', oldAccountName+wg('iYear')+wg('iMonth')+'转移账套');
		]]></bind>
		
		<bind element="iYear" event="change"><![CDATA[
			PT.f().genAccountName();
		]]></bind>
		
		<bind element="iMonth" event="change"><![CDATA[
			PT.f().genAccountName();
		]]></bind>
		
		<bind element="validateLog" event="click"><![CDATA[
			PT.showModalForm('common_account_trans_log', null, '900px', '625px', 'load');
		]]></bind>
		
		<bind element="processLog" event="click"><![CDATA[
			PT.showModalForm('common_account_trans_log', null, '900px', '625px', 'load');
		]]></bind>
		
		<bind element="finish" event="click" extendway="override"><![CDATA[
		    if (wg('processStatus')==1){
		    	alert('数据正在处理中，请在完全处理后再试');
		    	return;
		    }
	   		PT.closeWin();
		]]></bind>
		<bind element="cancel" event="click" extendway="override"><![CDATA[
		    if (wg('processStatus')==1){
		    	alert('数据正在处理中，请在完全处理后再试');
		    	return;
		    }
		    PT.closeWin();
		]]></bind>
		<bind element="this" event="canClose" extendway="override"><![CDATA[
		    if (wg('processStatus')==1){
		    	return '数据正在处理中，现在退出可能会出现数据问题，建议在完全处理后再退出， 确认退出该向导工具吗？';
		    }
	 		return '确认退出该向导工具吗？';
		]]></bind>
		<bind element="this" event="onClose" extendway="before"><![CDATA[
			PT.ns('logout');
		    return true;
		]]></bind>
		<bind element="this" event="onCreate" extendway="after"><![CDATA[
			PT.setDisabled('next',true);
			w('processEnvBar').previousSibling.previousSibling.innerText = '正在检测工具环境,请稍候...';
			PT.as('checkEnviroment', '', {},function(result){
				if (result==null) {w('processEnvBar').hiding();PT.setDisabled('next',false);
				/*加载业务领域信息*/
				PT.ns('loadField');}
				else {w('processEnvBar').previousSibling.previousSibling.innerText = '检测失败，工具环境不完整，请联系管理人员';}
				w('processEnvBar').finish();
				PT.ns('loadAccountInfo');
			});
		]]></bind>
	</form>
</Forms>
