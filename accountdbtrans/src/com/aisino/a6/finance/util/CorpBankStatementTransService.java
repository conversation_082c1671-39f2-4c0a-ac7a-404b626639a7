package com.aisino.a6.finance.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.aisino.account.db.trans.interfaces.IAfterAccountTrans;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.db.Eso;
import com.aisino.platform.view.DataMsgBus;

/**
 * 
 * <p>Description: 日记账转账升级数据处理类</p> 
 * @category 工具_自动转账工具_出纳日记账
 * <AUTHOR>
 * @version $Revision$ 2012-6-20
 * <AUTHOR> modification by huangshun)
 * @since 1.0
 */
public class CorpBankStatementTransService implements IAfterAccountTrans {

	/**
	 * 
	 * @see com.aisino.account.db.trans.interfaces.IAfterAccountTrans#doAfter(com.aisino.platform.db.DbSvr, com.aisino.platform.db.DbSvr, com.aisino.platform.view.DataMsgBus)
	 */
	@SuppressWarnings("unchecked")
	public void doAfter(DbSvr srcDbSvr, DbSvr destDbSvr, DataMsgBus bus) {
		List<Map> initList = srcDbSvr.queryIdForList("finance_corpstatement_trans.calNewInit", bus);
		process(srcDbSvr, destDbSvr, bus, "CA_CorpStatement", initList);
		initList = srcDbSvr.queryIdForList("finance_bankstatement_trans.calNewInit", bus);
		process(srcDbSvr, destDbSvr, bus, "CA_BankStatement", initList);
	}
	
	/**
	 * 
	 * <p>Discription:[方法功能中文描述]</p> 
	 *@param srcDbSvr 源数据库连接
	 *@param destDbSvr 目标数据库连接
	 *@param bus 
	 *@param table 操作数据库表
	 *@param initList 初始数据
	 */
	@SuppressWarnings("unchecked")
	private void process(DbSvr srcDbSvr, DbSvr destDbSvr, DataMsgBus bus, String table, List<Map> initList){
		Map nextPeriod = getNextPeriod(bus);
		List<Eso> esos = new ArrayList<Eso>();
		if (CollectionUtils.isNotEmpty(initList)){
			for (Map initMap: initList){
				esos.add(new Eso("update "+table+" set iAmount=?, iAmount_f=?, dDate='"+nextPeriod.get("iYear")+"-"+nextPeriod.get("iMonth")+"-01' where cBankAcctGUID=? and iInitFlag=0", new Object[]{initMap.get("iAmount"), initMap.get("iAmount_f"), initMap.get("cBankAcctGUID")}));
			}
		}
		esos.add(new Eso("delete from "+table+" where (iYear<? or (iYear=? and iPeriod<=?)) and iBankContrast is not null and convert(varchar(10),dContrastDate,120)<=?"
				,new Object[]{bus.getString("iYear"),bus.getString("iYear"),bus.getString("iMonth"),bus.getString("dDate")}));
		esos.add(new Eso("update "+table+" set iInitFlag=1 where (iYear<? or (iYear=? and iPeriod<=?)) and iBankContrast is not null and convert(varchar(10),dContrastDate,120)>?"
				,new Object[]{bus.getString("iYear"),bus.getString("iYear"),bus.getString("iMonth"),bus.getString("dDate")}));
		esos.add(new Eso("update "+table+" set iInitFlag=1 where (iYear<? or (iYear=? and iPeriod<=?)) and iBankContrast is null and iInitFlag<>0 "
				,new Object[]{bus.getString("iYear"),bus.getString("iYear"),bus.getString("iMonth")}));
		
		srcDbSvr.executeAllEso(esos);
		
		/*List<Eso> destEso = new ArrayList<Eso>();
		destEso.add(new Eso("update "+table+" set iBankContrast=null, dContrastDate=null where (iYear<? or (iYear=? and iPeriod<=?)) and iBankContrast is not null and convert(varchar(10),dContrastDate,120)>?"
				,new Object[]{bus.getString("iYear"),bus.getString("iYear"),bus.getString("iMonth"),bus.getString("dDate")}));
		destEso.add(new Eso("delete from "+table+" where (iYear>? or (iYear=? and iPeriod>?))"
				,new Object[]{bus.getString("iYear"),bus.getString("iYear"),bus.getString("iMonth")}));
		destDbSvr.executeAllEso(destEso);*/
	}

	@SuppressWarnings("unchecked")
	private Map getNextPeriod(DataMsgBus bus) {
		int year = Integer.parseInt(bus.getString("iYear"));
		int month = Integer.parseInt(bus.getString("iMonth"));
		Map result = new HashMap();
		if (month==12){
			result.put("iYear", ++year);
			result.put("iMonth", 1);
		}else{
			result.put("iYear", year);
			result.put("iMonth", ++month);
		}
		return result;
	}
}
