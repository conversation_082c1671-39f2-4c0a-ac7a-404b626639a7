package com.aisino.a6.finance.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.LinkedMap;
import org.apache.commons.lang.ObjectUtils;

import com.aisino.account.db.trans.interfaces.IBeforeAccountTrans;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.db.Eso;
import com.aisino.platform.db.SqlInfo;
import com.aisino.platform.keygen.SimpleKey;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.DataMsgBus;

/**
 * 
 * <p>
 * Description: 年结处理现金流量期初
 * </p>
 * 
 * @category A6 5.1_cashflow
 * <AUTHOR>
 * @version $Revision$ Dec 29, 2011
 * <AUTHOR> modification by l<PERSON>hai<PERSON><PERSON>)
 * @since 1.0
 */
public class CashFlowService implements IBeforeAccountTrans {
	/**
	 * 用第一个数据库连接
	 */
	@SuppressWarnings("unchecked")
	public void doBefore(DbSvr sdb, DbSvr ddb, DataMsgBus bus) {
		// 关联查询现金流量项目和币种,获得全部数据.
		Map<String, Map> init = convertCashFlowMap(sdb.queryIdForList("finance_cashflow_trans.queryCashFlowInit", bus));
		// 获得总账启用期间年
		Map glInit = sdb.queryIdFirstRow("finance_cashflow_trans.queryGLInit", bus);
		String initYear = ObjectUtils.toString(glInit.get("iinityear"));
		// 对比总账启用期间和结转月份
		/**
		 * 如果结转年大于建账年,那么期初使用全部是0的数据,不使用原来的现金流量期初 如果结转年等于建账年,那么期初使用原来的现金流量期初
		 * 如果结转年小于建账年,数据不合理
		 */
		Map nextPeriod = getNextPeriod(bus);
		bus.send("iNextYear", MapUtils.getString(nextPeriod, "iYear", initYear));
		bus.send("iNextMonth", MapUtils.getString(nextPeriod, "iMonth", initYear));
		if (Integer.parseInt(initYear) == Integer.parseInt(MapUtils.getString(nextPeriod, "iYear", initYear))) {
			init.putAll(convertCashFlowMap(sdb.queryIdForList("finance_cashflow_trans.queryCashFlowInitData", bus)));
		}
		Map<String, Map> cashflow = convertCashFlowMap(sdb.queryIdForList("finance_cashflow_trans.queryCashFlow", bus));
		// 计算
		for (Map.Entry<String, Map> m:cashflow.entrySet()) {
			String key = (String) m.getKey();
			Map flow = m.getValue();
			String item = ObjectUtils.toString(flow.get("ccashflowitemguid"));
			String curr = ObjectUtils.toString(flow.get("ccurguid"));
			String itemdir = ObjectUtils.toString(flow.get("dir"));
			BigDecimal amt = new BigDecimal(ObjectUtils.toString(flow.get("ifamount"), "0"));
			amt = amt.multiply(new BigDecimal(itemdir));
			Map current = init.get(item + "-" + curr);
			BigDecimal initAmt = new BigDecimal(MapUtils.getString(current, "ifamount", "0"));
			if (current==null){
				current = new HashMap();
			}
			current.put("ifamount", initAmt.add(amt));
			init.put(item + "-" + curr, current);
			updateParent(key, init, amt);
		}
		// **获得插入sql
		SqlInfo insertSql = sdb.getSQLInfo("finance_cashflow_trans.insertCashFlow");
		List<Eso> insert = new ArrayList<Eso>();
		for (Map.Entry<String, Map> m:init.entrySet()) {
			String key = (String) m.getKey();
			String[] guids = key.split("-");
			if(StringUtil.isNotBlank(guids[0])){
				Map<String, Object> param = new HashMap<String, Object>();
				param.put("cguid", SimpleKey.getInstance().genKey());
				param.put("amt", new BigDecimal(ObjectUtils.toString(m.getValue().get("ifamount"),"0")).toPlainString());
				param.put("item", guids[0]);
				param.put("curr", guids[1]);
				param.put("iyear", MapUtils.getString(nextPeriod, "iYear", initYear));
				param.put("imonth", MapUtils.getString(nextPeriod, "iMonth", initYear));
				insert.add(insertSql.getEso(param));
			}
		}
		sdb.executeId("finance_cashflow_trans.deleteCashFlow");
		sdb.executeAllEso(insert);
	}

	/**
	 * Discription:更新父节点数据
	 * @param key 当前数据key
	 * @param init 现金流数据集
	 */
	@SuppressWarnings("unchecked")
	private void updateParent(String key, Map<String, Map> init, BigDecimal amt) {
		Map current = init.get(key);
		if (current==null){
			return;
		}
		String curr = MapUtils.getString(current, "ccurguid");
		String parentId = MapUtils.getString(current, "cparentid");
		String parentKey = parentId + "-" + curr;
		Map parent = init.get(parentKey);
		BigDecimal parentAMT = new BigDecimal(MapUtils.getString(parent, "ifamount", "0"));
		BigDecimal perentdir;
		//父节点的现金流量方向若没有，则与当前节点的现金流量方向一致
		if (parent!=null && parent.get("dir")==null){
			if ("000000".equals(parent.get("cparentid").toString())){
				perentdir = BigDecimal.ONE;
			}else{
				perentdir = new BigDecimal(MapUtils.getString(current, "dir", "1"));
			}
			parent.put("dir", perentdir);
		}else {
			perentdir = new BigDecimal(MapUtils.getString(parent, "dir", "1"));
		}
		BigDecimal currentdir = new BigDecimal(MapUtils.getString(current, "dir", "1"));
		if (parent!=null){
			parent.put("ifamount", parentAMT.add(amt.multiply(perentdir).multiply(currentdir)));
		}
		if (!"000000".equals(current.get("cparentid"))) {
			updateParent(parentKey, init, amt);
		}
	}

	/**
	 * 
	 * Discription:变换map结构,方便使用
	 * 
	 * @param cashflow
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private Map<String, Map> convertCashFlowMap(List<Map> cashflow) {
		Map<String, Map> map = new LinkedMap();
		if (CollectionUtils.isEmpty(cashflow)){
			return map;
		}
		for (Map flow : cashflow) {
			String item = ObjectUtils.toString(flow.get("ccashflowitemguid"));
			String curr = ObjectUtils.toString(flow.get("ccurguid"));
			map.put(item + "-" + curr, flow);
		}
		return map;
	}
	
	@SuppressWarnings("unchecked")
	private Map getNextPeriod(DataMsgBus bus) {
		int year = Integer.parseInt(bus.getString("iYear"));
		int month = Integer.parseInt(bus.getString("iMonth"));
		Map result = new HashMap();
		if (month==12){
			result.put("iYear", ++year);
			result.put("iMonth", 1);
		}else{
			result.put("iYear", year);
			result.put("iMonth", ++month);
		}
		return result;
	}
}
