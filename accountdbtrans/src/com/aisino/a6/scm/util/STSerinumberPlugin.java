package com.aisino.a6.scm.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.aisino.a6.business.st.util.serialnumber.plugin.SerialNumber;
import com.aisino.account.db.trans.interfaces.IAfterAccountTrans;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;


/***
 * 库存序列号的结转  张汝超  2017年10月31日 15:20:45
 * 
 * */
public class STSerinumberPlugin implements IAfterAccountTrans{


	
	/**
	 * 
	 * <p>Discription:转账数据插入之后数据处理.</p> 
	 *@param srcDbSvr 源数据库连接
	 *@param destDbSvr 目标数据库连接
	 *@param bus
	 */
	@Override
	public void doAfter(DbSvr srcDbSvr, DbSvr destDbSvr, DataMsgBus bus) {
		// TODO Auto-generated method stub
		
		updateSerinumberZK(srcDbSvr,bus);
		
	}
	
	public void updateSerinumberZK(DbSvr db,DataMsgBus bus){
		
	
		/*SerialNumber number=new SerialNumber();
		List<Object[]> SerialNumberBatchUptae=new ArrayList<Object[]>();///序列号表批量更新
		List<Object[]> stkrecordlineBatchUptae=new ArrayList<Object[]>();///出入库表批量更新

		
		//结转仍然在库的数据
		List<Map> SerinumberList=db.queryIdForList("scm_st_trans.getTransSerinumberZK", bus);
		if(CollectionUtil.isEmpty(SerinumberList))
			return;
		String BeforeStklineID=CollectionUtil.getStringFromMap(SerinumberList.get(0), "stklineID");
		String crowsninfonew="";
		String crowsninfo="";
		for(int i=0;i<SerinumberList.size();i++){
		    String cguid=CollectionUtil.getStringFromMap(SerinumberList.get(i), "SerID");
		    String cStkInGUID=CollectionUtil.getStringFromMap(SerinumberList.get(i), "stklineID");
		    String cStkInHeadGUID=CollectionUtil.getStringFromMap(SerinumberList.get(i), "stkheadID");
		    String SerialNumber=CollectionUtil.getStringFromMap(SerinumberList.get(i), "cSerialNumber");
		    //String iOutFlag=CollectionUtil.getStringFromMap(SerinumberList.get(i), "iOutFlag");
		    
		
	        //组织序列号表更新数据
	    	SerialNumberBatchUptae.add(new Object[]{cStkInGUID,cStkInHeadGUID,cguid});
		
				    if(BeforeStklineID.equals(cStkInGUID)){
					    crowsninfonew=crowsninfonew+SerialNumber+',';
					    crowsninfo=number.getRowSn(crowsninfo,SerialNumber);
					    if(i==SerinumberList.size()-1){
					    	crowsninfonew=crowsninfonew.substring(0, crowsninfonew.lastIndexOf(","));
					        stkrecordlineBatchUptae.add(new Object[]{crowsninfonew,crowsninfo,BeforeStklineID});}
				    }else{
				    	//组织出入库子表更新数据
				    	crowsninfonew=crowsninfonew.substring(0, crowsninfonew.lastIndexOf(","));
				    	stkrecordlineBatchUptae.add(new Object[]{crowsninfonew,crowsninfo,BeforeStklineID});
				    	crowsninfonew=SerialNumber+',';
				    	crowsninfo=number.getRowSn(crowsninfo,SerialNumber);
				    	BeforeStklineID=cStkInGUID;
				    }
		    
		  }*/
		//2018-09-03 tss 同步序列号表
		db.update("UPDATE sn SET sn.cstkinguid = sl.cguid,sn.cstkinheadguid = sl.cHeadGUID FROM ST_SerialNumber sn,ST_StkRecordLine sl WHERE sn.cmatguid = sl.cmatguid AND sn.cstoreguid = sl.cstoreguid");	
		//db.batchUpdate("update st_stkrecordline SET crowsninfonew=?,crowsninfo=? WHERE CGUID=?", stkrecordlineBatchUptae);	
	}
		    
	
	
  /*  public void delSerinumber(AbstractForm form, DataMsgBus bus,DbSvr db){
		
		
		//删去截至期间前面的已经出库的对应的入库的序列号表的数据
		db.executeId("scm_st_trans.delSerinumberZK", bus);
		
		//删去截至期间前面的已经出库的序列号表的数据
		db.executeId("scm_st_trans.delSerinumberYC", bus);
		
	}
*/
	
	
	
	
	
	
}
