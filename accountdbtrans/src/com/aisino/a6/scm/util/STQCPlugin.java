package com.aisino.a6.scm.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.aisino.a6.business.st.util.serialnumber.plugin.SerialNumber;
import com.aisino.account.db.trans.interfaces.IAfterAccountTrans;
import com.aisino.account.db.trans.interfaces.IBeforeAccountTrans;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;


/***
 * 结转为期初数据  张汝超  2017年12月5日 10:38:03
 * 
 * */
public class STQCPlugin implements IBeforeAccountTrans{


	
	/**
	 * 
	 * <p>Discription:转账数据插入之后数据处理.</p> 
	 *@param srcDbSvr 源数据库连接
	 *@param destDbSvr 目标数据库连接
	 *@param bus
	 */
	@Override
	public void doBefore(DbSvr srcDbSvr, DbSvr destDbSvr, DataMsgBus bus) {
		// TODO Auto-generated method stub
		//结转出入库表
		//建立结转数据临时表
		srcDbSvr.executeId("scm_st_trans.stkrecord_TEM");
		//将结转数据存入临时表
		srcDbSvr.executeId("scm_st_trans.insertstinitbill_stkrecord_TEM", bus);
		//将数据存入出入库单主子表
		srcDbSvr.executeId("scm_st_trans.insertstinitbill_stkrecord", bus);
		srcDbSvr.executeId("scm_st_trans.insertstinitbill_stkrecordline", bus);
		//结转物品货位表
		srcDbSvr.executeId("scm_st_trans.dealposbill", bus);
	}
	


	
	

	
	
	
	
	
}
