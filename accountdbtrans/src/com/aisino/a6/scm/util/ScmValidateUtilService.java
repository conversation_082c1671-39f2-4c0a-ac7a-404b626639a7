package com.aisino.a6.scm.util;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

import com.aisino.account.db.trans.exception.ValidateException;
import com.aisino.account.db.trans.exception.WarndateException;
import com.aisino.account.db.trans.interfaces.IValidateAccountTrans;
import com.aisino.account.db.trans.util.ListMapUtils;
import com.aisino.platform.core.Guid;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.view.DataMsgBus;

public class ScmValidateUtilService implements IValidateAccountTrans{
	
	public String getEndDate(int year,int month) {
        Calendar cal = Calendar.getInstance();//设置年份
        cal.set(Calendar.YEAR,year);//设置月份
        cal.set(Calendar.MONTH, month-1);//获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);//设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);//格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dEndDate = sdf.format(cal.getTime())+" 23:59:59";
        return dEndDate;
    }
	
	@Override
	public void validate(DbSvr db, DataMsgBus bus, Map validation) throws ValidateException {
		// TODO Auto-generated method stub
		String type = CollectionUtil.getStringFromMap(validation, "cDefine1");//cDefine1校验的类型，1：严格控制，2：提示警告
		String sqlid = CollectionUtil.getStringFromMap(validation, "cDefine2");//cDefine2校验的sqlid
		String dEndDate = getEndDate(bus.getInteger("iYear"), bus.getInteger("iMonth"));
		bus.put("ddate", dEndDate);
		bus.put("IAsys", ListMapUtils.getSysinit(db, "IA"));//存货是否启用
		bus.put("IAonly",ListMapUtils.checkIAonly(db));//存货是否单独启用
		bus.put("GLsys", ListMapUtils.getSysinit(db, "GL"));//总账是否启用
		bus.put("ARsys", ListMapUtils.getSysinit(db, "AR"));//应收是否启用
		bus.put("APsys", ListMapUtils.getSysinit(db, "AP"));//应付是否启用
		List<Map> msgs = db.queryIdForList(sqlid, bus);
		if(msgs!=null&&!msgs.isEmpty()){
			List<Object[]> msgParam = new ArrayList<Object[]>();
			String clogguid = Guid.g();
			for(Map msg : msgs){
				Object[] param = new Object[13];
				param[0] = Guid.g();//cguid
				param[1] = clogguid;//clogguid
				param[2] = type;//type
				param[3] = CollectionUtil.getStringFromMap(msg,"cbillcode");//cbillcode
				param[4] = CollectionUtil.getStringFromMap(msg,"ddate");//ddate
				param[5] = CollectionUtil.getStringFromMap(msg,"cbilltype");//cbilltype
				param[6] = CollectionUtil.getStringFromMap(msg,"iaccountflag");//iaccountflag
				param[7] = CollectionUtil.getStringFromMap(msg,"iauditstatus");//iauditstatus
				param[8] = CollectionUtil.getStringFromMap(msg,"vouchflag");//vouchflag
				param[9] = CollectionUtil.getStringFromMap(msg,"overflag");//overflag
				param[10] = CollectionUtil.getStringFromMap(msg,"isettleflag");//iSettleFlag
				param[11] = CollectionUtil.getStringFromMap(msg,"iexeflag");//iExeFlag
				param[12] = CollectionUtil.getStringFromMap(msg,"icheckstatus");//iCheckStatus
				msgParam.add(param);
			}
			db.batchUpdate("insert into CO_TDLogMsg(cguid,clogguid,type,cbillcode,ddate,cbilltype,iaccountflag,iauditstatus,vouchflag,overflag,iSettleFlag,iExeFlag,iCheckStatus) values(?,?,?,?,?,?,?,?,?,?,?,?,?)", msgParam);
			if("1".equalsIgnoreCase(type)){//严格控制
				throw new ValidateException("请查看详情",clogguid);
			}else if("2".equalsIgnoreCase(type)){//提示警告
				throw new WarndateException("请查看详情",clogguid);
			}
		}
	}
}
