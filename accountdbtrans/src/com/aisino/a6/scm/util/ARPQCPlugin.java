package com.aisino.a6.scm.util;

import com.aisino.account.db.trans.interfaces.IBeforeAccountTrans;
import com.aisino.account.db.trans.util.ListMapUtils;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

public class ARPQCPlugin implements IBeforeAccountTrans{

	@Override
	public void doBefore(DbSvr srcDbSvr, DbSvr destDbSvr, DataMsgBus bus) {
		// TODO Auto-generated method stub
		//单据范围：结转日期之前、已审核、未完全核销的期初收（付）款单，期初其他应收（付）单，期初销售（采购）发票，收（付）款单，销售发票，销售费用发票，采购发票，采购费用发票
		//更新原单据上的期初标识、单据日期、结转标志
		//收（付）款单
		srcDbSvr.executeId("scm_ar_trans.updatePayment", bus);
		//其他应收（付）款单
		srcDbSvr.executeId("scm_ar_trans.updateAPVoucher", bus);
		if("1".equalsIgnoreCase(ListMapUtils.getSysinit(srcDbSvr, "AR"))){//应收是否启用
			//销售发票
			srcDbSvr.executeId("scm_ar_trans.updateSAinvoice",bus);
			//销售费用发票
			srcDbSvr.executeId("scm_ar_trans.updateSACainvoice",bus);
		}
		if("1".equalsIgnoreCase(ListMapUtils.getSysinit(srcDbSvr, "AP"))){//应付是否启用
			//采购发票
			srcDbSvr.executeId("scm_ar_trans.updatePUinvoice",bus);
			//采购费用发票
			srcDbSvr.executeId("scm_ar_trans.updatePUCainvoice",bus);
			//委外发票
			srcDbSvr.executeId("scm_ar_trans.updateOMinvoice",bus);
		}
		//根据结转期初数据登记应收（付）明细账表，把剩余未核销金额登记明细账
		if(!"1".equalsIgnoreCase(srcDbSvr.getStringResult("select top 1 1 from AP_DetailAccount where iInventoryFlag = 1"))){
			//处理单据类型为空的数据
			srcDbSvr.execute("update ap set cVouTypeGUID = (select top 1 cVouTypeGUID from AP_DetailAccount where cVouTypeGUID is not null and cVouCode = ap.cVouCode and cSupGUID = ap.cSupGUID and iCheckDir = 0) from AP_DetailAccount ap where ap.cVouTypeGUID is null");
			//先将并账类型的明细单独处理
			srcDbSvr.executeId("scm_ar_trans.updatebg",bus);
			srcDbSvr.executeId("scm_ar_trans.updateDetailAccount", bus);
		}
		srcDbSvr.execute("delete from AP_DetailAccount where isnull(iInventoryFlag, 0) = 1 and iDebitAMT_F = 0 and iCreditAMT_F = 0");
		//根据期初应收（付）明细账，更新应收（付）总账表
		//srcDbSvr.execute("delete from AP_Account where isnull(iInventoryFlag, 0) = 1");
		//srcDbSvr.executeId("scm_ar_trans.updateAccount");
	}

}
