package com.aisino.account.db.trans.util;


import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;

import com.aisino.platform.db.DbSvr;
import com.aisino.platform.keygen.SimpleKey;
import com.aisino.platform.util.SessUtil;

public class TDLog {

	public static final String LOG_TYPE_OPERATOR = "1";
	
	public static final String LOG_TYPE_RULES = "2";
	
	public static final String LOG_TYPE_SOURCE_DB = "3";
	
	public static final String LOG_TYPE_DEST_DB = "4";
	
	public static final String LOG_TYPE_OTHER = "5";
	
	public static final String LOG_RESULT_SUCESSFULL = "y";
	
	public static final String LOG_RESULT_FAIL = "n";
	
	public static final String LOG_RESULT_WARN = "w";
	
	private static DbSvr db = null;
	
	private static String accid = "";
	
	public static void log(String logType, String module, String item, String result, String desp, String expt, String clogguid){
		if (StringUtils.isNotBlank(ObjectUtils.toString(SessUtil.getAttribute("com.aisino.platform.datasource.curr")))){
			try {
				accid = ObjectUtils.toString(SessUtil.getAttribute("com.aisino.platform.datasource.curr"));
			}catch (Exception e) {
				e.printStackTrace();
			}
		}
		db = DbSvr.getDbService(accid);
		if(clogguid==null||clogguid==""){
			clogguid = SimpleKey.getInstance().genKey();
		}
		db.executeId("account_trans.log",new Object[]{ clogguid, logType, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), module, item, result, desp, expt!=null?expt.length()>500?expt.substring(0, 500):expt:expt});
	}
	public static void log(String logType, String module, String item, String result, String desp, String expt){
		log(logType, module, item, result, desp, expt, null);
	}
	public static void log(String item, String desp){
		log(LOG_TYPE_OPERATOR, null, item, null, desp, null);
	}
	
	private TDLog(){
		
	}
	
}
