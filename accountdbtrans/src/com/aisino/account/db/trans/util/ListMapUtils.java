package com.aisino.account.db.trans.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.map.LinkedMap;
import org.springframework.util.CollectionUtils;

import com.aisino.platform.db.DbSvr;

/**
 * 
 * <p>Description: 转账升级数据处理</p> 
 * @category 工具_自动转账工具_工具类
 * <AUTHOR>
 * @version $Revision$ 2012-6-20
 * <AUTHOR> modification by huangshun)
 * @since 1.0
 */
public class ListMapUtils {

	/**
	 * 
	 * <p>Discription:根据某字段对数据列表进行分组合计(归类)</p> 
	 *@param list 数据列表. 
	 *@param groupKey 分组的字段
	 *@return
	 */
	@SuppressWarnings("unchecked")
	public static Map groupListMap(List<Map> list, String... groupKey){
		if(CollectionUtils.isEmpty(list)) 
			return null;
		Map map = new LinkedMap();
		for (Map row:list){
			StringBuffer complexKey = new StringBuffer();
			for (String key:groupKey){
				complexKey.append(row.get(key));
			}
			if (map.get(complexKey.toString())==null){
				map.put(complexKey.toString(), new ArrayList<Map>());
			}
			List<Map> innerList = (List<Map>)map.get(complexKey.toString());
			innerList.add(row);
			map.put(complexKey.toString(), innerList);
		}
		return map;
	}
	
	public static String getSysinit(DbSvr db, String cSubSysCode) {
		String sys = db.getStringResult("select 1 from CO_SysInit where cSubSysCode = ? and iInitYear is not null and iInitMonth is not null", cSubSysCode);
		if(sys==null||sys=="") sys = "0";
		return sys;
	}
	public static String checkIAonly(DbSvr db) {
		String sys = db.getStringResult("select 1 from CO_SysInit where cSubSysCode = 'IA' and iInitYear is not null and iInitMonth is not null and not exists (select 1 from CO_SysInit where cSubSysCode in ('SA','PU','ST') and iInitYear is not null and iInitMonth is not null)");
		if(sys==null||sys=="") sys = "0";
		return sys;
	}
	
}
