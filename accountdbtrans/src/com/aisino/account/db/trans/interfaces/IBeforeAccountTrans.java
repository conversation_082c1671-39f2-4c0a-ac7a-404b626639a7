package com.aisino.account.db.trans.interfaces;

import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

/**
 * 
 * <p>Description: 升级数据处理, 升级前处理</p> 
 * @category 工具_自动转账工具_插入前接口
 * <AUTHOR>
 * @version $Revision$ 2012-6-20
 * <AUTHOR> modification by huangshun)
 * @since 1.0
 */
public interface IBeforeAccountTrans {
	/**
	 * 
	 * <p>Discription:升级前,处理数据</p> 
	 *@param srcDbSvr 源数据库连接
	 *@param destDbSvr 目标数据库连接
	 *@param bus
	 */
	public void doBefore(DbSvr srcDbSvr, DbSvr destDbSvr, DataMsgBus bus);
}
