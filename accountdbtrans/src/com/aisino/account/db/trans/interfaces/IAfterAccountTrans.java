package com.aisino.account.db.trans.interfaces;

import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

/**
 * 
 * <p>Description: 升级数据处理, 升级后处理接口</p> 
 * @category 工具_自动转账工具_插入后接口
 * <AUTHOR>
 * @version $Revision$ 2012-6-20
 * <AUTHOR> modification by huangshu<PERSON>)
 * @since 1.0
 */
public interface IAfterAccountTrans {
	/**
	 * 
	 * <p>Discription:转账数据插入之后数据处理.</p> 
	 *@param srcDbSvr 源数据库连接
	 *@param destDbSvr 目标数据库连接
	 *@param bus
	 */
	public void doAfter(DbSvr srcDbSvr, DbSvr destDbSvr, DataMsgBus bus);
}
