package com.aisino.account.db.trans.interfaces;

import java.util.Map;

import com.aisino.account.db.trans.exception.ValidateException;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

/**
 * 
 * <p>Description: 转账升级数据处理, 数据校验</p> 
 * @category 工具_自动转账工具_校验接口
 * <AUTHOR>
 * @version $Revision$ 2012-6-20
 * <AUTHOR> modification by huangshun)
 * @since 1.0
 */
public interface IValidateAccountTrans {
	/**
	 * 
	 * <p>Discription:对数据进行转账校验</p> 
	 *@param db 
	 *@param bus
	 *@throws ValidateException
	 */
	public void validate(DbSvr db, DataMsgBus bus, Map validation) throws ValidateException;
}
