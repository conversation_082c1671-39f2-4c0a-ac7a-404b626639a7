package com.aisino.account.db.trans.plugin;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.account.db.trans.exception.ValidateException;
import com.aisino.account.db.trans.exception.WarndateException;
import com.aisino.account.db.trans.interfaces.IValidateAccountTrans;
import com.aisino.account.db.trans.util.ListMapUtils;
import com.aisino.account.db.trans.util.TDLog;
import com.aisino.platform.core.Plugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.SubmitListener;

public class ValidatePlugin extends Plugin implements SubmitListener {
	
	private String grid = "list1";

	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		validate(form, bus);
	}

	private void validate(AbstractForm form, DataMsgBus bus) {
		//TDLog.log(TDLog.LOG_TYPE_OPERATOR, null, "截止期间与历史账套信息选择", null, "截止期间"+bus.getString("iYear")+"年"+bus.getString("iMonth")+"期间,历史账套号:"+bus.getString("cDestDbGUID").trim()+" 历史账套名:"+bus.getString("cDestDbName").trim(), null);
		DbSvr db = DbSvr.getDbService(null);
		List<Map> validations = db.queryIdForList("account_trans.loadValidation", bus);
		Map validateMap = ListMapUtils.groupListMap(validations, "cModule");
		List<Map> checkList = db.queryIdForList("account_trans.loadCheckGrid", bus);
		Map result = new HashMap();
		boolean hasFail = false;
		boolean hasWarn = false;
		for (int i=0; i<checkList.size(); i++){
			Map checkRow = checkList.get(i);
			checkRow.put("cresult", "成功");
			List<Map> rowValidations = validateMap==null?null:((List<Map>)validateMap.get(checkRow.get("cModule")));
			if (rowValidations==null){
				continue;
			}
			for (Map validation: rowValidations){
				hasFail = !validateRow(checkRow, validation, db, bus) || hasFail;
				if("警告".equalsIgnoreCase(CollectionUtil.getStringFromMap(checkRow, "cresult"))){
					hasWarn = true;
				}
			}
		}
		result.put("hasFail", hasFail);
		result.put("hasWarn", hasWarn);
		form.setReturn(result);
		if (!hasFail&&!hasWarn){
			TDLog.log(TDLog.LOG_TYPE_RULES, null, "校验", TDLog.LOG_RESULT_SUCESSFULL, "校验全部通过", null);
		}
		form.updateWidgetValue(grid, checkList);
	}
	
	private boolean validateRow(Map checkRow, Map validation, DbSvr db, DataMsgBus bus) {
		String cClassName = (String)validation.get("cClassName");
		if (cClassName==null){
			return true;
		}
		try {
			Object obj = Class.forName(cClassName).newInstance();
			if (!(obj instanceof IValidateAccountTrans)){
				TDLog.log(TDLog.LOG_TYPE_RULES, (String)checkRow.get("cModule"), "加载校验类"+cClassName, TDLog.LOG_RESULT_FAIL, "配置的校验类"+cClassName+"没有实现规定接口", null);
			}
			IValidateAccountTrans checker = (IValidateAccountTrans)obj;
			checker.validate(db, bus, validation);
		}catch (ClassNotFoundException e) {
			TDLog.log(TDLog.LOG_TYPE_RULES, (String)checkRow.get("cModule"), "加载校验类"+cClassName, TDLog.LOG_RESULT_FAIL, "配置的校验类"+cClassName+"不存在", e+e.getMessage());
			e.printStackTrace();
			checkRow.put("cresult", "失败");
			return false;
		}catch (InstantiationException e) {
			TDLog.log(TDLog.LOG_TYPE_RULES, (String)checkRow.get("cModule"), "初始化校验类"+cClassName, TDLog.LOG_RESULT_FAIL, "配置的校验类"+cClassName+"初始化失败", e+e.getMessage());
			e.printStackTrace();
			checkRow.put("cresult", "失败");
			return false;
		}catch (IllegalAccessException e) {
			e.printStackTrace();
			checkRow.put("cresult", "失败");
			return false;
		}catch (WarndateException e) {
			TDLog.log(TDLog.LOG_TYPE_RULES, (String)checkRow.get("cModule"), "执行校验类"+cClassName, TDLog.LOG_RESULT_WARN, "校验类"+cClassName+"有警告,原因:"+e.getMessage(), e+e.getMessage(), e.getLogGuid());
			checkRow.put("cresult", "警告");
			return true;
		}catch (ValidateException e) {
			TDLog.log(TDLog.LOG_TYPE_RULES, (String)checkRow.get("cModule"), "执行校验类"+cClassName, TDLog.LOG_RESULT_FAIL, "校验类"+cClassName+"不通过,原因:"+e.getMessage(), e+e.getMessage(), e.getLogGuid());
			checkRow.put("cresult", "失败");
			return false;
		}catch (ClassCastException e){
			TDLog.log(TDLog.LOG_TYPE_RULES, (String)checkRow.get("cModule"), "执行校验类"+cClassName, TDLog.LOG_RESULT_FAIL, "配置的校验类"+cClassName+"初始化失败", e+e.getMessage());
			checkRow.put("cresult", "失败");
			return false;
		}
		return true;
	}

	@Override
	public void setValue(String name, String value) {
		if ("grid".equalsIgnoreCase(name)){
			grid = value; 
		}
	}

}
