package com.aisino.account.db.trans.plugin;

import java.util.List;

import org.springframework.util.CollectionUtils;

import com.aisino.platform.core.Plugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.SubmitListener;

/**
 * 
 * <p>Description: 转账升级数据处理</p> 
 * @category 工具_自动转账工具_删除操作日志
 * <AUTHOR>
 * @version $Revision$ 2012-6-20
 * <AUTHOR> modification by huangshun)
 * @since 1.0
 */
public class DeleteLogPlugin extends Plugin implements SubmitListener{

	@Override
	public void setValue(String name, String value) {
		
	}

	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		DbSvr db = DbSvr.getDbService(null);
		List<String> cguids = bus.getList("ids");
		if(CollectionUtils.isEmpty(cguids)) 
			return;
		StringBuffer sql = new StringBuffer("delete from CO_TDLog where cGUID in (");
		for (String cguid:cguids){
			sql.append("'").append(cguid).append("',");
		}
		sql.deleteCharAt(sql.length()-1).append(")");
		db.update(sql.toString());
	}

}
