package com.aisino.account.db.trans.plugin;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import com.aisino.a6.finance.business.acctclose.service.AccountDbService;
import com.aisino.account.db.trans.interfaces.IAfterAccountTrans;
import com.aisino.account.db.trans.interfaces.IBeforeAccountTrans;
import com.aisino.account.db.trans.util.ListMapUtils;
import com.aisino.account.db.trans.util.TDLog;
import com.aisino.account.db.trans.util.TDPrefer;
import com.aisino.aos.account.util.AccountsUtil;
import com.aisino.platform.config.xmlObject.Ds;
import com.aisino.platform.config.xmlObject.Link;
import com.aisino.platform.core.Plugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.db.Eso;
import com.aisino.platform.db.SqlInfo;
import com.aisino.platform.exception.CodeException;
import com.aisino.platform.jdbc.datasource.Dm;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.SubmitListener;

public class TransDbDataPlugin extends Plugin implements SubmitListener {

	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		
		if (bus.isAction("backupAccount")){
			backupAccount(form, bus);
		}else if (bus.isAction("transAccount")){
			try{
				transAccount(form, bus);
				TDLog.log(TDLog.LOG_TYPE_OPERATOR, null, "数据转移完成", TDLog.LOG_RESULT_SUCESSFULL, "数据转移成功", null);
			}catch(Exception e){
				TDLog.log(TDLog.LOG_TYPE_OPERATOR, null, "数据转移失败", TDLog.LOG_RESULT_FAIL, "数据转移失败", e+e.getMessage());
				e.printStackTrace();
				deleteAccount(form, bus);
				deleteBackupAccount(form, bus);
				if (e.getMessage()==null){
					form.setReturn("出现未知错误");
				}else{
					form.setReturn(e.getMessage());
				}
			}
		}
	}

	private void deleteBackupAccount(AbstractForm form, DataMsgBus bus) {
		Map backup = getLastBackUpFileMap(bus.getString("account"));
		String filePath = backup.get("cfiledir").toString()+"\\"+backup.get("cfilename");
		File backupFile = new File(filePath);
		if (backupFile.exists()){
			backupFile.delete();
		}
		DbSvr dbsvrxt = DbSvr.getDefaultDbService();//获取系统链接
		dbsvrxt.update("delete from Acct_backups where cacctid=? and cfilename=?", bus.getString("account"), backup.get("cfilename"));
	}

	@SuppressWarnings("unchecked")
	private void transAccount(AbstractForm form, DataMsgBus bus) {
		clearRelation();
		//copyAccount(form, bus);
		DbSvr srcDb = DbSvr.getDbService(null);
		DbSvr destDb = null;
		/*try {
			destDb = DbSvr.getDbService(bus.getString("accid"));
		}catch (Exception e) {
			e.printStackTrace();
		}*/
		bus.send("dDate", srcDb.queryIdForString("account_trans.getLastInPeriod", bus));
		bus.send("IAsys", ListMapUtils.getSysinit(srcDb, "IA"));//存货是否启用
		bus.send("IAonly",ListMapUtils.checkIAonly(srcDb));//存货是否单独启用
		bus.send("GLsys", ListMapUtils.getSysinit(srcDb, "GL"));//总账是否启用
		bus.send("ARsys", ListMapUtils.getSysinit(srcDb, "AR"));//应收是否启用
		bus.send("APsys", ListMapUtils.getSysinit(srcDb, "AP"));//应付是否启用
		List<Map> transList = srcDb.queryIdForList("account_trans.getTransData", bus);
		Map transMap = ListMapUtils.groupListMap(transList, "cModule");
		boolean hasError = false;
		if (MapUtils.isNotEmpty(transMap)){
			for (Iterator<Map.Entry> iter=transMap.entrySet().iterator();iter.hasNext();){
				Map.Entry entry = iter.next();
				hasError = !transDbData(srcDb, destDb, (List<Map>)entry.getValue(), bus, entry.getKey().toString()) || hasError;
			}
		}
		if (!"y".equalsIgnoreCase(TDPrefer.get("TD_ErrorPause")) && hasError){
			throw new CodeException("account.db.trans.checkTransferData");
		}
		addRelation(bus.getString("accid"), bus.getString("account"), bus.getString("iYear"),  bus.getString("iMonth"));
	}

	/**
	 * 清除被删除的账套(此操作必须在复制账套前)
	 */
	private void clearRelation() {
		DbSvr dbsvrxt = DbSvr.getDefaultDbService() ;//获取系统链接
		dbsvrxt.update("delete from ac_relation where cAcctId not in (select accid from accinfo)");
	}

	private void addRelation(String cAcctid, String cParentid, String iYear, String iMonth) {
		DbSvr dbsvrxt = DbSvr.getDefaultDbService();//获取系统链接
		Map cParent = dbsvrxt.getOneRecorder("select iLevel, cFullCode from ac_relation where cAcctId=?", cParentid);
		if (cParent==null || cParent.isEmpty()){
			dbsvrxt.update("insert into ac_relation(cAcctId, iLeaf, iLevel, cFullCode, dDate) select accid cAccId, 0 iLeaf, 0 iLevel, accid+'.' cFullCode, crdate dDate from accinfo where accid=?", cParentid);
			cParent = dbsvrxt.getOneRecorder("select iLevel, cFullCode from ac_relation where cAcctId=?", cParentid);
		}
		int level = MapUtils.getIntValue(cParent, "iLevel", 0);
		String cFullCode = MapUtils.getString(cParent, "cFullCode", "");
		dbsvrxt.update("insert into ac_relation(cAcctId, cParentId, iLeaf, iLevel, cFullCode, dDate, iYear, iMonth) values(?,?,?,?,?,?,?,?)"
				, cAcctid, cParentid, 1, level+1, cFullCode+cAcctid+".", new Date(), iYear, iMonth);
		dbsvrxt.update("update ac_relation set iLeaf=0 where cAcctId=?", cParentid);
	}

	private void copyAccount(AbstractForm form, DataMsgBus bus) {
		
		checkAccount(bus);
		
		String destAcct = bus.getString("accid").trim();
		String destName = bus.getString("accname").trim();
		
		Map backup = getLastBackUpFileMap(bus.getString("account"));
		
		String newdbname = AccountsUtil.getSysVertion().get("cWebVersion").toString().substring(0, 2)+destAcct+bus.getString("curYear");
		
		String filePath = backup.get("cfiledir").toString()+"\\"+backup.get("cfilename");
		
		StringBuffer sql = new StringBuffer("use master ");
		sql.append("restore database ").append(newdbname).append(" from disk ='").append(filePath).append("' with ");
				
		try{
			DbSvr db = DbSvr.getDbService(null);
			List<Map> dbFileList = db.getListResult("restore filelistonly from disk='"+filePath+"'");
			for (Map dbFile:dbFileList){
				sql.append(" move '").append(dbFile.get("LogicalName")).append("' to '").append(dbFile.get("PhysicalName").toString().replaceAll(backup.get("dbname").toString(), newdbname))
				.append("',");
			}
			sql.deleteCharAt(sql.length()-1).append(" use ").append(backup.get("dbname"));
			if (db.isInTransaction()){
				db.commit();
			}
			db.release();
			db.update(sql.toString());
			updateSysDb(destAcct, destName, newdbname);
			addDataSource(destAcct, destName, newdbname);
		}catch (Exception e) {
			TDLog.log(TDLog.LOG_TYPE_DEST_DB, null, "复制账套", TDLog.LOG_RESULT_FAIL, "复制账套失败", e+e.getMessage());
			e.printStackTrace();
			throw new CodeException("account.db.trans.checkCopyAccount");
		}
	}
	
	private void addDataSource(String destAcct, String destName, String newdbname) {
		DbSvr dbsvrxt = DbSvr.getDefaultDbService();//获取系统链接
		Link syslink = Dm.searchDs(dbsvrxt.getDsId()).getLink();
		Ds ds = new Ds();
		ds.setId(destAcct);
		ds.setName(destName);
		ds.setEncrypt("false");
		Link link = new Link();
		link.setDbname(newdbname);
		link.setIp(syslink.getIp());
		link.setPort(syslink.getPort());
		link.setUser(syslink.getUser());
		link.setPwd(syslink.getPwd());
		link.setTimeout("20000");
		ds.setLink(link);
		ds.setType("mssql");
		ds.setVersion(dbsvrxt.getDatabaseVersion());
		ds.setCharset("utf-8");
		ds.setInner("true");
		Dm.removeDataSource(destAcct);
		Dm.addNotProductXmlDataSource(ds);
	}

	private void updateSysDb(String accid, String accname, String dbname) {
		DbSvr dbsvrxt = DbSvr.getDefaultDbService();//获取系统链接
		Link link = Dm.searchDs(dbsvrxt.getDsId()).getLink();
		dbsvrxt.update("insert into accinfo(accid, accname, dbname, dbtype, connuser, connpsw, dbpath, port, CRDATE, ISJOBBACKUP, ISHIDDEN, cDbVersion, cCreateUser) values(?,?,?,?,?,?,?,?,?,?,?,?,?) "
				, accid, accname, dbname, dbsvrxt.getDatabaseType(), link.getUser(), link.getPwd(), link.getIp(), link.getPort()
				, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), 1, 0, dbsvrxt.getDatabaseVersion(), "init");
		grantUser(accid, DbSvr.getDbService(null), dbsvrxt);
	}

	private void checkAccount(DataMsgBus bus) {
		if(Dm.isReachMaxDb()) {
			throw new CodeException("account.db.trans.maxAccount");
		}
		String destAcct = bus.getString("accid");
		String destAcctName = bus.getString("accname");
		DbSvr dbsvrxt = DbSvr.getDefaultDbService();//获取系统链接
		if(dbsvrxt.isHasResult("select accid from accinfo where accid=?", new Object[]{destAcct})){//如果存在相同的账套号
			throw new CodeException("account.db.trans.dupAccountId");
		}
		
		if(dbsvrxt.isHasResult("select accname from accinfo where accname=?", new Object[]{destAcctName})){//如果存在相同的账套名称
			throw new CodeException("account.db.trans.dupAccountName");
		}
	}

	@SuppressWarnings("unchecked")
	private Map getLastBackUpFileMap(String accid){
		DbSvr dbsvrxt = DbSvr.getDefaultDbService();//获取系统链接
		Map result = dbsvrxt.getOneRecorder("select top 1 cfiledir, cfilename from Acct_backups where cAcctID=? and cfilename like 'A6"+accid+"_%' order by cfilename desc", accid);
		if (result==null){
			result = dbsvrxt.getOneRecorder("select dbname from accinfo where accid=?", accid);
		}else{
			result.putAll(dbsvrxt.getOneRecorder("select dbname from accinfo where accid=?", accid));
		}
		return result;
	}

	public void deleteAccount(AbstractForm form, DataMsgBus bus){
		AccountDbService ads = new AccountDbService();
		ads.deleteAccount(bus.getString("accid"));
	}

	private void grantUser(String naccid, DbSvr accountdb, DbSvr sysdb) {
		List<Map> userList = accountdb.getListResult(" select cGUID, cName, cPWD from AOS_RMS_USER ");
		List<Eso> esos = new ArrayList<Eso>();
		for (Map user: userList){
			esos.add(new Eso("insert into Acct_LinkUser(cAcctID, cUserID, cUserName, cUserPass) values(?,?,?,?)", new Object[]{naccid, MapUtils.getString(user, "cGUID"), MapUtils.getString(user, "cName"), MapUtils.getString(user, "cPWD")}));
		}
		Map initUser = sysdb.getOneRecorder("select top 1 * from Acct_users where cRole='admin'");
		esos.add(new Eso("insert into Acct_UserAccts(cAcctID,cUserID) values(?,?)", new Object[]{naccid,MapUtils.getString(initUser, "cuserid")}));
		sysdb.executeAllEso(esos);
	}
	
	private void backupAccount(AbstractForm form, DataMsgBus bus) {
		AccountDbService ads = new AccountDbService();
		ads.backupAccount(bus.getString("accid"));
	}

	@Override
	public void setValue(String name, String value) {

	}
	
	private boolean transDbData(DbSvr sourceDb, DbSvr destDb, List<Map> transferDatas, DataMsgBus bus, String cModule){
		System.out.println(cModule+"模块卸载前数据处理");
		long startTime_bf = System.currentTimeMillis();
		doBeforeTrans(sourceDb, destDb, bus, cModule);
		long endTime_bf = System.currentTimeMillis();
		System.out.println(cModule+"模块卸载前数据处理时间： "+(endTime_bf-startTime_bf)+"ms");
		List<Eso> srcEsos = new ArrayList<Eso>();
		List<Eso> destEsos = new ArrayList<Eso>();
		boolean result = true;
		for (Map transferData: transferDatas){
			String srcSql = MapUtils.getString(transferData, "cSrcSQLScript", "");
			String destSql = MapUtils.getString(transferData, "cHstrSQLScript", "");
			if (StringUtil.isNotBlank(srcSql)){
				if (srcSql.indexOf("\r\n")!=-1){
					String[] srcSqls = srcSql.split("\r\n");
					for (int i=0;i<srcSqls.length;i++){
						if (StringUtils.isNotBlank(srcSqls[i])){
							srcEsos.add(new SqlInfo(srcSqls[i]).getEso(bus));
						}
					}
				}else{
					srcEsos.add(new SqlInfo(srcSql).getEso(bus));
				}
			}
			if (StringUtil.isNotBlank(destSql)){
				if (destSql.indexOf("\r\n")!=-1){
					String[] destSqls = destSql.split("\r\n");
					for (int i=0;i<destSqls.length;i++){
						if (StringUtils.isNotBlank(destSqls[i])){
							destEsos.add(new SqlInfo(destSqls[i]).getEso(bus));
						}
					}
				}else{
					destEsos.add(new SqlInfo(destSql).getEso(bus));
				}
			}
		}
		try{
			System.out.println(cModule+"模块数据卸载");
			long startTime = System.currentTimeMillis();
			sourceDb.executeAllEso(srcEsos);
			long endTime = System.currentTimeMillis();
			System.out.println(cModule+"模块数据卸载时间： "+(endTime-startTime)+"ms");
		}catch (Exception e){
			TDLog.log(TDLog.LOG_TYPE_SOURCE_DB, cModule, "执行转移语句", TDLog.LOG_RESULT_FAIL, "数据库操作失败", e+e.getMessage());
			result = false;
			e.printStackTrace();
			if (!"n".equalsIgnoreCase(TDPrefer.get("TD_ErrorPause"))){
				throw new CodeException("account.db.trans.checkTransferData");
			}
		}
		/*try{
			destDb.executeAllEso(destEsos);
		}catch (Exception e){
			TDLog.log(TDLog.LOG_TYPE_DEST_DB, cModule, "执行转移语句", TDLog.LOG_RESULT_FAIL, "数据库操作失败", e+e.getMessage());
			result = false;
			e.printStackTrace();
			if (!"n".equalsIgnoreCase(TDPrefer.get("TD_ErrorPause"))){
				throw new CodeException("account.db.trans.checkTransferData");
			}
		}*/
		System.out.println(cModule+"模块卸载后数据处理");
		long startTime_af = System.currentTimeMillis();
		doAfterTrans(sourceDb, destDb, bus, cModule);
		long endTime_af = System.currentTimeMillis();
		System.out.println(cModule+"模块卸载后数据处理时间： "+(endTime_af-startTime_af)+"ms");
		modifySysInit(sourceDb, destDb, bus, cModule);
		
		return result;
	}

	private void modifySysInit(DbSvr sourceDb, DbSvr destDb, DataMsgBus bus, String cModule) {
		List<Eso> sourceEso = new ArrayList<Eso>();
		List<Eso> destEso = new ArrayList<Eso>();
		
		if ("RE".equalsIgnoreCase(cModule)){
			return;
		}
		Map initPeriodMap = sourceDb.getOneRecorder("select iInitYear, iInitMonth from CO_SysInit where cSubSysCode=?", cModule);
		int initYear = Integer.parseInt(MapUtils.getString(initPeriodMap, "iInitYear"));
		int initPeriod = Integer.parseInt(MapUtils.getString(initPeriodMap, "iInitMonth"));
		int year = Integer.parseInt(bus.getString("iYear"));
		int period = Integer.parseInt(bus.getString("iMonth"));
		
		Map nextPeriod = getNextPeriod(bus);
		
		if (initPeriodMap ==null || initPeriodMap.isEmpty()){
			return;
		}
		if (year<initYear || (initYear==year && period<initPeriod)){
			destEso.add(new Eso("delete from CO_SysInit where cSubSysCode=?", new Object[]{cModule}));
		}else{
			sourceEso.add(new Eso("update CO_SysInit set iInitYear=?, iInitMonth=? where cSubSysCode=?", new Object[]{nextPeriod.get("iYear"), nextPeriod.get("iMonth"), cModule}));
			destEso.add(new Eso("update CO_SysInit set iYear=?, iMonth=? where cSubSysCode=?", new Object[]{bus.getString("iYear"),bus.getString("iMonth"), cModule}));
		}
		sourceEso.add(new Eso("delete from CO_SysClosing where iYear<? or (iYear=? and iMonth<=?) and cSubSysCode=?", new Object[]{bus.getString("iYear"), bus.getString("iYear"), bus.getString("iMonth"),cModule}));
		sourceDb.executeAllEso(sourceEso);
		destEso.add(new Eso("delete from CO_SysClosing where iYear>? or (iYear=? and iMonth>=?) and cSubSysCode=?", new Object[]{bus.getString("iYear"), bus.getString("iYear"), bus.getString("iMonth"),cModule}));
		//destDb.executeAllEso(destEso);
	}

	@SuppressWarnings("unchecked")
	private Map getNextPeriod(DataMsgBus bus) {
		int year = Integer.parseInt(bus.getString("iYear"));
		int month = Integer.parseInt(bus.getString("iMonth"));
		Map result = new HashMap();
		if (month==12){
			result.put("iYear", ++year);
			result.put("iMonth", 1);
		}else{
			result.put("iYear", year);
			result.put("iMonth", ++month);
		}
		return result;
	}

	private void doAfterTrans(DbSvr srcDb, DbSvr destDb, DataMsgBus bus, String cModule) {
		List<Map> processes = srcDb.queryIdForList("account_trans.getCustomerProcess", cModule, 3);
		if (processes==null || processes.isEmpty()){
			return;
		}
		for (Map process:processes){
			String cClassName = (String)process.get("cClassName");
			Object obj;
			try {
				obj = Class.forName(cClassName).newInstance();
				if (!(obj instanceof IAfterAccountTrans)){
					TDLog.log(TDLog.LOG_TYPE_OTHER, cModule, "加载处理类"+cClassName, TDLog.LOG_RESULT_FAIL, "配置的处理类"+cClassName+"没有实现规定接口", null);
				}
				IAfterAccountTrans doAfter = (IAfterAccountTrans)obj;
				doAfter.doAfter(srcDb, destDb, bus);
			}catch (InstantiationException e) {
				TDLog.log(TDLog.LOG_TYPE_OTHER, cModule, "初始化处理类"+cClassName, TDLog.LOG_RESULT_FAIL, "配置的处理类"+cClassName+"初始化失败", e+e.getMessage());
				e.printStackTrace();
			}catch (IllegalAccessException e) {
				TDLog.log(TDLog.LOG_TYPE_OTHER, cModule, "访问处理类"+cClassName, TDLog.LOG_RESULT_FAIL, "配置的处理类"+cClassName+"访问不合法", e+e.getMessage());
				e.printStackTrace();
			}catch (ClassNotFoundException e) {
				TDLog.log(TDLog.LOG_TYPE_OTHER, cModule, "加载处理类"+cClassName, TDLog.LOG_RESULT_FAIL, "配置的处理类"+cClassName+"不存在", e+e.getMessage());
				e.printStackTrace();
			}
		}
	}

	private void doBeforeTrans(DbSvr srcDb, DbSvr destDb, DataMsgBus bus, String cModule) {
		List<Map> processes = srcDb.queryIdForList("account_trans.getCustomerProcess", cModule, 2);
		if (processes==null || processes.isEmpty()){
			return;
		}
		for (Map process:processes){
			String cClassName = (String)process.get("cClassName");
			Object obj;
			try {
				obj = Class.forName(cClassName).newInstance();
				if (!(obj instanceof IBeforeAccountTrans)){
					TDLog.log(TDLog.LOG_TYPE_OTHER, cModule, "加载处理类"+cClassName, TDLog.LOG_RESULT_FAIL, "配置的处理类"+cClassName+"没有实现规定接口", null);
				}
				IBeforeAccountTrans doBefore = (IBeforeAccountTrans)obj;
				doBefore.doBefore(srcDb, destDb, bus);
			}catch (InstantiationException e) {
				TDLog.log(TDLog.LOG_TYPE_OTHER, cModule, "初始化处理类"+cClassName, TDLog.LOG_RESULT_FAIL, "配置的处理类"+cClassName+"初始化失败", e+e.getMessage());
				e.printStackTrace();
			}catch (IllegalAccessException e) {
				TDLog.log(TDLog.LOG_TYPE_OTHER, cModule, "访问处理类"+cClassName, TDLog.LOG_RESULT_FAIL, "配置的处理类"+cClassName+"访问不合法", e+e.getMessage());
				e.printStackTrace();
			}catch (ClassNotFoundException e) {
				TDLog.log(TDLog.LOG_TYPE_OTHER, cModule, "加载处理类"+cClassName, TDLog.LOG_RESULT_FAIL, "配置的处理类"+cClassName+"不存在", e+e.getMessage());
				e.printStackTrace();
			}
		}
	}

}
