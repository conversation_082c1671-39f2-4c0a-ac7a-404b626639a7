package com.aisino.account.db.trans.plugin;

import java.util.Map;

import org.apache.commons.collections.MapUtils;

import com.aisino.account.db.trans.util.TDLog;
import com.aisino.platform.core.MS;
import com.aisino.platform.core.Plugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.CodeException;
import com.aisino.platform.jdbc.DataSourceUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.SubmitListener;

/**
 * 
 * <p>Description: 转账升级数据处理类</p> 
 * @category 工具_自动转账工具_校验用户信息
 * <AUTHOR>
 * @version $Revision$ 2012-6-20
 * <AUTHOR> modification by huangshun)
 * @since 1.0
 */
public class CheckPwdPlugin extends Plugin implements SubmitListener {

	private String user;

	private String pwd;

	private String account;

	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {

		DbSvr db = DbSvr.getDbService(bus.getString(account));
		if (db == null) {
			return;
		}
		Map userMap = db.getOneRecorder("select cpwd, cguid from aos_rms_user where cname=?", bus.getString(user));
		String dbPwd = MapUtils.getString(userMap, "cpwd");
		MS ms=new MS("ACS.PwdSvc");
		String inputPwd =ms.doService(bus.getString(pwd), MapUtils.getString(userMap, "cguid")).toString();
		/**
		 * 使用登录信息对用户数据进行校验,并记录日志.
		 */
		if (!inputPwd.equals(dbPwd)) {
			TDLog.log(TDLog.LOG_TYPE_OPERATOR, null, "登录信息", TDLog.LOG_RESULT_FAIL, bus.getString(user) + "登录" + bus.getString(account) + "账套失败,用户名或密码不正确", null);
			throw new CodeException("account.db.trans.checkFail");
		}
		TDLog.log(TDLog.LOG_TYPE_OPERATOR, null, "登录信息", TDLog.LOG_RESULT_SUCESSFULL, bus.getString(user) + "登录" + bus.getString(account) + "账套成功", null);
	}

	@Override
	public void setValue(String name, String value) {
		if ("user".equalsIgnoreCase(name)) {
			this.user = value;
		} else if ("pwd".equalsIgnoreCase(name)) {
			this.pwd = value;
		} else if ("account".equalsIgnoreCase(name)) {
			this.account = value;
		}
	}

}
