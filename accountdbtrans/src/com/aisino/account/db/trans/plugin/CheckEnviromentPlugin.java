package com.aisino.account.db.trans.plugin;

import java.io.File;

import org.apache.commons.lang.StringUtils;

import com.aisino.platform.core.ProductInfo;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.SubmitListener;

/**
 * 
 * <p>Description: 转账升级数据处理</p> 
 * @category 工具_自动转账工具_测查工具环境
 * <AUTHOR>
 * @version $Revision$ 2012-6-20
 * <AUTHOR> modification by huangshun)
 * @since 1.0
 */
public class CheckEnviromentPlugin implements SubmitListener{

	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		checkTable("ac_relation",DbSvr.getDefaultDbService());
		DbSvr db = DbSvr.getDbService(null);
		checkTable("CO_TDFM",db);
		checkTable("CO_TDParamCfg",db);
		checkTable("CO_RuleCfg",db);
		checkTable("CO_TransferData",db);
		checkTable("CO_TDLog",db);
		checkTable("CO_TDLogMsg",db);
		newfield(db);
		db.commit();
	}

	private void checkTable(String table, DbSvr db) {
		System.out.println(db.getDsId()+":"+db.getStringResult("select object_id(N'"+table+"')"));
		if (StringUtils.isNotBlank(db.getStringResult("select object_id(?)", table))){
			return;
		}
		File f = new File(getTableScriptPath(table));
		if (f.exists()){
			db.executeScriptFile(f,"gbk");
		}
		File f1 = new File(getDataScriptPath(table));
		if (f1.exists()){
			db.executeScriptFile(f1,"gbk");
		}
	}

	private String getTableScriptPath(String table) {
		StringBuffer webpath = new StringBuffer(ProductInfo.getClassFilePath());
		if (webpath.toString().startsWith("/")){
			webpath.deleteCharAt(0);
		}
		return webpath.toString().replace("WEB-INF/classes", "/pt/res/create/table/mssql/"+table+".sql");
	}
	
	private String getDataScriptPath(String table) {
		StringBuffer webpath = new StringBuffer(ProductInfo.getClassFilePath());
		if (webpath.toString().startsWith("/")){
			webpath.deleteCharAt(0);
		}
		return webpath.toString().replace("WEB-INF/classes", "/pt/res/create/data/mssql/"+table+".sql");
	}
	
	private void newfield(DbSvr db) {
		File f = new File(getTableScriptPath("NewField"));
		if (f.exists()){
			db.executeScriptFile(f,"gbk");
		}
	}

}
