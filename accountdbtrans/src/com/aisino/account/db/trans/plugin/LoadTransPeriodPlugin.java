package com.aisino.account.db.trans.plugin;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import com.aisino.account.db.trans.util.TDLog;
import com.aisino.platform.core.Plugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.CodeException;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.SubmitListener;

public class LoadTransPeriodPlugin extends Plugin implements SubmitListener {

	@SuppressWarnings("unchecked")
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		DbSvr db = DbSvr.getDbService(null);
		if (bus.isAction("loadPeriod")){
			form.updateWidgetLocalData("iYear", db.queryIdForList("account_trans.loadPeriod", bus));
			List<Map> notCloseSys = db.queryIdForList("account_trans.getErverNotClose",bus);
			if (CollectionUtils.isNotEmpty(notCloseSys)){
				StringBuffer name = new StringBuffer();
				for (Map notClose: notCloseSys){
					name.append(MapUtils.getString(notClose, "cSysName")).append(",");
				}
				name.deleteCharAt(name.length()-1);
				TDLog.log(TDLog.LOG_TYPE_OPERATOR, null, "加载初始化期间", TDLog.LOG_RESULT_FAIL, name.toString()+"从未结账，无法结转", null);
				throw new CodeException("account.db.trans.NerverErverClose",name.toString());
			}
			Map defaultPeriod = db.queryIdFirstRow("account_trans.loadDefaultPeriod", bus);
			if (defaultPeriod==null){
				TDLog.log(TDLog.LOG_TYPE_OPERATOR, null, "加载初始化期间", TDLog.LOG_RESULT_FAIL, "加载初始化期间失败,没有可选的期间", null);
				throw new CodeException("account.db.trans.checkSubsystem");
			}
			form.updateWidgetValue("cDestDbGUID", genDbCode());
			form.updateWidgetValue("iYear", defaultPeriod.get("iYear"));
			form.updateWidgetValue("iMonth", defaultPeriod.get("iMonth"));
		}else if (bus.isAction("checkPeriod")){
			String msg = db.queryIdForString("account_trans.checkPeriod", bus);
			if (StringUtil.isNotBlank(msg)){
				throw new CodeException("account.db.trans.checkPeriodFail", msg, bus.getString("iYear"),bus.getString("iMonth"));
			}
			Number min = db.queryIdForNumber("account_trans.getMinInitYearMonth", bus);
			if (min==null || min.intValue()>bus.getInteger("iYear").intValue()*1000+bus.getInteger("iMonth").intValue()){
				throw new CodeException("account.db.trans.haveNoInitSys", bus.getString("iYear"),bus.getString("iMonth"));
			}
			DbSvr dbsvrxt = DbSvr.getDefaultDbService();//获取系统链接
			if (dbsvrxt.isHasResult("select * from accinfo where accid=?", bus.getString("cDestDbGUID").trim())){
				throw new CodeException("account.db.trans.dupAccountId");
			}
			if (dbsvrxt.isHasResult("select * from accinfo where accname=?", bus.getString("cDestDbName").trim())){
				throw new CodeException("account.db.trans.dupAccountName");
			}
		}
	}

	private Object genDbCode() {
		String accountCode = String.valueOf(Double.valueOf(Math.random()*100000).intValue());
		DbSvr dbsvrxt = DbSvr.getDefaultDbService();//获取系统链接
		while (dbsvrxt.isHasResult("select * from accinfo where accid=?", accountCode)){
			accountCode = String.valueOf(Double.valueOf(Math.random()*100000).intValue());
		}
		return accountCode;
	}

	@Override
	public void setValue(String name, String value) {
		
	}

}
