package com.aisino.account.db.trans.plugin;

import com.aisino.account.db.trans.util.TDLog;
import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.config.xmlObject.Ds;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.jdbc.datasource.Dm;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.SubmitListener;

/**
 * 
 * <p>Description: 转账升级数据处理类</p> 
 * @category 工具_自动转账工具_加载当前账套信息
 * <AUTHOR>
 * @version $Revision$ 2012-6-20
 * <AUTHOR> modification by huangshun)
 * @since 1.0
 */
public class LoadAccountInfoPlugin implements SubmitListener {

	@Override
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		DbSvr db = DbSvr.getDbService(null);
		if (db == null) {
			return;
		}
		Ds ds = Dm.searchDs(db.getDsId());
		String account = ds.getId();
		String accountName = ds.getName();
		form.updateWidgetValue("account", account);
		form.updateWidgetValue("accountName", accountName);
		TDLog.log(TDLog.LOG_TYPE_OPERATOR, null, "登录信息", TDLog.LOG_RESULT_SUCESSFULL, SessionHelper.getCurrentUserRealName() + "登录" + accountName + "账套成功", null);
	}

}
