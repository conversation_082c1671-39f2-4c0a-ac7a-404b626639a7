package com.aisino.account.db.trans.plugin;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.LinkedMap;
import org.springframework.util.CollectionUtils;

import com.aisino.account.db.trans.util.ListMapUtils;
import com.aisino.account.db.trans.util.TDLog;
import com.aisino.platform.core.Plugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.veng.core.UICreator;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.Layout;
import com.aisino.platform.view.basicHelp.BasicWidget;
import com.aisino.platform.view.layout.TabLayout;
import com.aisino.platform.view.layout.TableLayout;
import com.aisino.platform.view.layout.TitleLayout;
import com.aisino.platform.view.listener.SubmitListener;

/**
 * 
 * <p>Description: 转账升级数据处理</p> 
 * @category 工具_自动转账工具_获取参数
 * <AUTHOR>
 * @version $Revision$ 2012-6-20
 * <AUTHOR> modification by huangshun)
 * @since 1.0
 */
public class LoadPreferPlugin extends Plugin implements SubmitListener{

	@Override
	public void setValue(String name, String value) {
		
	}
	
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		
		TDLog.log(TDLog.LOG_TYPE_OPERATOR, null, "业务领域选择", null, "业务领域选择"+bus.getList("cFieldName"), null);
		DbSvr db = DbSvr.getDbService(null);
		List<Map> prefers = db.queryIdForList("account_trans.loadPrefer", bus);
		form.updateWidgetValue("preferCode", getPreferCodes(prefers));
		Layout l = getPreferLayout(bus, getConfigList(prefers));
		if(!l.isEmpty()){
			l.setHeight("250");
			l.setWidth("650");
			form.updateLayout("layout.config", l, bus);
		}
	}

	@SuppressWarnings("unchecked")
	public Layout getPreferLayout(DataMsgBus bus, List<Map> domains){
		Layout tabLayout = new TabLayout();
		if(CollectionUtils.isEmpty(domains))
			return tabLayout;
		tabLayout.setName("configTab");
		for (Map domain: domains){
			TableLayout tableLayout = new TableLayout();
			tableLayout.setGroupSize(1);
			tableLayout.setDesp(domain.get("cDomainName").toString());
			List<Map> modules = (List<Map>)domain.get("cModule");
			for (Map module:modules){
				TitleLayout titleLayout = new TitleLayout();
				titleLayout.setWidth("600px");
				titleLayout.setLeftSpaceWidth("8px");
				titleLayout.setTopSpaceWidth("8px");
				titleLayout.setDesp(module.get("cModuleName").toString());
				List<Map> configs = (List<Map>)module.get("cConfig");
				TableLayout configContainer = new TableLayout();
				configContainer.setWidth("600px");
				configContainer.setGroupSize(3);
				configContainer.setCellspacing(15);
				for (Map config:configs){
					BasicWidget basicwidget = UICreator.createBasicWidget(bus.getForm().getId(), config.get("notelabel").toString());
					if (config.get("isvalid").toString().equals("0")){
						basicwidget.setDisabled(true);
					}
					basicwidget.setDefaultValue(MapUtils.getString(config, "defvalue"));
					configContainer.addEntry(basicwidget);
				}
				titleLayout.addEntry(configContainer);
				tableLayout.addEntry(titleLayout);
			}
			tabLayout.addEntry(tableLayout);
		}
		return tabLayout;
	}

	@SuppressWarnings("unchecked")
	public List<Map> getTestCofig(){
		List<Map> testConfig = new ArrayList<Map>();
		List<Map> testModule = new ArrayList<Map>();
		List<Map> testDomain = new ArrayList<Map>();
		
		Map config1 = new HashMap();
		config1.put("notelabel", "<basic name=\"A6_BM_LOANBILL\" linkToState=\"1,2\" label=\"借款单\" attr=\"truevalue:y;falsevalue:n\" widget=\"TrueFalseBox\"/>");
		config1.put("realvalue", "y");
		Map config2 = new HashMap();
		config2.put("notelabel", "<basic name=\"A6_BM_LOANBILL\" linkToState=\"1,2\" label=\"借款单1\" attr=\"truevalue:y;falsevalue:n\" widget=\"TrueFalseBox\"/>");
		config2.put("realvalue", "y");
		Map config3 = new HashMap();
		config3.put("notelabel", "<basic name=\"A6_BM_LOANBILL\" linkToState=\"1,2\" label=\"借款单1\" attr=\"truevalue:y;falsevalue:n\" widget=\"TrueFalseBox\"/>");
		config3.put("realvalue", "y");
		testConfig.add(config1);
		testConfig.add(config2);
		testConfig.add(config3);
		
		Map module1 = new HashMap();
		module1.put("cModuleName", "模块一");
		module1.put("cConfig", testConfig);
		
		Map module2 = new HashMap();
		module2.put("cModuleName", "模块二");
		module2.put("cConfig", testConfig);
		
		Map module3 = new HashMap();
		module3.put("cModuleName", "模块三");
		module3.put("cConfig", testConfig);
		
		testModule.add(module1);
		testModule.add(module2);
		testModule.add(module3);
		
		Map domain1 = new HashMap();
		domain1.put("cDomainName", "通用");
		domain1.put("cModule", testModule);
		Map domain2 = new HashMap();
		domain2.put("cDomainName", "财务");
		domain2.put("cModule", testModule);
		
		testDomain.add(domain1);
		testDomain.add(domain2);
		
		return testDomain;
	}

	@SuppressWarnings("unchecked")
	private String getPreferCodes(List<Map> prefers) {
		if(CollectionUtils.isEmpty(prefers))
			return null;
		StringBuffer codes = new StringBuffer();
		for (Map prefer:prefers){
			codes.append(prefer.get("code")).append(",");
		}
		return codes.toString();
	}

	@SuppressWarnings("unchecked")
	private List<Map> getConfigList(List<Map> prefers) {
		if(CollectionUtils.isEmpty(prefers))
			return null;
		List<Map> domains = new ArrayList<Map>();
		Map fieldMap = ListMapUtils.groupListMap(prefers, "cDomainName");
		for (Iterator iter=fieldMap.entrySet().iterator();iter.hasNext();){
			Map.Entry entry = (Map.Entry)iter.next();
			List<Map> domain = (List<Map>)entry.getValue();
			Map moduleMap = ListMapUtils.groupListMap(domain, "cModuleName");
			List<Map> moduleList = new ArrayList<Map>();
			for (Iterator iter1 = moduleMap.entrySet().iterator();iter1.hasNext();){
				Map.Entry entry1 = (Map.Entry)iter1.next();
				Map module = new LinkedMap();
				module.put("cModuleName", entry1.getKey());
				module.put("cConfig", entry1.getValue());
				moduleList.add(module);
			}
			Map domainrow = new LinkedMap();
			domainrow.put("cDomainName", entry.getKey());
			domainrow.put("cModule", moduleList);
			domains.add(domainrow);
		}
		return domains;
	}
	
}
