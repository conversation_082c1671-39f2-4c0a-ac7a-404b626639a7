package com.aisino.account.db.trans.plugin;

import java.util.ArrayList;
import java.util.List;

import com.aisino.account.db.trans.util.TDLog;
import com.aisino.platform.core.Plugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.db.Eso;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.SubmitListener;

public class SavePreferPlugin extends Plugin implements SubmitListener {

	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		if (bus.getString("preferCode")==null){
			return;
		}
		String[] preferCodes = bus.getString("preferCode").split(",");
		DbSvr db = DbSvr.getDbService(null);
		List<Eso> esos = new ArrayList<Eso>();
		StringBuffer preferLog = new StringBuffer();
		for (String prefer: preferCodes){
			esos.add(new Eso("update CO_TDParamCfg set realvalue=? where code=?", new Object[]{bus.getString(prefer), prefer}));
			preferLog.append(" ").append(prefer).append(":").append(bus.getString(prefer)).append(",");
		}
		preferLog.deleteCharAt(preferLog.length()-1);
		TDLog.log(TDLog.LOG_TYPE_OPERATOR, null, "业务参数设置", null, "参数保存:"+preferLog.toString(), null);
		db.executeAllEso(esos);
	}

	@Override
	public void setValue(String name, String value) {
		
	}

}
