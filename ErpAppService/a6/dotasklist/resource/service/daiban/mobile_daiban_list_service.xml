<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE Services SYSTEM "service_definition.dtd" >
<Services>
<!-- 待办分类（借款报销EM、协同办公OA、生产审批MA、财务审批FI、采购审批PU、销售审批SA、库存审批ST） -->
<reg on="App.DaibanList.EM"	
	class = "com.aisino.app.a6.web.dotasklist.daiban.service.DaibanEMListService"
    desp = "待办借款报销单列表查询"
/>

<reg on="App.DaibanList.OA"	
	class = "com.aisino.app.a6.web.dotasklist.daiban.service.DaibanOAListService"
    desp = "协同办公列表查询"
/>

<reg on="App.DaibanList.MA"	
	class = ""
    desp = ""
/>


<!-- 财务审批待办列表服务 -->

<reg on="App.DaibanList.FI"	
	class = "com.aisino.app.a6.web.dotasklist.daiban.service.DaibanFIListService"
    desp = "待办财务列表查询"
/>


<!-- 采购审批待办列表服务 -->
<!-- list格式：cguid,cbilltype,cbillcode,title,submitter,ddate -->
<reg on="App.DaibanList.PU"	
	class = "com.aisino.app.a6.web.dotasklist.daiban.service.DaibanPUListService"
    desp = "待办采购列表查询"
/>

<!-- 销售审批待办列表服务 -->
<!-- list格式：cguid,cbilltype,cbillcode,title,submitter,ddate -->
<reg on="App.DaibanList.SA"	
	class = "com.aisino.app.a6.web.dotasklist.daiban.service.DaibanSAListService"
    desp = "待办销售列表查询"
/>


<!-- 库存审批待办列表服务 -->
<!-- list格式：cguid,cbilltype,cbillcode,title,submitter,ddate -->
 <reg on="App.DaibanList.ST"	                       
	class = "com.aisino.app.a6.web.dotasklist.daiban.service.DaibanSTListService"
    desp = "待办库存列表查询"
/>
 
</Services>