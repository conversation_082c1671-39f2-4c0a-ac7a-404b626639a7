<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE Services SYSTEM "service_definition.dtd" >
<Services>
<!-- 已办分类（借款报销EM、协同办公OA、生产审批MA、财务审批FI、采购审批PU、销售审批SA、库存审批ST） -->
<reg on="App.YibanList.EM"	
	class = "com.aisino.app.a6.web.dotasklist.yiban.service.YibanEMListService"
    desp = "已办借款报销单列表查询"
/>

<reg on="App.YibanList.OA"	
	class = "com.aisino.app.a6.web.dotasklist.yiban.service.YibanOAListService"
    desp = "已办协同办公列表"
/>

<reg on="App.YibanList.MA"	
	class = ""
    desp = ""
/>

<!-- 财务审批已办列表服务 -->

<reg on="App.YibanList.FI"	
	class = "com.aisino.app.a6.web.dotasklist.yiban.service.YibanFIListService"
    desp = "已办财务列表查询"
/>


<!-- 采购审批已办列表服务 -->
<!-- list格式：cguid,cbilltype,cbillcode,title,submitter,ddate -->
<reg on="App.YibanList.PU"	
	class = "com.aisino.app.a6.web.dotasklist.yiban.service.YibanPUListService"
    desp = "已办采购列表查询"
/>

<!-- 销售审批已办列表服务 -->
<!-- list格式：cguid,cbilltype,cbillcode,title,submitter,ddate -->
<reg on="App.YibanList.SA"	
	class = "com.aisino.app.a6.web.dotasklist.yiban.service.YibanSAListService"
    desp = "已办销售列表查询"
/>


<!-- 库存审批已办列表服务 -->
 <reg on="App.YibanList.ST"	                       
	class = "com.aisino.app.a6.web.dotasklist.yiban.service.YibanSTListService"
    desp = "已办库存列表查询"
/> 

</Services>