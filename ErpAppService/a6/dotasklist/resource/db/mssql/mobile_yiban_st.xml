<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
  	<sql group="mobile_yiban_stlist">
  		
		<i id="getallstlist">
			<![CDATA[  		        
				select top 100 * from (
					SELECT 
						st.cguid   as hidden_cguid, 
						st.cbillcode,
						pl.crealname as tjr,
						st.cCheckWay as  hidden_ccheckway,
						'库存调拨单'+st.cbillcode  as bt,
						'072' as hidden_cbilltype,
						'' as bm,
						'' as kh,
						'' as ywy,
						'' as ywlx,
						'' as ywlc,
						CONVERT(varchar(100),st.dCheckTime,120) as blsj
					FROM ST_StkTrans st
					LEFT JOIN AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
					WHERE st.iAuditStatus = 'checked' 
					AND st.cCheckWay='0'
					AND st.cAuditorGUID={currUserGUID}
					AND (CONVERT(varchar(100),st.dCheckTime,23)={today} or '*' = {today:'*'})
					AND ('*' = {keyword:'*'} or '库存调拨单' like '%{keyword}%')		       
					
				UNION ALL
					
					SELECT 
						st.cguid as hidden_cguid, 
						st.cbillcode,
						pl.crealname as tjr,
						'2' as hidden_cCheckWay,
						'库存调拨单'+st.cbillcode  as bt,
						'072' as hidden_cbilltype,
						'' as bm,
						'' as kh,
						'' as ywy,
						'' as ywlx,
						'' as ywlc,
						CONVERT(varchar(100),b.END_,120) as blsj
					FROM ST_StkTrans st
					LEFT JOIN AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
					LEFT JOIN JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
					LEFT JOIN JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
					INNER JOIN(
						SELECT a.DBID_,
							case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
							case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
						FROM JBPM4_HIST_TASK a 
						LEFT JOIN (
							SELECT TIME_,HTASK_,USERID_
							FROM JBPM4_HIST_DETAIL
							WHERE USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
						) DETAIL on DETAIL.HTASK_ = a.DBID_
						WHERE
						/*对同一实例已完成任务过滤:取最近完成的任务*/
						a.DBID_ in (
				               SELECT max(t.dbid_)  
							   FROM JBPM4_HIST_TASK t
							   INNER JOIN JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
							   WHERE ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
							   		and d.CLASS_='status'
							   		and d.NEW_STR_ !='cancel'
							   		and d.NEW_STR_ !='retreat'
							   		and d.NEW_STR_ !='delegate'
							   		and d.NEW_STR_ !='rollback-type'
							   		and d.NEW_STR_ !='jump-type'
							   		and d.NEW_STR_!='rollback-target'
							   		and d.NEW_STR_ !='delegated'
							   GROUP BY t.execution_
						)
					) b on b.actTask = c.HTASK_
					LEFT JOIN WF_Process_Variable v on e.ID_ = v.procInstId
					LEFT JOIN WF_ProcessInfo h on v.processTemplate = h.cGUID
					LEFT JOIN WF_Process pr on pr.cguid = h.cNodeTemplate 
					WHERE pr.cForm ='business_st_StTrans'
						AND (CONVERT(varchar(100),b.END_,23) = {today} or '*' = {today:'*'})
						AND ('*' = {keyword:'*'} or '库存调拨单' like '%{keyword}%')
						AND st.cCheckWay = '2'
										
				UNION ALL
					
					SELECT 
						st.cguid as hidden_cguid, 
						st.cbillcode,
						pl.crealname as tjr,
						st.cCheckWay as  hidden_ccheckway,
						CASE 
							WHEN st.cBillType= '070' THEN '材料出库单' + st.cbillcode
							WHEN st.cBillType= '020' THEN '销售出库单' + st.cbillcode
							WHEN st.cBillType= '021' THEN '期初销售出库单' + st.cbillcode
						END AS bt,
						st.cBillType as hidden_cbilltype,
						de.cname as bm,
						cu.cName as kh,
						em.cName as ywy,
						bustp.cName as ywlx,
						buspr.cName as ywlc,
						CONVERT(varchar(100),st.dCheckTime,120) as blsj
					FROM ST_StkRecord st
					LEFT JOIN ST_StkRecordLine sl ON sl.cHeadGUID = st.cGUID
					LEFT JOIN AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
					LEFT JOIN CM_Customer cu ON cu.cGUID = st.cCustGUID
					LEFT JOIN CM_Department de ON de.cGUID = st.cDeptGUID
					LEFT JOIN CM_Employee em ON em.cGUID = st.cEmpGUID
					LEFT JOIN CO_BusinessType bustp ON bustp.cGUID = st.cBusType
					LEFT JOIN CO_BusinessProcess buspr ON buspr.cGUID = st.cBusProcess
					WHERE st.iAuditStatus = 'checked' 
					and exists ( select 1 from ST_StkRecordLine a 
								inner join cm_material ma on ma.cguid = a.cmatguid
								inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
								where a.cHeadGUID = st.cGUID )
					AND st.cCheckWay='0'
					AND st.cAuditorGUID={currUserGUID}
					AND (CONVERT(varchar(100),st.dCheckTime,23)={today} or '*' = {today:'*'})
					AND ('*' = {keyword:'*'} or '材料出库单' like '%{keyword}%' or '销售出库单' like '%{keyword}%')
				
				UNION ALL
					
					SELECT 
						st.cguid as hidden_cguid, 
						st.cbillcode,
						pl.crealname as tjr,
						'2' as hidden_cCheckWay,
						'销售出库单'+st.cbillcode  as bt,
						st.cBillType as hidden_cbilltype,
						de.cname as bm,
						cu.cName as kh,
						em.cName as ywy,
						bustp.cName as ywlx,
						buspr.cName as ywlc,
						CONVERT(varchar(100),b.END_,120) as blsj
					FROM ST_StkRecord st
					LEFT JOIN ST_StkRecordLine sl ON sl.cHeadGUID = st.cGUID
					LEFT JOIN AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
					LEFT JOIN CM_Customer cu ON cu.cGUID = st.cCustGUID
					LEFT JOIN CM_Department de ON de.cGUID = st.cDeptGUID
					LEFT JOIN CM_Employee em ON em.cGUID = st.cEmpGUID
					LEFT JOIN CO_BusinessType bustp ON bustp.cGUID = st.cBusType
					LEFT JOIN CO_BusinessProcess buspr ON buspr.cGUID = st.cBusProcess
					LEFT JOIN JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
					LEFT JOIN JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
					INNER JOIN(
						SELECT a.DBID_,
							case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
							case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
						FROM JBPM4_HIST_TASK a 
						LEFT JOIN (
							SELECT TIME_,HTASK_,USERID_
							FROM JBPM4_HIST_DETAIL
							WHERE USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
						) DETAIL on DETAIL.HTASK_ = a.DBID_
						WHERE
						/*对同一实例已完成任务过滤:取最近完成的任务*/
						a.DBID_ in (
				               SELECT max(t.dbid_)  
							   FROM JBPM4_HIST_TASK t
							   INNER JOIN JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
							   WHERE ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
							   		and d.CLASS_='status'
							   		and d.NEW_STR_ !='cancel'
							   		and d.NEW_STR_ !='retreat'
							   		and d.NEW_STR_ !='delegate'
							   		and d.NEW_STR_ !='rollback-type'
							   		and d.NEW_STR_ !='jump-type'
							   		and d.NEW_STR_!='rollback-target'
							   		and d.NEW_STR_ !='delegated'
							   GROUP BY t.execution_
						)
					) b on b.actTask = c.HTASK_
					LEFT JOIN WF_Process_Variable v on e.ID_ = v.procInstId
					LEFT JOIN WF_ProcessInfo h on v.processTemplate = h.cGUID
					LEFT JOIN WF_Process pr on pr.cguid = h.cNodeTemplate 
					WHERE pr.cForm ='business_sa_sastkout'
					    and exists ( select 1 from ST_StkRecordLine a 
								inner join cm_material ma on ma.cguid = a.cmatguid
								inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
								where a.cHeadGUID = st.cGUID )
						AND (CONVERT(varchar(100),b.END_,23) = {today} or '*' = {today:'*'})
						AND ('*' = {keyword:'*'} or '销售出库单' like '%{keyword}%')
						AND st.cCheckWay = '2'
					
				UNION ALL
					
					SELECT 
						st.cguid as hidden_cguid, 
						st.cbillcode,
						pl.crealname as tjr,
						'2' as hidden_cCheckWay,
						'材料出库单'+st.cbillcode as bt,
						st.cBillType as hidden_cbilltype,
						de.cname as bm,
						cu.cName as kh,
						em.cName as ywy,
						bustp.cName as ywlx,
						buspr.cName as ywlc,
						CONVERT(varchar(100),b.END_,120) as blsj
					FROM ST_StkRecord st
					LEFT JOIN ST_StkRecordLine sl ON sl.cHeadGUID = st.cGUID
					LEFT JOIN AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
					LEFT JOIN CM_Customer cu ON cu.cGUID = st.cCustGUID
					LEFT JOIN CM_Department de ON de.cGUID = st.cDeptGUID
					LEFT JOIN CM_Employee em ON em.cGUID = st.cEmpGUID
					LEFT JOIN CO_BusinessType bustp ON bustp.cGUID = st.cBusType
					LEFT JOIN CO_BusinessProcess buspr ON buspr.cGUID = st.cBusProcess
					LEFT JOIN JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
					LEFT JOIN JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
					INNER JOIN(
						SELECT a.DBID_,
							case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
							case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
						FROM JBPM4_HIST_TASK a 
						LEFT JOIN (
							SELECT TIME_,HTASK_,USERID_
							FROM JBPM4_HIST_DETAIL
							WHERE USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
						) DETAIL on DETAIL.HTASK_ = a.DBID_
						WHERE
						/*对同一实例已完成任务过滤:取最近完成的任务*/
						a.DBID_ in (
				               SELECT max(t.dbid_)  
							   FROM JBPM4_HIST_TASK t
							   INNER JOIN JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
							   WHERE ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
							   		and d.CLASS_='status'
							   		and d.NEW_STR_ !='cancel'
							   		and d.NEW_STR_ !='retreat'
							   		and d.NEW_STR_ !='delegate'
							   		and d.NEW_STR_ !='rollback-type'
							   		and d.NEW_STR_ !='jump-type'
							   		and d.NEW_STR_!='rollback-target'
							   		and d.NEW_STR_ !='delegated'
							   GROUP BY t.execution_
						)
					) b on b.actTask = c.HTASK_
					LEFT JOIN WF_Process_Variable v on e.ID_ = v.procInstId
					LEFT JOIN WF_ProcessInfo h on v.processTemplate = h.cGUID
					LEFT JOIN WF_Process pr on pr.cguid = h.cNodeTemplate 
					WHERE pr.cForm ='business_st_stinout_clckd'
					    and exists ( select 1 from ST_StkRecordLine a 
								inner join cm_material ma on ma.cguid = a.cmatguid
								inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
								where a.cHeadGUID = st.cGUID )
						AND (CONVERT(varchar(100),b.END_,23) = {today} or '*' = {today:'*'})
						AND ('*' = {keyword:'*'} or '材料出库单' like '%{keyword}%')
						AND st.cCheckWay = '2'
					
					@filter[STDateDataRightSetBeforeQueryPlugin_feild,
	            ST_StkTrans.cCreatorGUID=AOS_RMS_USER.cguid,
	            ST_StkTransLine.cMatGUID=CM_Material.cguid,
	            ST_StkTransLine.cItemGUID=CM_Project.cguid,
	            ST_StkTransLine.cMatGUID=CM_Material.cguid and CM_Material.cMatCGUID=CM_MatClass.cGUID,
	            
	        	ST_StkRecordLine.cMatGUID=CM_Material.cguid and CM_Material.cMatCGUID=CM_MatClass.cGUID,
	        	ST_StkRecordLine.cMatGUID=CM_Material.cguid,
	        	ST_StkRecordLine.cItemGUID=CM_Project.cguid,
	        	ST_StkRecord.cStoreGUID=CM_Storehouse.cguid,
	        	ST_StkRecordLine.cStoreGUID=CM_Storehouse.cguid,
	        	ST_StkRecord.cCreatorGUID=AOS_RMS_USER.cguid,
	        	st_stkrecord.cEmpGUID=CM_Employee.cGUID,
	        	st_stkrecord.cdeptguid=CM_Department.cguid,
	            st_stkrecord.cCustGUID=CM_Customer.cGUID,
	            st_stkrecord.cCustGUID=CM_Customer.cGUID and cm_customer.cclassguid=cm_customerclass.cguid,
	            ST_StkRecord.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
	            ST_StkRecord.cSupGUID=cm_supplier.cguid
	            
	            
	        	
			]
				)sa
					ORDER BY blsj desc,cbillcode  desc
			]]>						
		</i>
			
		<i id="getallstnum">
			<![CDATA[  		
				SELECT sum(t.num) as num from(
					SELECT count(1) num
					FROM ST_StkTrans st
					WHERE iAuditStatus='checked'
					AND  st.cCheckWay='0'
					AND st.cAuditorGUID={currUserGUID}
					AND CONVERT(char(10),st.dCheckTime,23)=CONVERT(varchar(100),getdate(),23)
	        
				UNION ALL
	             
					SELECT count(1) num 
					from ST_StkTrans st
					left join JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
					left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
					inner join(
						select a.DBID_,
							case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
							case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
						from JBPM4_HIST_TASK a 
						left join (
							select TIME_,HTASK_,USERID_
							from JBPM4_HIST_DETAIL
							where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
						) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		     		where
						/*对同一实例已完成任务过滤:取最近完成的任务*/
						a.DBID_ in (
							select max(t.dbid_)  
							from JBPM4_HIST_TASK t
							inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
							where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
							and d.CLASS_='status'
							and d.NEW_STR_ !='cancel'
							and d.NEW_STR_ !='retreat'
							and d.NEW_STR_ !='delegate'
							and d.NEW_STR_ !='rollback-type'
							and d.NEW_STR_ !='jump-type'
							and d.NEW_STR_!='rollback-target'
							and d.NEW_STR_ !='delegated'
							group by t.execution_
						)
					) b on b.actTask = c.HTASK_
					left join WF_Process_Variable v on e.ID_ = v.procInstId
					left join WF_ProcessInfo h on v.processTemplate = h.cGUID
					left join WF_Process pr on pr.cguid = h.cNodeTemplate 
					where pr.cForm ='business_st_StTrans'
						and CONVERT(char(10),b.END_,120)=CONVERT(varchar(100),getdate(),23)
						and st.cCheckWay = '2'
						
				UNION ALL	
					
					SELECT count(1) num
					FROM ST_StkRecord st
					WHERE iAuditStatus='checked'
					AND  st.cCheckWay='0'
					AND st.cAuditorGUID={currUserGUID}
					AND CONVERT(char(10),st.dCheckTime,23)=CONVERT(varchar(100),getdate(),23)
				
				UNION ALL
				
					SELECT count(1) num 
					from ST_StkRecord st
					left join JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
					left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
					inner join(
						select a.DBID_,
							case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
							case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
						from JBPM4_HIST_TASK a 
						left join (
							select TIME_,HTASK_,USERID_
							from JBPM4_HIST_DETAIL
							where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
						) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		     		where
						/*对同一实例已完成任务过滤:取最近完成的任务*/
						a.DBID_ in (
							select max(t.dbid_)  
							from JBPM4_HIST_TASK t
							inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
							where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
							and d.CLASS_='status'
							and d.NEW_STR_ !='cancel'
							and d.NEW_STR_ !='retreat'
							and d.NEW_STR_ !='delegate'
							and d.NEW_STR_ !='rollback-type'
							and d.NEW_STR_ !='jump-type'
							and d.NEW_STR_!='rollback-target'
							and d.NEW_STR_ !='delegated'
							group by t.execution_
						)
					) b on b.actTask = c.HTASK_
					left join WF_Process_Variable v on e.ID_ = v.procInstId
					left join WF_ProcessInfo h on v.processTemplate = h.cGUID
					left join WF_Process pr on pr.cguid = h.cNodeTemplate 
					where CONVERT(char(10),b.END_,120)=CONVERT(varchar(100),getdate(),23)
						and (pr.cForm ='business_st_stinout_clckd' or pr.cForm='business_sa_sastkout')
						and st.cCheckWay = '2'				
				)t
					@filter[STDateDataRightSetBeforeQueryPlugin_feild,
				        ST_StkTrans.cCreatorGUID=AOS_RMS_USER.cguid,
				        ST_StkTransLine.cMatGUID=CM_Material.cguid,
				      	ST_StkTransLine.cItemGUID=CM_Project.cguid,
				        ST_StkTransLine.cMatGUID=CM_Material.cguid and CM_MatClass.cGUID=CM_Material.cMatCGUID,
				        ST_StkRecordLine.cMatGUID=CM_Material.cguid and CM_MatClass.cGUID=CM_Material.cMatCGUID,
				        ST_StkRecordLine.cItemGUID=CM_Project.cguid,
				        ST_StkRecordLine.cMatGUID=CM_Material.cguid,
				        ST_StkRecordLine.cStoreGUID=CM_Storehouse.cguid,
				        ST_StkRecord.cCreatorGUID=AOS_RMS_USER.cguid
			        ]
			]]>
		</i>
	</sql>
	
</sqls>