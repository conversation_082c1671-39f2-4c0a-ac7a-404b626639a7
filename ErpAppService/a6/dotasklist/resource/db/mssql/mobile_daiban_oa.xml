<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<!-- 增加协同办公sql文件 -->
	<sql group="mobile_daiban_oaList">
		<!-- 2018-01-05 quchj 修正日期查询条件为YYMMDD -->
		<i id="getAllList" desp="获取所有列表数据">
			<![CDATA[
				select top 100 *
				from(
					select wf.cguid as hidden_cguid,'2' as hidden_cCheckWay,'oa_zdy' as hidden_cbilltype,wf.CNAME as bt,
					 wf.cBillStatus as hidden_iauditstatus,
						CONVERT(varchar(100),e.START_,120) as sqsj,v.ownerName as czy
					from WF_Form[-filter] wf
					left join AOS_RMS_USER u on u.cguid = wf.CCREATOR
					left join JBPM4_EXECUTION d on d.KEY_ = wf.cguid
					left join WF_Process_Variable v on d.ID_= v.procInstId 
					inner join(
		                select distinct a.PROCINST_,a.CREATE_ as  START_
		                from JBPM4_TASK a
		                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
						where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
						  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
						  and a.DBID_ NOT IN
						   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
					) e on e.PROCINST_ = d.INSTANCE_
					where 'workFlow' = {spqxZdy:''} 
					and ($like(u.cRealName,keyword)
						or (CONVERT(varchar(100),e.START_,112) like '%{keyword}%')
						or ('*' = {keyword:'*'} or wf.CNAME like '%{keyword}%')
					)
					union
					select wf.cguid as hidden_cguid,'2' as hidden_cCheckWay,'oa_zdy' as hidden_cbilltype,wf.CNAME as bt,
					 wf.cBillStatus as hidden_iauditstatus,
						CONVERT(varchar(100),e.START_,120) as sqsj,v.ownerName as czy
					from WF_Form wf
					left join v_oa_eFormUserInfo u on u.cguid = wf.CCREATOR
					left join JBPM4_EXECUTION d on d.KEY_ = wf.cguid
					left join WF_Process_Variable v on d.ID_= v.procInstId
					inner join(
		                select distinct a.PROCINST_,a.CREATE_ as  START_
		                from JBPM4_TASK a
		                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
						where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
						  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
						  and a.DBID_ NOT IN
						   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
					) e on e.PROCINST_ = d.INSTANCE_
					where 'workFlowSjqx' = {spqxZdy:''} 
					and ($like(u.cRealName,keyword)
						or (CONVERT(varchar(100),e.START_,112) like '%{keyword}%')
						or ('*' = {keyword:'*'} or wf.CNAME like '%{keyword}%')
					)
					@filter[WF_Form.CCREATOR=v_oa_eFormUserInfo.cguid]
				)oa
				order by oa.sqsj desc
			]]>
		</i>
		
		<i id="getAllListNum" desp="获取所有列表的总数">
			<![CDATA[
				select sum(oa.num) as num
				from(
					select count(1) num
					from WF_Form[-filter] wf
					left join AOS_RMS_USER u on u.cguid = wf.CCREATOR
					left join JBPM4_EXECUTION d on d.KEY_ = wf.cguid
					inner join(
		                select distinct a.PROCINST_
		                from JBPM4_TASK a
		                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
						where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
						  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
						  and a.DBID_ NOT IN
						   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
					) e on e.PROCINST_ = d.INSTANCE_
					where 'workFlow' = {spqxZdy:''} 
					union
					select count(1) num
					from WF_Form wf
					left join v_oa_eFormUserInfo u on u.cguid = wf.CCREATOR
					left join JBPM4_EXECUTION d on d.KEY_ = wf.cguid
					inner join(
		                select distinct a.PROCINST_
		                from JBPM4_TASK a
		                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
						where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
						  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
						  and a.DBID_ NOT IN
						   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
					) e on e.PROCINST_ = d.INSTANCE_
					where 'workFlowSjqx' = {spqxZdy:''} 
					@filter[WF_Form.CCREATOR=v_oa_eFormUserInfo.cguid]
				)oa
			]]>
		</i>
	</sql>
	
	<sql group="mobile_daiban_oaBill" desp="协同办公单据加载">
		<i id="loadZdyMain" desp="自定义表单主表信息">
			<![CDATA[
				select wf.CNAME as '标题'
				from WF_Form wf
				where wf.cguid={cGUID}
			]]>
		</i>
		<i id="getWfFiles" desp="获取自定义表单附件">
			<![CDATA[
				select cguid+'_'+cName as cFileName,'uploads' as cFileUrl,cname as cFileRealName
				from WF_Attachment
				where CFORMGUID = ? 
				and CSTEP='first'
				and CASSOCIATEDGUID is NULL
			]]>
		</i>
	</sql>
</sqls>