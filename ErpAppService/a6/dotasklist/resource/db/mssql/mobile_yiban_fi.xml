<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
  	<sql group="mobile_yiban_filist">
  	
  	
  	<i id="getallfilist">
  	    select top 100 * from (
  	select 
            	
                st.cguid as hidden_cguid, 
				  st.cvoucode,
                pl.crealname as hidden_cCreator,
               st.cCheckWay as  hidden_ccheckway,                
               '082' as hidden_cbilltype,
                '付款单'+st.cVouCode as bt,
                 CONVERT(varchar(50),st.dAuditDate,120)  as blsj,
                pl.crealname as "sqr",
				null as "ywy",
                cd.cname  as "bm",
                 convert(nvarchar, cast(round( st.iPayAMT ,2) as money),1)  as je,
                cs.cname as "gys"
                
		        from AP_Payment st
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Supplier cs on cs.cguid=st.cSupGUID
                where st.iAuditStatus = 'checked' 
                and st.cCheckWay='0'
                and st.cflag='AP'
                and st.cAuditGUID={currUserGUID}
                and (( convert(varchar(50),st.dAuditDate,23)={today}) or '*' = {today:'*'})
		        and ('*' = {keyword:'*'} or '付款单' like '%{keyword}%')
	union all         
		        select 
            	  st.cguid as hidden_cguid, 
				  st.cvoucode,
                pl.crealname as hidden_cCreator,
               st.cCheckWay as  hidden_ccheckway,                
               '082' as hidden_cbilltype,
                '付款单'+st.cVouCode as bt,
                CONVERT(varchar(50),b.END_,120)  as blsj,
                pl.crealname as "sqr",
				null as "ywy",
                cd.cname  as "bm",
                 convert(nvarchar, cast(round( st.iPayAMT ,2) as money),1)  as je,
                cs.cname as "gys"
		        from AP_Payment st
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Supplier cs on cs.cguid=st.cSupGUID
		        left join JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
	left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
	inner join(
		select a.DBID_,
			case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
			case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
		from JBPM4_HIST_TASK a 
		left join (
			select TIME_,HTASK_,USERID_
	        from JBPM4_HIST_DETAIL
	        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
	     ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		where
		/*对同一实例已完成任务过滤:取最近完成的任务*/
		a.DBID_ in (
               select max(t.dbid_)  
			   from JBPM4_HIST_TASK t
			   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
			   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
			   group by t.execution_
                 )
	) b on b.actTask = c.HTASK_
	left join WF_Process_Variable v on e.ID_ = v.procInstId
	left join WF_ProcessInfo h on v.processTemplate = h.cGUID
	left join WF_Process pr on pr.cguid = h.cNodeTemplate 
	where pr.cForm ='bus_ap_bill_form'
	and st.cflag='AP'
	and (CONVERT(char(10),b.END_,120)={today} or '*' = {today:'*'})
	and st.cCheckWay = '2'
    and ('*' = {keyword:'*'} or '付款单' like '%{keyword}%')                
      union all
  	
  	   select 
            	
                  st.cguid as hidden_cguid, 
				  st.cvoucode,
                pl.crealname as hidden_cCreator,
               st.cCheckWay as  hidden_ccheckway,                
               '105' as hidden_cbilltype,
                 '其它付款单'+st.cVouCode as bt,
                CONVERT(varchar(50),st.dAuditDate,120) as  blsj,
                null  as "sqr",
				 ce.cname as "ywy",
                cd.cname  as "bm",
                 convert(nvarchar, cast(round( st.iPayAMT ,2) as money),1)  as je,
                null as "gys"
                
		        from CA_OtherPayment st
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Employee ce on st.cEmpGUID=ce.cguid
                where st.iAuditStatus = 'checked' 
                and st.cCheckWay='0'
                 and  st.cflag='P'
                 and st.cAuditGUID={currUserGUID}
		         and ( convert(varchar(50),st.dAuditDate,23)={today}  or '*' = {today:'*'})
		         and ('*' = {keyword:'*'} or '其它付款单' like '%{keyword}%')
		         union all
	select 
            	
                 st.cguid as hidden_cguid, 
				  st.cvoucode,
                pl.crealname as hidden_cCreator,
               st.cCheckWay as  hidden_ccheckway,                
               '105' as hidden_cbilltype,
                 '其它付款单'+st.cVouCode as bt,
                CONVERT(varchar(50),b.END_,120)  as blsj,
                null  as "sqr",
				 ce.cname as "ywy",
                cd.cname  as "bm",
                 convert(nvarchar, cast(round( st.iPayAMT ,2) as money),1)  as je,
                null as "gys"
		        from CA_OtherPayment st
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Employee ce on st.cEmpGUID=ce.cguid
	left join JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
	left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
	inner join(
		select a.DBID_,
			case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
			case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
		from JBPM4_HIST_TASK a 
		left join (
			select TIME_,HTASK_,USERID_
	        from JBPM4_HIST_DETAIL
	        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
	     ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		where
		/*对同一实例已完成任务过滤:取最近完成的任务*/
		a.DBID_ in (
               select max(t.dbid_)  
			   from JBPM4_HIST_TASK t
			   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
			   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
			   group by t.execution_
                 )
	) b on b.actTask = c.HTASK_
	left join WF_Process_Variable v on e.ID_ = v.procInstId
	left join WF_ProcessInfo h on v.processTemplate = h.cGUID
	left join WF_Process pr on pr.cguid = h.cNodeTemplate 
	where pr.cForm ='finance_ca_apvoucher_edit_form'
	  and  st.cflag='P'
	and (CONVERT(char(10),b.END_,120)={today} or '*' = {today:'*'})
	and st.cCheckWay = '2'
    and ('*' = {keyword:'*'} or '其它付款单' like '%{keyword}%')
  
  	   @filter[AP_Payment.cEmpGUID=cm_employee.cGUID,
		       AP_Payment.cSupGUID=cm_supplier.cguid,
		       AP_Payment.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
		       AP_Payment.cAcctGUID=gl_account.cGUID,AP_Payment.cDeptGUID=cm_department.cguid,
		       AP_Payment.cCreatorGUID=AOS_RMS_USER.cguid,AP_Payment.cItemGUID=cm_project.cGUID,
		       AP_Payment.cBankAcctNub=cm_bankline.cguid,
		       aos_rms_user,CA_OtherPayment.cAcctGUID=gl_account.cGUID,CA_OtherPayment.cRBankAcctNub=cm_bankline.cguid]
		  )m
		  
		  order by  blsj desc , cvoucode  desc
		   
  	
  	
  	
  	</i>	
  	<i id="getallfinum">
  	    
  	
  	  select sum(t.num) as num from(	
		 	 select 
  			count(1) num
  	   from AP_Payment ap 
       @table[AP_Payment ap]
  	  where ap.iAuditStatus='checked'
  	    and ap.cflag='AP'
  	    and ap.cCheckWay='0'
  	    and ap.cAuditGUID={currUserGUID}
  	    and CONVERT(char(10),ap.dAuditDate,23)=CONVERT(varchar(100),getdate(),23)
  	   
  union all
	select 
	     count(1) num 
	from AP_Payment ap
	left join JBPM4_HIST_PROCINST e on ap.cguid = e.KEY_
	left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
	inner join(
		select a.DBID_,
			case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
			case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
		from JBPM4_HIST_TASK a 
		left join (
			select TIME_,HTASK_,USERID_
	        from JBPM4_HIST_DETAIL
	        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
	     ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		where
		/*对同一实例已完成任务过滤:取最近完成的任务*/
		a.DBID_ in (
               select max(t.dbid_)  
			   from JBPM4_HIST_TASK t
			   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
			   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
			   group by t.execution_
                 )
	) b on b.actTask = c.HTASK_
	left join WF_Process_Variable v on e.ID_ = v.procInstId
	left join WF_ProcessInfo h on v.processTemplate = h.cGUID
	left join WF_Process pr on pr.cguid = h.cNodeTemplate 
	where pr.cForm ='bus_ap_bill_form'
	and CONVERT(char(10),b.END_,23)=CONVERT(varchar(100),getdate(),23)
	and ap.cCheckWay = '2'	
	and ap.cflag='AP'
	
	
	union all
	
	 select 
  			count(1) num
  	   from CA_OtherPayment cao 
  	  where cao.iAuditStatus='checked'
  	    and cao.cCheckWay='0'
  	    and cao.cAuditGUID={currUserGUID}
  	    and CONVERT(char(10),cao.dAuditDate,23)=CONVERT(varchar(100),getdate(),23)
  	    and  cao.cflag='P'
       union all
	select 
	     count(1) num 
	from CA_OtherPayment cao
	left join JBPM4_HIST_PROCINST e on cao.cguid = e.KEY_
	left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
	inner join(
		select a.DBID_,
			case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
			case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
		from JBPM4_HIST_TASK a 
		left join (
			select TIME_,HTASK_,USERID_
	        from JBPM4_HIST_DETAIL
	        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
	     ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		where
		/*对同一实例已完成任务过滤:取最近完成的任务*/
		a.DBID_ in (
               select max(t.dbid_)  
			   from JBPM4_HIST_TASK t
			   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
			   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
			   group by t.execution_
                 )
	) b on b.actTask = c.HTASK_
	left join WF_Process_Variable v on e.ID_ = v.procInstId
	left join WF_ProcessInfo h on v.processTemplate = h.cGUID
	left join WF_Process pr on pr.cguid = h.cNodeTemplate 
	where pr.cForm ='finance_ca_apvoucher_edit_form'
	and CONVERT(char(10),b.END_,23)=CONVERT(varchar(100),getdate(),23)
	and cao.cCheckWay = '2'
	and  cao.cflag='P'
	 @filter[AP_Payment.cEmpGUID=cm_employee.cGUID,
		       AP_Payment.cSupGUID=cm_supplier.cguid,
		       AP_Payment.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
		       AP_Payment.cAcctGUID=gl_account.cGUID,AP_Payment.cDeptGUID=cm_department.cguid,
		       AP_Payment.cCreatorGUID=AOS_RMS_USER.cguid,AP_Payment.cItemGUID=cm_project.cGUID,
		       AP_Payment.cBankAcctNub=cm_bankline.cguid,
		       aos_rms_user,CA_OtherPayment.cAcctGUID=gl_account.cGUID,CA_OtherPayment.cRBankAcctNub=cm_bankline.cguid]
	
	)t
  	
  	
  	
  	
  	
  	</i>	
  	
  		<i id="appaymentlist">
  		select 
            	distinct st.cguid  as hidden_cguid,
                pl.crealname as hidden_cCreator,
                st.cCheckWay as  hidden_ccheckway,
				'082' as hidden_cbilltype,
                '付款单'+st.cVouCode as "标题",
		          CONVERT(varchar(50),st.dAuditDate,120) as '办理时间', 
                 pl.crealname as "申请人",
                cd.cname  as "申请部门",
                st.iPayAMT as "付款金额",
                cs.cname as "供应商"
		        from AP_Payment st
                left join AP_PaymentDetail stl on stl.cPaymentGUID = st.cGUID 
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Supplier cs on cs.cguid=st.cSupGUID
                where st.iAuditStatus = 'checked' 
                and st.cCheckWay='0'
                and st.cflag='AP'
                and st.cAuditGUID={currUserGUID}
                and ( convert(varchar(50),st.dAuditDate,23)={today})
		        and ( $like(pl.crealname,keyword)
		         or ('*' = {keyword:'*'} or '付款单' like '%{keyword}%'))
		        @filter[AP_Payment.cEmpGUID=cm_employee.cGUID,
		       AP_Payment.cSupGUID=cm_supplier.cguid,
		       AP_Payment.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
		       AP_Payment.cAcctGUID=gl_account.cGUID,AP_Payment.cDeptGUID=cm_department.cguid,
		       AP_Payment.cCreatorGUID=AOS_RMS_USER.cguid,AP_Payment.cItemGUID=cm_project.cGUID,
		       AP_Payment.cBankAcctNub=cm_bankline.cguid]
	union all         
		        select 
            	st.cguid  as hidden_cguid,
                pl.crealname as hidden_cCreator,
                (select  cCheckWay from  BILL_SETTING where cFormId = 'bus_ap_bill_form') as  hidden_ccheckway,
				'082' as hidden_cbilltype,
                '付款单'+st.cVouCode as "标题",
		          CONVERT(varchar(50),st.dAuditDate,120) as '办理时间', 
                pl.crealname as "申请人",
                cd.cname  as "申请部门",
                st.iPayAMT as "付款金额",
                cs.cname as "供应商"
		        from AP_Payment st
                left join AP_PaymentDetail stl on stl.cPaymentGUID = st.cGUID 
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Supplier cs on cs.cguid=st.cSupGUID
		        left join JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
	left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
	inner join(
		select a.DBID_,
			case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
			case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
		from JBPM4_HIST_TASK a 
		left join (
			select TIME_,HTASK_,USERID_
	        from JBPM4_HIST_DETAIL
	        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
	     ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		where
		/*对同一实例已完成任务过滤:取最近完成的任务*/
		a.DBID_ in (
               select max(t.dbid_)  
			   from JBPM4_HIST_TASK t
			   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
			   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
			   group by t.execution_
                 )
	) b on b.actTask = c.HTASK_
	left join WF_Process_Variable v on e.ID_ = v.procInstId
	left join WF_ProcessInfo h on v.processTemplate = h.cGUID
	left join WF_Process pr on pr.cguid = h.cNodeTemplate 
	where pr.cForm ='bus_ap_bill_form'
	and st.cflag='AP'
	and CONVERT(char(10),b.END_,120)={today}
	and st.cCheckWay = '2'
	and ($like(v.ownerName,keyword) or $like(b.END_,keyword) or('*' = {keyword:'*'} or left(v.processName,charindex(',',v.processName)-1) like '%{keyword}%'))
                
  
		</i>		
		<i id="appaymentnum">	
		  select sum(t.num) as num from(	
		 	 select 
  			count(1) num
  	   from AP_Payment ap 
       @table[AP_Payment ap]
  	  where ap.iAuditStatus='checked'
  	    and ap.cflag='AP'
  	    and ap.cCheckWay='0'
  	    and ap.cAuditGUID={currUserGUID}
  	    and CONVERT(char(10),ap.dAuditDate,23)=CONVERT(varchar(100),getdate(),23)
  	    @filter[AP_Payment.cEmpGUID=cm_employee.cGUID,
		       AP_Payment.cSupGUID=cm_supplier.cguid,
		       AP_Payment.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
		       AP_Payment.cAcctGUID=gl_account.cGUID,AP_Payment.cDeptGUID=cm_department.cguid,
		       AP_Payment.cCreatorGUID=AOS_RMS_USER.cguid,AP_Payment.cItemGUID=cm_project.cGUID,
		       AP_Payment.cBankAcctNub=cm_bankline.cguid]
  union all
	select 
	     count(1) num 
	from AP_Payment ap
	left join JBPM4_HIST_PROCINST e on ap.cguid = e.KEY_
	left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
	inner join(
		select a.DBID_,
			case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
			case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
		from JBPM4_HIST_TASK a 
		left join (
			select TIME_,HTASK_,USERID_
	        from JBPM4_HIST_DETAIL
	        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
	     ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		where
		/*对同一实例已完成任务过滤:取最近完成的任务*/
		a.DBID_ in (
               select max(t.dbid_)  
			   from JBPM4_HIST_TASK t
			   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
			   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
			   group by t.execution_
                 )
	) b on b.actTask = c.HTASK_
	left join WF_Process_Variable v on e.ID_ = v.procInstId
	left join WF_ProcessInfo h on v.processTemplate = h.cGUID
	left join WF_Process pr on pr.cguid = h.cNodeTemplate 
	where pr.cForm ='bus_ap_bill_form'
	and CONVERT(char(10),b.END_,23)=CONVERT(varchar(100),getdate(),23)
	and ap.cCheckWay = '2'	
	and ap.cflag='AP'
	)t
		</i>
		
		<i id="caapvoucherlist">
  			select 
            	distinct   st.cguid  as hidden_cguid,
                pl.crealname as hidden_cCreator,
                st.cCheckWay as  hidden_ccheckway,
				 '105' as hidden_cbilltype,
                '其它付款单'+st.cVouCode as "标题",
		       	 CONVERT(varchar(50),st.dAuditDate,120) as '办理时间', 
                 ce.cname as "业务员",
                cd.cname  as "部门",
                st.iPayAMT as "付款金额"
		        from CA_OtherPayment st
                left join CA_OtherPaymentLine stl on stl.cHeadGUID = st.cGUID 
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Employee ce on st.cEmpGUID=ce.cguid
                where st.iAuditStatus = 'checked' 
                and st.cCheckWay='0'
                 and  st.cflag='P'
                 and st.cAuditGUID={currUserGUID}
		         and ( convert(varchar(50),st.dAuditDate,23)={today})
		         and ( $like(pl.crealname,keyword)
		         or ('*' = {keyword:'*'} or '其它付款单' like '%{keyword}%'))
		         @filter[aos_rms_user,CA_OtherPayment.cAcctGUID=gl_account.cGUID,CA_OtherPayment.cRBankAcctNub=cm_bankline.cguid]
		         union all
	select 
            	st.cguid  as hidden_cguid,
                pl.crealname as hidden_cCreator,
                (select  cCheckWay from  BILL_SETTING where cFormId = 'finance_ca_apvoucher_edit_form') as  hidden_ccheckway,
				  '105' as hidden_cbilltype,
                '其它付款单'+st.cVouCode as "标题",
		       	 CONVERT(varchar(50),st.dAuditDate,120) as '办理时间', 
                 ce.cname as "业务员",
                cd.cname  as "部门",
                st.iPayAMT as "付款金额"
		        from CA_OtherPayment st
                left join CA_OtherPaymentLine stl on stl.cHeadGUID = st.cGUID 
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Employee ce on st.cEmpGUID=ce.cguid
	left join JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
	left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
	inner join(
		select a.DBID_,
			case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
			case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
		from JBPM4_HIST_TASK a 
		left join (
			select TIME_,HTASK_,USERID_
	        from JBPM4_HIST_DETAIL
	        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
	     ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		where
		/*对同一实例已完成任务过滤:取最近完成的任务*/
		a.DBID_ in (
               select max(t.dbid_)  
			   from JBPM4_HIST_TASK t
			   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
			   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
			   group by t.execution_
                 )
	) b on b.actTask = c.HTASK_
	left join WF_Process_Variable v on e.ID_ = v.procInstId
	left join WF_ProcessInfo h on v.processTemplate = h.cGUID
	left join WF_Process pr on pr.cguid = h.cNodeTemplate 
	where pr.cForm ='finance_ca_apvoucher_edit_form'
	  and  st.cflag='P'
	and CONVERT(char(10),b.END_,120)={today}
	and st.cCheckWay = '2'
	and ($like(v.ownerName,keyword) or $like(b.END_,keyword) or('*' = {keyword:'*'} or left(v.processName,charindex(',',v.processName)-1) like '%{keyword}%'))
  
		</i>		
		<i id="caapvouchernum">	
		 select sum(t.num) as num from(	
		 	 select 
  			count(1) num
  	   from CA_OtherPayment cao 
       @table[CA_OtherPayment cao]
  	  where cao.iAuditStatus='checked'
  	    and cao.cCheckWay='0'
  	    and cao.cAuditGUID={currUserGUID}
  	    and CONVERT(char(10),cao.dAuditDate,23)=CONVERT(varchar(100),getdate(),23)
  	    and  cao.cflag='P'
  	   	@filter[aos_rms_user,CA_OtherPayment.cAcctGUID=gl_account.cGUID,CA_OtherPayment.cRBankAcctNub=cm_bankline.cguid]
       union all
	select 
	     count(1) num 
	from CA_OtherPayment cao
	left join JBPM4_HIST_PROCINST e on cao.cguid = e.KEY_
	left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
	inner join(
		select a.DBID_,
			case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
			case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
		from JBPM4_HIST_TASK a 
		left join (
			select TIME_,HTASK_,USERID_
	        from JBPM4_HIST_DETAIL
	        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
	     ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		where
		/*对同一实例已完成任务过滤:取最近完成的任务*/
		a.DBID_ in (
               select max(t.dbid_)  
			   from JBPM4_HIST_TASK t
			   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
			   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
			   group by t.execution_
                 )
	) b on b.actTask = c.HTASK_
	left join WF_Process_Variable v on e.ID_ = v.procInstId
	left join WF_ProcessInfo h on v.processTemplate = h.cGUID
	left join WF_Process pr on pr.cguid = h.cNodeTemplate 
	where pr.cForm ='finance_ca_apvoucher_edit_form'
	and CONVERT(char(10),b.END_,23)=CONVERT(varchar(100),getdate(),23)
	and cao.cCheckWay = '2'
	and  cao.cflag='P'
	)t
		</i>
		
		
		
	</sql>
	
</sqls>