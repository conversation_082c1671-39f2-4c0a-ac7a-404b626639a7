<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
    <!-- 参照PU结构 -->
  	<sql group="mobile_daiban_salist">
  	
  	
  	  <i id="getallsalist"  desp="全部union起来的sql">
            select top 100 sa.* from (
                 /*销售订单手工审批*/
            	select 
            	 st.cguid as hidden_cguid,
            	 st.cbillcode,
            	 pl.crealname as tjr,
                 '0' as  hidden_ccheckway,
                 st.iAuditStatus as hidden_iauditstatus,
                '销售订单'+st.cbillcode  as bt,
		         '066' as hidden_cbilltype,
		         CONVERT(varchar(50), st.dcreatortime,120) as sqsj,
                  convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
                 ce.cName    as  ywy,
                 cd.cName    as   bm,
                 null as fplx,
                 null as se
		         from SA_Order st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from SA_OrderLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid	
		         where st.iAuditStatus = 'saved' 
		         and exists(select 1 from SA_OrderLine a 
						inner join cm_material ma on ma.cguid = a.cmatguid
						inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
						where a.cHeadGUID = st.cGUID)
		         and ({sgSaOrder:'false'}='true')
		         and (  ('*' = {keyword:'*'} or convert(varchar(50),st.dCreatorTime,112) like '%{keyword}%')
		         or $like(pl.crealname,keyword)
		         or ('*' = {keyword:'*'} or '销售订单' like '%{keyword}%'))
		         
		
		union all
	       /*销售订单工作流审批*/
            	 select 
            	 st.cguid as hidden_cguid,
            	  st.cbillcode,
            	 pl.crealname as tjr,
                 '2' as  hidden_ccheckway,
                 st.iAuditStatus as hidden_iauditstatus,
                '销售订单'+st.cbillcode as bt,
		         '066' as hidden_cbilltype,
		          CONVERT(varchar(100),e.START_,120) as sqsj,
                 convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
                 ce.cName    as  ywy,
                 cd.cName    as   bm,
                 null as fplx,
                 null as se
		         from SA_Order[-filter] st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from SA_OrderLine[-filter] l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
                 left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
                 inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where  ({wfSaOrder:'false'}='true')
		        and ( (CONVERT(varchar(100),e.START_,112) like '%{keyword}%') 
		              or $like(pl.crealname,keyword)
					  or ('*' = {keyword:'*'} 
					  or '销售订单' like '%{keyword}%'))
		union all
		     /*销售订单工作流审批，流程审批，此为当用户没有待办事项的权限， 却拥有列表或者单据，还有审核的权限时的情况，与上面sql相比，多加入了数据权限的过滤*/
            	 select 
            	 st.cguid as hidden_cguid,
            	  st.cbillcode,
            	 pl.crealname as tjr,
                 '2' as  hidden_ccheckway,
                 st.iAuditStatus as hidden_iauditstatus,
                '销售订单'+st.cbillcode as bt,
		         '066' as hidden_cbilltype,
		          CONVERT(varchar(100),e.START_,120) as sqsj,
                  convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
                 ce.cName    as  ywy,
                 cd.cName    as   bm,
                 null as fplx,
                 null as se
		         from SA_Order st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from SA_OrderLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
                 left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where ({wffilterSaOrder:'false'}='true')
				and exists(select 1 from SA_OrderLine a 
						inner join cm_material ma on ma.cguid = a.cmatguid
						inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
						where a.cHeadGUID = st.cGUID)
		        and ((CONVERT(varchar(100),e.START_,112) like '%{keyword}%') 
		              or $like(pl.crealname,keyword)
					  or ('*' = {keyword:'*'} 
					  or '销售订单' like '%{keyword}%'))
					  
				union all
				
					select 
            	 st.cguid as hidden_cguid,
            	 st.cInvCode,
            	 pl.crealname as tjr,
                 '0' as  hidden_ccheckway,
                 st.iAuditStatus as hidden_iauditstatus,
                '销售发票'+st.cInvCode  as bt,
		         '085' as hidden_cbilltype,
		         CONVERT(varchar(50), st.dcreatortime,120) as sqsj,
                  convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
                 ce.cName    as  ywy,
                 cd.cName    as   bm,
                 g.cName as fplx,
                 convert(nvarchar, cast(round(stl.iTax ,2) as money),1)  as se
		         from SA_Invoice st    
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal,
		  				sum(isnull(l.iTax,0)) as iTax
		  				from SA_InvoiceLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
                 LEFT JOIN CO_GeneralCode g ON st.cSInvType = g.cCode AND g.cClass = '2'	
		         where st.iAuditStatus = 'saved' 
		         and exists(select 1 from SA_InvoiceLine a 
						inner join cm_material ma on ma.cguid = a.cmatguid
						inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
						where a.cHeadGUID = st.cGUID)
		         and ({sgSaInvoice:'false'}='true')
		         and (  ('*' = {keyword:'*'} or convert(varchar(50),st.dCreatorTime,112) like '%{keyword}%')
		         or $like(pl.crealname,keyword)
		         or ('*' = {keyword:'*'} or '销售发票' like '%{keyword}%'))	
		         and st.csystype='SA'
		         and st.iInitFlag='0'
		         
                 
                union all
                
                 select 
            	 st.cguid as hidden_cguid,
            	  st.cInvCode,
            	 pl.crealname as tjr,
                 '2' as  hidden_ccheckway,
                 st.iAuditStatus as hidden_iauditstatus,
                '销售发票'+st.cInvCode as bt,
                 '085' as hidden_cbilltype,
  		          CONVERT(varchar(100),e.START_,120) as sqsj,
                 convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
                 ce.cName    as  ywy,
                 cd.cName    as   bm,
                   g.cName as fplx,
                 convert(nvarchar, cast(round(stl.iTax ,2) as money),1)  as se
		         from SA_Invoice[-filter] st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal,
		  				sum(isnull(l.iTax,0)) as iTax
		  				from SA_InvoiceLine[-filter] l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
                 left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
                 LEFT JOIN CO_GeneralCode g ON st.cSInvType = g.cCode AND g.cClass = '2'	
                 inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where  ({wfSaInvoice:'false'}='true')
		        and ( (CONVERT(varchar(100),e.START_,112) like '%{keyword}%') 
		              or $like(pl.crealname,keyword)
					  or ('*' = {keyword:'*'} 
					  or '销售发票' like '%{keyword}%'))
			    and st.csystype='SA'
			   
                
				union all
            	 select 
            	 st.cguid as hidden_cguid,
            	  st.cInvCode,
            	 pl.crealname as tjr,
                 '2' as  hidden_ccheckway,
                 st.iAuditStatus as hidden_iauditstatus,
                '销售发票'+st.cInvCode as bt,
		         '085' as hidden_cbilltype,
		          CONVERT(varchar(100),e.START_,120) as sqsj,
                  convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
                 ce.cName    as  ywy,
                 cd.cName    as   bm,
                   g.cName as fplx,
                 convert(nvarchar, cast(round(stl.iTax ,2) as money),1)  as se
		         from SA_Invoice st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal,
		  				sum(isnull(l.iTax,0)) as iTax
		  				from SA_InvoiceLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
                 left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
                 LEFT JOIN CO_GeneralCode g ON st.cSInvType = g.cCode AND g.cClass = '2'	
				inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where ({wffilterSaInvoice:'false'}='true' and st.iInitFlag='0')
				and exists(select 1 from SA_InvoiceLine a 
						inner join cm_material ma on ma.cguid = a.cmatguid
						inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
						where a.cHeadGUID = st.cGUID)
		        and ( (CONVERT(varchar(100),e.START_,112) like '%{keyword}%') 
		              or $like(pl.crealname,keyword)
					  or ('*' = {keyword:'*'} 
					  or '销售发票' like '%{keyword}%'))
			    and st.csystype='SA'
                
				union all
            	 select 
            	 st.cguid as hidden_cguid,
            	  st.cInvCode,
            	 pl.crealname as tjr,
                 '2' as  hidden_ccheckway,
                 st.iAuditStatus as hidden_iauditstatus,
                '销售发票'+st.cInvCode as bt,
		         '085' as hidden_cbilltype,
		          CONVERT(varchar(100),e.START_,120) as sqsj,
                  convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
                 ce.cName    as  ywy,
                 cd.cName    as   bm,
                   g.cName as fplx,
                 convert(nvarchar, cast(round(stl.iTax ,2) as money),1)  as se
		         from SA_Invoice st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal,
		  				sum(isnull(l.iTax,0)) as iTax
		  				from SA_InvoiceLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
                 left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
                 LEFT JOIN CO_GeneralCode g ON st.cSInvType = g.cCode AND g.cClass = '2'	
				inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where ( {qcwffilterSaInvoice:'false'}='true' and  st.iInitFlag='2' )
				and exists(select 1 from SA_InvoiceLine a 
						inner join cm_material ma on ma.cguid = a.cmatguid
						inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
						where a.cHeadGUID = st.cGUID)
		        and ( (CONVERT(varchar(100),e.START_,112) like '%{keyword}%') 
		              or $like(pl.crealname,keyword)
					  or ('*' = {keyword:'*'} 
					  or '销售发票' like '%{keyword}%'))
			    and st.csystype='SA'
			    
			        union all            
 			  /*零售单手工审批*/
            	select 
            	 st.cguid as hidden_cguid,
            	 st.cbillcode,
            	 pl.crealname as tjr,
               '0' as  hidden_ccheckway,
               st.iAuditStatus as hidden_iauditstatus,
               '零售单'+st.cbillcode  as bt,
		           '212' as hidden_cbilltype,
		           CONVERT(varchar(50), st.dcreatortime,120) as sqsj,
               convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
               ce.cName    as  ywy,
               cd.cName    as   bm,
               null as fplx,
               null as se
		         from ST_StkRecord st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from ST_StkRecordLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid	
		         where st.iAuditStatus = 'saved' 
		               and st.cBillType = '212'
					   and st.iInitFlag = '0'
					   and st.iRSFlag = '0'
							/*为了配合权限使用*/
		         and exists(select 1 from ST_StkRecordLine a 
						inner join cm_material ma on ma.cguid = a.cmatguid
						inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
						where a.cHeadGUID = st.cGUID)
		         and ({sgSaRetail:'false'}='true')
		         and (  ('*' = {keyword:'*'} or convert(varchar(50),st.dCreatorTime,112) like '%{keyword}%')
		         or $like(pl.crealname,keyword)
		         or ('*' = {keyword:'*'} or '零售单' like '%{keyword}%'))
		         
		
		union all
	       /*零售单工作流审批*/
            	 select 
            	 st.cguid as hidden_cguid,
            	 st.cbillcode,
            	 pl.crealname as tjr,
               '2' as  hidden_ccheckway,
               st.iAuditStatus as hidden_iauditstatus,
               '零售单'+st.cbillcode as bt,
		           '212' as hidden_cbilltype,
		           CONVERT(varchar(100),e.START_,120) as sqsj,
               convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
               ce.cName    as  ywy,
               cd.cName    as   bm,
               null as fplx,
               null as se
		         from ST_StkRecord[-filter] st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from ST_StkRecordLine[-filter] l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
                 left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
                 inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where  ({wfSaRetail:'false'}='true')
				       and st.cBillType = '212'
					   and st.iInitFlag = '0'
					   and st.iRSFlag = '0'
		        	   and ( (CONVERT(varchar(100),e.START_,112) like '%{keyword}%') 
		              or $like(pl.crealname,keyword)
					  or ('*' = {keyword:'*'} 
					  or '零售单' like '%{keyword}%'))
		union all
		     /*零售单工作流审批，流程审批，此为当用户没有待办事项的权限， 却拥有列表或者单据，还有审核的权限时的情况，与上面sql相比，多加入了数据权限的过滤*/
					 select 
						st.cguid as hidden_cguid,
						st.cbillcode,
						pl.crealname as tjr,
						'2' as  hidden_ccheckway,
						st.iAuditStatus as hidden_iauditstatus,
						'零售单'+st.cbillcode as bt,
		        '212' as hidden_cbilltype,
		        CONVERT(varchar(100),e.START_,120) as sqsj,
            convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
            ce.cName    as  ywy,
            cd.cName    as   bm,
            null as fplx,
            null as se
		        from ST_StkRecord st
            inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from ST_StkRecordLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
                 left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
						inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where ({wffilterSaRetail:'false'}='true')
			   and st.cBillType = '212'
			   and st.iInitFlag = '0'
			   and st.iRSFlag = '0'
				and exists(select 1 from ST_StkRecordLine a 
						inner join cm_material ma on ma.cguid = a.cmatguid
						inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
						where a.cHeadGUID = st.cGUID)
		        and ((CONVERT(varchar(100),e.START_,112) like '%{keyword}%') 
		              or $like(pl.crealname,keyword)
					  or ('*' = {keyword:'*'} 
					  or '零售单' like '%{keyword}%')) 
		        @filter[
		         SA_Invoice.cCustGUID=CM_Customer.cguid,
            		SA_Invoice.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
              		SA_Invoice.cDeptGUID=cm_department.cguid,
              		SA_Invoice.cEmpGUID=CM_Employee.cguid,
              		SA_Invoice.cCreatorGUID=AOS_RMS_USER.cguid,
              		SA_Invoice.cBusProcess=CO_BusinessProcess.cGUID,
              		SA_InvoiceLine.cMatGUID=CM_Material.cGUID,
              		SA_InvoiceLine.cMatGUID=CM_Material.cGUID and CM_Material.cMatCGUID=CM_MatClass.cGUID,
              		SA_InvoiceLine.cStoreGUID=CM_Storehouse.cguid,
              		SA_InvoiceLine.cItemGUID=CM_Project.cguid,
			        SA_Order.cCustGUID=CM_Customer.cguid,
			        SA_Order.cDeptGUID=cm_department.cguid,
			        SA_Order.cEmpGUID=CM_Employee.cguid,
			        SA_Order.cCreatorGUID=AOS_RMS_USER.cguid,
			        SA_Order.cAcctIItemGuid= CM_Project.cguid,
			        SA_Order.cBusProcess=CO_BusinessProcess.cGUID,
			        SA_OrderLine.cMatGUID=CM_Material.cGUID,
			        SA_OrderLine.cStoreGUID=CM_Storehouse.cguid,
			        SA_Order.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
			        ST_StkRecord.cCustGUID=CM_Customer.cguid,
			        ST_StkRecord.cDeptGUID=cm_department.cguid,
			        ST_StkRecord.cEmpGUID=CM_Employee.cguid,
			        ST_StkRecord.cCreatorGUID=AOS_RMS_USER.cguid,
			        ST_StkRecord.cAcctIItemGuid= CM_Project.cguid,
			        ST_StkRecord.cBusProcess=CO_BusinessProcess.cGUID,
			        ST_StkRecordLine.cMatGUID=CM_Material.cGUID,
			        ST_StkRecordLine.cStoreGUID=CM_Storehouse.cguid,
			        ST_StkRecord.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
			        CM_MatClass
		        ])sa
		        order by sqsj desc
		</i>	
  	  
  	<i id="getallsanum">
  	
  	
  	     select sum(sao.num)  from 
  	            (select 
            	  count(1) as num
		         from SA_Order st
                 inner join(
		  				select l.cheadguid	
		  				from SA_OrderLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
		         where st.iAuditStatus = 'saved' 
		         and ({sgSaOrder:'false'}='true')
	             and exists(select 1 from SA_OrderLine a 
					inner join cm_material ma on ma.cguid = a.cmatguid
					inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
					where a.cHeadGUID = st.cGUID)
		       
  	       union all
  	
	         		 select 
		          count(1) as num
		         from SA_Order[-filter]  po 
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				inner join(
	                select distinct a.PROCINST_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where 
				({wfSaOrder:'false'}='true')
		    union all
		
	         		 select 
		        count(1) as num
		        from SA_Order po 
		         inner join(
		  				select l.cheadguid
		  				from SA_OrderLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid =  po .cGUID 
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
			    inner join(
	                select distinct a.PROCINST_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where
				 ({wffilterSaOrder:'false'}='true')
				  and exists(select 1 from SA_OrderLine a 
					inner join cm_material ma on ma.cguid = a.cmatguid
					inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
					where a.cHeadGUID = po.cGUID)
					
					union all
				
					select 
            	  count(1) as num
		         from SA_Invoice st    
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from SA_InvoiceLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid	
		         where st.iAuditStatus = 'saved' 
		         and exists(select 1 from SA_InvoiceLine a 
						inner join cm_material ma on ma.cguid = a.cmatguid
						inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
						where a.cHeadGUID = st.cGUID)
		         and ({sgSaInvoice:'false'}='true')
		         and st.csystype='SA'
		         and st.iInitFlag='0'
                 
                union all
                
               	select 
            	  count(1) as num
		         from SA_Invoice[-filter] st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from SA_InvoiceLine[-filter] l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
                 left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
                 inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where  ({wfSaInvoice:'false'}='true')
			    and st.csystype='SA'
		      
                
				union all
            	
            		select 
            	  count(1) as num
		         from SA_Invoice st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from SA_InvoiceLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
                 left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
		    	where ({wffilterSaInvoice:'false'}='true' and st.iInitFlag='0')
				and exists(select 1 from SA_InvoiceLine a 
						inner join cm_material ma on ma.cguid = a.cmatguid
						inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
						where a.cHeadGUID = st.cGUID)				
		          and st.csystype='SA'
                
				union all
            	
            		select 
            	  count(1) as num
		         from SA_Invoice st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from SA_InvoiceLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
                 left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where ( {qcwffilterSaInvoice:'false'}='true' and  st.iInitFlag='2' )
				and exists(select 1 from SA_InvoiceLine a 
						inner join cm_material ma on ma.cguid = a.cmatguid
						inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
						where a.cHeadGUID = st.cGUID)				
		          and st.csystype='SA'
		         
		          union all	
							select 
            	  count(1) as num
		         from ST_StkRecord st
                 inner join(
		  				select l.cheadguid	
		  				from ST_StkRecordLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
		         where st.iAuditStatus = 'saved' 
		         and st.cBillType = '212'
			    and st.iInitFlag = '0'
			     and st.iRSFlag = '0'
		         and ({sgSaRetail:'false'}='true')
	             and exists(select 1 from ST_StkRecordLine a 
					inner join cm_material ma on ma.cguid = a.cmatguid
					inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
					where a.cHeadGUID = st.cGUID)
		       
  	       union all
  	
	         		 select 
		          count(1) as num
		         from ST_StkRecord[-filter]  st
				left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				inner join(
	                select distinct a.PROCINST_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where 
				({wfSaRetail:'false'}='true')
			   and st.cBillType = '212'
			   and st.iInitFlag = '0'
			   and st.iRSFlag = '0'

		    union all	
	         		 select 
		        count(1) as num
		        from ST_StkRecord st 
		         inner join(
		  				select l.cheadguid
		  				from ST_StkRecordLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid =  st.cGUID 
				left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
			    inner join(
	                select distinct a.PROCINST_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where
				 ({wffilterRetail:'false'}='true')
				      and st.cBillType = '212'
			  		  and st.iInitFlag = '0'
			    	 and st.iRSFlag = '0'
				  and exists(select 1 from ST_StkRecordLine a 
					inner join cm_material ma on ma.cguid = a.cmatguid
					inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
					where a.cHeadGUID = st.cGUID)
					
		        @filter[
		        	SA_Invoice.cCustGUID=CM_Customer.cguid,
            		SA_Invoice.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
              		SA_Invoice.cDeptGUID=cm_department.cguid,
              		SA_Invoice.cEmpGUID=CM_Employee.cguid,
              		SA_Invoice.cCreatorGUID=AOS_RMS_USER.cguid,
              		SA_Invoice.cBusProcess=CO_BusinessProcess.cGUID,
              		SA_InvoiceLine.cMatGUID=CM_Material.cGUID,
              		SA_InvoiceLine.cMatGUID=CM_Material.cGUID and CM_Material.cMatCGUID=CM_MatClass.cGUID,
              		SA_InvoiceLine.cStoreGUID=CM_Storehouse.cguid,
              		SA_InvoiceLine.cItemGUID=CM_Project.cguid,	
					SA_Order.cCustGUID=CM_Customer.cguid,
			        ST_StkRecord.cDeptGUID=cm_department.cguid,
			        ST_StkRecord.cEmpGUID=CM_Employee.cguid,
			        ST_StkRecord.cCreatorGUID=AOS_RMS_USER.cguid,
			        ST_StkRecord.cAcctIItemGuid= CM_Project.cguid,
			        ST_StkRecord.cBusProcess=CO_BusinessProcess.cGUID,
			        ST_StkRecordLine.cMatGUID=CM_Material.cGUID,
			        ST_StkRecordLine.cStoreGUID=CM_Storehouse.cguid,
			        ST_StkRecord.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
			        SA_Order.cDeptGUID=cm_department.cguid,
			        SA_Order.cEmpGUID=CM_Employee.cguid,
			        SA_Order.cCreatorGUID=AOS_RMS_USER.cguid,
			        SA_Order.cAcctIItemGuid= CM_Project.cguid,
			        SA_Order.cBusProcess=CO_BusinessProcess.cGUID,
			        SA_OrderLine.cMatGUID=CM_Material.cGUID,
			        SA_OrderLine.cStoreGUID=CM_Storehouse.cguid,
			        SA_Order.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
			        CM_MatClass]
		        )sao
		</i>				
	</sql>
	
	<sql group="mobile_daiban_sabill">
  		<i id="saordermain"  desp="销售订单主表加载">
            select 
                 convert(nvarchar, cast(round(stl.iTotal ,2) as money),1) as "销售总金额",
                  case when icloseflag='0'  then '未终止'
                  else '已终止' end as 'sa_order.icloseflag'
		         from SA_Order st
                 inner join(
		  				select l.cheadguid,
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from SA_OrderLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID                 
		         where st.cguid={cGUID} 
		</i>
		
		<i id="saorderdetail" desp="销售订单子表加载">
		   SELECT
		     case when pl.icloseflag='0'  then '未终止'
                  else '已终止' end as 'sa_orderline.icloseflag',
                  
                   ar.crealname 'sa_orderline.ccloserguid',
                  
               case when pl.iisgift='0'  then '否'
                  else '是' end as 'sa_orderline.iisgift',
                       
               case when pl.csarefepricemethod='z'  then '主计量单位'
                  else '辅计量单位' end as 'sa_orderline.csarefepricemethod',
                          
                  
		    pl.cguid,
			('['+cm.cMatCode+']'+cm.cMatName) as "cm_material.cname.sa_orderline.cmatguid",
			 case 
             when isnull(cm.iPrecision,0)=0 then left(cast(pl.iUnitQTY as varchar(21)),charindex('.',cast(pl.iUnitQTY as varchar(21)))-1)
             else left(cast(pl.iUnitQTY as varchar(21)) , charindex('.',cast(pl.iUnitQTY as varchar(21))) +cm.iPrecision)
             end  +u.cName  as "sa_orderline.iunitqty",
			 convert(nvarchar, cast(round(pl.iTotal,2) as money),1)  as "sa_orderline.itotal"
		FROM
			sa_orderline pl
			left join AOS_RMS_USER ar ON pl.ccloserguid = ar.cguid
			left join CM_Material cm on pl.cmatguid = cm.cguid	
			left join CM_Unit u on pl.cMUnitGUID = u.cguid
		where pl.cheadguid={cGUID}
		</i>
		
		<i id="sainvoicemain"  desp="销售发票主表加载">
            select               
                 convert(nvarchar, cast(round(stl.iTotal ,2) as money),1) as "发票总金额",
                    case when cSInvType='0' then '普通发票'
                    else '专用发票' end as   "sa_invoice.csinvtype"
		         from sa_invoice st
                 inner join(
		  				select l.cheadguid,
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from SA_InvoiceLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
		         where st.cguid={cGUID} 
		</i>
		
		<i id="sainvoicedetail"  desp="销售发票子表加载">
		   SELECT
		    pl.cguid,
			('['+cm.cMatCode+']'+cm.cMatName) as "cm_material.cname.sa_invoiceline.cmatguid",
			 case 
             when isnull(cm.iPrecision,0)=0 then left(cast(pl.irefepriceqty as varchar(21)),charindex('.',cast(pl.irefepriceqty as varchar(21)))-1)
             else left(cast(pl.irefepriceqty as varchar(21)) , charindex('.',cast(pl.irefepriceqty as varchar(21))) +cm.iPrecision)
             end  +u.cName  as "sa_invoiceline.irefepriceqty",
			 convert(nvarchar, cast(round(pl.iTotal,2) as money),1)  as "sa_invoiceline.itotal",
			 convert(nvarchar, cast(round(pl.itax,2) as money),1)  as "sa_invoiceline.itax",
			 convert(nvarchar, cast(round(pl.iTaxPrice,2) as money),1)  as "sa_invoiceline.itaxprice",
			 (select cvalue from aos_pref_value where ccode='U3_ST_InvoicePrecision') as U3_ST_InvoicePrecision,
			 case when iisgift='0'  then '否'
            else '是' end as 'sa_invoiceline.iisgift',
           case when pl.csarefepricemethod='z'  then '主计量单位'
           else '辅计量单位' end as 'sa_invoiceline.csarefepricemethod',
			 pl.iTaxPrice
		FROM 
			SA_InvoiceLine pl
			left join CM_Material cm on pl.cmatguid = cm.cguid	
			left join CM_Unit u on pl.cSaRefePriceUnitGUID = u.cguid
		where pl.cheadguid={cGUID}
		</i>
		
		
		<i id="saretailmain"  desp="零售单主表加载">
            select               
                 convert(nvarchar, cast(round(stl.iTotal ,2) as money),1) as "零售总金额"
		    from ST_StkRecord st
            inner join(
		  				select l.cheadguid,
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from ST_StkRecordLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
		    where st.cguid={cGUID} 
		</i>
		
		<i id="saretaildetail"  desp="零售单子表加载">
		   SELECT
		    pl.cguid,
			('['+cm.cMatCode+']'+cm.cMatName) as "cm_material.cname.st_stkrecordline.cmatguid",
			 case 
             when isnull(cm.iPrecision,0)=0 then left(cast(pl.irefepriceqty as varchar(21)),charindex('.',cast(pl.irefepriceqty as varchar(21)))-1)
             else left(cast(pl.irefepriceqty as varchar(21)) , charindex('.',cast(pl.irefepriceqty as varchar(21))) +cm.iPrecision)
             end  +u.cName  as "st_stkrecordline.irefepriceqty",
			 convert(nvarchar, cast(round(pl.iTotal,2) as money),1)  as "st_stkrecordline.itotal",
			 convert(nvarchar, cast(round(pl.itax,2) as money),1)  as "st_stkrecordline.itax",
			 convert(nvarchar, cast(round(pl.iTaxPrice,2) as money),1)  as "st_stkrecordline.itaxprice",
			 pl.itaxprice,
			 (select cvalue from aos_pref_value where ccode='U3_ST_PricePrecision') as U3_ST_PricePrecision,
			 case when iisgift='0'  then '否'
             else '是' end as 'st_stkrecordline.iisgift',
             case when pl.csarefepricemethod='z'  then '主计量单位'
                 else '辅计量单位' end as 'st_stkrecordline.csarefepricemethod'
		FROM 
			ST_StkRecordLine pl
			left join CM_Material cm on pl.cmatguid = cm.cguid	
			left join CM_Unit u on pl.cSaRefePriceUnitGUID = u.cguid
		where pl.cheadguid={cGUID}
		</i>
		
	</sql>
	
</sqls>