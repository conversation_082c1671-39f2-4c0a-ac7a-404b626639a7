<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_list_saorder_sql" desp="列表数据加载">
		<i id="saOrderList" desp="销售订单列表sql">
			<![CDATA[
				select top 100 * from(
				      select st.cguid as hidden_cguid,
				             st.cbillcode,
				             st.dDate,
				             case st.iAuditStatus when 'tsaved' then '暂存' when 'saved' then '保存待提交' when 'checking' then '提交待审核' 
				                  when 'checked' then '审核完成' when 'revise' then '退回修改中' when 'refuse' then '否决并终止' else '' end as iAuditStatusName,
				             st.iAuditStatus,    
				             c.cName as customer,
							       convert(nvarchar, cast(round(stl.iTotal_F ,2) as money),1) as saleMoney,   
				       	     CONVERT(varchar(50), st.dcreatortime,120) as dcreatortime
						 from 
							  		 SA_Order st
						 inner JOIN (select l.cheadguid,	
													       sum(isnull(l.iTotal_F,0)) as iTotal_F
													from SA_OrderLine l
								          group by l.cheadguid  
						  				  ) as stl on stl.cheadguid = st.cGUID 
						left JOIN CM_Customer c ON st.cCustGUID = c.cGUID
						LEFT JOIN CM_Department d ON st.cDeptGUID = d.cGUID
						LEFT JOIN CM_Employee e ON st.cEmpGUID = e.cGUID
						left JOIN AOS_RMS_USER p1 on  st.cCreatorGUID = p1.cguid
						@table[SA_Order st,CM_Customer cs]
						where $like(st.cBillCode,cBillCode)
							and $between(st.dDate,startDate,endDate)
							and $between(st.dcreatortime,clockStart,null)
							and $equal(st.cCustGUID,cCustGUID)	
							and $equal(st.cDeptGUID,cDeptGUID)	
							and $equal(st.cEmpGUID,cEmpGUID)
							and $equal(st.cBusType,cbustypeguid)
							and $equal(st.cBusProcess,cbusprocessguid)	
							and exists(select 1 from SA_OrderLine a 
								inner join cm_material ma on ma.cguid = a.cmatguid
								inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
								where a.cHeadGUID = st.cGUID)				
				     @filter[SA_Order.cCustGUID=CM_Customer.cguid,
						    SA_Order.cDeptGUID=cm_department.cguid,
						    SA_Order.cEmpGUID=CM_Employee.cguid,
						    SA_Order.cCreatorGUID=AOS_RMS_USER.cguid,
						    SA_Order.cAcctIItemGuid= CM_Project.cguid,
						    SA_Order.cBusProcess=CO_BusinessProcess.cGUID,
						    SA_OrderLine.cMatGUID=CM_Material.cGUID,
						    SA_OrderLine.cStoreGUID=CM_Storehouse.cguid,
						    SA_Order.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
						    CM_MatClass]
				)a
				order by a.dDate DESC, a.cbillcode DESC
			]]>
		</i>
		
				<i id="saOrderSum" desp="获取所有销售订单笔数">
			<![CDATA[
				select count(1) as salenum
			    from(
						select 
							     DISTINCT st.cguid				       	   
						from SA_OrderLine stl 
						inner join SA_Order st on stl.cheadguid = st.cGUID 
						left JOIN CM_Customer c ON st.cCustGUID = c.cGUID
						LEFT JOIN CM_Department d ON st.cDeptGUID = d.cGUID
						LEFT JOIN CM_Employee e ON st.cEmpGUID = e.cGUID
						left JOIN AOS_RMS_USER p1 on  st.cCreatorGUID = p1.cguid
						@table[SA_Order st,CM_Customer c]
						where  st.iAuditStatus != 'tsaved'  
						    and $like(st.cBillCode,cBillCode)
							and $between(st.dDate,startDate,endDate)
							and $between(st.dcreatortime,clockStart,null)
							and $equal(st.cCustGUID,cCustGUID)	
							and $equal(st.cDeptGUID,cDeptGUID)	
							and $equal(st.cEmpGUID,cEmpGUID)
							and $equal(st.cBusType,cbustypeguid)
							and $equal(st.cBusProcess,cbusprocessguid)	
							and exists(select 1 from SA_OrderLine a 
								inner join cm_material ma on ma.cguid = a.cmatguid
								inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
								where a.cHeadGUID = st.cGUID)						
				      @filter[SA_Order.cCustGUID=CM_Customer.cguid,
						    SA_Order.cDeptGUID=cm_department.cguid,
						    SA_Order.cEmpGUID=CM_Employee.cguid,
						    SA_Order.cCreatorGUID=AOS_RMS_USER.cguid,
						    SA_Order.cAcctIItemGuid= CM_Project.cguid,
						    SA_Order.cBusProcess=CO_BusinessProcess.cGUID,
						    SA_OrderLine.cMatGUID=CM_Material.cGUID,
						    SA_OrderLine.cStoreGUID=CM_Storehouse.cguid,
						    SA_Order.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
						    CM_MatClass]
				)a
			]]>
		</i>
		
		
		<i id="saOrderAmtSum" desp="获取所有销售订单总金额">
			<![CDATA[
				select convert(nvarchar, cast(round(sum(iTotal_F), 2) as money),1) as salemoney
			    from(
						select 
							 (isnull(stl.iTotal_F,0)) as iTotal_F				       	   
						from SA_OrderLine stl 
						inner join SA_Order st on stl.cheadguid = st.cGUID 
						left JOIN CM_Customer c ON st.cCustGUID = c.cGUID
						LEFT JOIN CM_Department d ON st.cDeptGUID = d.cGUID
						LEFT JOIN CM_Employee e ON st.cEmpGUID = e.cGUID
						left JOIN AOS_RMS_USER p1 on  st.cCreatorGUID = p1.cguid
						@table[SA_Order st,CM_Customer c]
						where  st.iAuditStatus != 'tsaved'  
						    and $like(st.cBillCode,cBillCode)
							and $between(st.dDate,startDate,endDate)
							and $between(st.dcreatortime,clockStart,null)
							and $equal(st.cCustGUID,cCustGUID)	
							and $equal(st.cDeptGUID,cDeptGUID)	
							and $equal(st.cEmpGUID,cEmpGUID)
							and $equal(st.cBusType,cbustypeguid)
							and $equal(st.cBusProcess,cbusprocessguid)
							and exists(select 1 from SA_OrderLine a 
								inner join cm_material ma on ma.cguid = a.cmatguid
								inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
								where a.cHeadGUID = st.cGUID)						
				      @filter[SA_Order.cCustGUID=CM_Customer.cguid,
						    SA_Order.cDeptGUID=cm_department.cguid,
						    SA_Order.cEmpGUID=CM_Employee.cguid,
						    SA_Order.cCreatorGUID=AOS_RMS_USER.cguid,
						    SA_Order.cAcctIItemGuid= CM_Project.cguid,
						    SA_Order.cBusProcess=CO_BusinessProcess.cGUID,
						    SA_OrderLine.cMatGUID=CM_Material.cGUID,
						    SA_OrderLine.cStoreGUID=CM_Storehouse.cguid,
						    SA_Order.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
						    CM_MatClass]
				)a
			]]>
		</i>
	</sql>
</sqls>