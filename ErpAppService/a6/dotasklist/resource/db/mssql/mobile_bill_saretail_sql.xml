<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_bill_saretail_sql" desp="零售单单据SQL">		
		<i id="getMatSelectInfo" desp="选择物品确定后带入相关信息">
			<![CDATA[
	 
				SELECT  '' as cguid,
						a.cGUID AS cmatguid,
						a.cMatCode AS cmatcode,
						a.cMatName AS cmatname,
						isnull(a.cSpec, '') AS cspec,
						case when isnull(a.iPrecision,0)=0 then left(cast(isnull(cur.iCurQuan,0) as varchar(21)),charindex('.',cast(isnull(cur.iCurQuan,0) as varchar(21)))-1)
					       else left(cast(isnull(cur.iCurQuan,0) as varchar(21)) , charindex('.',cast(isnull(cur.iCurQuan,0) as varchar(21))) +a.iPrecision)
					       end + u1.cName as iStoreCurUnitQty,
						a.iPrecision AS iMatPrecision,
						a.cUnitGUID,	
					    u.cName as cUnitName,
					    (select cvalue from AOS_PREF_VALUE where ccode='U3_ST_PricePrecision') as iPricePrecision,
					    u.cName cUnitName,
					  	0 as iisgift,
			            0 as iunitprice_f,
			            0 as itotal_f,
						0 as iamt_f,
						a.itaxrate,
						0 as itax_f,
						(select cvalue from AOS_PREF_VALUE where ccode='U3_SA_PriceType') as ipricetype,
						(select cvalue from AOS_PREF_VALUE where ccode='U3_SA_QuotedPriceTax') as ipricetax,
						case (select cvalue from AOS_PREF_VALUE where ccode='U3_SA_DoTaxRate') when 'n' then 'not' else 'can' end as u3_sa_dotaxrate,
						a.isnstart,
						'' as crowsninfo,
						'' as crowsninfonew 
					FROM
					 cm_material a
					INNER JOIN CM_MatClass ac ON ac.cGUID = a.cMatCGUID
					LEFT JOIN CM_Unit u ON a.cUnitGUID = u.cGUID
					LEFT JOIN (
						SELECT
							SUM (c.iCurQty) AS iCurQuan,
							cMatGUID,
							cStkUnitGUID
					 FROM
						ST_CurrentStock c
				 	 where $equal(c.cstoreguid,cstoreguid) 
					 	 and exists(select 1 from CM_Storehouse a 								
									where a.cGUID = c.cstoreguid)
					GROUP BY
						cMatGUID,
						cStkUnitGUID
					) cur ON cur.cMatGUID = a.cGUID
					LEFT JOIN CM_Unit u1 ON cur.cStkUnitGUID = u1.cGUID
					where $in(a.cguid,idls) 		
					@filter[CM_Material,
					        CM_MatClass,
					        ST_CurrentStock.cStoreGUID=CM_Storehouse.cguid]
			]]>
		</i>
		
		<i id="getSaRetailMain" desp="获取零售单主表数据">
			<![CDATA[
			select 
						sr.cBillCode,
						CONVERT(varchar(100),sr.dDate,23) as dDate,
						sr.cCustGUID,
						cc.cName as cCustName,
						sr.cStoreGUID,
						cs.cName as cStoreName,
						sr.cDeptGUID,
						cd.cName as cDeptName,
						sr.cEmpGUID,
						ce.cName as cEmpName,
						sr.iRedFlag,
						sr.iMolingAMT,
						sr.ireceivableamt,
						sr.ichangeamt,
						sr.iPaidAMT,
						sr.cCurGUID,
            			sr.iAuditStatus,
            			sr.invtype,
            			sr.cCreatorGUID  
			from ST_StkRecord sr
			left join CM_Customer cc on sr.cCustGUID=cc.cguid
		    LEFT JOIN CM_Storehouse cs on sr.cStoreGUID=cs.cguid
		    LEFT JOIN CM_Department cd ON sr.cDeptGUID = cd.cGUID
			LEFT JOIN CM_Employee ce ON sr.cEmpGUID = ce.cGUID
			WHERE sr.cGUID = ?
			]]>
		</i>
		
		<i id="getSaRetailDetail" desp="获取零售单子表数据">
			<![CDATA[
		 SELECT top 100 * from( 
			select s.cGUID,
			       s.cMatGUID,	
				   m.cMatName,
			       m.cMatCode,
			       m.cSpec,
		           s.cUnitGUID,
		           u2.cName as cunitname,
		           'false' as checked,
				   s.iQTY,
			       s.iUnitPrice_F,
			       s.iAMT_F,
			       s.iTax_F,
			       s.iTotal_F,
			       s.iTaxPrice_F,
			       s.iDisAMT_F,
			       s.iDisAMT,
			       s.iisgift,			     
			       s.iTaxRate,			         
			       s.iDisRate,
				   s.iDisRateCon,	
				   s.iTaxQuotedPrice,
                   s.iQuotedPrice,			    
			       s.cStoreGUID,
			       store.cName as cStoreName,
				   m.iPrecision as iMatPrecision,
			       m.iGuaranteeFlag,			      
			       m.iBatchFlag,     
				   m.isnstart,
                   m.iSubUnit,
                   s.crowsninfo,
                   s.crowsninfonew,
				   case when s.cStoreGUID is not null then 
					   ((case when isnull(m.iPrecision,0)=0 then left(cast(isnull(cur.iCurQuan,0) as varchar(21)),charindex('.',cast(isnull(cur.iCurQuan,0) as varchar(21)))-1)
				       else left(cast(isnull(cur.iCurQuan,0) as varchar(21)) , charindex('.',cast(isnull(cur.iCurQuan,0) as varchar(21))) +m.iPrecision)
				       end)+ u3.cName)
         		   else ((case when isnull(m.iPrecision,0)=0 then left(cast(isnull(cur1.iCurQuan,0) as varchar(21)),charindex('.',cast(isnull(cur1.iCurQuan,0) as varchar(21)))-1)
				       else left(cast(isnull(cur1.iCurQuan,0) as varchar(21)) , charindex('.',cast(isnull(cur1.iCurQuan,0) as varchar(21))) +m.iPrecision)
				       end)+ u4.cName) 
				   end as iStoreCurUnitQty,
				   (select cvalue from AOS_PREF_VALUE where ccode='U3_ST_PricePrecision') as iPricePrecision,
				   (select cvalue from AOS_PREF_VALUE where ccode='U3_SA_PriceType') as ipricetype,
				   (select cvalue from AOS_PREF_VALUE where ccode='U3_SA_QuotedPriceTax') as ipricetax,
				   (select cvalue from AOS_PREF_VALUE where ccode='U3_SA_DoTaxRate') as u3_sa_dotaxrate,
				    s.iNumber,
				    s.cItemGUID
			  from ST_StkRecordLine s
			 inner join CM_Material m on m.cGUID = s.cMatGUID
			  LEFT JOIN CM_Unit u1 ON u1.cGUID = s.cMUnitGUID
			  LEFT JOIN CM_Unit u2 on u2.cGUID = s.cUnitGUID
			  LEFT JOIN ST_StkRecord v on s.cHeadGUID = v.cGUID
			  Left join CM_StoreHouse store on store.cguid=s.cStoreGUID
			  LEFT JOIN (
						SELECT
							SUM (c.iCurQty) AS iCurQuan,
							cMatGUID,
							cStkUnitGUID,
							cStoreGuid
						FROM
							ST_CurrentStock c				
						GROUP BY
							cMatGUID,
							cStkUnitGUID,
							cStoreGuid
					) cur ON cur.cMatGUID = m.cGUID and cur.cStoreGuid=s.cstoreguid
			LEFT JOIN (
						SELECT
							SUM (c.iCurQty) AS iCurQuan,
							cMatGUID,
							cStkUnitGUID
						FROM
							ST_CurrentStock c				
						GROUP BY
							cMatGUID,
							cStkUnitGUID
					) cur1 ON cur1.cMatGUID = m.cGUID
			LEFT JOIN CM_Unit u3 ON cur.cStkUnitGUID = u3.cGUID	
			LEFT JOIN CM_Unit u4 ON cur1.cStkUnitGUID = u4.cGUID	
			 where s.cHeadGUID = {cGUID}			 
	 	)cm
	        order by iNumber
			]]>
		</i>
		
		<i id="getpayNowInfo" desp="获取现结信息"><![CDATA[
			select s.cguid as hidden_cguid,
				   s.cSettleTypeGUID,
                   ss.cName as csettletypename ,
 	               convert(nvarchar, cast(round(sum(isnull(s.iPayAMT_F,0)) ,2) as money),1)  as ipayamt_f	               		
	       from SA_RetailPayNow s
	       left join CA_SettleMode ss on ss.cguid = s.cSettleTypeGUID
	       where s.cHeadGUID ={cGUID}
	       group by s.cguid,s.cHeadGUID,s.cSettleTypeGUID,ss.cName		
		]]>
     </i> 
         	
		<i id="getFile" desp="获取零售单附件">
			<![CDATA[
				SELECT cGuid as hidden_cguid ,
				       cFileName,
				       cFileUrl
				FROM AOS_FILE_FILES
				WHERE cGroupGuid=?
			]]>
		</i>
	</sql>
</sqls>