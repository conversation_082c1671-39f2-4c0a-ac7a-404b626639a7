<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_yiban_oaList" desp="协同办公已办列表sql">
		<i id="getAllList" desp="获取所有列表数据">
			<![CDATA[
				select top 100 *
				from(
					select wf.cguid as hidden_cguid,'2' as hidden_cCheckWay,'oa_zdy' as hidden_cbilltype,wf.CNAME as bt,
						CONVERT(varchar(100),b.END_,20) as blsj,v.owner<PERSON><PERSON> as tjr
					from WF_Form wf
					left join JBPM4_HIST_PROCINST e on wf.cguid = e.KEY_
					left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
					inner join(
						select a.DBID_,
							case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
							case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
						from JBPM4_HIST_TASK a 
						left join (
							select TIME_,HTASK_,USERID_
					        from JBPM4_HIST_DETAIL
					        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
					     ) DETAIL on DETAIL.HTASK_ = a.DBID_
			   	 		where
						/*对同一实例已完成任务过滤:取最近完成的任务*/
						a.DBID_ in (
	                           select max(t.dbid_)  
							   from JBPM4_HIST_TASK t
							   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
							   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
							   		and d.CLASS_='status'
							   		and d.NEW_STR_ !='cancel'
							   		and d.NEW_STR_ !='retreat'
							   		and d.NEW_STR_ !='delegate'
							   		and d.NEW_STR_ !='rollback-type'
							   		and d.NEW_STR_ !='jump-type'
							   		and d.NEW_STR_!='rollback-target'
							   		and d.NEW_STR_ !='delegated'
							   group by t.execution_
	                    )
					) b on b.actTask = c.HTASK_
					left join WF_Process_Variable v on e.ID_ = v.procInstId
					left join WF_ProcessInfo h on v.processTemplate = h.cGUID
					left join WF_Process pr on pr.cguid = h.cNodeTemplate 
					where pr.cForm ='pt_eform_processtext'
					and ('*' = {today:'*'} or CONVERT(varchar(100),b.END_,23) ={today})
					and ('*' = {keyword:'*'} or wf.CNAME like '%{keyword}%')
				)oa
				order by oa.blsj desc
			]]>
		</i>
		
		<i id="getAllListNum" desp="获取所有列表数目">
			<![CDATA[
				select sum(oa.num) as num
				from(
					select count(1) num
					from WF_Form wf
					left join JBPM4_HIST_PROCINST e on wf.cguid = e.KEY_
					left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
					inner join(
						select a.DBID_,
							case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
							case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
						from JBPM4_HIST_TASK a 
						left join (
							select TIME_,HTASK_,USERID_
					        from JBPM4_HIST_DETAIL
					        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
					     ) DETAIL on DETAIL.HTASK_ = a.DBID_
			   	 		where
						/*对同一实例已完成任务过滤:取最近完成的任务*/
						a.DBID_ in (
	                           select max(t.dbid_)  
							   from JBPM4_HIST_TASK t
							   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
							   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
							   		and d.CLASS_='status'
							   		and d.NEW_STR_ !='cancel'
							   		and d.NEW_STR_ !='retreat'
							   		and d.NEW_STR_ !='delegate'
							   		and d.NEW_STR_ !='rollback-type'
							   		and d.NEW_STR_ !='jump-type'
							   		and d.NEW_STR_!='rollback-target'
							   		and d.NEW_STR_ !='delegated'
							   group by t.execution_
	                    )
					) b on b.actTask = c.HTASK_
					left join WF_Process_Variable v on e.ID_ = v.procInstId
					left join WF_ProcessInfo h on v.processTemplate = h.cGUID
					left join WF_Process pr on pr.cguid = h.cNodeTemplate 
					where pr.cForm ='pt_eform_processtext'
					and CONVERT(varchar(100),b.END_,23)=CONVERT(varchar(100),getdate(),23)
				)oa
			]]>
		</i>
	</sql>
</sqls>