<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
    <!-- 参照PU结构 -->
	<sql group="mobile_daiban_stlist">
		<i id="getallstlist"  desp="全部union起来的sql"><![CDATA[
			select top 100 sa.* from 
			(
	            select 
					st.cguid as hidden_cguid,
					st.cbillcode,
					pl.crealname as tjr,
					'0' as  hidden_ccheckway,
					st.iAuditStatus as hidden_iauditstatus,
					'库存调拨单'+st.cbillcode  as bt,
					'072' as hidden_cbilltype,
					'' as bm,
					'' as kh,
					'' as ywy,
					'' as ywlx,
					'' as ywlc,
					CONVERT(varchar(50), st.dcreatortime,120) as sqsj
				from ST_StkTrans st
				left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid	
				where st.iAuditStatus = 'saved' 
					and exists ( select 1 from ST_StkTransline a 
								inner join cm_material ma on ma.cguid = a.cmatguid
								inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
								where a.cHeadGUID = st.cGUID )
					and ({sgStTrans:'false'}='true')
					and (  ('*' = {keyword:'*'} or convert(varchar(50),st.dCreatorTime,112) like '%{keyword}%')
					or $like(pl.crealname,keyword)
					or ('*' = {keyword:'*'} or '库存调拨单' like '%{keyword}%'))

			union all
	
				select 
					st.cguid as hidden_cguid,
					st.cbillcode,
					pl.crealname as tjr,
					'2' as  hidden_ccheckway,
					st.iAuditStatus as hidden_iauditstatus,
					'库存调拨单'+st.cbillcode  as bt,
					'072' as hidden_cbilltype,
					'' as bm,
					'' as kh,
					'' as ywy,
					'' as ywlx,
					'' as ywlc,
					CONVERT(varchar(100),e.START_,120) as sqsj
				from ST_StkTrans [-filter]  st
				left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid	
				left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				inner join(
					select distinct a.PROCINST_,a.CREATE_ as  START_
					from JBPM4_TASK a
					left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
						(a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					and a.DBID_ NOT IN
							(SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
					) e on e.PROCINST_ = d.INSTANCE_
				where  ({wfStTrans:'false'}='true')
					and ( (CONVERT(varchar(100),e.START_,112) like '%{keyword}%') 
					or $like(pl.crealname,keyword)
					or ('*' = {keyword:'*'} or '库存调拨单' like '%{keyword}%'))
			
			union all
			
				select 
					st.cguid as hidden_cguid,
					st.cbillcode,
					pl.crealname as tjr,
					'2' as  hidden_ccheckway,
					st.iAuditStatus as hidden_iauditstatus,
					'库存调拨单'+st.cbillcode  as bt,
					'072' as hidden_cbilltype,
					'' as bm,
					'' as kh,
					'' as ywy,
					'' as ywlx,
					'' as ywlc,
					CONVERT(varchar(100),e.START_,120) as sqsj
       			from ST_StkTrans   st
				left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid	
				left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				inner join(
					select distinct a.PROCINST_,a.CREATE_ as  START_
					from JBPM4_TASK a
					left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
							(a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
						and a.DBID_ NOT IN
							(SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
					) e on e.PROCINST_ = d.INSTANCE_
				where  ({wffilterStTrans:'false'}='true')
					and ( (CONVERT(varchar(100),e.START_,112) like '%{keyword}%') 
					or $like(pl.crealname,keyword)
					or ('*' = {keyword:'*'} or '库存调拨单' like '%{keyword}%'))
		
			union all
				/*材料出库，销售出库*/ 
				select 
					st.cguid as hidden_cguid,
					st.cbillcode,
					pl.crealname as tjr,
					'0' as  hidden_ccheckway,
					st.iAuditStatus as hidden_iauditstatus,
					CASE 
						WHEN st.cBillType='070' THEN '材料出库单' + st.cbillcode
						WHEN st.cBillType= '020' THEN '销售出库单' + st.cbillcode
						WHEN st.cBillType= '021' THEN '期初销售出库单' + st.cbillcode
					END AS bt,
					st.cBillType as hidden_cbilltype,
					de.cname as bm,
					cu.cName as kh,
					em.cName as ywy,
					bustp.cName as ywlx,
					buspr.cName as ywlc,
					CONVERT(varchar(50), st.dcreatortime,120) as sqsj
				FROM ST_StkRecord st
				LEFT JOIN ST_StkRecordLine sl ON sl.cHeadGUID = st.cGUID 
				LEFT JOIN AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid	
				LEFT JOIN CM_Customer cu ON cu.cGUID = st.cCustGUID
				LEFT JOIN CM_Department de ON de.cGUID = st.cDeptGUID
				LEFT JOIN CM_Employee em ON em.cGUID = st.cEmpGUID
				LEFT JOIN CO_BusinessType bustp ON bustp.cGUID = st.cBusType
				LEFT JOIN CO_BusinessProcess buspr ON buspr.cGUID = st.cBusProcess
				where st.iAuditStatus = 'saved' 
					and exists ( select 1 from ST_StkRecordLine a 
								inner join cm_material ma on ma.cguid = a.cmatguid
								inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
								where a.cHeadGUID = st.cGUID )
					and (	(({sgstinout:'false'}='true')
							and st.cBillType = '070'
							and (('*' = {keyword:'*'} or convert(varchar(50),st.dCreatorTime,112) like '%{keyword}%')
							or $like(pl.crealname,keyword)
							or ('*' = {keyword:'*'} or '材料出库单' like '%{keyword}%')))
						or 
							(({sgsastkout:'false'}='true')
							and st.cBillType in ('020','021')
							and (('*' = {keyword:'*'} or convert(varchar(50),st.dCreatorTime,112) like '%{keyword}%')
							or $like(pl.crealname,keyword)
							or ('*' = {keyword:'*'} or '销售出库单' like '%{keyword}%')))
						)
					
					
			union all
			
				select 
					st.cguid as hidden_cguid,
					st.cbillcode,
					pl.crealname as tjr,
					'2' as  hidden_ccheckway,
					st.iAuditStatus as hidden_iauditstatus,
					CASE 
						WHEN st.cBillType='070' THEN '材料出库单' + st.cbillcode
						WHEN st.cBillType= '020' THEN '销售出库单' + st.cbillcode
						WHEN st.cBillType= '021' THEN '期初销售出库单' + st.cbillcode
					END AS bt,
					st.cBillType as hidden_cbilltype,
					de.cname as bm,
					cu.cName as kh,
					em.cName as ywy,
					bustp.cName as ywlx,
					buspr.cName as ywlc,
					CONVERT(varchar(100),e.START_,120) as sqsj
				from ST_StkRecord [-filter]  st
				left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid	
				left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				LEFT JOIN CM_Customer cu ON cu.cGUID = st.cCustGUID
				LEFT JOIN CM_Department de ON de.cGUID = st.cDeptGUID
				LEFT JOIN CM_Employee em ON em.cGUID = st.cEmpGUID
				LEFT JOIN CO_BusinessType bustp ON bustp.cGUID = st.cBusType
				LEFT JOIN CO_BusinessProcess buspr ON buspr.cGUID = st.cBusProcess
				inner join(
					select distinct a.PROCINST_,a.CREATE_ as  START_
					from JBPM4_TASK a
					left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
						(a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					and a.DBID_ NOT IN
						(SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where ( ({wfstinout:'false'}='true')
						and st.cBillType = '070'
						and ((CONVERT(varchar(100),e.START_,112) like '%{keyword}%') 
						or $like(pl.crealname,keyword)
						or ('*' = {keyword:'*'} or '材料出库单' like '%{keyword}%' )) )
					or ( ({wfsastkout:'false'}='true')
						and st.cBillType in ('020','021')
						and ((CONVERT(varchar(100),e.START_,112) like '%{keyword}%') 
						or $like(pl.crealname,keyword)
						or ('*' = {keyword:'*'} or '销售出库单' like '%{keyword}%' )) )
					
				
			union all
					
				select 
					st.cguid as hidden_cguid,
					st.cbillcode,
					pl.crealname as tjr,
					'2' as  hidden_ccheckway,
					st.iAuditStatus as hidden_iauditstatus,
					CASE 
						WHEN st.cBillType='070' THEN '材料出库单' + st.cbillcode
						WHEN st.cBillType= '020' THEN '销售出库单' + st.cbillcode
						WHEN st.cBillType= '021' THEN '期初销售出库单' + st.cbillcode
					END AS bt,
					st.cBillType as hidden_cbilltype,
					de.cname as bm,
					cu.cName as kh,
					em.cName as ywy,
					bustp.cName as ywlx,
					buspr.cName as ywlc,
					CONVERT(varchar(100),e.START_,120) as sqsj
       			from ST_StkRecord st
       			LEFT JOIN ST_StkRecordLine sl ON sl.cHeadGUID = st.cGUID 
				left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid	
				LEFT JOIN CM_Customer cu ON cu.cGUID = st.cCustGUID
				LEFT JOIN CM_Department de ON de.cGUID = st.cDeptGUID
				LEFT JOIN CM_Employee em ON em.cGUID = st.cEmpGUID
				LEFT JOIN CO_BusinessType bustp ON bustp.cGUID = st.cBusType
				LEFT JOIN CO_BusinessProcess buspr ON buspr.cGUID = st.cBusProcess
				left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				inner join(
					select distinct a.PROCINST_,a.CREATE_ as  START_
					from JBPM4_TASK a
					left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
							(a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
						and a.DBID_ NOT IN
							(SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
					) e on e.PROCINST_ = d.INSTANCE_
				where	( ({wffilterstinout:'false'}='true')
							and st.cBillType = '070'
							and ( (CONVERT(varchar(100),e.START_,112) like '%{keyword}%') 
							or $like(pl.crealname,keyword)
							or ('*' = {keyword:'*'} or '材料出库单' like '%{keyword}%')) )
					or	( ({wffiltersastkout:'false'}='true')
							and st.cBillType in ('020','021')
							and ( (CONVERT(varchar(100),e.START_,112) like '%{keyword}%') 
							or $like(pl.crealname,keyword)
							or ('*' = {keyword:'*'} or '销售出库单' like '%{keyword}%')) )	
						
		    @filter[STDateDataRightSetBeforeQueryPlugin_feild,
	            ST_StkTrans.cCreatorGUID=AOS_RMS_USER.cguid,
	            ST_StkTransLine.cMatGUID=CM_Material.cguid,
	            ST_StkTransLine.cItemGUID=CM_Project.cguid,
	            ST_StkTransLine.cMatGUID=CM_Material.cguid and CM_Material.cMatCGUID=CM_MatClass.cGUID,
	            
	        	ST_StkRecordLine.cMatGUID=CM_Material.cguid and CM_Material.cMatCGUID=CM_MatClass.cGUID,
	        	ST_StkRecordLine.cMatGUID=CM_Material.cguid,
	        	ST_StkRecordLine.cItemGUID=CM_Project.cguid,
	        	ST_StkRecord.cStoreGUID=CM_Storehouse.cguid,
	        	ST_StkRecordLine.cStoreGUID=CM_Storehouse.cguid,
	        	ST_StkRecord.cCreatorGUID=AOS_RMS_USER.cguid,
	        	st_stkrecord.cEmpGUID=CM_Employee.cGUID,
	        	st_stkrecord.cdeptguid=CM_Department.cguid,
	            st_stkrecord.cCustGUID=CM_Customer.cGUID,
	            st_stkrecord.cCustGUID=CM_Customer.cGUID and cm_customer.cclassguid=cm_customerclass.cguid,
	            ST_StkRecord.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
	            ST_StkRecord.cSupGUID=cm_supplier.cguid
	            
	            
	        	
			]
		)sa
			order by sqsj desc
		]]></i>	
  	  
		<i id="getallstnum"><![CDATA[
	  	     select sum(sao.num)  from 
	  	     (
				select 
	            	  count(1) as num
				from ST_StkTrans st
				left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
				where st.iAuditStatus = 'saved' 
				and ({sgStTrans:'false'}='true')
			
			union all
		         	
				select 
					count(1) as num
				from ST_StkTrans[-filter]  po 
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				inner join(
					select distinct a.PROCINST_
					from JBPM4_TASK a
					left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' 
						and (a.ASSIGNEE_ = {currUserGUID} or 
							(a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
						and a.DBID_ NOT IN
							(SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where ({wfStTrans:'false'}='true')					
			
			union all
		   
				select 
					count(1) as num
				from ST_StkTrans po 
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				inner join(
					select distinct a.PROCINST_
					from JBPM4_TASK a
					left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' 
						and (a.ASSIGNEE_ = {currUserGUID} or 
							(a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
						and a.DBID_ NOT IN
							(SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where ({wffilterStTrans:'false'}='true')	
			
			union all
				/*销售出库单，材料出库单*/
				select 
	            	  count(1) as num
				from ST_StkRecord st
				left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
				where st.iAuditStatus = 'saved' 
				and ( (({sgstinout:'false'}='true')
						and st.cBillType ='070') 
					or (({sgsastkout:'false'}='true')
						and st.cBillType in ('020','021'))
					)
				and st.cBillType in ('020','021','070')
				
			union all	
				
				select 
					count(1) as num
				from ST_StkRecord[-filter]  po 
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				inner join(
					select distinct a.PROCINST_
					from JBPM4_TASK a
					left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' 
						and (a.ASSIGNEE_ = {currUserGUID} or 
							(a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
						and a.DBID_ NOT IN
							(SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where ( ({wfstinout:'false'}='true')	
						and po.cBillType ='070' )
					or ( ({wfsastkout:'false'}='true')	
						and po.cBillType in ('020','021') )
					and po.cBillType in ('020','021','070')
					
			union all	
				
				select 
					count(1) as num
				from ST_StkRecord po 
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				inner join(
					select distinct a.PROCINST_
					from JBPM4_TASK a
					left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' 
						and (a.ASSIGNEE_ = {currUserGUID} or 
							(a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
						and a.DBID_ NOT IN
							(SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where  ( ({wffilterstinout:'false'}='true')
						and po.cBillType = '070' )
					or ( ({wffiltersastkout:'false'}='true')
						and po.cBillType in ('020','021') )
					and po.cBillType in ('020','021','070')
								
			    @filter[STDateDataRightSetBeforeQueryPlugin_feild,
		            ST_StkTrans.cCreatorGUID=AOS_RMS_USER.cguid,
		            ST_StkTransLine.cMatGUID=CM_Material.cguid,
		            ST_StkTransLine.cItemGUID=CM_Project.cguid,
		            ST_StkTransLine.cMatGUID=CM_Material.cguid and CM_MatClass.cGUID=CM_Material.cMatCGUID,
		            ST_StkRecordLine.cMatGUID=CM_Material.cguid and CM_MatClass.cGUID=CM_Material.cMatCGUID,
		        	ST_StkRecordLine.cItemGUID=CM_Project.cguid,
		        	ST_StkRecordLine.cMatGUID=CM_Material.cguid,
		        	ST_StkRecordLine.cStoreGUID=CM_Storehouse.cguid,
		        	ST_StkRecord.cCreatorGUID=AOS_RMS_USER.cguid
	            ]
			)sao
		]]></i>
	</sql>		
	<sql group="mobile_daiban_stbill">
	
		<i id="sttransmain"  desp="调拨单主表加载"><![CDATA[
			select 
				'库存调拨单'+st.cbillcode as "标题"
			from ST_StkTrans st
			where st.cguid={cGUID}
		]]></i>
		
		<i id="ststkrecordmain"  desp="材料，销售出库单主表加载"><![CDATA[
			select 
				CASE 
					WHEN st.cBillType='070' THEN '材料出库单' + st.cbillcode
					WHEN st.cBillType= '020' THEN '销售出库单' + st.cbillcode
					WHEN st.cBillType= '021' THEN '期初销售出库单' + st.cbillcode 
				END AS "标题"
			from ST_StkRecord st
			where st.cguid={cGUID}
		]]></i>
		
		<i id="sttransdetail"  desp="调拨单明细表加载"><![CDATA[
			SELECT
				pl.cguid,
				('['+cm.cMatCode+']'+cm.cMatName) as "cm_material.cname.st_stktransline.cmatguid",
				case 
					when isnull(cm.iPrecision,0)=0 then left(cast(pl.iUnitQTY as varchar(21)),charindex('.',cast(pl.iUnitQTY as varchar(21)))-1)
						else left(cast(pl.iUnitQTY as varchar(21)) , charindex('.',cast(pl.iUnitQTY as varchar(21))) +cm.iPrecision)
					end  +u.cName  as "st_stktransline.iunitqty",
				cs.cname as "cm_storehouse.cname.st_stktransline.coutstoreguid",
				cs1.cname as "cm_storehouse.cname.st_stktransline.cinstoreguid"
			FROM ST_StkTransline pl
			left join CM_Material cm on pl.cmatguid = cm.cguid	
			left join CM_Unit u on pl.cMUnitGUID = u.cguid
			left join  CM_Storehouse cs on cs.cguid=pl.coutstoreguid
			left join  CM_Storehouse cs1 on cs1.cguid=pl.cinstoreguid
			where pl.cheadguid={cGUID}
		]]></i>
	
		<i id="ststkrecorddetail" desp="材料，销售出库单明细加载"><![CDATA[
			SELECT
				pl.cguid,
				('['+cm.cMatCode+']'+cm.cMatName) as "cm_material.cname.st_stkrecordline.cmatguid",
				cm.cspec as "cm_material.cspec.st_stkrecordline.cmatguid",
				case 
					when isnull(cm.iPrecision,0)=0 then left(cast(pl.iUnitQTY as varchar(21)),charindex('.',cast(pl.iUnitQTY as varchar(21)))-1)
						else left(cast(pl.iUnitQTY as varchar(21)) , charindex('.',cast(pl.iUnitQTY as varchar(21))) +cm.iPrecision)
					end  +u.cName  as "st_stkrecordline.iqty",
				cs.cname as "cm_storehouse.cname.st_stkrecordline.storeguid",
				case 
					when isnull(CAST((SELECT cvalue FROM AOS_PREF_VALUE	WHERE ccode = 'U3_ST_PricePrecision') AS INT),0)=0 then left(cast(pl.iTotal_F as varchar(21)),charindex('.',cast(pl.iTotal_F as varchar(21)))-1)
						else left(cast(pl.iTotal_F as varchar(21)) , charindex('.',cast(pl.iTotal_F as varchar(21))) +CAST ((SELECT cvalue FROM AOS_PREF_VALUE	WHERE ccode = 'U3_ST_PricePrecision') AS INT))
					end as "st_stkrecordline.itotal"
			FROM ST_StkRecordLine pl
			left join CM_Material cm on pl.cmatguid = cm.cguid
			left join CM_Unit u on pl.cMUnitGUID = u.cguid
			left join CM_Storehouse cs on cs.cguid=pl.cStoreGUID
			where pl.cheadguid={cGUID}
		]]></i>
	</sql>
	
</sqls>