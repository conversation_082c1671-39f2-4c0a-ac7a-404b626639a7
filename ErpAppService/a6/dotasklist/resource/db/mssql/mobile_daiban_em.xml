<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_daiban_emList">
		<!-- 2017-02-28 qcj 修正获取所有列表数据和数目的sql -->
		<!-- 2017-03-02 qcj 增加金额的千分位显示 -->
		<!-- 2017-03-06 qcj 修正工作流审批代办事项取值sql -->
		<!-- 2017-03-08 qch 修正工作流审批中人员参数，修正申请人数据权限参数取值 -->
		<i id="getAllList" desp="获取所有列表数据">
			<![CDATA[
				select top 100 *
				from(
				select loan.cguid as hidden_cguid,loan.cCode as hidden_code,'0' as hidden_cCheckWay,
				   loan.cStatusEnumGUID as hidden_iauditstatus,
					'oa_008' as hidden_cbilltype,'借款申请'+loan.cCode as bt,
					CONVERT(varchar(100),loan.dAppDate,23) as sqsj,
					convert(nvarchar, cast(round(loan.iLoanAMT, 2) as money),1) as je,
					loanee.cName as zy,lDept.cName as bm
				from OA_LoanBill loan
				left join CM_Employee loanee on loanee.cGUID = loan.cLoaneeGUID
				left join CM_Department lDept on lDept.cGUID = loan.cLoaneeDeptGUID
				where 'sg'={sgspqxLoan:''}
				and loan.cStatusEnumGUID = 'saved'
				and ($like(loanee.cName,keyword)
					or (CONVERT(varchar(100),loan.dAppDate,112) like '%{keyword}%')
					or ('*' = {keyword:'*'} or '借款申请' like '%{keyword}%')
				)
				union all
				select loan.cguid as hidden_cguid,loan.cCode as hidden_code,'2' as hidden_cCheckWay,
				loan.cStatusEnumGUID as hidden_iauditstatus,
					'oa_008' as hidden_cbilltype,'借款申请'+loan.cCode as bt,
					CONVERT(varchar(100),loan.dAppDate,23) as sqsj,
					convert(nvarchar, cast(round(loan.iLoanAMT, 2) as money),1) as je,
					loanee.cName as zy,lDept.cName as bm
				from OA_LoanBill[-filter] loan
				left join CM_Employee loanee on loanee.cGUID = loan.cLoaneeGUID
				left join CM_Department lDept on lDept.cGUID = loan.cLoaneeDeptGUID
				left join JBPM4_EXECUTION d on d.KEY_ = loan.cguid
				inner join(
	                select distinct a.PROCINST_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where 'workFlow' = {spqxLoan:''} 
				and ($like(loanee.cName,keyword)
					or (CONVERT(varchar(100),loan.dAppDate,112) like '%{keyword}%')
					or ('*' = {keyword:'*'} or '借款申请' like '%{keyword}%')
					)
				union all
				select loan.cguid as hidden_cguid,loan.cCode as hidden_code,'2' as hidden_cCheckWay,
				    loan.cStatusEnumGUID as hidden_iauditstatus,
					'oa_008' as hidden_cbilltype,'借款申请'+loan.cCode as bt,
					CONVERT(varchar(100),loan.dAppDate,23) as sqsj,
					convert(nvarchar, cast(round(loan.iLoanAMT, 2) as money),1) as je,
					loanee.cName as zy,lDept.cName as bm
				from OA_LoanBill loan
				left join CM_Employee loanee on loanee.cGUID = loan.cLoaneeGUID
				left join CM_Department lDept on lDept.cGUID = loan.cLoaneeDeptGUID
				left join JBPM4_EXECUTION d on d.KEY_ = loan.cguid
				inner join(
	                select distinct a.PROCINST_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where 'workFlowSjqx'={spqxLoan:''}
				and ($like(loanee.cName,keyword)
					or (CONVERT(varchar(100),loan.dAppDate,112) like '%{keyword}%')
					or ('*' = {keyword:'*'} or '借款申请' like '%{keyword}%')
					)
				union all
				select eab.cguid as hidden_cguid,eab.cCode as hidden_code,'0' as hidden_cCheckWay,
				    eab.cStatusEnumGUID as hidden_iauditstatus,
					'oa_010' as hidden_cbilltype,'报销申请'+eab.cCode as bt,
					CONVERT(varchar(100),eab.cRefundDate,23) as sqsj,
					convert(nvarchar, cast(round(eab.iTotalAMT, 2) as money),1) as je,
					expUser.cName as zy,expDept.cName as bm
				from OA_ExpenseAccountBill eab
				left join CM_Employee expUser on expUser.cGUID = eab.cExpUserGUID
				left join CM_Department expDept on expDept.cGUID = eab.cExpDeptGUID
				where 'sg'={sgspqxEm:''}
				and eab.cStatusEnumGUID = 'saved'
				and ($like(expUser.cName,keyword) 
					or (CONVERT(varchar(100),eab.cRefundDate,112) like '%{keyword}%')
					or ('*' = {keyword:'*'} or '报销申请' like '%{keyword}%')
				)
				union all
				select eab.cguid as hidden_cguid,eab.cCode as hidden_code,'2' as hidden_cCheckWay,
					eab.cStatusEnumGUID as hidden_iauditstatus,
					'oa_010' as hidden_cbilltype,'报销申请'+eab.cCode as bt,
					CONVERT(varchar(100),eab.cRefundDate,23) as sqsj,
					convert(nvarchar, cast(round(eab.iTotalAMT, 2) as money),1) as je,
					expUser.cName as zy,expDept.cName as bm
				from OA_ExpenseAccountBill[-filter] eab
				left join CM_Employee expUser on expUser.cGUID = eab.cExpUserGUID
				left join CM_Department expDept on expDept.cGUID = eab.cExpDeptGUID
				left join JBPM4_EXECUTION d on d.KEY_ = eab.cguid
				inner join(
	                select distinct a.PROCINST_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where 'workFlow' = {spqxEm:''} 
				and ($like(expUser.cName,keyword) 
					or (CONVERT(varchar(100),eab.cRefundDate,112) like '%{keyword}%')
					or ('*' = {keyword:'*'} or '报销申请' like '%{keyword}%')
				)
				union all
				select eab.cguid as hidden_cguid,eab.cCode as hidden_code,'2' as hidden_cCheckWay,
				eab.cStatusEnumGUID as hidden_iauditstatus,
					'oa_010' as hidden_cbilltype,'报销申请'+eab.cCode as bt,
					CONVERT(varchar(100),eab.cRefundDate,23) as sqsj,
					convert(nvarchar, cast(round(eab.iTotalAMT, 2) as money),1) as je,
					expUser.cName as zy,expDept.cName as bm
				from OA_ExpenseAccountBill eab
				left join CM_Employee expUser on expUser.cGUID = eab.cExpUserGUID
				left join CM_Department expDept on expDept.cGUID = eab.cExpDeptGUID
				left join JBPM4_EXECUTION d on d.KEY_ = eab.cguid
				inner join(
	                select distinct a.PROCINST_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where 'workFlowSjqx' = {spqxEm:''}
				and ($like(expUser.cName,keyword) 
					or (CONVERT(varchar(100),eab.cRefundDate,112) like '%{keyword}%')
					or ('*' = {keyword:'*'} or '报销申请' like '%{keyword}%')
				)
				@filter[OA_LoanBill.cUserGUID={currUserEmpGUID} or OA_LoanBill.cLoaneeGUID={currUserEmpGUID} or '1'={currUserGUID},
					or OA_LoanBill.cLoaneeGUID=v_oa_loanExpenseUserInfo.cguid,
					or OA_LoanBill.cLoaneeDeptGUID=v_oa_loanExpenseDeptInfo.cguid,
					or OA_LoanBill.cStatusEnumGUID=v_oa_billStatus.cguid,
					OA_ExpenseAccountBill.cUserGUID={currUserEmpGUID} or OA_ExpenseAccountBill.cExpUserGUID={currUserEmpGUID} or '1'={currUserGUID},
					or OA_ExpenseAccountBill.cExpUserGUID=v_oa_loanExpenseUserInfo.cguid,
					or OA_ExpenseAccountBill.cExpDeptGUID=v_oa_loanExpenseDeptInfo.cguid,
					or OA_ExpenseAccountBill.cStatusEnumGUID=v_oa_billStatus.cguid]
				)em
				order by em.sqsj desc,em.hidden_code desc
			]]>
		</i>
		
		<i id="getAllListNum" desp="获取所有列表的总数">
			<![CDATA[
				select sum(em.num) as num
				from
				(
					select count(1) num
					from OA_LoanBill loan
					left join CM_Employee loanee on loanee.cGUID = loan.cLoaneeGUID
					left join CM_Department lDept on lDept.cGUID = loan.cLoaneeDeptGUID
					where 'sg'={sgspqxLoan:''}
					and loan.cStatusEnumGUID = 'saved'
					and ($like(loanee.cName,keyword)
						or (CONVERT(varchar(100),loan.dAppDate,112) like '%{keyword}%')
						or ('*' = {keyword:'*'} or '借款申请' like '%{keyword}%')
					)
					union all
					select count(1) num
					from OA_LoanBill[-filter] loan
					left join CM_Employee loanee on loanee.cGUID = loan.cLoaneeGUID
					left join CM_Department lDept on lDept.cGUID = loan.cLoaneeDeptGUID
					left join JBPM4_EXECUTION d on d.KEY_ = loan.cguid
					inner join(
		                select distinct a.PROCINST_
		                from JBPM4_TASK a
		                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
						where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
						  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
						  and a.DBID_ NOT IN
						   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
					) e on e.PROCINST_ = d.INSTANCE_
					where 'workFlow' = {spqxLoan:''} 
					and ($like(loanee.cName,keyword)
						or (CONVERT(varchar(100),loan.dAppDate,112) like '%{keyword}%')
						or ('*' = {keyword:'*'} or '借款申请' like '%{keyword}%')
						)
					union all
					select count(1) num
					from OA_LoanBill loan
					left join CM_Employee loanee on loanee.cGUID = loan.cLoaneeGUID
					left join CM_Department lDept on lDept.cGUID = loan.cLoaneeDeptGUID
					left join JBPM4_EXECUTION d on d.KEY_ = loan.cguid
					inner join(
		                select distinct a.PROCINST_
		                from JBPM4_TASK a
		                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
						where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
						  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
						  and a.DBID_ NOT IN
						   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
					) e on e.PROCINST_ = d.INSTANCE_
					where 'workFlowSjqx'={spqxLoan:''}
					and ($like(loanee.cName,keyword)
						or (CONVERT(varchar(100),loan.dAppDate,112) like '%{keyword}%')
						or ('*' = {keyword:'*'} or '借款申请' like '%{keyword}%')
						)
					union all
					select count(1) num
					from OA_ExpenseAccountBill eab
					left join CM_Employee expUser on expUser.cGUID = eab.cExpUserGUID
					left join CM_Department expDept on expDept.cGUID = eab.cExpDeptGUID
					where 'sg'={sgspqxEm:''}
					and eab.cStatusEnumGUID = 'saved'
					and ($like(expUser.cName,keyword) 
						or (CONVERT(varchar(100),eab.cRefundDate,112) like '%{keyword}%')
						or ('*' = {keyword:'*'} or '报销申请' like '%{keyword}%')
					)
					union all
					select count(1) num
					from OA_ExpenseAccountBill[-filter] eab
					left join CM_Employee expUser on expUser.cGUID = eab.cExpUserGUID
					left join CM_Department expDept on expDept.cGUID = eab.cExpDeptGUID
					left join JBPM4_EXECUTION d on d.KEY_ = eab.cguid
					inner join(
		                select distinct a.PROCINST_
		                from JBPM4_TASK a
		                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
						where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
						  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
						  and a.DBID_ NOT IN
						   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
					) e on e.PROCINST_ = d.INSTANCE_
					where 'workFlow' = {spqxEm:''} 
					and ($like(expUser.cName,keyword) 
						or (CONVERT(varchar(100),eab.cRefundDate,112) like '%{keyword}%')
						or ('*' = {keyword:'*'} or '报销申请' like '%{keyword}%')
					)
					union all
					select count(1) num
					from OA_ExpenseAccountBill eab
					left join CM_Employee expUser on expUser.cGUID = eab.cExpUserGUID
					left join CM_Department expDept on expDept.cGUID = eab.cExpDeptGUID
					left join JBPM4_EXECUTION d on d.KEY_ = eab.cguid
					inner join(
		                select distinct a.PROCINST_
		                from JBPM4_TASK a
		                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
						where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
						  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
						  and a.DBID_ NOT IN
						   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
					) e on e.PROCINST_ = d.INSTANCE_
					where 'workFlowSjqx' = {spqxEm:''}
					and ($like(expUser.cName,keyword) 
						or (CONVERT(varchar(100),eab.cRefundDate,112) like '%{keyword}%')
						or ('*' = {keyword:'*'} or '报销申请' like '%{keyword}%')
					)
					@filter[OA_LoanBill.cUserGUID={currUserEmpGUID} or OA_LoanBill.cLoaneeGUID={currUserEmpGUID} or '1'={currUserGUID},
						or OA_LoanBill.cLoaneeGUID=v_oa_loanExpenseUserInfo.cguid,
						or OA_LoanBill.cLoaneeDeptGUID=v_oa_loanExpenseDeptInfo.cguid,
						or OA_LoanBill.cStatusEnumGUID=v_oa_billStatus.cguid,
						OA_ExpenseAccountBill.cUserGUID={currUserEmpGUID} or OA_ExpenseAccountBill.cExpUserGUID={currUserEmpGUID} or '1'={currUserGUID},
						or OA_ExpenseAccountBill.cExpUserGUID=v_oa_loanExpenseUserInfo.cguid,
						or OA_ExpenseAccountBill.cExpDeptGUID=v_oa_loanExpenseDeptInfo.cguid,
						or OA_ExpenseAccountBill.cStatusEnumGUID=v_oa_billStatus.cguid]
				)em
			]]>
		</i>
		
		
	</sql>
	
	<!-- 借款报销单详情数据 -->
	<sql group="mobile_daiban_emBill">
		<!-- 2017-01-10 qcj 修正借款报销单据详情显示 -->
		<!-- 2017-01-20 qcj 修正报销单详情日期格式为年月日 
		<i id="expenseAccountMain" desp="报销单主表数据">
			<![CDATA[
				select '报销申请'+eab.cCode as '标题',expUser.cName as '报销人',
					CONVERT(varchar(100),eab.cRefundDate,23) as '报销日期',eab.cReason as '报销原因',
					isnull(eab.iTotalAMT,0) as '报销金额',eab.iRefundNum as '单据张数'
				from OA_ExpenseAccountBill eab
				left join CM_Employee expUser on expUser.cGUID = eab.cExpUserGUID
				left join CM_Department expDept on expDept.cGUID = eab.cExpDeptGUID
				where eab.cguid={cGUID}
			]]>
		</i>
		-->
		<!-- 2017-08-01 quchj 根据单据设计修正报销单查询主表特殊处理字段 -->
		<!-- 2017-10-21 quchj 修正对于借款报销单金额精度显示问题 -->
		<i id="expenseAccountMain" desp="报销单主表特殊字段">
			<![CDATA[
				select CONVERT(varchar(100),eab.cRefundDate,23) as 'oa_expenseaccountbill.crefunddate',
					convert(nvarchar, cast(round(isnull(eab.iTotalAMT,0) ,2) as money),1) as 'oa_expenseaccountbill.itotalamt'
				from OA_ExpenseAccountBill eab
				where eab.cguid={cGUID}
			]]>
		</i>
		<!-- 2017-01-06 qcj 修正查询关联表 -->
		<!-- 2017-01-16 qcj 修正报销金额取值发生金额而不再是实报金额 
		<i id="expenseAccountDetail" desp="报销单明细数据">
			<![CDATA[
				select expClass.cName as '费用类别',dbo.f_clearZero(cast(ecLine.iOpeningAMT as varchar(21))) as '报销金额'
				from OA_ExpenseAccLine ecLine
				left join OA_ExpenseClass expClass on expClass.cGUID = ecLine.cExpenseClassGUID
				where ecLine.cheadguid={cGUID}
			]]>
		</i>
		-->
		<!-- 2017-08-01 quchj 根据单据设计修正报销单查询子表特殊处理字段 -->
		<i id="expenseAccountDetail" desp="报销单子表特殊字段">
			<![CDATA[
				select ecLine.cguid,
					convert(nvarchar, cast(round(isnull(ecLine.iOpeningAMT,0) ,2) as money),1) as 'oa_expenseaccline.iopeningamt'
				from OA_ExpenseAccLine ecLine
				where ecLine.cheadguid={cGUID}
			]]>
		</i>
		
		<!-- 2017-08-01 quchj 根据单据设计修正借款单查询特殊处理字段 -->
		<!-- 2017-01-20 qcj 修正借款单详情日期格式为年月日 
		<i id="LoanMain" desp="借款单主表数据">
			<![CDATA[
				select '借款申请'+loan.cCode as '标题',loanee.cName as '借款人', lDept.cName as '借款部门',
					CONVERT(varchar(100),loan.dAppDate,23) as '申请日期',
					CONVERT(varchar(100),loan.dRepaymentDate,23) as '预计还款日期',
					isnull(loan.iLoanAMT,0) as '借款金额',loan.cReason as '借款原因'
				from OA_LoanBill loan
				left join CM_Employee loanee on loanee.cGUID = loan.cLoaneeGUID
				left join CM_Department lDept on lDept.cGUID = loan.cLoaneeDeptGUID
				where loan.cguid={cGUID}
			]]>
		</i>
		-->
		<i id="loanMain" desp="借款单特殊处理字段">
			<![CDATA[
				select loan.cguid,CONVERT(varchar(100),loan.dAppDate,23) as 'oa_loanbill.dappdate',
					CONVERT(varchar(100),loan.dRepaymentDate,23) as 'oa_loanbill.drepaymentdate',
					convert(nvarchar, cast(round(isnull(loan.iLoanAMT,0) ,2) as money),1) as 'oa_loanbill.iloanamt'
				from OA_LoanBill loan
				where loan.cguid={cGUID}
			]]>
		</i>
	</sql>
</sqls>