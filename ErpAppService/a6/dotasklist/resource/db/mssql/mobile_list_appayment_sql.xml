<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_list_ap_payment_sql" desp="列表数据加载">
		<i id="ApPaymentList" desp="（默认按客户）应收列表"><![CDATA[
			select top 100 percent
				detail.iYear,detail.iPeriod,detail.cVouGUID,detail.cLineGUID,detail.cVouCode,CONVERT(varchar(10), detail.dVouDate, 120) AS dVouDate,
				detail.cVouKind,detail.cVouTypeName,detail.dTallykDate,detail.cCheckCode,detail.cCheckType,
				custClass.cGUID cSupClassGUID,custClass.cCode cSupClassCode,custClass.cName cSupClass,
				detail.cSupGUID,CM_Customer.cCode cSupCode,CM_Customer.cName cSupName,
				detail.cDeptGUID,CM_Department.cCode cDeptCode,CM_Department.cName cDeptName,
				detail.cEmpGUID,CM_Employee.cCode cEmpCode,CM_Employee.cName cEmpName,
				matClass.cCode cMatCCode,matClass.cName cMatCName,
				detail.cMatGUID,CM_Material.cCode cMatCode,CM_Material.cName cMatName,
				CM_Project.cCode cItemCode, CM_Project.cName cItemName, CM_Project.cGUID cItemGUID,
				detail.cCurGUID,GL_Currency.cName cCurName,
				isnull(saInv.cRemark,'') + isnull(saDis.cRemark,'') + isnull(st.cRemark,'') + isnull(carr.cRemark,'') + isnull(voucher.cSummary,'') + isnull(pay.cRemark,'') as cRemark,CA_SettleMode.cName cSettleName,
				isnull(detail.iDebitAMT_F, 0) iDebitAMT_F, isnull(detail.iDebitAMT, 0) iDebitAMT,
				isnull(detail.iCreditAMT_F, 0) iCreditAMT_F, isnull(detail.iCreditAMT, 0) iCreditAMT,
				detail.iBalanceAMT_F, detail.iBalanceAMT,
				isnull(detail.iBalanceAMT_F, 0) iBalanceglAMT_F, isnull(detail.iBalanceAMT, 0) iBalanceglAMT,
				(isnull(detail.iBalanceAMT_F, 0) + isnull(detail.iDebitAMT_F, 0) - isnull(detail.iCreditAMT_F, 0)) as iInitAMT_F,
				(isnull(detail.iBalanceAMT, 0) + isnull(detail.iDebitAMT, 0) - isnull(detail.iCreditAMT, 0)) as iInitAMT
			@field[]
  			from (
  				select a.iYear, a.iPeriod, a.cVouGUID, a.cLineGUID, a.cVouCode, a.dVouDate, a.cVouKind,
  					a.cVouTypeName, a.dTallykDate, a.cCheckCode, a.cCheckType,
  					a.cSettleTypeGUID, a.cSupGUID, a.cDeptGUID, a.cEmpGUID, a.cMatGUID, a.cItemGUID, a.cCurGUID,
  					a.iDebitAMT_F, a.iDebitAMT, a.iDebitQTY, a.iCreditAMT_F, a.iCreditAMT, a.iCreditQTY,
					null iBalanceAMT_F, null iBalanceAMT, null iBalanceQTY
				from
  				(
  					/* 已审核的单据应收,此部分只取转账业务的借方金额作为应收，贷方金额作为实收  */	
	  				select detail.iYear, detail.iPeriod, detail.cSettleTypeGUID, detail.cSupGUID, detail.cDeptGUID, detail.cEmpGUID,
	  					detail.cMatGUID, detail.cItemGUID, detail.cCurGUID, CONVERT(varchar(10), detail.dVouDate, 120) as dVouDate,
						CONVERT(varchar(10), isnull(detail.dTallykDate,detail.dVouDate), 120) as dTallykDate, detail.cCheckCode, detail.cRemark cCheckType,
						detail.cVouKind, detail.cVouGUID, detail.cVocLGUID cLineGUID, detail.cVouCode,
						case detail.cVouKind when '085' then '销售发票' when '086' then '应收单' else '销售费用发票' end cVouTypeName,
						detail.iDebitAMT_F, detail.iDebitAMT, detail.iDebitQTY,
						detail.iCreditAMT_F, detail.iCreditAMT, detail.iCreditQTY
					from AP_DetailAccount detail
					where detail.cFlag = 'AR'
						  and detail.iCheckDir != 0
						  and detail.iPeriod != 0
	  					  and detail.cVouKind in ('085', '086', '099')
	  					  and detail.cRemark != '收款核销'
	  					  and $between(detail.dTallykDate, startdate, enddate)
	  				union all
	  				/* 汇兑损益部分取数  */ 
	  				select detail.iYear, detail.iPeriod, detail.cSettleTypeGUID, detail.cSupGUID, detail.cDeptGUID, detail.cEmpGUID,
	  					detail.cMatGUID, detail.cItemGUID, detail.cCurGUID, CONVERT(varchar(10), detail.dVouDate, 120) as dVouDate,
						CONVERT(varchar(10), isnull(detail.dTallykDate,detail.dVouDate), 120) as dTallykDate, null cCheckCode, null cCheckType,
						'888' cVouKind, detail.cRelatingGUID cVouGUID, detail.cVocLGUID cLineGUID, detail.cVouCode, '汇兑损益' cVouTypeName,
						detail.iDebitAMT_F, detail.iDebitAMT, detail.iDebitQTY,
						detail.iCreditAMT_F, detail.iCreditAMT, detail.iCreditQTY
					from AP_DetailAccount detail
					where detail.cFlag = 'AR'
						and detail.cRemark = '汇兑损益'
						and $between(detail.dTallykDate, startdate, enddate)
	  				UNION ALL
	  				/* 单据实收，此部分只处理收款核销部分，根据是否显示核销记录条件过滤  */
	  				select detail.iYear, detail.iPeriod, detail.cSettleTypeGUID, detail.cSupGUID, detail.cDeptGUID, detail.cEmpGUID,
	  					detail.cMatGUID, detail.cItemGUID, detail.cCurGUID, CONVERT(varchar(10), detail.dVouDate, 120) as dVouDate,
						CONVERT(varchar(10), isnull(detail.dTallykDate,detail.dVouDate), 120) as dTallykDate, detail.cCheckCode, detail.cRemark cCheckType,
						detail.cVouKind, detail.cVouGUID, detail.cVocLGUID cLineGUID, detail.cVouCode, '收款单' cVouTypeName,
						0 iDebitAMT_F, 0 iDebitAMT, 0 iDebitQTY,
						case detail.cRemark when '收款核销' then (case when detail.dVouDate >= {startdate} then detail.iDebitAMT_F else 0 end ) else detail.iCreditAMT_F end as iCreditAMT_F,
						case detail.cRemark when '收款核销' then (case when detail.dVouDate >= {startdate} then detail.iDebitAMT else 0 end )  else detail.iCreditAMT end as iCreditAMT,
						case detail.cRemark when '收款核销' then (case when detail.dVouDate >= {startdate} then detail.iDebitQTY else 0 end )  else detail.iCreditQTY end as iCreditQTY
	  				from AP_DetailAccount detail
					where detail.cFlag = 'AR'
						  and detail.iPrePay = 0
						  and detail.cRemark != '收款核销'
						  and detail.cVouKind = '087'
						  and detail.iCheckDir != 0
						  and $between(detail.dTallykDate, startdate, enddate)
					UNION ALL
					/* 为了解决现结的应收金额查询原始单据的借贷方金额作为应收和实收。这里现结的应收和实收都不显示。当显示核销记录时，原始收款单已经核销的收款单不显示 */
					select pe.iYear, pe.iMonth iPeriod, a.cSettleTypeGUID, a.cSupGUID, a.cDeptGUID, a.cEmpGUID,
						a.cMatGUID, a.cItemGUID, a.cCurGUID, CONVERT(varchar(10), a.dVouDate, 120) as dVouDate,
						CONVERT(varchar(10), a.dVouDate, 120) as dTallykDate, null cCheckCode, null cCheckType,
						a.cVouKind, a.cGUID cVouGUID, a.cLineGUID, a.cVouCode, a.cVouTypeName,
						a.iDebitAMT_F, a.iDebitAMT, a.iDebitQTY, a.iCreditAMT_F, a.iCreditAMT, a.iCreditQTY
					from V_ARAPBillInfo a
						 left join GL_Period pe on a.dVouDate between pe.dBeginDate and pe.dEndDate and pe.iLeaf = 1
					WHERE a.cFlag = 'AR'
						  and a.cVouKind not in ('085', '087')
						  and a.iAuditStatus = 'checked'
						  and $between(a.dVouDate, startdate, enddate)
					UNION ALL
					/* 此部分只解决显示核销记录时，查询出未核销的单据金额做为实收  */
					select * from (
						select pe.iYear, pe.iMonth iPeriod, a.cSettleTypeGUID, a.cSupGUID, a.cDeptGUID, a.cEmpGUID,
							null cMatGUID, a.cItemGUID, a.cCurGUID, CONVERT(varchar(10), a.dVouDate, 120) as dVouDate,
							CONVERT(varchar(10), a.dVouDate, 120) as dTallykDate, null cCheckCode, null cCheckType,
							'087' cVouKind, a.cGUID cVouGUID, null cLineGUID, a.cVouCode,
							case a.iAuditStatus when 'checked' then 
								case when a.iPrePay = 0 then '收款单' else '预收单' end
							else case when a.iPrePay = 0 then '收款单(未审核)' else '预收单(未审核)' end end cVouTypeName,
							0 iDebitAMT_F, 0 iDebitAMT, 0 iDebitQTY,
							case '0' when 0 then (a.iPayAMT_F + isnull(a.ignoreAMT_F,0)) - isnull((select w.iPayAMT_F + isnull(w.ignoreAMT_F,0) from ap_payment w where w.cguid = a.iPreDevolveGUID and w.iprepay = 1),0) else
							(a.iPayAMT_F + isnull(a.ignoreAMT_F,0)) - isnull((select w.iPayAMT_F + isnull(w.ignoreAMT_F,0) from ap_payment w where w.cguid = a.iPreDevolveGUID and w.iprepay = 1),0) - isnull((select sum(d.iCreditAMT_F) from ap_detailaccount d where a.cguid = d.cPaymentGUID and a.iprepay = 0 and d.cVouKind in ('085', '099')), 0) -
							isnull((select sum(b.iCheckAMT_F) from AP_APCheck b where a.cGUID = b.cVouGUID and b.cType = 'AR01'
							and CONVERT(varchar(10), b.dVouDate, 120) between {iDateLower:b.dVouDate} and {iDateUpper:b.dVouDate}), 0) end as iCreditAMT_F,
							case '0' when 0 then (a.iPayAMT + isnull(a.ignoreAMT,0)) - isnull((select w.iPayAMT + isnull(w.ignoreAMT,0) from ap_payment w where w.cguid = a.iPreDevolveGUID and w.iprepay = 1),0) else
							(a.iPayAMT + isnull(a.ignoreAMT,0)) - isnull((select w.iPayAMT + isnull(w.ignoreAMT,0) from ap_payment w where w.cguid = a.iPreDevolveGUID and w.iprepay = 1),0) - isnull((select sum(d.iCreditAMT) from ap_detailaccount d where a.cguid = d.cPaymentGUID and a.iprepay = 0 and d.cVouKind in ('085', '099')), 0) -
							isnull((select sum(b.iCheckAMT) from AP_APCheck b where a.cGUID = b.cVouGUID and b.cType = 'AR01'
							and CONVERT(varchar(10), b.dVouDate, 120) between {iDateLower:b.dVouDate} and {iDateUpper:b.dVouDate}), 0) end as iCreditAMT, 0 iCreditQTY
						from AP_Payment a
							 left join GL_Period pe on a.dVouDate between pe.dBeginDate and pe.dEndDate and pe.iLeaf = 1
						where a.cFlag = 'AR'
							  and a.iAuditStatus != 'tsaved'
							  and cInvGUID is null
							  and a.iAuditStatus = 'checked'
							  and $between(a.dVouDate, startdate, enddate)
					) a where a.iCreditAMT_F != 0 and a.iCreditAMT != 0
					UNION ALL
					/* 现结金额不受任何条件影响,包含时显示  */
					select pe.iYear, pe.iMonth iPeriod, a.cSettleTypeGUID, a.cSupGUID, a.cDeptGUID, a.cEmpGUID,
						null cMatGUID, a.cItemGUID, a.cCurGUID, CONVERT(varchar(10), a.dVouDate, 120) as dVouDate,
						CONVERT(varchar(10), a.dVouDate, 120) as dTallykDate, null cCheckCode, null cCheckType,
						'087'cVouKind, a.cGUID cVouGUID, null cLineGUID, a.cVouCode, '收款单(现结)' cVouTypeName,
						0 iDebitAMT_F, 0 iDebitAMT, 0 iDebitQTY,
						(a.iPayAMT_F + isnull(a.ignoreAMT_F,0)) iCreditAMT_F, (a.iPayAMT + isnull(a.ignoreAMT,0)) iCreditAMT, 0 iCreditQTY
					from AP_Payment a
						 left join GL_Period pe on a.dVouDate between pe.dBeginDate and pe.dEndDate and pe.iLeaf = 1
					WHERE a.cFlag = 'AR'
						  and cInvGUID is not null
						  and $between(a.dVouDate, startdate, enddate)
					
					UNION ALL
					/* 处理现结金额的应收部分，包含现结时应收减少，不包含现结时应收增加  */
					select pe.iYear, pe.iMonth iPeriod, a.cSettleTypeGUID, a.cCustGUID, a.cDeptGUID, a.cEmpGUID,
						b.cMatGUID, b.cItemGUID, a.cCurGUID, CONVERT(varchar(10), a.dDate, 120) as dVouDate,
						CONVERT(varchar(10), a.dDate, 120) as dTallykDate, null cCheckCode, null cCheckType,
						'085' cVouKind, a.cGUID cVouGUID, b.cguid cLineGUID, a.cInvCode cVouCode,
						case a.iAuditStatus when 'checked' then '销售发票' else '销售发票(未审核)' end cVouTypeName,
						case when '1' = 1 then b.iTotal_F else b.iTotal_F - b.iPaymentTotal_F end as iDebitAMT_F,
						case when '1' = 1 then b.iTotal else b.iTotal - b.iPaymentTotal end as iDebitAMT, b.iUnitQTY iDebitQTY,
						0 iCreditAMT_F, 0 iCreditAMT, 0 iCreditQTY
					from SA_Invoice a
	 					 left join SA_InvoiceLine b on a.cGUID = b.cHeadGUID
						 left join GL_Period pe on a.dDate between pe.dBeginDate and pe.dEndDate and pe.iLeaf = 1
					WHERE a.iAuditStatus = 'checked'
						  and a.iAuditStatus != 'tsaved'
						  and $between(a.dDate, startdate, enddate)
						  
					UNION ALL
					/* 预收部分，包含预收时查询  */
					select detail.iYear, detail.iPeriod, detail.cSettleTypeGUID, detail.cSupGUID, detail.cDeptGUID, detail.cEmpGUID,
	  					detail.cMatGUID, detail.cItemGUID, detail.cCurGUID, CONVERT(varchar(10), detail.dVouDate, 120) as dVouDate,
						CONVERT(varchar(10), isnull(detail.dTallykDate,detail.dVouDate), 120) as dTallykDate, detail.cCheckCode, detail.cRemark cCheckType,
						detail.cVouKind, detail.cVouGUID, detail.cVocLGUID cLineGUID, detail.cVouCode, '预收单' cVouTypeName,
						0 iDebitAMT_F, 0 iDebitAMT, 0 iDebitQTY,
						case detail.cRemark when '预收冲预付' then -detail.iDebitAMT_F when '预付冲预收' then -detail.iDebitAMT_F
							else detail.iCreditAMT_F end as iCreditAMT_F,
						case detail.cRemark when '预收冲预付' then -detail.iDebitAMT when '预付冲预收' then -detail.iDebitAMT
							else detail.iCreditAMT end as iCreditAMT, 
						case detail.cRemark when '预收冲预付' then -detail.iDebitQTY when '预付冲预收' then -detail.iDebitQTY
							else detail.iCreditQTY end as iCreditQTY
					from AP_DetailAccount detail
					where detail.cFlag = 'AR'
						  and detail.iPeriod != 0
						  and detail.iCheckDir != 0
	  					  and detail.cVouKind = '087'
	  					  and detail.iPrePay = 1
	  					  and $between(detail.dTallykDate, startdate, enddate)
	  				/* 是否包含销售发货单部分，查询发货单的未开票金额  */
					/* 是否包含销售出库单部分，查询出库单的未开票金额  */
					/* 是否包含零售单部分，查询零售单的未开票金额  */
				) a
				/* 期初余额 */
				UNION ALL
				SELECT null iYear, '0' iPeriod, null cVouGUID, null cLineGUID, null cVouCode, null dVouDate,
					   null cVouKind, '期初余额：' cVouTypeName, null dCheckDate, null cCheckCode, null cCheckType, null cSettleTypeGUID, cSupGUID, cDeptGUID, cEmpGUID, null cMatGUID, cItemGUID, cCurGUID, 
					   null iDebitAMT_F, null iDebitAMT, null iDebitQTY, null iCreditAMT_F, null iCreditAMT, null iCreditQTY,
					   iInitAMT_F iBalanceAMT_F, iInitAMT iBalanceAMT, iInitQTY iBalanceQTY
				from (
					/* 从总帐表取期初数据按照会计期间查询时取到期初金额  */
					SELECT a.cSupGUID, a.cDeptGUID, a.cEmpGUID, a.cMatGUID, a.cItemGUID, a.cCurGUID,
						a.iDebitAMT_F, a.iDebitAMT, a.iDebitQTY, a.iCreditAMT_F, a.iCreditAMT, a.iCreditQTY,
						case when a.iPrePay = '1' then -a.iInitAMT_F else a.iInitAMT_F end as iInitAMT_F,
						case when a.iPrePay = '1' then -a.iInitAMT else a.iInitAMT end as iInitAMT, a.iInitQTY
					from AP_Account a
						 left join GL_Period pe on pe.iYear = a.iYear and pe.iMonth = a.iPeriod and pe.iLeaf = 1
					WHERE a.cFlag = 'AR'
						  and {iInitDate} between pe.dBeginDate and pe.dEndDate
			        
			        UNION ALL
			        /* 查询明细账的借方金额-贷方金额，最后按照汇总方式取得一个余额 */
			        select a.cSupGUID, a.cDeptGUID, a.cEmpGUID, a.cMatGUID, a.cItemGUID, a.cCurGUID,
			        	0 iDebitAMT_F, 0 iDebitAMT, 0 iDebitQTY, 0 iCreditAMT_F, 0 iCreditAMT, 0 iCreditQTY,
			        	(a.iDebitAMT_F - a.iCreditAMT_F) iInitAMT_F, (a.iDebitAMT - a.iCreditAMT) iInitAMT, (a.iDebitQTY - a.iCreditQTY) iInitQTY
			        from ap_detailaccount a
			        	 left join GL_Period pe on pe.iYear = a.iYear and pe.iMonth = a.iPeriod and pe.iLeaf = 1
			        where a.cFlag = 'AR'
			        	  and a.iPeriod != 0
			        	  and $between(a.dTallykDate, iInitDate, iPreStartdate)
			        	  
					/* 包含未审核单据时，查询会计月第一天到起始时间内未审核单据试图 */
					/* 期初未审核单据 */
					/* 包含销售发货单时，查询会计月第一天到起始时间内发货单的未开票金额  */
					/* 包含销售出库单时，查询会计月第一天到起始时间内出库单的未开票金额  */
				) b
			) detail
  			left join CM_Customer ON detail.cSupGUID = CM_Customer.cGUID
  			left JOIN CM_CustomerClass custClass ON custClass.cGUID = CM_Customer.cClassGUID
  			LEFT JOIN CM_Material ON CM_Material.cGUID = detail.cMatGUID
 			left JOIN CM_MatClass matClass ON matClass.cGUID = CM_Material.cMatCGUID
 			LEFT JOIN CM_Carriage ON CM_Carriage.cGUID = detail.cMatGUID
 			LEFT JOIN CM_Department ON CM_Department.cGUID = detail.cDeptGUID
  			LEFT JOIN CM_Employee ON CM_Employee.cGUID = detail.cEmpGUID
  			LEFT JOIN GL_Currency ON GL_Currency.cGUID = detail.cCurGUID
  			left join CM_Project on detail.cItemGUID = CM_Project.cGUID
  			left join SA_Invoice saInv on saInv.cGUID = detail.cVouGUID
  			left join SA_InvoiceLine sainvLine on sainvLine.cguid = detail.cLineGUID
  			left join SA_CarriageInvoice carr on carr.cGUID = detail.cVouGUID
  			left join SA_CarriageInvoiceLine carrLine on carrLine.cGUID = detail.cLineGUID
  			left join SA_Dispatch saDis on saDis.cGUID = detail.cVouGUID
  			left join SA_DispatchLine saDisL on saDisL.cGUID = detail.cLineGUID
  			left join ST_StkRecord st on st.cGUID = detail.cVouGUID
  			left join ST_StkRecordLine stl on stl.cGUID = detail.cLineGUID
  			left join AP_APVoucher voucher on voucher.cGUID = detail.cVouGUID and voucher.cFlag = 'AR'
 			left join AP_Payment pay on pay.cGUID = detail.cVouGUID and pay.cFlag = 'AR'
  			left JOIN CA_SettleMode ON CA_SettleMode.cGUID = detail.cSettleTypeGUID
  			@table[SA_Invoice saInv,SA_InvoiceLine sainvLine,SA_CarriageInvoice carr,SA_CarriageInvoiceLine carrLine,SA_Dispatch saDis,SA_DispatchLine saDisL,ST_StkRecord st,ST_StkRecordLine stl,AP_APVoucher voucher,AP_Payment pay,CM_Customer,CM_Material,CM_CustomerClass,CM_Department,CM_Employee,GL_Currency,CM_Project]
  			where $equal(CM_Employee.cGUID,cempguid)
				and $equal(CM_Customer.cGUID,ccustguid)
				and $equal(CM_Department.cGUID,cdeptguid)
				and $equal(CM_Project.cGUID,cItemGUID)
			@where[and]
			order by detail.iBalanceAMT_F, detail.dVouDate
			@order[detail.iBalanceAMT_F, detail.dVouDate]
			@filter[AP_DetailAccount.cSupGUID=CM_Customer.cGUID,AP_DetailAccount.cSupGUID=CM_Customer.cGUID and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,AP_DetailAccount.cDeptGUID=cm_department.cguid,AP_DetailAccount.cEmpGUID=cm_employee.cGUID,AP_DetailAccount.cMatGUID=CM_Material.cguid,AP_DetailAccount.cMatGUID=CM_Material.cguid and CM_Material.cMatCGUID=CM_MatClass.cGUID,AP_DetailAccount.cItemGUID=cm_project.cGUID,
				AP_Payment.cSupGUID=CM_Customer.cGUID,AP_Payment.cSupGUID=CM_Customer.cGUID and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,AP_Payment.cDeptGUID=cm_department.cguid,AP_Payment.cEmpGUID=cm_employee.cGUID,AP_Payment.cItemGUID=cm_project.cGUID,
				SA_Invoice.cCustGUID=CM_Customer.cGUID and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,SA_Invoice.cCustGUID=CM_Customer.cGUID,SA_Invoice.cEmpGUID=cm_employee.cGUID,SA_Invoice.cDeptGUID=cm_department.cguid,SA_Invoice.cCreatorGUID=AOS_RMS_USER.cguid,SA_InvoiceLine.cItemGUID=cm_project.cGUID,SA_InvoiceLine.cMatGUID=CM_Material.cguid and CM_Material.cMatCGUID = CM_MatClass.cGUID,SA_InvoiceLine.cMatGUID=CM_Material.cguid,
				V_ARAPBillInfo.cSupGUID=CM_Customer.cGUID,V_ARAPBillInfo.cSupGUID=CM_Customer.cGUID and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,V_ARAPBillInfo.cDeptGUID=cm_department.cguid,V_ARAPBillInfo.cEmpGUID=cm_employee.cGUID,V_ARAPBillInfo.cItemGUID=cm_project.cGUID,V_ARAPBillInfo.cMatGUID=CM_Material.cguid,V_ARAPBillInfo.cMatGUID=CM_Material.cguid and CM_Material.cMatCGUID=CM_MatClass.cGUID,
				AP_Account.cSupGUID=CM_Customer.cGUID,AP_Account.cSupGUID=CM_Customer.cGUID and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,AP_Account.cDeptGUID=cm_department.cguid,AP_Account.cEmpGUID=cm_employee.cGUID,AP_Account.cItemGUID=cm_project.cGUID,AP_Account.cMatGUID=CM_Material.cguid,AP_Account.cMatGUID=CM_Material.cguid and CM_Material.cMatCGUID=CM_MatClass.cGUID,
				SA_Dispatch.cCustGUID=CM_Customer.cGUID,SA_Dispatch.cCustGUID=CM_Customer.cGUID and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,SA_Dispatch.cDeptGUID=cm_department.cguid,SA_Dispatch.cEmpGUID=cm_employee.cGUID,SA_DispatchLine.cMatGUID=CM_Material.cguid,SA_DispatchLine.cMatGUID=CM_Material.cguid and CM_Material.cMatCGUID=CM_MatClass.cGUID,SA_DispatchLine.cItemGUID=cm_project.cGUID]
		]]></i>
				
	</sql>
</sqls>