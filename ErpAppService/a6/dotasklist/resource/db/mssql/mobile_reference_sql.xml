<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_reference_sql">
		<i id="referMatClass" desp="物品分类参照">
			<![CDATA[
			select        	
			    cGUID code,
				cCode+' '+cname name
			from 
				cm_matClass a
				where a.ileaf=1 and a.istatus=1
				@filter[cm_matClass]
				order by iViewOrder,ccode
			]]>
		</i>
		
		<i id="referMaterial"  desp="物品参照">
		    <![CDATA[
		    SELECT top 100 * from( 
				SELECT
						a.cGUID AS hidden_cguid,
						a.cMatCode AS cmatcode,
						a.cMatName AS cmatname,
						isnull(a.cSpec, '') AS cspec,
						(case when isnull(a.iPrecision,0)=0 then left(cast(isnull(cur.iCurQuan,0) as varchar(21)),charindex('.',cast(isnull(cur.iCurQuan,0) as varchar(21)))-1)
					     else left(cast(isnull(cur.iCurQuan,0) as varchar(21)) , charindex('.',cast(isnull(cur.iCurQuan,0) as varchar(21))) +a.iPrecision)
					     end)+ u1.cName as iStoreCurUnitQty,
						 a.iPrecision AS iMatPrecision,
					    (select cvalue from AOS_PREF_VALUE where ccode='U3_ST_PricePrecision') as iPricePrecision,
					    u.cName cUnitName,
					  	'false' as checked,
						0 as iqty,
					    0 as iTaxPrice_F,
						a.iSubUnit,
						a.iGuaranteeFlag,
						a.iBatchFlag,
						a.iServisFlag,
						cs.irateflag, 
						case when ((select top 1 1 from meta_keyattr where cMatGuid = a.cguid) = 1) then 1 else 0 end as iFreeFlag
					FROM
					 cm_material a
					INNER JOIN CM_MatClass ac ON ac.cGUID = a.cMatCGUID
					LEFT JOIN CM_Unit u ON a.cUnitGUID = u.cGUID
					left join CM_UnitClass cs on cs.cGUID=u.cClassGUID
					LEFT JOIN (
						SELECT
							SUM (c.iCurQty) AS iCurQuan,
							cMatGUID,
							cStkUnitGUID
						FROM
							ST_CurrentStock c
					  where $equal(c.cstoreguid,cstoreguid) 
							and exists(select 1 from CM_Storehouse a 								
									where a.cGUID = c.cstoreguid)
						GROUP BY
							cMatGUID,
							cStkUnitGUID
					) cur ON cur.cMatGUID = a.cGUID
					LEFT JOIN CM_Unit u1 ON cur.cStkUnitGUID = u1.cGUID
					where
						  a.iStatus = 1 and ac.iStatus =1 
					and a.cMatCGUID=ac.cGUID
					and $equal(a.cmatcguid,cmatclassguid)
					and ($like(a.cname,keyword) or $like(a.ccode,keyword) or $like(a.cspec,keyword)or $like(a.cHotCode,keyword) ) 
					@filter[cm_material,CM_MatClass])cm
					order by hidden_cguid,cmatcode
			]]>
		</i>
			
	</sql>

</sqls>