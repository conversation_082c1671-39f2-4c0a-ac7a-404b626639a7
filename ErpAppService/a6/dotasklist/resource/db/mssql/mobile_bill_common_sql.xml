<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_bill_common_sql">
	<!-- 代办公共sql -->
		<i id="getBillCode" desp="获取单据编号">
			<![CDATA[
				SELECT cguid
				FROM Bill_Sequence bs
				WHERE
					(
						(
							bs.cAdminOrgnId = ''
							AND bs.cOrgnId = ''
						)
						OR (
							bs.cAdminOrgnId IS NULL
							AND bs.cOrgnId IS NULL
						)
						OR (
							bs.cAdminOrgnId = ?
							AND bs.cOrgnId = ?
						)
						OR (
							bs.cAdminOrgnId = ?
							AND bs.cOrgnId = ?
						)
					)
				AND bs.cCode = (
					SELECT s.cCode
					FROM BILL_BILLS b
					JOIN BILL_SEQUENCE s ON b.cSequenceId = s.cGuid
					WHERE b.cFormId = ?
					and b.cBusinessCode=?
				)
				ORDER BY bs.cguid
			]]>
		</i>
		
		<i id="getFile" desp="获取单据附件">
			<![CDATA[
				SELECT cGuid as hidden_cguid ,
				       cFileName,
				       cFileUrl
				FROM AOS_FILE_FILES
				WHERE cGroupGuid=?
			]]>
		</i>
		

	</sql>
</sqls>