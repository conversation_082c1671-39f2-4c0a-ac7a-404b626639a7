<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_list" desp="列表数据加载">
		<!-- 2017-10-18 quchj 增加单据审批方式和单据类型查询显示,增加按照创建时间倒序排序 -->
		<i id="expenseAccountList" desp="报销申请列表sql">
			<![CDATA[
				select top 100 *
				from(
				select expacct.cguid as hidden_cguid,expacct.cReason as bxyy,oeg.cName as ywlx,
					CONVERT(varchar(100),expacct.cRefundDate,23) as bxrq,expacct.dCreateDate as hidden_cjrq,
					convert(nvarchar, cast(round(expacct.iTotalAMT, 2) as money),1) as bxje,
					expacct.cStatusEnumGUID as hidden_djzt,expacct.cCheckWay as hidden_cCheckWay,
					'oa_010' as hidden_cbilltype
				from OA_ExpenseAccountBill expacct
				left join OA_ExpenseGroup oeg on oeg.cGUID=expacct.cGroupTypeGUID
				where expacct.cguid in (
					select e1.cguid
					from OA_ExpenseAccountBill[-filter] e1
					where e1.cStatusEnumGUID in ('tsaved','saved','revise')
					and 'wtj' = {spzt:''}
					union
					select e2.cguid
					from OA_ExpenseAccountBill[-filter] e2
					where e2.cStatusEnumGUID in ('checking')
					and 'spz' = {spzt:''}
					union
					select e3.cguid
					from OA_ExpenseAccountBill[-filter] e3
					where e3.cStatusEnumGUID in ('checked','i020','i023')
					and 'ywc' = {spzt:''}
				)
				@filter[OA_ExpenseAccountBill.cUserGUID={currUserEmpGUID} or OA_ExpenseAccountBill.cExpUserGUID={currUserEmpGUID} or '1'={currUserGUID},
					or OA_ExpenseAccountBill.cExpUserGUID=v_oa_loanExpenseUserInfo.cguid,
					or OA_ExpenseAccountBill.cExpDeptGUID=v_oa_loanExpenseDeptInfo.cguid,
					or OA_ExpenseAccountBill.cStatusEnumGUID=v_oa_billStatus.cguid]
				)em
				order by em.bxrq desc,em.hidden_cjrq desc
			]]>
		</i>
		
		<!-- 2017-08-03 quchj 增加获取报销单总金额 -->
		<i id="expenseAccountBXJESum" desp="获取所有报销单总报销金额">
			<![CDATA[
				select convert(nvarchar, cast(round(sum(bxje), 2) as money),1) as zbxje
				from(
					select isnull(expacct.iTotalAMT,0) as bxje
					from OA_ExpenseAccountBill expacct
					left join OA_ExpenseGroup oeg on oeg.cGUID=expacct.cGroupTypeGUID
					where expacct.cguid in (
						select e1.cguid
						from OA_ExpenseAccountBill[-filter] e1
						where e1.cStatusEnumGUID in ('tsaved','saved','revise')
						and 'wtj' = {spzt:''}
						union
						select e2.cguid
						from OA_ExpenseAccountBill[-filter] e2
						where e2.cStatusEnumGUID in ('checking')
						and 'spz' = {spzt:''}
						union
						select e3.cguid
						from OA_ExpenseAccountBill[-filter] e3
						where e3.cStatusEnumGUID in ('checked','i020','i023')
						and 'ywc' = {spzt:''}
					)
					@filter[OA_ExpenseAccountBill.cUserGUID={currUserEmpGUID} or OA_ExpenseAccountBill.cExpUserGUID={currUserEmpGUID} or '1'={currUserGUID},
						or OA_ExpenseAccountBill.cExpUserGUID=v_oa_loanExpenseUserInfo.cguid,
						or OA_ExpenseAccountBill.cExpDeptGUID=v_oa_loanExpenseDeptInfo.cguid,
						or OA_ExpenseAccountBill.cStatusEnumGUID=v_oa_billStatus.cguid]
				)em
			]]>
		</i>
	</sql>
</sqls>