<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
  	<sql group="mobile_yiban_salist">
  		
  		 <i id="saorderlist">
  		        
  		        select top 100 * from (
  		         select 
            	 st.cguid   as hidden_cguid, 
            	 st.cbillcode,
            	  null as tjr,
                 st.cCheckWay as  hidden_ccheckway,
                '销售订单'+st.cbillcode as bt,
		         '066' as hidden_cbilltype,
		         CONVERT(varchar(100),st.dCheckTime,120) as blsj, 
                 convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
                 ce.cName    as  ywy,
                 cd.cName    as   bm,
                  null as fplx,
                 null as se
		         from SA_Order st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from SA_OrderLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.c<PERSON><PERSON><PERSON> 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
		         where st.iAuditStatus = 'checked' 
		         and st.cCheckWay='0'
		         and st.cAuditorGUID={currUserGUID}
		         and (CONVERT(char(10),st.dCheckTime,23)={today} or '*' = {today:'*'})
		         and (('*' = {keyword:'*'} or '销售订单' like '%{keyword}%'))
		       
		       
		        union all
		         select 
            	 st.cguid   as hidden_cguid, 
            	    st.cbillcode,
            	    null as tjr,
                   '2' as hidden_cCheckWay,
                   '销售订单'+st.cbillcode as bt,
		           '066' as hidden_cbilltype,
		           CONVERT(varchar(100),b.END_,120) as blsj, 
                  convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
                   ce.cName    as  ywy,
                   cd.cName    as   bm,
                    null as fplx,
                 null as se
		            from SA_Order st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal
		  				from SA_OrderLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
		         left join JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
	             left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
	             inner join(
						  select a.DBID_,
							case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
							case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
						  from JBPM4_HIST_TASK a 
						  left join (
							select TIME_,HTASK_,USERID_
					        from JBPM4_HIST_DETAIL
					        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
					       ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		     where
		         /*对同一实例已完成任务过滤:取最近完成的任务*/
				        a.DBID_ in (
		               select max(t.dbid_)  
					   from JBPM4_HIST_TASK t
					   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
					   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
					   		and d.CLASS_='status'
					   		and d.NEW_STR_ !='cancel'
					   		and d.NEW_STR_ !='retreat'
					   		and d.NEW_STR_ !='delegate'
					   		and d.NEW_STR_ !='rollback-type'
					   		and d.NEW_STR_ !='jump-type'
					   		and d.NEW_STR_!='rollback-target'
					   		and d.NEW_STR_ !='delegated'
					   group by t.execution_
                   )
						) b on b.actTask = c.HTASK_
						left join WF_Process_Variable v on e.ID_ = v.procInstId
						left join WF_ProcessInfo h on v.processTemplate = h.cGUID
						left join WF_Process pr on pr.cguid = h.cNodeTemplate 
						where pr.cForm ='business_sa_saorder'
						and (CONVERT(varchar(100),b.END_,23) = {today} or '*' = {today:'*'})
		                and (('*' = {keyword:'*'} or '销售订单' like '%{keyword}%'))
						and st.cCheckWay = '2'
						
					union all
					
					
				
  		         select 
            	 st.cguid   as hidden_cguid, 
            	 st.cInvCode as cbillcode,
            		 pl.crealname as tjr,
                 st.cCheckWay as  hidden_ccheckway,
                '销售发票'+st.cInvCode as bt,
		         '085' as hidden_cbilltype,
		         CONVERT(varchar(100),st.dCheckTime,120) as blsj, 
                 convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
                 ce.cName    as  ywy,
                 cd.cName    as   bm,
                  g.cName as fplx,
                 convert(nvarchar, cast(round(stl.iTax ,2) as money),1)  as se
		         from SA_Invoice st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal,
		  				sum(isnull(l.iTax,0)) as iTax
		  				from SA_InvoiceLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                  left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
                 LEFT JOIN CO_GeneralCode g ON st.cSInvType = g.cCode AND g.cClass = '2'	
		         where st.iAuditStatus = 'checked' 
		         and st.cCheckWay='0'
		         and st.cAuditorGUID={currUserGUID}
		         and (CONVERT(char(10),st.dCheckTime,23)={today} or '*' = {today:'*'})
		         and (('*' = {keyword:'*'} or '销售发票' like '%{keyword}%'))
		         and st.csystype='SA'
		         and st.iInitFlag='0'
		         
		       
		        union all
		         select 
            	 st.cguid   as hidden_cguid, 
            	    st.cInvCode  as cbillcode ,
            	     pl.crealname as tjr,
					 '2' as hidden_cCheckWay,                   
					 '销售发票'+st.cInvCode as bt,
		           '085' as hidden_cbilltype,
		           CONVERT(varchar(100),b.END_,120) as blsj, 
                  convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
                   ce.cName    as  ywy,
                   cd.cName    as   bm,
                    g.cName as fplx,
                 convert(nvarchar, cast(round(stl.iTax ,2) as money),1)  as se
		            from SA_Invoice st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal,
		  				sum(isnull(l.iTax,0)) as iTax
		  				from SA_InvoiceLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
		         left join JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
	             left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
	             LEFT JOIN CO_GeneralCode g ON st.cSInvType = g.cCode AND g.cClass = '2'	
	             inner join(
						  select a.DBID_,
							case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
							case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
						  from JBPM4_HIST_TASK a 
						  left join (
							select TIME_,HTASK_,USERID_
					        from JBPM4_HIST_DETAIL
					        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
					       ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		     where
		         /*对同一实例已完成任务过滤:取最近完成的任务*/
				        a.DBID_ in (
		               select max(t.dbid_)  
					   from JBPM4_HIST_TASK t
					   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
					   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
					   		and d.CLASS_='status'
					   		and d.NEW_STR_ !='cancel'
					   		and d.NEW_STR_ !='retreat'
					   		and d.NEW_STR_ !='delegate'
					   		and d.NEW_STR_ !='rollback-type'
					   		and d.NEW_STR_ !='jump-type'
					   		and d.NEW_STR_!='rollback-target'
					   		and d.NEW_STR_ !='delegated'
					   group by t.execution_
                   )
						) b on b.actTask = c.HTASK_
						left join WF_Process_Variable v on e.ID_ = v.procInstId
						left join WF_ProcessInfo h on v.processTemplate = h.cGUID
						left join WF_Process pr on pr.cguid = h.cNodeTemplate 
						where pr.cForm ='business_sa_sainvoice'
						and (CONVERT(varchar(100),b.END_,23) = {today} or '*' = {today:'*'})
		                and (('*' = {keyword:'*'} or '销售发票' like '%{keyword}%'))
						and st.cCheckWay = '2'
						and st.csystype='SA'
						union all
						
						     select 
            	 st.cguid   as hidden_cguid, 
            	 st.cbillcode,
            	 pl.crealname as tjr,
                 st.cCheckWay as  hidden_ccheckway,
                '零售单'+st.cbillcode as bt,
		          '212' as hidden_cbilltype,
		           CONVERT(varchar(100),st.dCheckTime,120) as blsj, 
                 convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
                 ce.cName    as  ywy,
                 cd.cName    as   bm,
                 '' as fplx,
                 '' as se
		         from ST_StkRecord st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal,
		  				sum(isnull(l.iTax,0)) as iTax
		  				from ST_StkRecordLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
                  left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid              
		         where st.iAuditStatus = 'checked' 
		         and st.cCheckWay='0'
		         and st.cAuditorGUID={currUserGUID}
		         and (CONVERT(char(10),st.dCheckTime,23)={today} or '*' = {today:'*'})
		         and (('*' = {keyword:'*'} or '零售单' like '%{keyword}%')) 
		         and st.cBillType = '212'
					   and st.iInitFlag = '0'
					   and st.iRSFlag = '0'
		         
		       
		        union all
		         select 
            	 st.cguid   as hidden_cguid, 
            	    st.cbillcode  as cbillcode ,
            	     pl.crealname as tjr,
					    '2' as hidden_cCheckWay,                   
					    '零售单'+st.cbillcode as bt,
		           '212' as hidden_cbilltype,
		           CONVERT(varchar(100),b.END_,120) as blsj, 
               convert(nvarchar, cast(round(stl.iTotal ,2) as money),1)  as je,
                   ce.cName    as  ywy,
                   cd.cName    as   bm,
                   '' as fplx,
                ''  as se
		            from ST_StkRecord st
                 inner join(
		  				select l.cheadguid,	
		  				sum(isnull(l.iTotal,0)) as iTotal,
		  				sum(isnull(l.iTax,0)) as iTax
		  				from ST_StkRecordLine l
				        group by l.cheadguid  
		  				) as stl on stl.cheadguid = st.cGUID 
                 left join CM_Employee ce on st.cEmpGUID=ce.cguid
                 left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid
                 left join CM_Department cd on st.cDeptGUID=cd.cguid
		         left join JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
	             left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_            
	             inner join(
						  select a.DBID_,
							case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
							case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
						  from JBPM4_HIST_TASK a 
						  left join (
							select TIME_,HTASK_,USERID_
					        from JBPM4_HIST_DETAIL
					        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
					       ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		     where
		         /*对同一实例已完成任务过滤:取最近完成的任务*/
				        a.DBID_ in (
		               select max(t.dbid_)  
					   from JBPM4_HIST_TASK t
					   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
					   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
					   		and d.CLASS_='status'
					   		and d.NEW_STR_ !='cancel'
					   		and d.NEW_STR_ !='retreat'
					   		and d.NEW_STR_ !='delegate'
					   		and d.NEW_STR_ !='rollback-type'
					   		and d.NEW_STR_ !='jump-type'
					   		and d.NEW_STR_!='rollback-target'
					   		and d.NEW_STR_ !='delegated'
					   group by t.execution_
                   )
						) b on b.actTask = c.HTASK_
						left join WF_Process_Variable v on e.ID_ = v.procInstId
						left join WF_ProcessInfo h on v.processTemplate = h.cGUID
						left join WF_Process pr on pr.cguid = h.cNodeTemplate 
						where pr.cForm ='business_sa_retail_form'
						and (CONVERT(varchar(100),b.END_,23) = {today} or '*' = {today:'*'})
		                and (('*' = {keyword:'*'} or '零售单' like '%{keyword}%'))
						and st.cCheckWay = '2'
						and st.cBillType = '212'
					  and st.iInitFlag = '0'
					  and st.iRSFlag = '0'
				
				 @filter[
		        	SA_Invoice.cCustGUID=CM_Customer.cguid,
            		SA_Invoice.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
              		SA_Invoice.cDeptGUID=cm_department.cguid,
              		SA_Invoice.cEmpGUID=CM_Employee.cguid,
              		SA_Invoice.cCreatorGUID=AOS_RMS_USER.cguid,
              		SA_Invoice.cBusProcess=CO_BusinessProcess.cGUID,
              		SA_InvoiceLine.cMatGUID=CM_Material.cGUID,
              		SA_InvoiceLine.cMatGUID=CM_Material.cGUID and CM_Material.cMatCGUID=CM_MatClass.cGUID,
              		SA_InvoiceLine.cStoreGUID=CM_Storehouse.cguid,
              		SA_InvoiceLine.cItemGUID=CM_Project.cguid,
		        SA_Order.cCustGUID=CM_Customer.cguid,
		        SA_Order.cDeptGUID=cm_department.cguid,
		        SA_Order.cEmpGUID=CM_Employee.cguid,
		        SA_Order.cCreatorGUID=AOS_RMS_USER.cguid,
		        SA_Order.cAcctIItemGuid= CM_Project.cguid,
		        SA_Order.cBusProcess=CO_BusinessProcess.cGUID,
		        SA_OrderLine.cMatGUID=CM_Material.cGUID,
		        SA_OrderLine.cStoreGUID=CM_Storehouse.cguid,
		        SA_Order.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
					ST_StkRecord.cCustGUID=CM_Customer.cguid,
			        ST_StkRecord.cDeptGUID=cm_department.cguid,
			        ST_StkRecord.cEmpGUID=CM_Employee.cguid,
			        ST_StkRecord.cCreatorGUID=AOS_RMS_USER.cguid,
			        ST_StkRecord.cAcctIItemGuid= CM_Project.cguid,
			        ST_StkRecord.cBusProcess=CO_BusinessProcess.cGUID,
			        ST_StkRecordLine.cMatGUID=CM_Material.cGUID,
			        ST_StkRecordLine.cStoreGUID=CM_Storehouse.cguid,
			        ST_StkRecord.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
			        CM_MatClass]
						)sa
						order by blsj desc,cbillcode  desc
						
		</i>	
		<i id="saordernum">	
		select sum(t.num) as num from(	  select 
		     count(1) num
  		     from SA_Order st
  	         where iAuditStatus='checked'
  	         and  st.cCheckWay='0'
  	         and st.cAuditorGUID={currUserGUID}
  	         and CONVERT(char(10),st.dCheckTime,23)=CONVERT(varchar(100),getdate(),23)
		     and (('*' = {keyword:'*'} or '销售订单' like '%{keyword}%'))
  	        
		        union all
	             select 
				          count(1) num 
							from SA_Order st
							left join JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
							left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
								inner join(
							select a.DBID_,
								case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
								case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
							from JBPM4_HIST_TASK a 
							left join (
							select TIME_,HTASK_,USERID_
					        from JBPM4_HIST_DETAIL
					        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
					         ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		     where
				/*对同一实例已完成任务过滤:取最近完成的任务*/
				a.DBID_ in (
	               select max(t.dbid_)  
				   from JBPM4_HIST_TASK t
				   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
				   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
					   group by t.execution_
		                 )
					) b on b.actTask = c.HTASK_
					left join WF_Process_Variable v on e.ID_ = v.procInstId
					left join WF_ProcessInfo h on v.processTemplate = h.cGUID
					left join WF_Process pr on pr.cguid = h.cNodeTemplate 
					where pr.cForm ='business_sa_saorder'
					and CONVERT(char(10),b.END_,120)=CONVERT(varchar(100),getdate(),23)
					and st.cCheckWay = '2'	
					
					union all
					
					select 
		     count(1) num
  		     from SA_Invoice st
  	         where iAuditStatus='checked'
  	         and  st.cCheckWay='0'
  	         and st.cAuditorGUID={currUserGUID}
  	         and CONVERT(char(10),st.dCheckTime,23)=CONVERT(varchar(100),getdate(),23)
		     and (('*' = {keyword:'*'} or '销售发票' like '%{keyword}%'))
  	         and st.csystype='SA'
  	         and st.iInitFlag='0'
		        union all
	             select 
				          count(1) num 
							from SA_Invoice st
							left join JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
							left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
								inner join(
							select a.DBID_,
								case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
								case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
							from JBPM4_HIST_TASK a 
							left join (
							select TIME_,HTASK_,USERID_
					        from JBPM4_HIST_DETAIL
					        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
					         ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		     where
				/*对同一实例已完成任务过滤:取最近完成的任务*/
				a.DBID_ in (
	               select max(t.dbid_)  
				   from JBPM4_HIST_TASK t
				   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
				   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
					   group by t.execution_
		                 )
					) b on b.actTask = c.HTASK_
					left join WF_Process_Variable v on e.ID_ = v.procInstId
					left join WF_ProcessInfo h on v.processTemplate = h.cGUID
					left join WF_Process pr on pr.cguid = h.cNodeTemplate 
					where pr.cForm ='business_sa_sainvoice'
					and CONVERT(char(10),b.END_,120)=CONVERT(varchar(100),getdate(),23)
					and st.cCheckWay = '2'	
					and st.csystype='SA'
					
					union all
					
					select 
		     count(1) num
  		     from ST_StkRecord st
  	         where iAuditStatus='checked'
  	         and  st.cCheckWay='0'
  	         and st.cAuditorGUID={currUserGUID}
  	         and CONVERT(char(10),st.dCheckTime,23)=CONVERT(varchar(100),getdate(),23)
		     and (('*' = {keyword:'*'} or '零售单' like '%{keyword}%'))
  	     and st.cBillType = '212'
			   and st.iInitFlag = '0'
			   and st.iRSFlag = '0'
		        union all
	             select 
				          count(1) num 
							from ST_StkRecord st
							left join JBPM4_HIST_PROCINST e on st.cguid = e.KEY_
							left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
								inner join(
							select a.DBID_,
								case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
								case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
							from JBPM4_HIST_TASK a 
							left join (
							select TIME_,HTASK_,USERID_
					        from JBPM4_HIST_DETAIL
					        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
					         ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		     where
				/*对同一实例已完成任务过滤:取最近完成的任务*/
				a.DBID_ in (
	               select max(t.dbid_)  
				   from JBPM4_HIST_TASK t
				   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
				   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
					   group by t.execution_
		                 )
					) b on b.actTask = c.HTASK_
					left join WF_Process_Variable v on e.ID_ = v.procInstId
					left join WF_ProcessInfo h on v.processTemplate = h.cGUID
					left join WF_Process pr on pr.cguid = h.cNodeTemplate 
					where pr.cForm ='business_sa_retail_form'
					and CONVERT(char(10),b.END_,120)=CONVERT(varchar(100),getdate(),23)
					and st.cCheckWay = '2'	
					and st.cBillType = '212'
					and st.iInitFlag = '0'
					and st.iRSFlag = '0'
					 @filter[
		         SA_Invoice.cCustGUID=CM_Customer.cguid,
            		SA_Invoice.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
              		SA_Invoice.cDeptGUID=cm_department.cguid,
              		SA_Invoice.cEmpGUID=CM_Employee.cguid,
              		SA_Invoice.cCreatorGUID=AOS_RMS_USER.cguid,
              		SA_Invoice.cBusProcess=CO_BusinessProcess.cGUID,
              		SA_InvoiceLine.cMatGUID=CM_Material.cGUID,
              		SA_InvoiceLine.cMatGUID=CM_Material.cGUID and CM_Material.cMatCGUID=CM_MatClass.cGUID,
              		SA_InvoiceLine.cStoreGUID=CM_Storehouse.cguid,
              		SA_InvoiceLine.cItemGUID=CM_Project.cguid,
		        SA_Order.cCustGUID=CM_Customer.cguid,
		        SA_Order.cDeptGUID=cm_department.cguid,
		        SA_Order.cEmpGUID=CM_Employee.cguid,
		        SA_Order.cCreatorGUID=AOS_RMS_USER.cguid,
		        SA_Order.cAcctIItemGuid= CM_Project.cguid,
		        SA_Order.cBusProcess=CO_BusinessProcess.cGUID,
		        SA_OrderLine.cMatGUID=CM_Material.cGUID,
		        SA_OrderLine.cStoreGUID=CM_Storehouse.cguid,
		        SA_Order.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
		       		ST_StkRecord.cCustGUID=CM_Customer.cguid,
			        ST_StkRecord.cDeptGUID=cm_department.cguid,
			        ST_StkRecord.cEmpGUID=CM_Employee.cguid,
			        ST_StkRecord.cCreatorGUID=AOS_RMS_USER.cguid,
			        ST_StkRecord.cAcctIItemGuid= CM_Project.cguid,
			        ST_StkRecord.cBusProcess=CO_BusinessProcess.cGUID,
			        ST_StkRecordLine.cMatGUID=CM_Material.cGUID,
			        ST_StkRecordLine.cStoreGUID=CM_Storehouse.cguid,
			        ST_StkRecord.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
			        CM_MatClass
			        ]		        
					)t
		</i>
	</sql>
</sqls>