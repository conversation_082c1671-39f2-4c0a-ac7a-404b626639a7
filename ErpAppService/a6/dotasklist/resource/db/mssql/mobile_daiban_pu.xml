<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
  	<sql group="mobile_daiban_pulist">
  	
  	
  		<i id="getallpulist" desp="全部union起来的sql">
  		<![CDATA[
  		
  	  		select  top 100 * from (
  		select 
  			po.cguid as hidden_cguid,
  			po.ccode,
  			'0' as hidden_cCheckWay,
  			po.iAuditStatus as hidden_iauditstatus,
  			'076' as hidden_cbilltype,
  			'采购订单'+po.ccode as bt,
  			CONVERT(varchar(100),po.dCreatorTime,120) as sqsj,
        	convert(nvarchar, cast(round(k.iTotal ,2) as money),1)  as je,
        	ce.cName as ywy,
            cd.cName as bm
  	        from PU_Order po 
             inner join(select pl.cheadguid,	
  			       sum(isnull(pl.iTotal,0)) as iTotal
  		      from PU_OrderLine pl
		     group by pl.cheadguid  
  			) as k on k.cheadguid = po.cGUID  
		   left join CM_Employee ce on po.cEmpGUID=ce.cguid
		   left join CM_Department cd on po.cDeptGUID=cd.cguid
		   left join AOS_RMS_USER u ON po.cCreatorGUID = u.cguid	
  	       where po.iAuditStatus='saved'
  	       and ({sgPuOrder:'false'}='true')
  	       and ((CONVERT(varchar(100),po.dCreatorTime,112) like '%{keyword}%')  or $like(u.crealname,keyword) or('*' = {keyword:'*'} or '采购订单' like '%{keyword}%'))
       	   and exists(select 1 from PU_OrderLine a 
										inner join cm_material ma on ma.cguid = a.cmatguid
										inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
										where a.cHeadGUID = po.cGUID)
   
   union all
   
            select 
            		   po.cguid as hidden_cguid,
                       po.ccode,
		  			   '2' as hidden_cCheckWay,
		  			   po.iAuditStatus as hidden_iauditstatus,
		  			   '076' as hidden_cbilltype,
		  			   '采购订单'+po.ccode bt,
		  			   CONVERT(varchar(100),e.START_,120) as sqsj,
        	convert(nvarchar, cast(round(k.iTotal ,2) as money),1)  as je,
		        	   ce.cName as ywy,
		               cd.cName as bm		      
				from PU_Order[-filter] po 
			    inner join(select pl.cheadguid,	
  			                    sum(isnull(pl.iTotal,0)) as iTotal
  		                   from PU_OrderLine[-filter] pl
		               group by pl.cheadguid  
  			            ) as k on k.cheadguid = po.cGUID 
  			    left join CM_Employee ce on po.cEmpGUID=ce.cguid
				left join CM_Department cd on po.cDeptGUID=cd.cguid
				left join AOS_RMS_USER u ON po.cCreatorGUID = u.cguid	
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				
				where 
				 ({wfPuOrder:'false'}='true')
				and ($like(u.crealname,keyword) or (CONVERT(varchar(100),e.START_,112) like '%{keyword}%') or ('*' = {keyword:'*'} or '采购订单' like '%{keyword}%'))
				
		union all
				
	            select 
	            	   po.cguid as hidden_cguid,
	            	   po.ccode,
		  			   '2' as hidden_cCheckWay,
		  			   po.iAuditStatus as hidden_iauditstatus,
		  			   '076' as hidden_cbilltype,
		  			   '采购订单'+po.ccode as bt,
		  			   CONVERT(varchar(100),e.START_,120) as sqsj,
        	convert(nvarchar, cast(round(k.iTotal ,2) as money),1)  as je,
		        	   ce.cName as ywy,
		               cd.cName as bm		      
				from PU_Order po 
			    inner join(select pl.cheadguid,	
  			                    sum(isnull(pl.iTotal,0)) as iTotal
  		                   from PU_OrderLine pl
		               group by pl.cheadguid  
  			            ) as k on k.cheadguid = po.cGUID 
  			    left join CM_Employee ce on po.cEmpGUID=ce.cguid
				left join CM_Department cd on po.cDeptGUID=cd.cguid
				left join AOS_RMS_USER u ON po.cCreatorGUID = u.cguid	
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where  ({wffilterPuOrder:'false'}='true')
				and exists(select 1 from PU_OrderLine a 
										inner join cm_material ma on ma.cguid = a.cmatguid
										inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
										where a.cHeadGUID = po.cGUID)
				and ($like(u.crealname,keyword) or (CONVERT(varchar(100),e.START_,112) like '%{keyword}%') or ('*' = {keyword:'*'} or '采购订单' like '%{keyword}%'))
   union all
  		select 
  			po.cguid as hidden_cguid,
  			po.cPlanCode as ccode,
  			'0' as hidden_cCheckWay,
  			po.iAuditStatus as hidden_iauditstatus,
  			'220' as hidden_cbilltype,
  			'采购计划'+po.cPlanCode as bt,
  			CONVERT(varchar(100),po.dCreatorTime,120) as sqsj,
	        '' as je,
	        '' as ywy,
	        cd.cName as bm
  	    from PU_Planhead po 
		   left join CM_Department cd on po.cDeptGUID=cd.cguid
		   left join AOS_RMS_USER u ON po.cCreatorGUID = u.cguid	
  	       where po.iAuditStatus='saved'
  	       and ({sgPuPlan:'false'}='true')
  	       and ((CONVERT(varchar(100),po.dCreatorTime,112) like '%{keyword}%')  or $like(u.crealname,keyword) or('*' = {keyword:'*'} or '采购计划' like '%{keyword}%'))
       	   and exists(select 1 from PU_Planline a 
										inner join cm_material ma on ma.cguid = a.cmatguid
										inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
										where a.cHeadGUID = po.cGUID)
   
   union all
   
            select 
            	 po.cguid as hidden_cguid,
              	 po.cPlanCode as ccode,
  			    '2' as hidden_cCheckWay,
  			     po.iAuditStatus as hidden_iauditstatus,
  			    '220' as hidden_cbilltype,
  			    '采购计划'+po.cPlanCode bt,
  			    CONVERT(varchar(100),e.START_,120) as sqsj,
				'' as je,
		        '' as ywy,
		        cd.cName as bm		      
				from PU_Planhead[-filter] po  
				left join CM_Department cd on po.cDeptGUID=cd.cguid
				left join AOS_RMS_USER u ON po.cCreatorGUID = u.cguid	
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				
				where 
				 ({wfPuPlan:'false'}='true')
				and ($like(u.crealname,keyword) or (CONVERT(varchar(100),e.START_,112) like '%{keyword}%') or ('*' = {keyword:'*'} or '采购计划' like '%{keyword}%'))
				
		union all
				
	            select 
	             po.cguid as hidden_cguid,
	             po.cPlanCode as ccode,
  			    '2' as hidden_cCheckWay,
  			     po.iAuditStatus as hidden_iauditstatus,
  			    '220' as hidden_cbilltype,
  			    '采购计划'+po.cPlanCode as bt,
  			    CONVERT(varchar(100),e.START_,120) as sqsj,
				'' as je,
		        ''as ywy,
		        cd.cName as bm		      
				from PU_Planhead po 
				left join CM_Department cd on po.cDeptGUID=cd.cguid
				left join AOS_RMS_USER u ON po.cCreatorGUID = u.cguid	
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where  ({wffilterPuPlan:'false'}='true')
				and exists(select 1 from PU_Planline a 
										inner join cm_material ma on ma.cguid = a.cmatguid
										inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
										where a.cHeadGUID = po.cGUID)
				and ($like(u.crealname,keyword) or (CONVERT(varchar(100),e.START_,112) like '%{keyword}%') or ('*' = {keyword:'*'} or '采购计划' like '%{keyword}%'))
				@filter[pu_order.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
				        pu_order.cSupGUID=cm_supplier.cguid,
				        pu_order.cDeptGUID=cm_department.cguid,
				        pu_order.cCreatorGUID=AOS_RMS_USER.cguid,
				        pu_order.cEmpGUID=CM_Employee.cGUID,
				        PU_OrderLine.cMatGUID=CM_Material.cguid,
				        PU_OrderLine.cItemGUID=CM_Project.cGUID,
				        CM_MatClass,
						PU_Planhead.cDeptGUID=cm_department.cguid,
						PU_Planhead.cCreatorGUID=AOS_RMS_USER.cguid,
						PU_Planline.cMatGUID=CM_Material.cguid,
						PU_Planline.cItemGUID=CM_Project.cGUID,
						PU_Planline.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
						PU_Planline.cSupGUID=cm_supplier.cguid,
						PU_Planline.cDeptGUID=cm_department.cguid,
						PU_Planline.cEmpGUID=CM_Employee.cGUID
						]

		)pu
			order by sqsj desc,ccode desc
     		]]>
		</i>	
  	
  	
  		<i id="getallpunum"> 	
  	      select sum(pu.num)  from 
  	            (select count(1)  num
  	               from PU_Order po  
	  	               inner join(select pl.cheadguid
	  		          from PU_OrderLine pl
			          group by pl.cheadguid  
	  			    ) as k on k.cheadguid = po.cGUID                  
				  left join CM_Employee ce on po.cEmpGUID=ce.cguid
				  left join CM_Department cd on po.cDeptGUID=cd.cguid
				  left join AOS_RMS_USER u ON po.cCreatorGUID = u.cguid	
  	            where po.iAuditStatus='saved'
  	           and ({sgPuOrder:'false'}='true')
  	           and exists(select 1 from PU_OrderLine a 
										inner join cm_material ma on ma.cguid = a.cmatguid
										inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
										where a.cHeadGUID = po.cGUID)
  	       union all
  	
	         		 select 
		          count(1) as num
		        from PU_Order[-filter] po 
		        inner join(select pl.cheadguid,	
  			                    sum(isnull(pl.iTotal,0)) as iTotal
  		                   from PU_OrderLine[-filter] pl
		               group by pl.cheadguid  
  			    ) as k on k.cheadguid = po.cGUID 
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				inner join(
	                select distinct a.PROCINST_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where {wfPuOrder:'false'}='true'				
		    union all
		
	         	 select 
		        count(1) as num
		        from PU_Order po 
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				inner join(
	                select distinct a.PROCINST_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_ 
				where ({wffilterPuOrder:'false'}='true')				
				and exists(select 1 from PU_OrderLine a 
										inner join cm_material ma on ma.cguid = a.cmatguid
										inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
										where a.cHeadGUID = po.cGUID)

		    union all
				select count(1)  num
  	               from PU_Planhead po                    
				  left join CM_Department cd on po.cDeptGUID=cd.cguid
				  left join AOS_RMS_USER u ON po.cCreatorGUID = u.cguid	
  	            where po.iAuditStatus='saved'
  	           and ({sgPuPlan:'false'}='true')
  	           and exists(select 1 from PU_Planline a 
										inner join cm_material ma on ma.cguid = a.cmatguid
										inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
										where a.cHeadGUID = po.cGUID)
  	       union all
  	
	         		 select 
		          count(1) as num
		        from PU_Planhead[-filter] po 			    
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				inner join(
	                select distinct a.PROCINST_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where {wfPuPlan:'false'}='true'				
		    union all
		
	         	 select 
		        count(1) as num
		        from PU_Planhead po 
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				inner join(
	                select distinct a.PROCINST_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_ 
				where ({wffilterPuPlan:'false'}='true')				
				and exists(select 1 from PU_Planline a 
										inner join cm_material ma on ma.cguid = a.cmatguid
										inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
										where a.cHeadGUID = po.cGUID)
			   	@filter[pu_order.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
				        pu_order.cSupGUID=cm_supplier.cguid,
				        pu_order.cDeptGUID=cm_department.cguid,
				        pu_order.cCreatorGUID=AOS_RMS_USER.cguid,
				        pu_order.cEmpGUID=CM_Employee.cGUID,
				        PU_OrderLine.cMatGUID=CM_Material.cguid,
				        PU_OrderLine.cItemGUID=CM_Project.cGUID,
				        CM_MatClass,
						PU_Planhead.cDeptGUID=cm_department.cguid,
						PU_Planhead.cCreatorGUID=AOS_RMS_USER.cguid,
						PU_Planline.cMatGUID=CM_Material.cguid,
						PU_Planline.cItemGUID=CM_Project.cGUID,
						PU_Planline.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
						PU_Planline.cSupGUID=cm_supplier.cguid,
						PU_Planline.cDeptGUID=cm_department.cguid,
						PU_Planline.cEmpGUID=CM_Employee.cGUID
						]
			)pu
		</i>		
  		
	</sql>
	
	
	
	<sql group="mobile_daiban_pubill">
  		<i id="puordermain" desp="采购订单页面主表">
  		<![CDATA[	
		SELECT 
		    convert(nvarchar, cast(round(k.iTotal ,2) as money),1)  as '采购总金额'
		FROM
			pu_order po
		  inner join(select pl.cheadguid,	
  			                sum(isnull(pl.iTotal,0)) as iTotal
  		               from PU_OrderLine pl
		               group by pl.cheadguid  
  			         ) as k on k.cheadguid = po.cGUID
		where po.cguid={cGUID}
				]]>
		</i>
		
		<i id="puorderdetail" desp="采购订单页面子表">
		<![CDATA[
		SELECT
		     pl.cguid,
			('['+cm.cMatCode+']'+cm.cMatName) as "cm_material.cname.pu_orderline.cmatguid",
			 case 
             when isnull(cm.iPrecision,0)=0 then left(cast(pl.iUnitQTY as varchar(21)),charindex('.',cast(pl.iUnitQTY as varchar(21)))-1)
             else left(cast(pl.iUnitQTY as varchar(21)) , charindex('.',cast(pl.iUnitQTY as varchar(21))) +cm.iPrecision)
             end  +u.cName  as "pu_orderline.iunitqty",
			 convert(nvarchar, cast(round(pl.iTotal,2) as money),1)  as "pu_orderline.itotal",
		     case when pl.cpurefepricemethod='z' then '主计量单位' else '辅计量单位' end as 'pu_orderline.cpurefepricemethod',
		     case when pl.icloseflag='1' then '已终止' else '未终止' end as 'pu_orderline.icloseflag'
		FROM
			pu_orderline pl
			left join CM_Material cm on pl.cmatguid = cm.cguid	
			left join CM_Unit u on pl.cMUnitGUID = u.cguid
		where pl.cheadguid={cGUID}
				]]>
		</i>
		
		<i id="puordermcheck" desp="采购订单页面审核主表">
  		<![CDATA[	
		SELECT
			*
		FROM
			pu_order po
			where po.cGUID = {cGUID}
				]]>
		</i>
		
		<i id="puorderdcheck" desp="采购订单页面审核子表">
		<![CDATA[
		SELECT
			*
		FROM
			pu_orderline pl
		where pl.cheadguid={cGUID}
				]]>
		</i>
					
		<i id="puplandetail" desp="采购计划页面子表">
		<![CDATA[
		SELECT
		     pl.cguid,
			('['+cm.cMatCode+']'+cm.cMatName) as "cm_material.cname.pu_planline.cmatguid",
			 case 
             when isnull(cm.iPrecision,0)=0 then left(cast(pl.iUnitQTY as varchar(21)),charindex('.',cast(pl.iUnitQTY as varchar(21)))-1)
             else left(cast(pl.iUnitQTY as varchar(21)) , charindex('.',cast(pl.iUnitQTY as varchar(21))) +cm.iPrecision)
             end  +u.cName  as "pu_planline.iunitqty",
		     case when pl.icloseflag='1' then '已终止' else '未终止' end as 'pu_planline.icloseflag'
		FROM
			pu_planline pl
			left join CM_Material cm on pl.cmatguid = cm.cguid	
			left join CM_Unit u on pl.cMUnitGUID = u.cguid
		where pl.cheadguid={cGUID}
				]]>
		</i>
		
		<i id="puplanmcheck" desp="采购计划页面审核主表">
  		<![CDATA[	
		SELECT
			*
		FROM
			PU_Planhead po
			where po.cGUID = {cGUID}
				]]>
		</i>
		
		<i id="puplandcheck" desp="采购计划页面审核子表">
		<![CDATA[
		SELECT
			*
		FROM
			pu_planline pl
		where pl.cheadguid={cGUID}
				]]>
		</i>
		
		
	 	<i id="pureceivemain">

		</i>
		
		<i id="pureceivedetail">

		</i>
		
		<i id="pustkinmain">

		</i>
		
		<i id="pustkindetail">

		</i>
	</sql>
	
</sqls>