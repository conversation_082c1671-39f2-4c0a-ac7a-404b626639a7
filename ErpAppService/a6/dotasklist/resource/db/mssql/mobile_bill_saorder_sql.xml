<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_bill_saorder_sql" desp="销售订单单据SQL">		
		<i id="getMatSelectInfo" desp="选择物品确定后带入相关信息">
			<![CDATA[
	 
				SELECT  '' as cguid,
						a.cGUID AS cmatguid,
						a.cMatCode AS cmatcode,
						a.cMatName AS cmatname,
						isnull(a.cSpec, '') AS cspec,
						'' as cstoreguid,
						'' as cstorename,
						(case when isnull(a.iPrecision,0)=0 then left(cast(isnull(cur.iCurQuan,0) as varchar(21)),charindex('.',cast(isnull(cur.iCurQuan,0) as varchar(21)))-1)
					       else left(cast(isnull(cur.iCurQuan,0) as varchar(21)) , charindex('.',cast(isnull(cur.iCurQuan,0) as varchar(21))) +a.iPrecision)
					       end)+ u1.cName as iStoreCurUnitQty,
						a.iPrecision AS iMatPrecision,
					    (select cvalue from AOS_PREF_VALUE where ccode='U3_ST_PricePrecision') as iPricePrecision,
					  	0 as iisgift,
			            0 as iunitprice_f,
			            0 as itotal_f,
						0 as iamt_f,
						a.itaxrate,
						0 as itax_f,
						(select cvalue from AOS_PREF_VALUE where ccode='U3_SA_PriceType') as ipricetype,
						(select cvalue from AOS_PREF_VALUE where ccode='U3_SA_QuotedPriceTax') as ipricetax,
						case (select cvalue from AOS_PREF_VALUE where ccode='U3_SA_DoTaxRate') when 'n' then 'not' else 'can' end as u3_sa_dotaxrate,
						cs.irateflag, 
						a.cSaUGUID,
						a.iSubUnit,
						case when a.csarefepricemethod='f' then a.cSaUGUID
			                 else a.cUnitGUID
			                 end as cUnitGUID,
			           	case when a.csarefepricemethod='f' then u0.cName
			                 else u.cName
			                 end as cUnitName,
						a.csarefepricemethod
					FROM
					 cm_material a
					INNER JOIN CM_MatClass ac ON ac.cGUID = a.cMatCGUID
					LEFT JOIN CM_Unit u0 ON a.cSaUGUID = u0.cGUID
					LEFT JOIN CM_Unit u ON a.cUnitGUID = u.cGUID 
					left join CM_UnitClass cs on cs.cGUID=u.cClassGUID
					LEFT JOIN (
						SELECT
							SUM (c.iCurQty) AS iCurQuan,
							cMatGUID,
							cStkUnitGUID
						FROM
							ST_CurrentStock c
					  where $equal(c.cstoreguid,cstoreguid) 
							and exists(select 1 from CM_Storehouse a 								
									where a.cGUID = c.cstoreguid)
						GROUP BY
							cMatGUID,
							cStkUnitGUID
					) cur ON cur.cMatGUID = a.cGUID
					LEFT JOIN CM_Unit u1 ON cur.cStkUnitGUID = u1.cGUID
					where $in(a.cguid,idls) 		
					@filter[CM_Material,
					        CM_MatClass,
					        ST_CurrentStock.cStoreGUID=CM_Storehouse.cguid]
			]]>
		</i>
		
		<i id="getSaOrderMain" desp="获取销售订单主表数据">
			<![CDATA[
			select 
						sr.cBillCode,
						CONVERT(varchar(100),sr.dDate,23) as dDate,
						sr.cCustGUID,
						cc.cName as cCustName,
						sr.cDeptGUID,
						sr.cCurGUID,
						sr.cBusProcess as cbusprocessguid,
						cbp.cname as cbusprocessname,
						sr.cBusType as cbustypeguid,
						cbt.cname as cbustypename,
						cd.cName as cDeptName,
						sr.cEmpGUID,
						ce.cName as cEmpName,
            			sr.iAuditStatus,
            			sr.cCreatorGUID        			
			from SA_Order sr
			left join CM_Customer cc on sr.cCustGUID=cc.cguid
            left join CO_BusinessProcess cbp on sr.cBusProcess=cbp.cguid
            left join CO_BusinessType cbt on sr.cBusType=cbt.cguid
		    LEFT JOIN CM_Department cd ON sr.cDeptGUID = cd.cGUID
			LEFT JOIN CM_Employee ce ON sr.cEmpGUID = ce.cGUID
			WHERE sr.cGUID = ?
			]]>
		</i>
		
		<i id="getSaOrderDetail" desp="获取销售订单子表数据">
			<![CDATA[
			select s.cGUID,			     
			       s.cMatGUID,	
				   m.cMatName,
			       m.cMatCode,
			       m.cSpec,
		           s.cSaRefePriceUnitGUID as cunitguid,
		           u1.cName as cunitname,
		           'false' as checked,
				   s.irefepriceqty as iQTY,
			       s.iUnitPrice_F,
			       s.iAMT_F,
			       s.iTax_F,
			       s.iTotal_F,
			       s.iTaxPrice_F,
			       s.iDisAMT_F,
			       s.iDisAMT,
			       s.iisgift,			     
			       s.iPerTaxRate,			         
			       s.iDisRate,
				   s.iDisRateCon,				    
			       s.cStoreGUID,
			       store.cName as cStoreName,
				   m.iPrecision as iMatPrecision,
			       m.iGuaranteeFlag,			      
			       m.iBatchFlag,     
				   m.isnstart,
                   m.iSubUnit,
                   cs.irateflag,
                   s.iUsedRebateAMT,
                   s.iTaxQuotedPrice,
                   s.iQuotedPrice,
		   		   case when s.cStoreGUID is not null then 
					   ((case when isnull(m.iPrecision,0)=0 then left(cast(isnull(cur.iCurQuan,0) as varchar(21)),charindex('.',cast(isnull(cur.iCurQuan,0) as varchar(21)))-1)
				       else left(cast(isnull(cur.iCurQuan,0) as varchar(21)) , charindex('.',cast(isnull(cur.iCurQuan,0) as varchar(21))) +m.iPrecision)
				       end)+ u3.cName)
         		   else ((case when isnull(m.iPrecision,0)=0 then left(cast(isnull(cur1.iCurQuan,0) as varchar(21)),charindex('.',cast(isnull(cur1.iCurQuan,0) as varchar(21)))-1)
				       else left(cast(isnull(cur1.iCurQuan,0) as varchar(21)) , charindex('.',cast(isnull(cur1.iCurQuan,0) as varchar(21))) +m.iPrecision)
				       end)+ u4.cName) 
				   end as iStoreCurUnitQty,
				   (select cvalue from AOS_PREF_VALUE where ccode='U3_ST_PricePrecision') as iPricePrecision,
				   (select cvalue from AOS_PREF_VALUE where ccode='U3_SA_PriceType') as ipricetype,
				   (select cvalue from AOS_PREF_VALUE where ccode='U3_SA_QuotedPriceTax') as ipricetax,
				   (select cvalue from AOS_PREF_VALUE where ccode='U3_SA_DoTaxRate') as u3_sa_dotaxrate,
					s.iNumber
			  from SA_OrderLine s
			  LEFT JOIN SA_Order v on s.cHeadGUID = v.cGUID
			  inner join CM_Material m on m.cGUID = s.cMatGUID
			  LEFT JOIN (
						SELECT
							SUM (c.iCurQty) AS iCurQuan,
							cMatGUID,
							cStkUnitGUID,
							cStoreGuid
						FROM
							ST_CurrentStock c				
						GROUP BY
							cMatGUID,
							cStkUnitGUID,
							cStoreGuid
					) cur ON cur.cMatGUID = m.cGUID and cur.cStoreGuid=s.cstoreguid
			 LEFT JOIN (
						SELECT
							SUM (c.iCurQty) AS iCurQuan,
							cMatGUID,
							cStkUnitGUID
						FROM
							ST_CurrentStock c				
						GROUP BY
							cMatGUID,
							cStkUnitGUID
					) cur1 ON cur1.cMatGUID = m.cGUID
			  LEFT JOIN CM_Unit u1 ON u1.cGUID = s.cSaRefePriceUnitGUID
			  LEFT JOIN CM_Unit u2 on u2.cGUID = s.cUnitGUID
			  LEFT JOIN CM_Unit u3 ON cur.cStkUnitGUID = u3.cGUID	
			  LEFT JOIN CM_Unit u4 ON cur1.cStkUnitGUID = u4.cGUID
			  left join CM_UnitClass cs on cs.cGUID=u2.cClassGUID
			  Left join CM_StoreHouse store on store.cguid=s.cStoreGUID
			where s.cHeadGUID = {cGUID} 
			order by iNumber
			]]>
		</i>
		
         	
		<i id="getFile" desp="获取销售订单附件">
			<![CDATA[
				SELECT cGuid as hidden_cguid ,
				       cFileName,
				       cFileUrl
				FROM AOS_FILE_FILES
				WHERE cGroupGuid=?
			]]>
		</i>
	</sql>
</sqls>