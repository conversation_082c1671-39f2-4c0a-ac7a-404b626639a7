<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
  	<sql group="mobile_yiban_pulist">
  		<i id="getallpulist" desp="获取采购已办列表">
  			<![CDATA[
   		select top 100 * from 
  	 (select 
  			po.cguid  as hidden_cguid,
  			po.ccode,
  			po.cCheckWay as hidden_cCheckWay,
  			'076' as hidden_cbilltype,
  			'采购订单'+po.ccode as bt,
  			CONVERT(varchar(100),po.dCheckTime,120) as blsj,
  			convert(nvarchar, cast(round(k.iTotal ,2) as money),1)  as je,
        	ce.cName as  ywy,
            cd.cName as   bm
  	   from PU_Order po 
      inner join(select pl.cheadguid,	
  			       sum(isnull(pl.iTotal,0)) as iTotal
  		      from PU_OrderLine pl
		  group by pl.cheadguid  
  			) as k on k.cheadguid = po.cGUID  
     left join CM_Employee ce on po.cEmpGUID=ce.cguid
     left join CM_Department cd on po.cDeptGUID=cd.cguid
     left join AOS_RMS_USER u ON po.cCreatorGUID = u.cguid	
  	  where po.iAuditStatus='checked'
  	    and po.cCheckWay='0'
  	    and po.cAuditorGuid={currUserGUID}
  	    and (CONVERT(varchar(100),po.dCheckTime,23) ={today} or  '*' = {today:'*'})
        and ('*' = {keyword:'*'} or '采购订单' like '%{keyword}%')
 
  union all
	select 
  			po.cguid  as hidden_cguid, 
	     po.ccode,
	     '2' as hidden_cCheckWay,
	     '076' as hidden_cbilltype,
	     '采购订单'+po.ccode as bt,
	     CONVERT(varchar(100),b.END_,20) as blsj,
	     convert(nvarchar, cast(round(k.iTotal ,2) as money),1)  as je,
    	 ce.cName as  ywy,
         cd.cName as   bm	
	from PU_Order po
	inner join(select pl.cheadguid,	
  			       sum(isnull(pl.iTotal,0)) as iTotal
  		      from PU_OrderLine pl
		  group by pl.cheadguid  
  			) as k on k.cheadguid = po.cGUID  
    left join CM_Employee ce on po.cEmpGUID=ce.cguid
    left join CM_Department cd on po.cDeptGUID=cd.cguid
	left join JBPM4_HIST_PROCINST e on po.cguid = e.KEY_
	left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
	inner join(
		select a.DBID_,
			case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
			case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
		from JBPM4_HIST_TASK a 
		left join (
			select TIME_,HTASK_,USERID_
	        from JBPM4_HIST_DETAIL
	        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
	     ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		where
		/*对同一实例已完成任务过滤:取最近完成的任务*/
		a.DBID_ in (
               select max(t.dbid_)  
			   from JBPM4_HIST_TASK t
			   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
			   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
			   group by t.execution_
                 )
	) b on b.actTask = c.HTASK_
	left join WF_Process_Variable v on e.ID_ = v.procInstId
	left join WF_ProcessInfo h on v.processTemplate = h.cGUID
	left join WF_Process pr on pr.cguid = h.cNodeTemplate 
	where pr.cForm ='business_pu_puorder_billform'	
	and po.cCheckWay = '2'
    and (CONVERT(varchar(100),b.END_,23) ={today}  or '*' = {today:'*'})
	and ('*' = {keyword:'*'} or '采购订单' like '%{keyword}%')


  union all
  
  select 
  			po.cguid  as hidden_cguid,
  			po.cPlanCode as ccode,
  			po.cCheckWay as hidden_cCheckWay,
  			'220' as hidden_cbilltype,
  			'采购计划'+po.cPlanCode as bt,
  			CONVERT(varchar(100),po.dCheckTime,120) as blsj,
  			''  as je,
        	'' as  ywy,
            cd.cName as bm
  	   from PU_Planhead po  
     left join CM_Department cd on po.cDeptGUID=cd.cguid
     left join AOS_RMS_USER u ON po.cCreatorGUID = u.cguid	
  	  where po.iAuditStatus='checked'
  	    and po.cCheckWay='0'
  	    and po.cAuditorGuid={currUserGUID}
  	    and (CONVERT(varchar(100),po.dCheckTime,23) ={today} or  '*' = {today:'*'})
        and ('*' = {keyword:'*'} or '采购计划' like '%{keyword}%')
        
        union all
	select 
  	     po.cguid  as hidden_cguid, 
	     po.cPlanCode as ccode,
	     '2' as hidden_cCheckWay,
	     '220' as hidden_cbilltype,
	     '采购计划'+po.cPlanCode as bt,
	     CONVERT(varchar(100),b.END_,20) as blsj,
	     '' as je,
    	 '' as ywy,
         cd.cName as bm	
    	from PU_Planhead po 
    left join CM_Department cd on po.cDeptGUID=cd.cguid
	left join JBPM4_HIST_PROCINST e on po.cguid = e.KEY_
	left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
	inner join(
		select a.DBID_,
			case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
			case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
		from JBPM4_HIST_TASK a 
		left join (
			select TIME_,HTASK_,USERID_
	        from JBPM4_HIST_DETAIL
	        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
	     ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		where
		/*对同一实例已完成任务过滤:取最近完成的任务*/
		a.DBID_ in (
               select max(t.dbid_)  
			   from JBPM4_HIST_TASK t
			   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
			   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
			   group by t.execution_
                 )
	) b on b.actTask = c.HTASK_
	left join WF_Process_Variable v on e.ID_ = v.procInstId
	left join WF_ProcessInfo h on v.processTemplate = h.cGUID
	left join WF_Process pr on pr.cguid = h.cNodeTemplate 
	where pr.cForm ='business_pu_purPlan_form'	
	and po.cCheckWay = '2'
    and (CONVERT(varchar(100),b.END_,23) ={today}  or '*' = {today:'*'})
	and ('*' = {keyword:'*'} or '采购计划' like '%{keyword}%')
	@filter[pu_order.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
	       pu_order.cSupGUID=cm_supplier.cguid,
	       pu_order.cDeptGUID=cm_department.cguid,
	       pu_order.cCreatorGUID=AOS_RMS_USER.cguid,
	       pu_order.cEmpGUID=CM_Employee.cGUID,
	       PU_OrderLine.cMatGUID=CM_Material.cguid,
	       PU_OrderLine.cItemGUID=CM_Project.cGUID,
	       CM_MatClass,
		    PU_Planhead.cDeptGUID=cm_department.cguid,
			PU_Planhead.cCreatorGUID=AOS_RMS_USER.cguid,
			PU_Planline.cMatGUID=CM_Material.cguid,
			PU_Planline.cItemGUID=CM_Project.cGUID,
			PU_Planline.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
			PU_Planline.cSupGUID=cm_supplier.cguid,
			PU_Planline.cDeptGUID=cm_department.cguid,
			PU_Planline.cEmpGUID=CM_Employee.cGUID
			]	)pu
	order by blsj desc,ccode desc
	
			]]>
		</i>		
		<i id="getallpunum"  desp="获取采购已办数目">	
			<![CDATA[	
	select sum(t.num) as num from(	
/*采购订单*/
		 select 
  			count(1) num
  	   from PU_Order po 
       @table[PU_Order po]
  	  where po.iAuditStatus='checked'
  	    and po.cCheckWay='0'
  	    and po.cAuditorGuid={currUserGUID}
  	    and CONVERT(varchar(100),po.dCheckTime,23)=CONVERT(varchar(100),getdate(),23)
       
  union all
	select 
	     count(1) num 
	from PU_Order po
	left join JBPM4_HIST_PROCINST e on po.cguid = e.KEY_
	left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
	inner join(
		select a.DBID_,
			case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
			case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
		from JBPM4_HIST_TASK a 
		left join (
			select TIME_,HTASK_,USERID_
	        from JBPM4_HIST_DETAIL
	        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
	     ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		where
		/*对同一实例已完成任务过滤:取最近完成的任务*/
		a.DBID_ in (
               select max(t.dbid_)  
			   from JBPM4_HIST_TASK t
			   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
			   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
			   group by t.execution_
                 )
	) b on b.actTask = c.HTASK_
	left join WF_Process_Variable v on e.ID_ = v.procInstId
	left join WF_ProcessInfo h on v.processTemplate = h.cGUID
	left join WF_Process pr on pr.cguid = h.cNodeTemplate 
	where pr.cForm ='business_pu_puorder_billform'
	and CONVERT(varchar(100),b.END_,23)=CONVERT(varchar(100),getdate(),23)
/*采购计划*/
  union all
		 	 select 
  			count(1) num
  	   from PU_Planhead po 
       @table[PU_Planhead po]
  	  where po.iAuditStatus='checked'
  	    and po.cCheckWay='0'
  	    and po.cAuditorGuid={currUserGUID}
  	    and CONVERT(varchar(100),po.dCheckTime,23)=CONVERT(varchar(100),getdate(),23)
       
  union all
	select 
	     count(1) num 
	from PU_Planhead po
	left join JBPM4_HIST_PROCINST e on po.cguid = e.KEY_
	left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
	inner join(
		select a.DBID_,
			case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
			case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
		from JBPM4_HIST_TASK a 
		left join (
			select TIME_,HTASK_,USERID_
	        from JBPM4_HIST_DETAIL
	        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
	     ) DETAIL on DETAIL.HTASK_ = a.DBID_
  	 		where
		/*对同一实例已完成任务过滤:取最近完成的任务*/
		a.DBID_ in (
               select max(t.dbid_)  
			   from JBPM4_HIST_TASK t
			   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
			   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
			   		and d.CLASS_='status'
			   		and d.NEW_STR_ !='cancel'
			   		and d.NEW_STR_ !='retreat'
			   		and d.NEW_STR_ !='delegate'
			   		and d.NEW_STR_ !='rollback-type'
			   		and d.NEW_STR_ !='jump-type'
			   		and d.NEW_STR_!='rollback-target'
			   		and d.NEW_STR_ !='delegated'
			   group by t.execution_
                 )
	) b on b.actTask = c.HTASK_
	left join WF_Process_Variable v on e.ID_ = v.procInstId
	left join WF_ProcessInfo h on v.processTemplate = h.cGUID
	left join WF_Process pr on pr.cguid = h.cNodeTemplate 
	where pr.cForm ='business_pu_purPlan_form'
	and CONVERT(varchar(100),b.END_,23)=CONVERT(varchar(100),getdate(),23)
	and po.cCheckWay = '2'	
	@filter[pu_order.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
        pu_order.cSupGUID=cm_supplier.cguid,
        pu_order.cDeptGUID=cm_department.cguid,
        pu_order.cCreatorGUID=AOS_RMS_USER.cguid,
        pu_order.cEmpGUID=CM_Employee.cGUID,
        PU_OrderLine.cMatGUID=CM_Material.cguid,
        PU_OrderLine.cItemGUID=CM_Project.cGUID,
        CM_MatClass,
		PU_Planhead.cDeptGUID=cm_department.cguid,
		PU_Planhead.cCreatorGUID=AOS_RMS_USER.cguid,
		PU_Planline.cMatGUID=CM_Material.cguid,
		PU_Planline.cItemGUID=CM_Project.cGUID,
		PU_Planline.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
		PU_Planline.cSupGUID=cm_supplier.cguid,
		PU_Planline.cDeptGUID=cm_department.cguid,
		PU_Planline.cEmpGUID=CM_Employee.cGUID
		]
	)t
			]]>
		</i>	
		
	</sql>
	
	
	
</sqls>