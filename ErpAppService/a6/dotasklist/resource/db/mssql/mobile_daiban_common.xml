<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_daiban_common">
	<!-- 代办公共sql -->
		<i id="getDeptRoleList" desp="获取用户部分角色分组信息">
			<![CDATA[
				select distinct 'D_'+e.cDeptGUID deptGroupRoleId 
				from AOS_RMS_EMPL e 
				join aos_rms_user u on e.cGUID = u.cEMP 
				where u.cGuid = ? 
				union 
				select distinct 'R_' + r.cGuid deptGroupRoleId 
				from aos_rms_role r 
				INNER JOIN AOS_RMS_USER_ORGN_ROLE_REL uorr ON uorr.cRoleID = r.cGuid 
				INNER JOIN AOS_RMS_USER_ORGN_REL uor ON uor.cGUID = uorr.cUserOrgnID 
				INNER JOIN aos_rms_user u ON uor.cUserID = u.cGuid 
				INNER JOIN AOS_RMS_EMPL a ON a.cGUID = u.cEMP 
				where u.cGuid = ? 
				union 
				select distinct 'G_'+g.cGroupGuid deptGroupRoleId 
				from WF_Process_UserToGroup g 
				where g.cUserGuid = ?
			]]>
		</i>
		
		<i id="getDaibanBillTaskId" desp="获取代办单据taskid">
			<![CDATA[
				select a.DBID_ as taskId
				from JBPM4_TASK a
				left join JBPM4_EXECUTION d on d.INSTANCE_ = a.PROCINST_
				left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
				left join WF_Process_Variable v on d.ID_= v.procInstId 
				left join WF_ProcessInfo h on v.processTemplate = h.cGUID 
				left join WF_Process pr on pr.cguid = h.cNodeTemplate 
				where a.STATE_ = 'open' 
				and (a.ASSIGNEE_ = {currUserGUID} or (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
				and a.DBID_ NOT IN (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				and d.KEY_={cguid}
			]]>
		</i>
	</sql>
</sqls>