<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_list_saretail_sql" desp="列表数据加载">
		<i id="saRetailList" desp="零售单列表sql">
			<![CDATA[
				select top 100 * from(
				      select st.cguid as hidden_cguid,
				             st.cbillcode,
				             st.dDate,
				             st.iredflag,
				             case st.iAuditStatus when 'tsaved' then '暂存' when 'saved' then '保存待提交' when 'checking' then '提交待审核' 
				                  when 'checked' then '审核完成' when 'revise' then '退回修改中' when 'refuse' then '否决并终止' else '' end as iAuditStatusName,
				             st.iAuditStatus,     
				             c.cName as customer,
							       convert(nvarchar, cast(round(stl.iTotal_F ,2) as money),1) as saleMoney,   
				       	     CONVERT(varchar(50), st.dcreatortime,120) as dcreatortime
						 from 
							  		 ST_StkRecord st
						 inner JOIN (select l.cheadguid,	
											sum(isnull(l.iTotal_F,0)) as iTotal_F
									   from ST_StkRecordLine l
								          group by l.cheadguid  
						  				  ) as stl on stl.cheadguid = st.cGUID 
						left JOIN CM_Customer c ON st.cCustGUID = c.cGUID
						LEFT JOIN CM_Department d ON st.cDeptGUID = d.cGUID
						LEFT JOIN CM_Employee e ON st.cEmpGUID = e.cGUID
						left JOIN AOS_RMS_USER p1 on  st.cCreatorGUID = p1.cguid
						@table[ST_StkRecord st,CM_Customer c]
						where st.cBillType = '212'
							and st.iInitFlag = '0'
							and st.iRSFlag = '0'
							and $like(st.cBillCode,cBillCode)
							and $between(st.dDate,startDate,endDate)
							and $between(st.dcreatortime,clockStart,null)
							and $equal(st.cCustGUID,cCustGUID)	
							and $equal(st.cDeptGUID,cDeptGUID)	
							and $equal(st.cEmpGUID,cEmpGUID)
							and exists(select 1 from ST_StkRecordLine a 
								inner join cm_material ma on ma.cguid = a.cmatguid
								inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
								left join CM_Storehouse store_inner ON a.cStoreGUID = store_inner.cGUID
								where a.cHeadGUID = st.cGUID)							
				     @filter[ST_StkRecord.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
				             ST_StkRecord.cCustGUID=CM_Customer.cguid,
				             ST_StkRecord.cDeptGUID=cm_department.cguid,
				             ST_StkRecord.cEmpGUID=CM_Employee.cguid,
				             ST_StkRecord.cCreatorGUID=AOS_RMS_USER.cguid,
				             ST_StkRecord.cBusProcess=CO_BusinessProcess.cGUID,
				             ST_StkRecordLine.cMatGUID=CM_Material.cGUID,
				             ST_StkRecordLine.cStoreGUID=CM_Storehouse.cguid,
				             CM_MatClass
				   		    ]
				)a
				order by a.dDate DESC, a.cbillcode DESC
			]]>
		</i>
		
				<i id="saRetailSum" desp="获取所有零售单笔数">
			<![CDATA[
				select count(1) as salenum
			    from(
						select 
							     DISTINCT st.cguid				       	   
						from ST_StkRecordLine stl 
						inner join ST_StkRecord st on stl.cheadguid = st.cGUID 
						left JOIN CM_Customer c ON st.cCustGUID = c.cGUID
						LEFT JOIN CM_Department d ON st.cDeptGUID = d.cGUID
						LEFT JOIN CM_Employee e ON st.cEmpGUID = e.cGUID
						left JOIN AOS_RMS_USER p1 on  st.cCreatorGUID = p1.cguid
						@table[ST_StkRecord st,CM_Customer c]
						where st.cBillType = '212'
							and st.iInitFlag = '0'
							and st.iRSFlag = '0'
							and st.iAuditStatus != 'tsaved'  
							and $like(st.cBillCode,cBillCode)
							and $between(st.dDate,startDate,endDate)
							and $between(st.dcreatortime,clockStart,null)
							and $equal(st.cCustGUID,cCustGUID)	
							and $equal(st.cDeptGUID,cDeptGUID)	
							and $equal(st.cEmpGUID,cEmpGUID)
							and exists(select 1 from ST_StkRecordLine a 
								inner join cm_material ma on ma.cguid = a.cmatguid
								inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
								left join CM_Storehouse store_inner ON a.cStoreGUID = store_inner.cGUID
								where a.cHeadGUID = st.cGUID)								
				     @filter[ST_StkRecord.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
				             ST_StkRecord.cCustGUID=CM_Customer.cguid,
				             ST_StkRecord.cDeptGUID=cm_department.cguid,
				             ST_StkRecord.cEmpGUID=CM_Employee.cguid,
				             ST_StkRecord.cCreatorGUID=AOS_RMS_USER.cguid,
				             ST_StkRecord.cBusProcess=CO_BusinessProcess.cGUID,
				             ST_StkRecordLine.cMatGUID=CM_Material.cGUID,
				             ST_StkRecordLine.cStoreGUID=CM_Storehouse.cguid,
				             CM_MatClass
				   		    ]
				)a
			]]>
		</i>
		
		
		<i id="saRetailAmtSum" desp="获取所有零售单总金额">
			<![CDATA[
				select convert(nvarchar, cast(round(sum(iTotal_F), 2) as money),1) as salemoney
			    from(
						select 
							     (isnull(stl.iTotal_F,0)) as iTotal_F				       	   
						from ST_StkRecordLine stl 
						inner join ST_StkRecord st on stl.cheadguid = st.cGUID 
						left JOIN CM_Customer c ON st.cCustGUID = c.cGUID
						LEFT JOIN CM_Department d ON st.cDeptGUID = d.cGUID
						LEFT JOIN CM_Employee e ON st.cEmpGUID = e.cGUID
						left JOIN AOS_RMS_USER p1 on  st.cCreatorGUID = p1.cguid
						@table[ST_StkRecord st,CM_Customer c]
						where st.cBillType = '212'
							and st.iInitFlag = '0'
							and st.iRSFlag = '0'
							and st.iAuditStatus != 'tsaved'  
							and $like(st.cBillCode,cBillCode)
							and $between(st.dDate,startDate,endDate)
							and $between(st.dcreatortime,clockStart,null)
							and $equal(st.cCustGUID,cCustGUID)	
							and $equal(st.cDeptGUID,cDeptGUID)	
							and $equal(st.cEmpGUID,cEmpGUID)
							and exists(select 1 from ST_StkRecordLine a 
							inner join cm_material ma on ma.cguid = a.cmatguid
							inner join CM_MatClass cmc on ma.cMatCGUID = cmc.cguid
							left join CM_Storehouse store_inner ON a.cStoreGUID = store_inner.cGUID
							where a.cHeadGUID = st.cGUID)					
				     @filter[ST_StkRecord.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
				             ST_StkRecord.cCustGUID=CM_Customer.cguid,
				             ST_StkRecord.cDeptGUID=cm_department.cguid,
				             ST_StkRecord.cEmpGUID=CM_Employee.cguid,
				             ST_StkRecord.cCreatorGUID=AOS_RMS_USER.cguid,
				             ST_StkRecord.cBusProcess=CO_BusinessProcess.cGUID,
				             ST_StkRecordLine.cMatGUID=CM_Material.cGUID,
				             ST_StkRecordLine.cStoreGUID=CM_Storehouse.cguid,
				             CM_MatClass
				   		    ]
				)a
			]]>
		</i>
	</sql>
</sqls>