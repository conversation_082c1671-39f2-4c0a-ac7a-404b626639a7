<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_yiban_emList" desp="借款报销已办列表sql">
		<!-- 2017-02-28 qcj 修正获取借款报销已办列表sql -->
		<!-- 2017-03-02 qcj 增加金额的千分位显示 -->
		<i id="getEMList" desp="获取借款报销已办列表">
			<![CDATA[
				select top 100 *
				from(
				select loan.cguid as hidden_cguid,'0' as hidden_cCheckWay,'oa_008' as hidden_cbilltype,
					'借款申请'+loan.cCode as bt, CONVERT(varchar(100),loan.dCheckTime,20) as blsj,
					convert(nvarchar, cast(round(loan.iLoanAMT, 2) as money),1) as je,
					loanee.cName as zy,lDept.cName as bm
				from OA_LoanBill loan
				left join CM_Employee loanee on loanee.cGUID = loan.cLoaneeGUID
				left join CM_Department lDept on lDept.cGUID = loan.cLoaneeDeptGUID
				where loan.cStatusEnumGUID = 'checked'
				and loan.cAuditorGuid={currUserGUID}
				and loan.cCheckWay in('0')
				and ('*' = {today:'*'} or CONVERT(varchar(100),loan.dCheckTime,23) ={today})
				and ('*' = {keyword:'*'} or '借款申请' like '%{keyword}%')
				union all
				select loan.cguid as hidden_cguid,'2' as hidden_cCheckWay,'oa_008' as hidden_cbilltype,
					'借款申请'+loan.cCode as bt, 
					CONVERT(varchar(100),b.END_,20) as blsj,
					convert(nvarchar, cast(round(loan.iLoanAMT, 2) as money),1) as je,
					loanee.cName as zy,lDept.cName as bm
				from OA_LoanBill loan
				left join CM_Employee loanee on loanee.cGUID = loan.cLoaneeGUID
				left join CM_Department lDept on lDept.cGUID = loan.cLoaneeDeptGUID
				left join JBPM4_HIST_PROCINST e on loan.cguid = e.KEY_
				left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
				inner join(
					select a.DBID_,
						case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
						case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
					from JBPM4_HIST_TASK a 
					left join (
						select TIME_,HTASK_,USERID_
				        from JBPM4_HIST_DETAIL
				        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
				     ) DETAIL on DETAIL.HTASK_ = a.DBID_
		   	 		where
					/*对同一实例已完成任务过滤:取最近完成的任务*/
					a.DBID_ in (
                           select max(t.dbid_)  
						   from JBPM4_HIST_TASK t
						   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
						   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
						   		and d.CLASS_='status'
						   		and d.NEW_STR_ !='cancel'
						   		and d.NEW_STR_ !='retreat'
						   		and d.NEW_STR_ !='delegate'
						   		and d.NEW_STR_ !='rollback-type'
						   		and d.NEW_STR_ !='jump-type'
						   		and d.NEW_STR_!='rollback-target'
						   		and d.NEW_STR_ !='delegated'
						   group by t.execution_
                    )
				) b on b.actTask = c.HTASK_
				left join WF_Process_Variable v on e.ID_ = v.procInstId
				left join WF_ProcessInfo h on v.processTemplate = h.cGUID
				left join WF_Process pr on pr.cguid = h.cNodeTemplate 
				where pr.cForm ='oa_fm_loanBill_edit_form'
				and loan.cCheckWay = '2'
				and ('*' = {today:'*'} or CONVERT(varchar(100),b.END_,23) ={today})
				and ('*' = {keyword:'*'} or '借款申请' like '%{keyword}%')
				union all
				select eab.cguid as hidden_cguid,'0' as hidden_cCheckWay,'oa_010' as hidden_cbilltype,
					'报销申请'+eab.cCode as bt,CONVERT(varchar(100),eab.dCheckTime,20) as blsj,
					convert(nvarchar, cast(round(eab.iTotalAMT, 2) as money),1) as je,
					expUser.cName as zy,expDept.cName as bm
				from OA_ExpenseAccountBill eab
				left join CM_Employee expUser on expUser.cGUID = eab.cExpUserGUID
				left join CM_Department expDept on expDept.cGUID = eab.cExpDeptGUID
				where eab.cAuditorGuid={currUserGUID}
				and eab.cStatusEnumGUID = 'checked'
				and eab.cCheckWay in ('0')
				and ('*' = {today:'*'} or CONVERT(varchar(100),eab.dCheckTime,23) =  {today})
				and ('*' = {keyword:'*'} or '报销申请' like '%{keyword}%')
				union all
				select eab.cguid as hidden_cguid,'2' as hidden_cCheckWay,'oa_010' as hidden_cbilltype,
					'报销申请'+eab.cCode as bt,
					CONVERT(varchar(100),b.END_,20) as blsj,
					convert(nvarchar, cast(round(eab.iTotalAMT, 2) as money),1) as je,
					expUser.cName as zy,expDept.cName as bm
				from OA_ExpenseAccountBill eab
				left join CM_Employee expUser on expUser.cGUID = eab.cExpUserGUID
				left join CM_Department expDept on expDept.cGUID = eab.cExpDeptGUID
				left join JBPM4_HIST_PROCINST e on eab.cguid = e.KEY_
				left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
				inner join(
					select a.DBID_,
						case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
						case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
					from JBPM4_HIST_TASK a 
					left join (
						select TIME_,HTASK_,USERID_
				        from JBPM4_HIST_DETAIL
				        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
				     ) DETAIL on DETAIL.HTASK_ = a.DBID_
		   	 		where
					/*对同一实例已完成任务过滤:取最近完成的任务*/
					a.DBID_ in (
                           select max(t.dbid_)  
						   from JBPM4_HIST_TASK t
						   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
						   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
						   		and d.CLASS_='status'
						   		and d.NEW_STR_ !='cancel'
						   		and d.NEW_STR_ !='retreat'
						   		and d.NEW_STR_ !='delegate'
						   		and d.NEW_STR_ !='rollback-type'
						   		and d.NEW_STR_ !='jump-type'
						   		and d.NEW_STR_!='rollback-target'
						   		and d.NEW_STR_ !='delegated'
						   group by t.execution_
                    )
				) b on b.actTask = c.HTASK_
				left join WF_Process_Variable v on e.ID_ = v.procInstId
				left join WF_ProcessInfo h on v.processTemplate = h.cGUID
				left join WF_Process pr on pr.cguid = h.cNodeTemplate 
				where pr.cForm ='oa_fm_expenseAccountBill_edit_form'
				and eab.cCheckWay = '2'
				and ('*' = {today:'*'} or CONVERT(varchar(100),b.END_,23) = {today})
				and ('*' = {keyword:'*'} or '报销申请' like '%{keyword}%')
				)em
				order by em.blsj desc
			]]>
		</i>
	
	
		
		<i id="getEMListNum" desp="获取已办列表">
			<![CDATA[
				select sum(em.num) as num
				from
				(
				select count(1) num
				from OA_LoanBill loan
				where CONVERT(varchar(100),loan.dCheckTime,23)=CONVERT(varchar(100),getdate(),23)
				and loan.cAuditorGuid={currUserGUID}
				and loan.cStatusEnumGUID = 'checked'
				and loan.cCheckWay in('0')
				union all
				select count(1) num
				from OA_LoanBill loan
				left join JBPM4_HIST_PROCINST e on loan.cguid = e.KEY_
				left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
				inner join(
					select a.DBID_,
						case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
						case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
					from JBPM4_HIST_TASK a 
					left join (
						select TIME_,HTASK_,USERID_
				        from JBPM4_HIST_DETAIL
				        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
				     ) DETAIL on DETAIL.HTASK_ = a.DBID_
		   	 		where
					/*对同一实例已完成任务过滤:取最近完成的任务*/
					a.DBID_ in (
                           select max(t.dbid_)  
						   from JBPM4_HIST_TASK t
						   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
						   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
						   		and d.CLASS_='status'
						   		and d.NEW_STR_ !='cancel'
						   		and d.NEW_STR_ !='retreat'
						   		and d.NEW_STR_ !='delegate'
						   		and d.NEW_STR_ !='rollback-type'
						   		and d.NEW_STR_ !='jump-type'
						   		and d.NEW_STR_!='rollback-target'
						   		and d.NEW_STR_ !='delegated'
						   group by t.execution_
                    )
				) b on b.actTask = c.HTASK_
				left join WF_Process_Variable v on e.ID_ = v.procInstId
				left join WF_ProcessInfo h on v.processTemplate = h.cGUID
				left join WF_Process pr on pr.cguid = h.cNodeTemplate 
				where pr.cForm ='oa_fm_loanBill_edit_form'
				and CONVERT(varchar(100),b.END_,23)=CONVERT(varchar(100),getdate(),23)
				and loan.cCheckWay = '2'
				union all
				select count(1) num
				from OA_ExpenseAccountBill eab
				where CONVERT(varchar(100),eab.dCheckTime,23)=CONVERT(varchar(100),getdate(),23)
				and eab.cAuditorGuid={currUserGUID}
				and eab.cStatusEnumGUID = 'checked'
				and eab.cCheckWay in ('0')
				union all
				select count(1) num
				from OA_ExpenseAccountBill eab
				left join JBPM4_HIST_PROCINST e on eab.cguid = e.KEY_
				left join JBPM4_HIST_ACTINST c on e.DBID_ = c.HPROCI_
				inner join(
					select a.DBID_,
						case when a.supertask_ is null then a.dbid_ else a.supertask_ end as actTask,
						case when DETAIL.TIME_ is null then a.END_ else DETAIL.TIME_  end as END_
					from JBPM4_HIST_TASK a 
					left join (
						select TIME_,HTASK_,USERID_
				        from JBPM4_HIST_DETAIL
				        where USERID_={currUserGUID} and ( NEW_STR_ in ('submit','cancel-end','rollback','suspended','resumed') or CLASS_='comment')
				     ) DETAIL on DETAIL.HTASK_ = a.DBID_
		   	 		where
					/*对同一实例已完成任务过滤:取最近完成的任务*/
					a.DBID_ in (
                           select max(t.dbid_)  
						   from JBPM4_HIST_TASK t
						   inner join JBPM4_HIST_DETAIL d on d.htask_ = t.dbid_
						   where ( d.USERID_={currUserGUID} or (t.assignee_ = {currUserGUID} and t.state_ = 'completed' ) )
						   		and d.CLASS_='status'
						   		and d.NEW_STR_ !='cancel'
						   		and d.NEW_STR_ !='retreat'
						   		and d.NEW_STR_ !='delegate'
						   		and d.NEW_STR_ !='rollback-type'
						   		and d.NEW_STR_ !='jump-type'
						   		and d.NEW_STR_!='rollback-target'
						   		and d.NEW_STR_ !='delegated'
						   group by t.execution_
                    )
				) b on b.actTask = c.HTASK_
				left join WF_Process_Variable v on e.ID_ = v.procInstId
				left join WF_ProcessInfo h on v.processTemplate = h.cGUID
				left join WF_Process pr on pr.cguid = h.cNodeTemplate 
				where pr.cForm ='oa_fm_expenseAccountBill_edit_form'
				and CONVERT(varchar(100),b.END_,23)=CONVERT(varchar(100),getdate(),23)
				and eab.cCheckWay = '2'
				) em
			]]>
		</i>
	</sql>
</sqls>