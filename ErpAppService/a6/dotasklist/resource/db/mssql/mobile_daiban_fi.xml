<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
    <!-- 参照PU结构 -->
  	<sql group="mobile_daiban_filist">
  	
  	
  	     <i id="getallfilist">	
		 
		    SELECT TOP 100 * FROM(
  	         select 
			      st.cguid as hidden_cguid, 
				  st.cvoucode,
                pl.crealname as hidden_cCreator,
                '0' as hidden_cCheckWay,
                st.iAuditStatus as hidden_iauditstatus,
                '082' as hidden_cbilltype,
                '付款单'+st.cVouCode as bt,
                CONVERT(varchar(50), st.cCreatorDate,120) as sqsj,
                pl.crealname as "sqr",
				null as "ywy",
                cd.cname  as "bm",
                 convert(nvarchar, cast(round( st.iPayAMT ,2) as money),1)  as je,
                cs.cname as "gys"
		        from AP_Payment st
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Supplier cs on cs.cguid=st.cSupGUID
                where st.iAuditStatus = 'saved' 
                and st.cflag='AP'
                and ({sgAP:'false'}='true')
		      	and (  ('*' = {keyword:'*'} or convert(varchar(50),st.cCreatorDate,112) like '%{keyword}%')
		         or $like(pl.crealname,keyword)
		         or ('*' = {keyword:'*'} or '付款单' like '%{keyword}%'))
				 
				 union all
				 
				 
				  
            	 select   
			    st.cguid as hidden_cguid, 
				  st.cvoucode,
                pl.crealname as hidden_cCreator,
                 '2' as hidden_cCheckWay,
                 st.iAuditStatus as hidden_iauditstatus,
                '082' as hidden_cbilltype,
                '付款单'+st.cVouCode as bt,
                CONVERT(varchar(50),e.START_,120) as sqsj,
                pl.crealname as "sqr",
				null as "ywy",
                cd.cname  as "bm",
                 convert(nvarchar, cast(round( st.iPayAMT ,2) as money),1)  as je,
                cs.cname as "gys"
		        from AP_Payment[-filter] st
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Supplier cs on cs.cguid=st.cSupGUID
                left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where ({wfAP:'false'}='true')	
				and st.cflag='AP'
		      	and (  '*' = {keyword:'*'} 
		      	      or convert(varchar(50),e.START_,112) like '%{keyword}%' 
		              or $like(pl.crealname,keyword)
		              or ('付款单' like '%{keyword}%'))
              
			  
			  union  all   
			 select   
			  st.cguid as hidden_cguid, 
				  st.cvoucode,
                pl.crealname as hidden_cCreator,
                 '2' as hidden_cCheckWay,
                 st.iAuditStatus as hidden_iauditstatus,
                '082' as hidden_cbilltype,
                '付款单'+st.cVouCode as bt,
                CONVERT(varchar(50), e.START_,120) as sqsj,
                pl.crealname as "sqr",
				null as "ywy",
                cd.cname  as "bm",
                 convert(nvarchar, cast(round( st.iPayAMT ,2) as money),1)  as je,
                cs.cname as "gys"
		        from AP_Payment st
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Supplier cs on cs.cguid=st.cSupGUID
                left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where  ({wffilterAP:'false'}='true')	
				and st.cflag='AP'
		      	and (  '*' = {keyword:'*'} 
		      	      or convert(varchar(50),e.START_,112) like '%{keyword}%' 
		              or $like(pl.crealname,keyword)
		              or ('付款单' like '%{keyword}%'))
		              
				union all

        select 
            	 st.cguid as hidden_cguid, 
				st.cVouCode,
                pl.crealname as hidden_cCreator,
                  '0' as  hidden_ccheckway,
                 st.iAuditStatus as hidden_iauditstatus,
                '105' as hidden_cbilltype,
                '其它付款单'+st.cVouCode as "bt",
                 CONVERT(varchar(50),st.dVouDate,23) as 'sqsj',
				 null as "sqr",
                 ce.cname as "ywy",
                cd.cname  as "bm",
                 convert(nvarchar, cast(round( st.iPayAMT ,2) as money),1)  as je,
				null as "gys"
		        from CA_OtherPayment st
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Employee ce on st.cEmpGUID=ce.cguid
                where st.iAuditStatus = 'saved'
                 and  st.cflag='P'
                 and ({sgCA:'false'}='true')
		      	and (  ('*' = {keyword:'*'} or convert(varchar(50),st.dVouDate,112) like '%{keyword}%' )
		         or $like(pl.crealname,keyword)
		         or ('*' = {keyword:'*'} or '其它付款单' like '%{keyword}%'))
				
				
                union all	

select 
            	 st.cguid as hidden_cguid, 
				st.cVouCode,
                pl.crealname as hidden_cCreator,
                  '2' as  hidden_ccheckway,
                  st.iAuditStatus as hidden_iauditstatus,
                '105' as hidden_cbilltype,
                '其它付款单'+st.cVouCode as "bt",
                 CONVERT(varchar(50),e.START_,120) as 'sqsj',
				 null as "sqr",
                 ce.cname as "ywy",
                cd.cname  as "bm",
                 convert(nvarchar, cast(round( st.iPayAMT ,2) as money),1)  as je,
				null as "gys"
                from CA_OtherPayment[-filter] st
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Employee ce on st.cEmpGUID=ce.cguid
                left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
                
              inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where
				({wfCA:'false'}='true')		
		      	and (  '*' = {keyword:'*'} 
		      	        or convert(varchar(50),e.START_,112) like '%{keyword}%'
		                or $like( pl.crealname,keyword)
		                or ('其它付款单' like '%{keyword}%'))
		         and  st.cflag='P'
		        
               union  all			   
					  
			select 
            	 st.cguid as hidden_cguid, 
				st.cVouCode,
                pl.crealname as hidden_cCreator,
                  '2' as  hidden_ccheckway,
                  st.iAuditStatus as hidden_iauditstatus,
                '105' as hidden_cbilltype,
                '其它付款单'+st.cVouCode as "bt",
                 CONVERT(varchar(50),e.START_,120) as 'sqsj',
				 null as "sqr",
                 ce.cname as "ywy",
                cd.cname  as "bm",
                 convert(nvarchar, cast(round( st.iPayAMT ,2) as money),1)  as je,
				null as "gys"
                from CA_OtherPayment st
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Employee ce on st.cEmpGUID=ce.cguid
                left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
                 inner join(
	                select distinct a.PROCINST_,a.CREATE_ as  START_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where  ({wffilterCA:'false'}='true')	
				and  st.cflag='P'
		      	and (  '*' = {keyword:'*'} 
		      	        or convert(varchar(50),e.START_,112) like '%{keyword}%'
		                or $like( pl.crealname,keyword)
		                or ('其它付款单' like '%{keyword}%'))
		     
               @filter[AP_Payment.cEmpGUID=cm_employee.cGUID,
		       AP_Payment.cSupGUID=cm_supplier.cguid,
		       AP_Payment.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
		       AP_Payment.cAcctGUID=gl_account.cGUID,AP_Payment.cDeptGUID=cm_department.cguid,
		       AP_Payment.cCreatorGUID=AOS_RMS_USER.cguid,AP_Payment.cItemGUID=cm_project.cGUID,
		       AP_Payment.cBankAcctNub=cm_bankline.cguid,aos_rms_user,CA_OtherPayment.cAcctGUID=gl_account.cGUID,CA_OtherPayment.cRBankAcctNub=cm_bankline.cguid]
		       
		       )M
		       ORDER BY sqsj DESC ,cvoucode DESC
			  
		</i>
  	
  	 <i id="getallfinum">	
		 
  	         

    SELECT sum(num) as num FROM(

select 
            	count(1)  num
		        from AP_Payment st
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Supplier cs on cs.cguid=st.cSupGUID
                where st.iAuditStatus = 'saved' 
				and ({sgAP:'false'}='true')
				and st.cflag='AP'
				
				union all
				
				select 
            	count(1) num
		        from AP_Payment[-filter] st
                left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				inner join(
	                select distinct a.PROCINST_
	                from JBPM4_TASK a
	                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
					where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
					  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
					  and a.DBID_ NOT IN
					   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where {wfAP:'false'}='true'
				and st.cflag='AP'
union all

          select 
            	 count(1) num
		         from AP_Payment st
                 left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				inner join(
                select distinct a.PROCINST_
                from JBPM4_TASK a
                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
				where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
				  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
				  and a.DBID_ NOT IN
				   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where  {wffilterAP:'false'}='true'	
				and st.cflag='AP'
union all
           select 
            	count(1)  num
		        from CA_OtherPayment st
                left join CA_OtherPaymentLine stl on stl.cHeadGUID = st.cGUID 
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Employee ce on st.cEmpGUID=ce.cguid
                where st.iAuditStatus = 'saved' 
		        and ({sgCA:'false'}='true')	
		        and  st.cflag='P'

union  all

          select 
            	 count(1)  num
                from CA_OtherPayment[-filter] st
                left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				inner join(
                select distinct a.PROCINST_
                from JBPM4_TASK a
                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
				where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
				  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
				  and a.DBID_ NOT IN
				   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where  {wfCA:'false'}='true'
				and  st.cflag='P'			
		      
  

union  all

select 
            	 count(1)  num
                from CA_OtherPayment  st
                left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
                inner join(
                select distinct a.PROCINST_
                from JBPM4_TASK a
                left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
				where a.STATE_ = 'open' and (a.ASSIGNEE_ = {currUserGUID} or 
				  (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
				  and a.DBID_ NOT IN
				   	 (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				) e on e.PROCINST_ = d.INSTANCE_
				where {wffilterCA:'false'}='true'
				and  st.cflag='P'			

  @filter[AP_Payment.cEmpGUID=cm_employee.cGUID,
		       AP_Payment.cSupGUID=cm_supplier.cguid,
		       AP_Payment.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
		       AP_Payment.cAcctGUID=gl_account.cGUID,AP_Payment.cDeptGUID=cm_department.cguid,
		       AP_Payment.cCreatorGUID=AOS_RMS_USER.cguid,AP_Payment.cItemGUID=cm_project.cGUID,
		       AP_Payment.cBankAcctNub=cm_bankline.cguid,aos_rms_user,CA_OtherPayment.cAcctGUID=gl_account.cGUID,CA_OtherPayment.cRBankAcctNub=cm_bankline.cguid]
		       
		       )m

				
		     
              	
		</i>
  	
  	
  	
  	
  	
  	
  	
  	
  	
  	
  	
  <!--  
  	
  		<i id="appaymentlist"   desp="手工审批">
  		select 
            	count(1)  num
		        from AP_Payment st
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Supplier cs on cs.cguid=st.cSupGUID
                where st.iAuditStatus = 'saved' 
		     
		    
		         
                
  
		</i>	
		
		<i id="appaymenlistByWorkflow"   desp="流程审批">
  		select 
            	distinct st.cguid  as hidden_cguid,
                pl.crealname as hidden_cCreator,
                (select  cCheckWay from  BILL_SETTING where cFormId = 'bus_ap_bill_form') as  hidden_ccheckway,
                '082' as hidden_cbilltype,
                '付款单'+st.cVouCode as "标题",
		         CONVERT(varchar(50), st.cCreatorDate,120) as '申请时间',
                pl.crealname as "申请人",
                cd.cname  as "申请部门",
                st.iPayAMT as "付款金额",
                cs.cname as "供应商"
		        from AP_Payment st
                left join AP_PaymentDetail stl on stl.cPaymentGUID = st.cGUID 
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Supplier cs on cs.cguid=st.cSupGUID
                 left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				 left join JBPM4_TASK a on a.PROCINST_ = d.INSTANCE_  
				 left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
				 left join WF_Process_Variable v on d.ID_= v.procInstId 
			     left join WF_ProcessInfo h on v.processTemplate = h.cGUID 
				 left join WF_Process pr on pr.cguid = h.cNodeTemplate 
				 left join JBPM4_HIST_PROCINST hp on a.PROCINST_ = hp.DBID_
				where a.STATE_ = 'open' 
				and (a.ASSIGNEE_ = {currUserGUID} or (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
				and a.DBID_ NOT IN (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				and pr.cForm ='bus_ap_bill_form'	
				and st.iAuditStatus!='revise'		
		      	and (  '*' = {keyword:'*'} 
		      	      or convert(varchar(50),hp.START_,112) like '%{keyword}%' 
		              or $like(v.ownerName,keyword)
		              or ('付款单' like '%{keyword}%'))
              
               
  
		</i>
		<i id="appaymenlistByWorkflowWithDatafilter"   desp="流程审批，此为当用户没有待办事项的权限，
		                                       却拥有列表或者单据，还有审核的权限时的情况，
		                                       与上面sql相比，多加入了数据权限的过滤">
  		select 
            	distinct st.cguid  as hidden_cguid,
                pl.crealname as hidden_cCreator,
                (select  cCheckWay from  BILL_SETTING where cFormId = 'bus_ap_bill_form') as  hidden_ccheckway,
                '082' as hidden_cbilltype,
                '付款单'+st.cVouCode as "标题",
		         CONVERT(varchar(50), st.cCreatorDate,120) as '申请时间',
                 pl.crealname as "申请人",
                cd.cname  as "申请部门",
                st.iPayAMT as "付款金额",
                cs.cname as "供应商"
		        from AP_Payment st
                left join AP_PaymentDetail stl on stl.cPaymentGUID = st.cGUID 
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Supplier cs on cs.cguid=st.cSupGUID
                 left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				 left join JBPM4_TASK a on a.PROCINST_ = d.INSTANCE_  
				 left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
				 left join WF_Process_Variable v on d.ID_= v.procInstId 
			     left join WF_ProcessInfo h on v.processTemplate = h.cGUID 
				 left join WF_Process pr on pr.cguid = h.cNodeTemplate 
				 left join JBPM4_HIST_PROCINST hp on a.PROCINST_ = hp.DBID_
				where a.STATE_ = 'open' 
				and (a.ASSIGNEE_ = {currUserGUID} or (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
				and a.DBID_ NOT IN (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				and pr.cForm ='bus_ap_bill_form'	
				and st.iAuditStatus!='revise'		
		      	and (  '*' = {keyword:'*'} 
		      	      or convert(varchar(50),hp.START_,112) like '%{keyword}%' 
		              or $like(v.ownerName,keyword)
		              or ('付款单' like '%{keyword}%'))
               @filter[AP_Payment.cEmpGUID=cm_employee.cGUID,
		       AP_Payment.cSupGUID=cm_supplier.cguid,
		       AP_Payment.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
		       AP_Payment.cAcctGUID=gl_account.cGUID,AP_Payment.cDeptGUID=cm_department.cguid,
		       AP_Payment.cCreatorGUID=AOS_RMS_USER.cguid,AP_Payment.cItemGUID=cm_project.cGUID,
		       AP_Payment.cBankAcctNub=cm_bankline.cguid]
  
		</i>						
		<i id="appaymentnum">	
		 
  	          select 
		        count(1)
		        from AP_Payment po 
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				left join JBPM4_TASK a on a.PROCINST_ = d.INSTANCE_  
				left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
				left join WF_Process_Variable v on d.ID_= v.procInstId 
				left join WF_ProcessInfo h on v.processTemplate = h.cGUID 
				left join WF_Process pr on pr.cguid = h.cNodeTemplate 
				where a.STATE_ = 'open' 
				and (a.ASSIGNEE_ = {currUserGUID} or (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
				and a.DBID_ NOT IN (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				and pr.cForm ='bus_ap_bill_form'
				and po.iAuditStatus!='revise'	
		</i>
		<i id="appaymentnumWorkFlow"  desp="与上述sql区别多了数据权限" >	
		 
  	          select 
		        count(1)
		        from AP_Payment po 
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				left join JBPM4_TASK a on a.PROCINST_ = d.INSTANCE_  
				left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
				left join WF_Process_Variable v on d.ID_= v.procInstId 
				left join WF_ProcessInfo h on v.processTemplate = h.cGUID 
				left join WF_Process pr on pr.cguid = h.cNodeTemplate 
				where a.STATE_ = 'open' 
				and (a.ASSIGNEE_ = {currUserGUID} or (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
				and a.DBID_ NOT IN (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				and pr.cForm ='bus_ap_bill_form'
				and po.iAuditStatus!='revise'	
				@filter[AP_Payment.cEmpGUID=cm_employee.cGUID,
		        AP_Payment.cSupGUID=cm_supplier.cguid,
		        AP_Payment.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,
		        AP_Payment.cAcctGUID=gl_account.cGUID,AP_Payment.cDeptGUID=cm_department.cguid,
		        AP_Payment.cCreatorGUID=AOS_RMS_USER.cguid,AP_Payment.cItemGUID=cm_project.cGUID,
		        AP_Payment.cBankAcctNub=cm_bankline.cguid]
		         		
		</i>
		
		<i id="caapvoucherlist"  desp="手工审批">
  			select 
            	distinct st.cguid  as hidden_cguid,
                pl.crealname as hidden_cCreator,
                (select  cCheckWay from  BILL_SETTING where cFormId = 'finance_ca_apvoucher_edit_form') as  hidden_ccheckway,
                '105' as hidden_cbilltype,
                '其它付款单'+st.cVouCode as "标题",
                 CONVERT(varchar(50),st.dVouDate,120) as '申请时间',
                 ce.cname as "业务员",
                cd.cname  as "部门",
                st.iPayAMT as "付款金额"
		        from CA_OtherPayment st
                left join CA_OtherPaymentLine stl on stl.cHeadGUID = st.cGUID 
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Employee ce on st.cEmpGUID=ce.cguid
                where st.iAuditStatus = 'saved' 
		      	and (  ('*' = {keyword:'*'} or convert(varchar(50),st.dVouDate,112) like '%{keyword}%' )
		         or $like(pl.crealname,keyword)
		         or ('*' = {keyword:'*'} or '其它付款单' like '%{keyword}%'))
               @filter[aos_rms_user,CA_OtherPayment.cAcctGUID=gl_account.cGUID,CA_OtherPayment.cRBankAcctNub=cm_bankline.cguid]
                
  
		</i>		
		
		
		<i id="caapvoucherByWorkflow"  desp="其它付款单流程审批">
  			select 
            	distinct st.cguid  as hidden_cguid,
                pl.crealname as hidden_cCreator,
                (select  cCheckWay from  BILL_SETTING where cFormId = 'finance_ca_apvoucher_edit_form') as  hidden_ccheckway,
                 '105' as hidden_cbilltype,
                '其它付款单'+st.cVouCode as "标题",
                 CONVERT(varchar(50),st.dVouDate,120) as '申请时间',
                 ce.cname as "业务员",
                cd.cname  as "部门",
                st.iPayAMT as "付款金额"
                from CA_OtherPayment st
                left join CA_OtherPaymentLine stl on stl.cHeadGUID = st.cGUID
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Employee ce on st.cEmpGUID=ce.cguid
                left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				 left join JBPM4_TASK a on a.PROCINST_ = d.INSTANCE_  
				 left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
				 left join WF_Process_Variable v on d.ID_= v.procInstId 
			     left join WF_ProcessInfo h on v.processTemplate = h.cGUID 
				 left join WF_Process pr on pr.cguid = h.cNodeTemplate 
				 left join JBPM4_HIST_PROCINST hp on a.PROCINST_ = hp.DBID_
				where a.STATE_ = 'open' 
				and (a.ASSIGNEE_ = {currUserGUID} or (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
				and a.DBID_ NOT IN (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				and pr.cForm ='finance_ca_apvoucher_edit_form'	
				and st.iAuditStatus!='revise'		
		      	and (  '*' = {keyword:'*'} 
		      	        or convert(varchar(50),hp.START_,112) like '%{keyword}%'
		                or $like(v.ownerName,keyword)
		                or ('其它付款单' like '%{keyword}%'))
  
		</i>	
		
		
		<i id="caapvoucherByWorkflowWithDatafilter"  desp="流程审批，此为当用户没有待办事项的权限，
		                                       却拥有列表或者单据，还有审核的权限时的情况，
		                                       与上面sql相比，多加入了数据权限的过滤">
  			select 
            	distinct st.cguid  as hidden_cguid,
                pl.crealname as hidden_cCreator,
                (select  cCheckWay from  BILL_SETTING where cFormId = 'finance_ca_apvoucher_edit_form') as  hidden_ccheckway,
                 '105' as hidden_cbilltype,
                '其它付款单'+st.cVouCode as "标题",
                 CONVERT(varchar(50),st.dVouDate,120) as '申请时间',
                 ce.cname as "业务员",
                cd.cname  as "部门",
                st.iPayAMT as "付款金额"
                from CA_OtherPayment st
                left join CA_OtherPaymentLine stl on stl.cHeadGUID = st.cGUID
		        left join AOS_RMS_USER pl ON st.cCreatorGUID = pl.cguid		
                left join CM_Department cd on st.cDeptGUID=cd.cguid
                left join CM_Employee ce on st.cEmpGUID=ce.cguid
                left join JBPM4_EXECUTION d on d.KEY_ = st.cguid
				 left join JBPM4_TASK a on a.PROCINST_ = d.INSTANCE_  
				 left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
				 left join WF_Process_Variable v on d.ID_= v.procInstId 
			     left join WF_ProcessInfo h on v.processTemplate = h.cGUID 
				 left join WF_Process pr on pr.cguid = h.cNodeTemplate 
				 left join JBPM4_HIST_PROCINST hp on a.PROCINST_ = hp.DBID_
				where a.STATE_ = 'open' 
				and (a.ASSIGNEE_ = {currUserGUID} or (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
				and a.DBID_ NOT IN (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				and pr.cForm ='finance_ca_apvoucher_edit_form'	
				and st.iAuditStatus!='revise'		
		      	and (  '*' = {keyword:'*'} 
		      	        or convert(varchar(50),hp.START_,112) like '%{keyword}%'
		                or $like(v.ownerName,keyword)
		                or ('其它付款单' like '%{keyword}%'))
		       @filter[aos_rms_user,CA_OtherPayment.cAcctGUID=gl_account.cGUID,CA_OtherPayment.cRBankAcctNub=cm_bankline.cguid]
		                
  
		</i>			
		<i id="caapvouchernum">	
  	          select 
		        count(1)
		        from CA_OtherPayment po 
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				left join JBPM4_TASK a on a.PROCINST_ = d.INSTANCE_  
				left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
				left join WF_Process_Variable v on d.ID_= v.procInstId 
				left join WF_ProcessInfo h on v.processTemplate = h.cGUID 
				left join WF_Process pr on pr.cguid = h.cNodeTemplate 
				where a.STATE_ = 'open' 
				and po.iAuditStatus!='revise'		
				and (a.ASSIGNEE_ = {currUserGUID} or (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
				and a.DBID_ NOT IN (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				and pr.cForm ='finance_ca_apvoucher_edit_form'	
  	         
		</i>
		<i id="caapvouchernumWorkFlow"   desp="与上述sql区别多了数据权限">	
  	          select 
		        count(1)
		        from CA_OtherPayment po 
				left join JBPM4_EXECUTION d on d.KEY_ = po.cguid
				left join JBPM4_TASK a on a.PROCINST_ = d.INSTANCE_  
				left join JBPM4_PARTICIPATION b on a.DBID_ = b.TASK_
				left join WF_Process_Variable v on d.ID_= v.procInstId 
				left join WF_ProcessInfo h on v.processTemplate = h.cGUID 
				left join WF_Process pr on pr.cguid = h.cNodeTemplate 
				where a.STATE_ = 'open' 
				and po.iAuditStatus!='revise'		
				and (a.ASSIGNEE_ = {currUserGUID} or (a.ASSIGNEE_ is null and (b.USERID_ = {currUserGUID} or #in(b.GROUPID_,deptRoleList))))
				and a.DBID_ NOT IN (SELECT htask_ from JBPM4_HIST_DETAIL where (USERID_= {currUserGUID} and NEW_STR_ ='delegate'))
				and pr.cForm ='finance_ca_apvoucher_edit_form'	
				@filter[aos_rms_user,CA_OtherPayment.cAcctGUID=gl_account.cGUID,CA_OtherPayment.cRBankAcctNub=cm_bankline.cguid]
				 
  	         
		</i>
	-->		
		
	</sql>
	
	<sql group="mobile_daiban_fibill">
  		<i id="appaymentmain" desp="付款单主表">
				select 
                    convert(nvarchar, cast(round( st.iPayAMT ,2) as money),1)  as "ap_payment.ipayamt"
		        from AP_Payment st
                where st.cguid={cGUID}
		</i>
		
		<i id="appaymentdetail" desp="付款单子表">
                select 
                     sol.cguid,
				     convert(nvarchar, cast(round(sol.iAMT ,2) as money),1)  as "ap_paymentdetail.iamt"
				from
				AP_PaymentDetail sol
				where sol.cPaymentGUID =?
		</i>
	
		<i id="caapvouchermain" desp="其它付款单主表">
		   
            	select                           
             	  convert(nvarchar, cast(round( st.iPayAMT ,2) as money),1)  as "ca_otherpayment.ipayamt"
		        from CA_OtherPayment st	
                where st.cguid={cGUID}
		</i>
		
		<i id="caapvoucherdetail" desp="其它付款单子表">
		      
                 select sol.cguid,
                 convert(nvarchar, cast(round(sol.iPayAMT ,2) as money),1)  as "ca_otherpaymentline.ipayamt"
				from    CA_OtherPaymentLine sol
				where sol.cHeadGUID =?
		</i>

		
	    <i id="isCaapvoucherExist"  desp="查看该用户是否有出纳其它付款单的权限">
		   select cNAME from AOS_RMS_PERM ap
           where cguid in (
                          select s.cPermID from AOS_User_Perm s
                           where s.cUserID=?)
           and ap.cMODULE='117110187852602325'
           and  (ap.cNAME='其它付款单' or  ap.cNAME='其它付款单列表')
		
		 </i>
		
	</sql>
	
</sqls>