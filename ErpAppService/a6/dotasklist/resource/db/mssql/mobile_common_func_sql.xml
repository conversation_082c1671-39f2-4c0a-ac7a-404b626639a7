<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_common_func_sql">
		<i id="getcurqty" desp="获取物品库存单位现存量">
			<![CDATA[
				SELECT  a.cGUID AS cmatguid,
						a.cMatCode AS cmatcode,
						a.cMatName AS cmatname,
						isnull(a.cSpec, '') AS cspec,					
						(case when isnull(a.iPrecision,0)=0 then left(cast(isnull(cur.iCurQuan,0) as varchar(21)),charindex('.',cast(isnull(cur.iCurQuan,0) as varchar(21)))-1)
					       else left(cast(isnull(cur.iCurQuan,0) as varchar(21)) , charindex('.',cast(isnull(cur.iCurQuan,0) as varchar(21))) +a.iPrecision)
					       end)+ u1.cName as iStoreCurUnitQty,
					   (case when isnull(a.iPrecision,0)=0 then left(cast(isnull(cur.iCurUnitQuan,0) as varchar(21)),charindex('.',cast(isnull(cur.iCurUnitQuan,0) as varchar(21)))-1)
					       else left(cast(isnull(cur.iCurUnitQuan,0) as varchar(21)) , charindex('.',cast(isnull(cur.iCurUnitQuan,0) as varchar(21))) +a.iPrecision)
					       end)+ u2.cName as iStoreCurMUnitQty
					FROM
					    cm_material a
					inner JOIN CM_MatClass cmmc ON cmmc.cGUID = a.cMatCGUID   
					LEFT JOIN (
						SELECT
							SUM (c.iCurQty) AS iCurQuan,
							sum(c.iCurUnitQty) AS iCurUnitQuan,
							cMatGUID,
							cStkUnitGUID,
							cMUnitGUID
						FROM
							ST_CurrentStock c
					  where $equal(c.cstoreguid,cstoreguid) 
					  		and $equal(c.cbatchguid,cbatchguid) 
					  		and $equal(c.cpositionguid,cpositionguid) 
					  		and $equal(c.doverdate,doverdate) 
					  		and $equal(c.dproduction,dproduction) 
					  		and $equal(c.iguaranteedays,iguaranteedays) 	
					  		and exists(select 1 from CM_Storehouse a 								
								where a.cGUID = c.cstoreguid)				  		
						GROUP BY
							cMatGUID,
							cStkUnitGUID,
							cMUnitGUID						
					) cur ON cur.cMatGUID = a.cGUID
					LEFT JOIN CM_Unit u1 ON cur.cStkUnitGUID = u1.cGUID	
					LEFT JOIN CM_Unit u2 ON cur.cMUnitGUID = u2.cGUID	
					where $equal(a.cguid,cmatguid)		
					@filter[
					ST_CurrentStock.cStoreGUID=CM_Storehouse.cguid,
					CM_Material,
					CM_MatClass]
			]]>
		</i>
		
			
		<i id="getscanmat" desp="扫码获取物品">
			<![CDATA[
					select  m.*,
							cs.irateflag,
							(select top 1 1 from meta_keyattr where cMatGuid = m.cguid) as iFreeFlag 
					from CM_Material m 
					inner JOIN CM_MatClass cmmc ON cmmc.cGUID = m.cMatCGUID
					LEFT JOIN CM_Unit u on u.cGUID = m.cUnitGUID 
					left join CM_UnitClass cs on cs.cGUID=u.cClassGUID 
					where $equal(m.cBarCode,cbarcode) 
					@filter[
						CM_Material,
						CM_MatClass]
			]]>
		</i>
	</sql>

</sqls>