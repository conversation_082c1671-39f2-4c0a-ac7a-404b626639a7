<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_bill_em" desp="借款报销单据SQL">
		<i id="getAllYwlx" desp="获取所有业务类型">
			<![CDATA[
				select cGUID code,cName name 
				from OA_ExpenseGroup 
				where cClassTypeGUID='i031'
				and iStatus='1'
				order by cCode
			]]>
		</i>
		<i id="getAllFylb" desp="获取业务类型下所有费用类别">
			<![CDATA[
				select cGUID code,cName name 
				from OA_ExpenseClass
				where cGroupTypeGUID=?
				and iStatus='1'
				order by cCode
			]]>
		</i>
		
		<i id="getAllEmpDeptList" desp="获取所有职员和部门信息">
			<![CDATA[
				SELECT emp.cGUID as empId, emp.cName as empName,dep.cGUID as depId, dep.cName as depName
				FROM CM_Employee emp
				LEFT JOIN CM_Department dep on emp.cDeptGUID = dep.cGUID
				WHERE emp.iStatus='1'
				AND	emp.cName like '%{queryName}%'  
			]]>	
		</i>
		
		<i id="getAllProjectList" desp="获取所有项目列表">
			<![CDATA[
				SELECT cp.cGUID as xmId, cp.cName as xmName
				FROM CM_Project cp
				WHERE cp.iStatus='1' 
				AND cp.iLeaf='1'
				AND cp.cName like '%{queryName}%'  
			]]>
		</i>
		
		<i id="getCommonProjectList" desp="获取常用项目列表">
			<![CDATA[
				SELECT cp.cGUID as xmId, cp.cName as xmName
				FROM CM_Project cp
				LEFT JOIN (
					SELECT eab.cAccItemTypeGUID,eab.cExpUserGUID,COUNT(eab.cAccItemTypeGUID) as pl
					FROM OA_ExpenseAccountBill eab
					WHERE $like(eab.cRefundDate,year)
					GROUP BY eab.cAccItemTypeGUID,eab.cExpUserGUID
					HAVING COUNT(eab.cAccItemTypeGUID)>0
				)eab on eab.cAccItemTypeGUID = cp.cGUID
				WHERE cp.iStatus='1' 
				AND cp.iLeaf='1'
				AND #equal(eab.cExpUserGUID,currEmpId)
				ORDER BY eab.pl DESC
			]]>
		</i>
		
		<i id="checkInv" desp="校验发票是否已报销过">
			<![CDATA[
				SELECT TOP 1 1
				FROM OA_ExpenseAccInvInfo
				WHERE FP_DM=? AND FP_HM = ? AND cHeadGUID != ?
			]]>
		</i>
		
		<i id="getEMBILLSEQUENCESQL" desp="获取报销单单据编号规则">
			<![CDATA[
				SELECT cguid
				FROM Bill_Sequence bs
				WHERE
					(
						(
							bs.cAdminOrgnId = ''
							AND bs.cOrgnId = ''
						)
						OR (
							bs.cAdminOrgnId IS NULL
							AND bs.cOrgnId IS NULL
						)
						OR (
							bs.cAdminOrgnId = ?
							AND bs.cOrgnId = ?
						)
						OR (
							bs.cAdminOrgnId = ?
							AND bs.cOrgnId = ?
						)
					)
				AND bs.cCode = (
					SELECT s.cCode
					FROM BILL_BILLS b
					JOIN BILL_SEQUENCE s ON b.cSequenceId = s.cGuid
					WHERE b.cFormId = 'oa_fm_expenseAccountBill_edit_form'
				)
				ORDER BY bs.cguid
			]]>
		</i>
		
		<!-- 2017-09-01 quchj 修正报销金额取值 -->
		<i id="getExpenseAccountMain" desp="获取报销单主表数据">
			<![CDATA[
				SELECT eab.cGUID cguid,eab.cCode ccode,eab.cGroupTypeGUID ywlxId,eg.cName ywlxName,eab.cReason bxyy,
					eab.cExpUserGUID bxrId,em.cName bxrName,eab.cExpDeptGUID bxrbmId,de.cName bxrbmName,
					CONVERT(nvarchar,cast(round(eab.iTotalAMT, 2) as numeric(20,2))) as bxje,eab.cStatusEnumGUID zt,
					CONVERT(varchar(100),eab.cRefundDate,23) bxrq,eab.cAccItemTypeGUID xmId,pj.cName xmName,
					eab.cTimeStamp sjc
				FROM OA_ExpenseAccountBill eab
				LEFT JOIN OA_ExpenseGroup eg on eg.cGUID = eab.cGroupTypeGUID
				LEFT JOIN CM_Employee em on em.cGUID = eab.cExpUserGUID
				LEFT JOIN CM_Department de on de.cGUID = eab.cExpDeptGUID
				LEFT JOIN CM_Project pj on pj.cGUID = eab.cAccItemTypeGUID
				WHERE eab.cGUID = ?
			]]>
		</i>
		<!-- 2017-09-01 quchj 修正报销金额取值 -->
		<i id="getExpenseAccountBxmx" desp="获取报销单报销明细数据">
			<![CDATA[
				SELECT eal.cGUID cguid,eal.cExpenseClassGUID fylbId,ec.cName fylbName,
					CONVERT(nvarchar, cast(round(eal.iOpeningAMT, 2) as numeric(20,2))) as bxje,
					CONVERT(varchar(100),eal.cStartDate,23) ksrq,CONVERT(varchar(100),eal.cOverDate,23) jsrq,
					eal.iDays ts,eal.cStarting sfd,eal.cFinishing mdd,eal.cRemark bz
				FROM OA_ExpenseAccLine eal
				LEFT JOIN OA_ExpenseClass ec on ec.cGUID = eal.cExpenseClassGUID
				WHERE eal.cHeadGUID = ?
			]]>
		</i>
		<!-- 2017-09-01 quchj 修正发票金额取值 -->
		<i id="getExpenseAccountFpmx" desp="获取报销单发票明细数据">
			<![CDATA[
				SELECT eai.cguid cguid,eai.FP_DM fpdm,eai.FP_HM fphm,
					CONVERT(nvarchar, cast(round(eai.iAMT, 2) as numeric(20,2))) as fpje,eai.cremark bz
				FROM OA_ExpenseAccInvInfo eai
				WHERE eai.cHeadGuid = ?
			]]>
		</i>
		
		<!-- 2017-09-03 quchj 增加获取报销单附件SQL -->
		<i id="getExpenseAccountFile" desp="获取报销单附件">
			<![CDATA[
				SELECT cGuid,cFileName,cFileUrl
				FROM AOS_FILE_FILES
				WHERE cGroupGuid=?
			]]>
		</i>
		
		<i id="checkEditState" desp="校验报销单时间戳和状态">
			<![CDATA[
				SELECT cTimeStamp sjc,cStatusEnumGUID zt
				FROM OA_ExpenseAccountBill
				WHERE cGUID = ?
			]]>
		</i>
		
		<i id="getEMbillState" desp="获取报销单单据状态">
			<![CDATA[
				SELECT cguid,cStatusEnumGUID zt
				FROM OA_ExpenseAccountBill
				WHERE cGUID = ?
			]]>
		</i>
	</sql>
</sqls>