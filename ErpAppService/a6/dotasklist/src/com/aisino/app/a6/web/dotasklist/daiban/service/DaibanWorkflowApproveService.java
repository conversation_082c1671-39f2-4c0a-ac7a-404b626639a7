package com.aisino.app.a6.web.dotasklist.daiban.service;

import java.util.ArrayList;
import java.util.List;

import com.aisino.app.a6.web.dotasklist.workflow.DotaskBillSubmitPlugin;
import com.aisino.platform.core.MS;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.view.DataMsgBus;

/**
 * 工作流审批详情
 * <AUTHOR>
 * @date 2016年12月16日
 *
 */
public class DaibanWorkflowApproveService implements IService{

	@Override
	public Object doService(Object... param) {
		// TODO Auto-generated method stub
		/* 2016-12-23 qcj 修正工作流审批参数传递为bus */
		DataMsgBus bus = (DataMsgBus)param[0];
		String cguid = bus.getString("cguid");
		String action = bus.getString("action");
		
		//String action = (String) param[0];  //处理方式，值为 agree：同意；backprev：返回上一步；backorigin：返回发起人；terminate:终止
		//String cbilltype=(String) param[1];    //单据cguid
		//String cguid =(String) param[2]; //单据类型：为010；076等等
		//String idea =(String) param[3]; //审批意见
		
		if (cguid == null || cguid.equals("")) {
			throw new BusinessException("找不到该单据,请刷新重试.");
		}else{
			//DataMsgBus bus = new DataMsgBus();
			//bus.send("cguid", cguid);
			//bus.send("action", action);
			//bus.send("cbilltype", cbilltype);
			//bus.send("idea", idea);
			if("commit".equals(action)){ //指定下一审批人提交
				/* 2017-01-20 qcj 增加对流程走向的判断校验 */
				String selected =bus.getString("selected")==null?"":bus.getString("selected"); //下一审批节点
				String preAssignedStr =bus.getString("preAssigned")==null?"":bus.getString("preAssigned"); //下一审核人
				String preAssigWay =bus.getString("preAssigWay")==null?"":bus.getString("preAssigWay"); //下一审核方式
				List preAssigned = new ArrayList();
				if("".equals(selected)){
					throw new BusinessException("请选择流程走向.");
				}else if(!selected.contains("结束")){
					/* 2017-03-15 qcj 增加审批时对结束分支的特殊处理 */
					if("".equals(preAssignedStr)){
						throw new BusinessException("请选择办理人.");
					}else if("".equals(preAssigWay)){
						throw new BusinessException("请选择指派办理方式.");
					}else{
						String preAssignedSt[] = preAssignedStr.split(",");
						for(int i=0;i<preAssignedSt.length; i++){
							preAssigned.add(preAssignedSt[i]);
						}
						bus.send("preAssigned", preAssigned);
					}
				}else{
					bus.send("preAssigned", preAssigned);
				}
				
				return new DotaskBillSubmitPlugin().doSubmit(bus);
			}else if("agree".equals(action)){
				/* 2017-01-10 qcj 增加修正工作流审批前校验服务 */
				String cbilltype=bus.getString("cbilltype");
				String firstsubmit=bus.getString("firstsubmit");
				MS m = new MS("App.Daiban"+cbilltype);  
				String s = (String)m.doService("beforeWFAgree",cguid,firstsubmit);	
				if(s != null && !"".equals(s)){
					return s;
				}else{
					return new DotaskBillSubmitPlugin().doSubmit(bus);
				}
			}else{
				return new DotaskBillSubmitPlugin().doSubmit(bus);
			}
		}
		
		//return null;
	}

}
