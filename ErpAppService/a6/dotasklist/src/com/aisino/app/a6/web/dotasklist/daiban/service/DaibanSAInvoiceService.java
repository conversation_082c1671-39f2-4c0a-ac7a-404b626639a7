
package com.aisino.app.a6.web.dotasklist.daiban.service;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.aisino.aos.preference.common.Prefer;
import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.dotasklist.common.plugin.BillDesignPlugin;
import com.aisino.app.web.dotasklist.daiban.plugin.DaibanUtil;
import com.aisino.app.web.notice.InsertCheckNoticePlugin;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.MS;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.JsUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;

public  class DaibanSAInvoiceService  implements IService{
	

	@Override
	public Object doService(Object... param) {	
		String action = (String) param[0];
		String cguid=(String) param[1];
		DbSvr db=DbSvr.getDbService(null);	
		String firstSubmit="true";
		if("browse".equals(action)){
			String cBusinessCode ="085";  
			Map m= BillDesignPlugin.getBill(cBusinessCode,cguid);   //调用接口传该单据的formid
			List<Map> main=(List<Map>) m.get("maindata");
			List<Map> detail=(List<Map>) m.get("detaildata");  //根据单据设计，获取将要显示的字段及对应的值
			
			List<Map> mainTemp =db.queryIdForList("mobile_daiban_sabill.sainvoicemain", cguid);
			List<Map> detailTemp =db.queryIdForList("mobile_daiban_sabill.sainvoicedetail",cguid);
			
			
			
		    /*2017年9月1日 zrc  处理销售发票的单价显示精度问题*/
			for(Map mm:detailTemp){
				if(!CollectionUtil.isBlankMap(mm)){
					int U3_ST_InvoicePrecision=Integer.valueOf((String) mm.get("U3_ST_InvoicePrecision"));
					String sa_invoiceline_itaxprice=(String) mm.get("sa_invoiceline.itaxprice");
					String iTaxPrice=String.valueOf(mm.get("iTaxPrice"));
					if(sa_invoiceline_itaxprice.contains(".")&&!"0E-9".equals(iTaxPrice)){
						String be=sa_invoiceline_itaxprice.split("\\.")[0];
						String af=sa_invoiceline_itaxprice.split("\\.")[1];
						String mk=iTaxPrice.split("\\.")[1];
						if(U3_ST_InvoicePrecision!=af.length()){
							sa_invoiceline_itaxprice=be+"."+mk.substring(0,U3_ST_InvoicePrecision);
							mm.put("sa_invoiceline.itaxprice", sa_invoiceline_itaxprice);
						}
					}
				}
				
			}
			
			Map map = new HashMap();
			boolean notHas=db.queryIdHasResult("mobile_common.checkIamtSAinvoice",SessionHelper.getCurrentUserId());
			//主表（将单据设置显示不出来的字段加到单据设计的结果集中,没有查看金额权限的，金额置空）
			if( main!=null&&!main.isEmpty()){
				Map jem = new HashMap();
				if(!notHas){									
					String je =(String) mainTemp.get(0).get("发票总金额");
					jem.put("name","发票总金额");
					jem.put("value",je);
					jem.put("code","sa_invoice.je");
					main.add(jem);					
				}
				map.put("main", main);	
			}
			
			//子表
			/*注意事项：
			 * a：没有特殊要处理的字段，不调用detailProcess该函数即可
			 * b: detailProcess第一个参数是单据设计结果集；第二个参数是1.0版本的sql部分
			 */
			
			if(detail!=null&&!detail.isEmpty()){
				detail=BillDesignPlugin.detailProcess(detail,detailTemp);//子表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("detail", detail);
			}
			if(main!=null&&!main.isEmpty()){
				main=BillDesignPlugin.mainProcess(main,mainTemp);//主表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("main", main);
			}
			
			//附件
			List<Map> files =db.queryIdForList("mobile_common.getFiles",cguid);
			if(CollectionUtil.isEmpty(main)||CollectionUtil.isEmpty(detail))
				 throw new BusinessException("并发删除,请重新查询");
			//附件权限
			String fileKey = "business_sa_sainvoice.viewfile";
			boolean hasfiledown= SecurityService.rescCheck(fileKey); 			
			if(!hasfiledown&&CollectionUtil.isNotEmpty(files)){
				Map m2=new HashMap();
				//此时app不允许下载附件
				m2.put("hasfiledown", "false");
				map.putAll(m2);
			}		
			if(files!=null&&!files.isEmpty())
		           map.put("file", files);
			return map;
		}else if("submit".equals(action)){					
			if((String) param[2]!=null)
				firstSubmit=(String) param[2];
			DataMsgBus nbus = new DataMsgBus();  
		    List<Map> list=new  ArrayList<Map>();
		    Map m=db.getOneRecorder("select * from SA_Invoice so where so.cGUID =?",cguid);
		    if (CollectionUtil.isBlankMap(m))
				 throw new BusinessException("单据不存在，可能已被并发删除");
		    list=db.getListResult("select * from SA_InvoiceLine sao where cheadguid=?",cguid);
		    nbus.putAll(m);
	    	nbus.send("list", list);	
	    	nbus.send("billcguid",cguid);	
	    	String msg=null;
	    	String ms=null;
	    	 if(firstSubmit.equals("true")){
	    		    boolean f=false;
		        	ms=Check("business_sa_sainvoice",nbus,"disauth_sa_check");
		        	if(StringUtil.isNotBlank(ms)&&ms.length()>3){
		        		nbus.put("flagStr", ms);
		        		f=true;
		        	}
		        	msg=Check("business_sa_sainvoice",nbus,"beforecheck_sa");
		        	if(msg!=null&&!msg.equals("{}")){
		        		return msg;
		        	}else{
		        		if(f)
		        		return JsUtil.toJsonObjectWithNull(getZheKouCheck(ms)).toString();
		        	}
		        }
	    	msg=Check("business_sa_sainvoice",nbus,"docheck");
  		    DaibanUtil.updateCheckWay("SA_Invoice",cguid);
	    	return msg;
		}else  if("beforeWFAgree".equals(action)){//处理工作流审批的beforecheck
			if((String) param[2]!=null)
				firstSubmit=(String) param[2];
			DataMsgBus nbus = new DataMsgBus();  
		    List<Map> list=new  ArrayList<Map>();
		    Map m=db.getOneRecorder("select * from SA_Invoice so where so.cGUID =?",cguid);
		    if (CollectionUtil.isBlankMap(m))
				 throw new BusinessException("单据不存在，可能已被并发删除");
		    list=db.getListResult("select * from SA_InvoiceLine sao where cheadguid=?",cguid);
		    nbus.putAll(m);
	    	nbus.send("list", list);	
	    	nbus.send("billcguid",cguid);
	    	nbus.send("isworkflow",true);
	    	String msg=null;
	    	String ms=null;
	        if(firstSubmit.equals("true")){
	        	 boolean f=false;
		        	ms=Check("business_sa_sainvoice",nbus,"disauth_sa_check");
		        	if(StringUtil.isNotBlank(ms)&&ms.length()>3){
		        		nbus.put("flagStr", ms);
		        		f=true;
		        	}
		        	msg=Check("business_sa_sainvoice",nbus,"beforecheck_sa");
		        	if("{}".equals(msg)){
		        		return null;
		        	}
		        	if(msg!=null){
		        		return msg;
		        	}else{
		        		if(f)
		        		return JsUtil.toJsonObjectWithNull(getZheKouCheck(ms)).toString();
		        	}
	        }
	    	return msg;
		}else {
			return null;
		}
	}
	
		public String Check(String fromid,DataMsgBus bus,String action){
			Map msg=new HashMap();
			String cbillcode=bus.getString("cinvcode");
			String cCreatorGUID=bus.getString("cCreatorGUID");
			String nextflagStr=bus.getString("flagStr");
			Object objMsg=null;
			String confirmmsg=null;
	    	try {
	    		 bus.setNewAction(action);
	    		 //返回信息：销售单据的beforeCheck校验：包括最低售价，最高售价，销售折扣，控制信用等。
	    		 /**此处跳出权限校验。否则会抛异常“没有[销售发票]功能的权限，不能进行操作”
	     		 * 因为PC端的工作流审核不会考虑单据以及审核权限之类的**/
	 	    	if(bus.getBoolean("isworkflow", false)){
	 	    		Set set = new HashSet();
	 	 		    set.add("acs");
	 	 		    set.add("pt_sql_filter");
	 	 		    MS m2 = new MS("ACS.SessService");
	 	 		    m2.doService(new Object[] { "putRescInSession", fromid });
	 	    	}
	    		 objMsg=AbstractForm.callFormSubmitService(fromid,bus,false);
					if(objMsg instanceof Map) {
						Map map=(Map) objMsg;
						//当有这些异常时候才返回
						if(map.containsKey("msglow")||map.containsKey("msghigh")||map.containsKey("msgicostprice")||map.containsKey("msgcust")){
							map.put("nextflagStr", nextflagStr);
							Map m=getReturnBeforeCheck(map);
							String savemsg=null;
							if(StringUtil.isNotBlank((String) m.get("errmsg"))){
								savemsg=m.get("errmsg").toString();
							}
							//存储审批失败信息到PT_Notice
							if(savemsg!=null)
							new  InsertCheckNoticePlugin().InsertCheckNotice(cCreatorGUID,"单据号"+cbillcode+"："+savemsg);
							msg.putAll(m);
						}else  if(map.containsKey("controltype")&&map.containsKey("msg")){
							/*销售折扣单独控制 2017年3月6日 19:17:38 */
							String flagStr="";
							if(StringUtil.isNotBlank((String) map.get("msg"))){
								flagStr =getdisauth_sa_check(map);
								new  InsertCheckNoticePlugin().InsertCheckNotice(cCreatorGUID,"单据号"+cbillcode+"："+"物品倒扣率超出限制");
							}
							return flagStr;
						}else{
							return null;
						}
					}
			} catch (Exception e) {
				DbSvr db=DbSvr.getDbService(null);	
				//回滚审核状态等等
				db.rollback();
				//存储审批失败信息到PT_Notice
				new  InsertCheckNoticePlugin().InsertCheckNotice(cCreatorGUID,e.getMessage());
				if(action.equals("docheck")){
					String emsg=getExceptiondoCheck(e.getMessage(),cbillcode);
					throw new  BusinessException(emsg);
					//throw new  BusinessException(JsUtil.toJsonObjectWithNull("单据号"+cbillcode+"：存在物品超出可用量，审核失败！"));
				}
				else
					throw new  BusinessException("单据号"+cbillcode+"："+e.getMessage());
					
			}
				return  JsUtil.toJsonObjectWithNull(msg).toString();
	    	   
		}
		
		public String  getdisauth_sa_check(Map m){
			
			Map ma=new HashMap(); 
			String flagStr=null;
			
			if("3".equals(m.get("controltype")))
				flagStr="yange";
			if("1".equals(m.get("controltype")))
				flagStr="tishi";
			if("2".equals(m.get("controltype")))
				flagStr="password";
			
			return flagStr;
		
		}
		
		//单独处理折扣率
		public Map getZheKouCheck(String controlFlag){
			Map ma=new HashMap();
		    boolean isStrict=false;
			if("yange".equals(controlFlag)){
				isStrict=true;
			}
			//严格控制
			if(isStrict){
				ma.put("errmsg","审核失败：物品倒扣率超出限制");
				return ma;
			}
			else{
				//提示控制
				boolean confirm=false;
				//弹出授权密码
				boolean passWordConfirm=false;   
				 if("password".equals(controlFlag))
					passWordConfirm=true;
				else{
					confirm=true;
				}
				ma.put("msg", "物品倒扣率超出限制"+"    要继续审核吗？");
				if(confirm){
					ma.put("confirm", true);
					return ma;
				}else if(passWordConfirm){
					ma.put("passWordConfirm", true);
					ma.put("password", Prefer.get("U3_SA_SuperRight"));
					return ma;
				}else{
					return ma;
				}
			}
			
		}
		public Map getReturnBeforeCheck(Map map){
			String nextflagStr=map.get("nextflagStr")==null?"":map.get("nextflagStr").toString();
			String msg="";
			if(StringUtil.isNotBlank(nextflagStr)){
				msg="物品倒扣率超出限制;";
			}
			Map ma=new HashMap(); 
			boolean isStrict=false;
			if(map.get("msglow")!=null){
				msg="物品销售价低于最低售价;";
				if(map.get("isStrictlow").equals("3"))
					isStrict=true;
			}
			if(map.get("msghigh")!=null){
				msg=msg+"物品销售价超出最高售价;";
				if(map.get("isStricthigh").equals("3"))
					isStrict=true;
			}
			if(map.get("msgicostprice")!=null){
				msg=msg+"物品无税价低于成本价;";
				if(map.get("isStricticostprice").equals("3"))
				    isStrict=true;
			}
			if(map.get("msgcust")!=null){
				String msgcust=map.get("msgcust").toString();
				if(msgcust.contains("逾期")&&msgcust.contains("信用额度")){
					msg=msg+"客户信用余额不足且超信用期限;";
				}else if(msgcust.contains("逾期")){
					msg=msg+"客户超信用期限;";
				}else if(msgcust.contains("信用额度")){
					msg=msg+"客户信用余额不足;";
				}
				if(map.get("isStrictcust").equals("3"))
				    isStrict=true;
			}
			if(msg.charAt(msg.length()-1)==';')
				msg=msg.substring(0, msg.length()-1);
			if("yange".equals(nextflagStr)){
				isStrict=true;
			}
				
			//严格控制
			if(isStrict){
				ma.put("errmsg","审核失败："+msg);
				return ma;
			}
			else{
				//提示控制
				boolean confirm=false;
				//弹出授权密码
				boolean passWordConfirm=false;   
				if(map.get("isStrictlow")!=null&&map.get("isStrictlow").equals("2")&&map.get("msglow")!=null){
					passWordConfirm=true;
				}else if(map.get("isStricthigh")!=null&&map.get("isStricthigh").equals("2")&&map.get("msghigh")!=null){
					passWordConfirm=true;
				}else if(map.get("isStricticostprice")!=null&&map.get("isStricticostprice").equals("2")&&map.get("msgicostprice")!=null){
					passWordConfirm=true;
				}else if(map.get("isStrictcust")!=null&&map.get("isStrictcust").equals("2")&&map.get("msgcust")!=null){
					passWordConfirm=true;
				}else if("password".equals(nextflagStr))
					passWordConfirm=true;
				else{
					confirm=true;
				}
				ma.put("msg", msg+"    要继续审核吗？");
				if(confirm){
					ma.put("confirm", true);
					return ma;
				}else if(passWordConfirm){
					ma.put("passWordConfirm", true);
					ma.put("password", Prefer.get("U3_SA_SuperRight"));
					return ma;
				}else{
					return ma;
				}
			}
			
		}
		
		
		
		
		
		
		
		
		//对docheck的异常信息的处理。手机端要求提示信息简洁，不需要像web端那么具体详细。
		public String getExceptiondoCheck(String errmsg,String cbillcode){
			
			String msg[]=errmsg.split("。");
			if(msg.length==1){
				return "单据号"+cbillcode+"："+errmsg;
			}else{
				return "单据号"+cbillcode+"：物品超出可用量";
			}
		}
		
     
}