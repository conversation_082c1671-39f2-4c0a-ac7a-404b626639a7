package com.aisino.app.a6.web.dotasklist.common.plugin;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import com.aisino.a6.business.sa.util.plugin.DoChangeCustGetPricePlugin;
import com.aisino.aos.preference.common.Prefer;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.MathUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

/**
 * 获取价格相关
 * @since 2018-09-12
 */
public class GetPricePlugin implements FormCreateListener{
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		// TODO Auto-generated method stub
		Map map= new HashMap();
		BigDecimal iprice= this.getprice(form, bus);
		map.put("getprice", iprice);
		form.setReturn(map);
	
	}	
	
	
	public BigDecimal getprice(AbstractForm form, DataMsgBus bus){
		
		DbSvr db= DbSvr.getDbService(null);
		BigDecimal iprice=new BigDecimal(0);  //这里都指含税单价
		String cCustGUID =bus.getString("ccustguid");
		String billname= bus.getString("billname");
	
        if("saretail".equalsIgnoreCase(billname) || "saorder".equalsIgnoreCase(billname)  ){
        	bus.put("defultunit", "SA");//有SA、PU、ST;
        	
        	//参照单行取价，往里塞变量
        	bus.send("iCurRate2", 1);        	
        	Map row = new HashMap();
        	String cMatGUID= bus.getString("cMatGUID")==null?"":bus.getString("cMatGUID").toString();
    		Map mat=db.queryIdFirstRow("a6_grid_materaltemplate.all_Material_refer",bus);
        	row.put("cMatGUID", cMatGUID);
        	row.put("itaxrate",mat.get("iPerTaxRate"));
        	
        	if("saretail".equalsIgnoreCase(billname)){
        		bus.send("formid", "business_sa_retail_form");
        		row.put("cSaRefePriceUnitGUID", mat.get("cMUnitGUID")); //零售单暂不支持辅计量
        	}
        	if("saorder".equalsIgnoreCase(billname)){
        		bus.send("formid", "business_sa_saorder");
        		row.put("cSaRefePriceUnitGUID", mat.get("cSaRefePriceUnitGUID")); //直接取销售计价单位的单价
        	}
        	
            Map result = new HashMap();          
            result = this.getquotedprice(row, cCustGUID, "", db, form, bus);
            String ipricetype =Prefer.get("U3_SA_PriceType");//价格是否含税 taxprice/price/空
            if("price".equalsIgnoreCase(ipricetype)){
            	iprice = (BigDecimal) result.get("iunitprice_f");          	
            }else{ //为空按含税单价为准
            	iprice = (BigDecimal) result.get("itaxprice_f");
            }
            //如果取不到报价,置为0
            if(iprice==null){
            	iprice = new BigDecimal(0);
            }
            
        }
		
		return iprice;
		
	}
	
	
	
	  public Map getquotedprice(Map row, String cCustGUID, String dDate, DbSvr db, AbstractForm form, DataMsgBus bus)
	    {
	        String formtype = "0";
	        if("business_sa_retail_form".equalsIgnoreCase(bus.getString("formid")))
	            formtype = "1";
	        String prefvalue = "2";
	        String itaxrate_s = CollectionUtil.getStringFromMap(row, "itaxrate");
	        if(itaxrate_s == null || itaxrate_s == "")
	            itaxrate_s = CollectionUtil.getStringFromMap(row, "ipertaxrate");
	        BigDecimal itaxrate = MathUtil.toBigDecimal(itaxrate_s);
	        String formid = form.getId();
	        if(formid.equals("business_sa_sainvoice") || formid.equals("bus_ar_sainitinvoice") || formid.equals("business_sa_carriageinvoice_form"))
	            prefvalue = Prefer.get("U3_ST_InvoicePrecision");
	        else
	            prefvalue = Prefer.get("U3_ST_PricePrecision");
	        Map cpriceinfo = new HashMap();
	        String iIsGift = CollectionUtil.getStringFromMap(row, "iIsGift");
	        if(iIsGift == null || iIsGift == "" || "0".equals(iIsGift))
	        {
	            String cMatGUID = CollectionUtil.getStringFromMap(row, "cMatGUID");
	            String cCUnitGUID = CollectionUtil.getStringFromMap(row, "cSaRefePriceUnitGUID");
	            String iChangeRate = db.getStringResult("SELECT iChangeRate  FROM   CM_MatUnitRate WHERE  CM_MatUnitRate.cMatGUID = ? AND CM_MatUnitRate.cUnitGUID = ?", new Object[] {
	                cMatGUID, cCUnitGUID
	            });
	            String methodchange = bus.getString("methodchange");
	            if(methodchange != null && methodchange != "")
	            {
	                String csarefepricemethod = CollectionUtil.getStringFromMap(row, "csarefepricemethod");
	                if("f".equals(csarefepricemethod))
	                    iChangeRate = CollectionUtil.getStringFromMap(row, "iChangeRate");
	                else
	                    iChangeRate = "1";
	            }
	            DataMsgBus rowBus = new DataMsgBus();
	            rowBus.send("cMatGUID", cMatGUID);
	            rowBus.send("cCUnitGUID", cCUnitGUID);
	            rowBus.send("cCustGUID", cCustGUID);
	            rowBus.send("dDate", dDate);
	            rowBus.send("iTaxRate", itaxrate_s);
	            rowBus.send("iChangeRate", iChangeRate);
	            rowBus.send("formtype", formtype);
	            DoChangeCustGetPricePlugin d = new DoChangeCustGetPricePlugin();
	            String SalcFree = d.getSalFreeInfo(cMatGUID, db);
	            int flag = 0;
	            if(!"".equals(SalcFree))
	            {
	                String SalcFrees[] = SalcFree.split(",");
	                for(int j = 0; j < SalcFrees.length; j++)
	                    if(row.get(SalcFrees[j]) == null)
	                        flag += 0;
	                    else
	                        flag++;

	                if(flag == SalcFrees.length)
	                {
	                    for(int j = 0; j < SalcFrees.length; j++)
	                        rowBus.send(SalcFrees[j], row.get(SalcFrees[j]).toString());

	                }
	            }
	            Map quoteHL = db.queryIdFirstRow("business_sa_util.gethighlowprice", rowBus);
	            String iLowPrice = quoteHL.get("iLowPrice") != null ? String.valueOf(quoteHL.get("iLowPrice")) : null;
	            String iHighPrice = quoteHL.get("iHighPrice") != null ? String.valueOf(quoteHL.get("iHighPrice")) : null;
	            if(StringUtil.isNotBlank(iLowPrice))
	                cpriceinfo.put("ilowprice", (new BigDecimal(iLowPrice)).setScale(Integer.parseInt(prefvalue), 4).toString());
	            else
	                cpriceinfo.remove("ilowprice");
	            if(StringUtil.isNotBlank(iHighPrice))
	                cpriceinfo.put("ihighprice", (new BigDecimal(iHighPrice)).setScale(Integer.parseInt(prefvalue), 4).toString());
	            else
	                cpriceinfo.remove("ihighprice");
	            Map quote = db.queryIdFirstRow("business_sa_util.SA_Material_refer", rowBus);
	            String U3_SA_QuotedPriceTax = Prefer.get("U3_SA_QuotedPriceTax");
	            form.storeIntoPageBus("U3_SA_QuotedPriceTax", U3_SA_QuotedPriceTax);
	            String getSalPriceWay = Prefer.get("U3_SA_GetSalPriceWay");
	            form.storeIntoPageBus("U3_SA_GetSalPriceWay", getSalPriceWay);
	            if(quote.get("iSalPrice") != null && quote.get("iSalPrice") != "")
	            {
	                String SalPrice = quote.get("iSalPrice").toString();
	                BigDecimal iSalPrice = new BigDecimal(SalPrice);
	                BigDecimal iCurRate2 = new BigDecimal(bus.getString("iCurRate2"));
	                if(formtype.equals("1"))
	                    getSalPriceWay = Prefer.get("U3_SA_GetRetailPriceWay");
	                BigDecimal hundred = new BigDecimal(100);
	                if("n".equals(U3_SA_QuotedPriceTax))
	                {
	                    BigDecimal iTaxQuotedPrice = itaxrate.add(hundred).divide(hundred, 2, RoundingMode.HALF_UP).multiply(iSalPrice);
	                    BigDecimal iQuotedPrice = iSalPrice;
	                    if(!getSalPriceWay.equals("matprice") && !getSalPriceWay.equals("custprice") && !getSalPriceWay.equals("rareferprice"))
	                    {
	                        iTaxQuotedPrice = itaxrate.add(hundred).divide(hundred, 2, RoundingMode.HALF_UP).multiply(iSalPrice).divide(iCurRate2, Integer.parseInt(prefvalue), RoundingMode.HALF_UP);
	                        iQuotedPrice = iSalPrice.divide(iCurRate2, Integer.parseInt(prefvalue), RoundingMode.HALF_UP);
	                    }
	                    cpriceinfo.put("itaxquotedprice", iTaxQuotedPrice);
	                    cpriceinfo.put("iquotedprice", iQuotedPrice);
	                    cpriceinfo.put("iunitprice_f", iQuotedPrice);
	                    cpriceinfo.put("itaxprice_f", iTaxQuotedPrice);
	                    cpriceinfo.put("ibefrebateprice", iTaxQuotedPrice);
	                    cpriceinfo.put("isquoted", "1");
	                    String pricestring = cpriceinfo.toString();
	                    StringBuffer price_str = new StringBuffer();
	                    price_str.append("");
	                    for(Iterator i$ = cpriceinfo.keySet().iterator(); i$.hasNext(); price_str.append(";"))
	                    {
	                        Object key = i$.next();
	                        price_str.append(key);
	                        price_str.append(":");
	                        price_str.append(cpriceinfo.get(key));
	                    }

	                    cpriceinfo.put("cpriceinfo", price_str.toString());
	                  //  row.putAll(cpriceinfo);
	                } else
	                {
	                    BigDecimal iTaxQuotedPrice;
	                    BigDecimal iQuotedPrice;
	                    if("cost".equalsIgnoreCase(getSalPriceWay))
	                    {
	                        iTaxQuotedPrice = itaxrate.add(hundred).divide(hundred, 2, RoundingMode.HALF_UP).multiply(iSalPrice);
	                        iQuotedPrice = iSalPrice;
	                        if(!getSalPriceWay.equals("matprice") && !getSalPriceWay.equals("custprice") && !getSalPriceWay.equals("rareferprice"))
	                        {
	                            iTaxQuotedPrice = itaxrate.add(hundred).divide(hundred, 2, RoundingMode.HALF_UP).multiply(iSalPrice).divide(iCurRate2, Integer.parseInt(prefvalue), RoundingMode.HALF_UP);
	                            iQuotedPrice = iSalPrice.divide(iCurRate2, Integer.parseInt(prefvalue), RoundingMode.HALF_UP);
	                        }
	                    } else
	                    {
	                        iTaxQuotedPrice = iSalPrice;
	                        iQuotedPrice = iSalPrice.divide(itaxrate.add(hundred).divide(hundred, 2, RoundingMode.HALF_UP), Integer.parseInt(prefvalue), RoundingMode.HALF_UP);
	                        if(!getSalPriceWay.equals("matprice") && !getSalPriceWay.equals("custprice") && !getSalPriceWay.equals("rareferprice"))
	                        {
	                            iTaxQuotedPrice = iSalPrice.divide(iCurRate2, Integer.parseInt(prefvalue), RoundingMode.HALF_UP);
	                            iQuotedPrice = iSalPrice.divide(itaxrate.add(hundred).divide(hundred, 2, RoundingMode.HALF_UP), Integer.parseInt(prefvalue), RoundingMode.HALF_UP).divide(iCurRate2, Integer.parseInt(prefvalue), RoundingMode.HALF_UP);
	                        }
	                    }
	                    cpriceinfo.put("itaxquotedprice", iTaxQuotedPrice);
	                    cpriceinfo.put("iquotedprice", iQuotedPrice);
	                    cpriceinfo.put("iunitprice_f", iQuotedPrice);
	                    cpriceinfo.put("itaxprice_f", iTaxQuotedPrice);
	                    cpriceinfo.put("ibefrebateprice", iTaxQuotedPrice);
	                    cpriceinfo.put("isquoted", "0");
	                    StringBuffer price_str = new StringBuffer();
	                    price_str.append("");
	                    for(Iterator i$ = cpriceinfo.keySet().iterator(); i$.hasNext(); price_str.append(";"))
	                    {
	                        Object key = i$.next();
	                        price_str.append(key);
	                        price_str.append(":");
	                        price_str.append(cpriceinfo.get(key));
	                    }

	                    cpriceinfo.put("cpriceinfo", price_str.toString());
	                  //  row.putAll(cpriceinfo);
	                }
	            } else
	            {
	                cpriceinfo.put("iquotedprice", null);
	            }
	        } else
	        {
	            BigDecimal price0 = (new BigDecimal(0)).setScale(Integer.parseInt(prefvalue), 4);
	            cpriceinfo.put("itaxquotedprice", price0);
	            cpriceinfo.put("iquotedprice", price0);
	            cpriceinfo.put("iunitprice_f", price0);
	            cpriceinfo.put("itaxprice_f", price0);
	            cpriceinfo.put("ibefrebateprice", price0);
	        }
	        return cpriceinfo;
	    }
	}

	
	
	