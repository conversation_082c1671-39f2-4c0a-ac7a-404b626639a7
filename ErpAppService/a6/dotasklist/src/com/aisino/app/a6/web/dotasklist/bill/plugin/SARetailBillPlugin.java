package com.aisino.app.a6.web.dotasklist.bill.plugin;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.a6.business.sa.price.plugin.SaGetHighLowPricePlugin;
import com.aisino.aos.bill.common.BillUtils;
import com.aisino.aos.bill.plugin.BillCheckPlugin;
import com.aisino.aos.bill.vo.BillSetting;
import com.aisino.aos.preference.common.Prefer;
import com.aisino.aos.system.SessionHelper;
import com.aisino.app.a6.web.dotasklist.common.plugin.SaCheckPlugin;
import com.aisino.app.a6.web.dotasklist.common.util.DealBillFunctionUtil;
import com.aisino.app.a6.web.dotasklist.common.util.GetBillInfoUtil;
import com.aisino.app.web.dotasklist.common.util.CommonUtil;
import com.aisino.app.web.dotasklist.daiban.plugin.DaibanUtil;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.MS;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.CsUtil;
import com.aisino.platform.util.DateUtil;
import com.aisino.platform.util.JsUtil;
import com.aisino.platform.util.NoCaseMap;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

/**
 * 零售单新增相关
 * @since 2018-09-08
 */
public class SARetailBillPlugin implements FormCreateListener{
	private static final long serialVersionUID = 1L;
	DealBillFunctionUtil dealfunc = new DealBillFunctionUtil();
	SaCheckPlugin sacheck= new SaCheckPlugin();
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		// TODO Auto-generated method stub
		DbSvr db = DbSvr.getDbService(null);
		String action = bus.getString("action");
		BillSetting billSetting = BillUtils.getBillSettingVoByFormId("business_sa_retail_form");
		if("new".equals(action)){ /*新增是数据加载*/
			Map rtn = new HashMap();
			String newRescKey = "business_sa_retail_form.newblue";
			if(dealfunc.checkBillPower(newRescKey) == true){
				//获取单据默认日期
				Date today =DateUtil.today();
				String dDate = DateUtil.date2Str(today);
				rtn.put("dDate", dDate);
				
				//获取默认仓库
				String cCreatorGUID=SessionHelper.getCurrentUserId();//登录人
				Map storeinfo = db.getOneRecorder("select top 1 l.cStoreGUID,cs.cname as cStoreName from ST_StkRecord s left join ST_StkRecordLine l on l.cheadguid = s.cguid left join CM_Storehouse cs on cs.cGUID = l.cStoreGUID where s.cCreatorGUID = ? and s.cBillType = '212' order by s.ddate DESC", cCreatorGUID);
				 if(storeinfo == null || storeinfo.size() == 0){
					    rtn.put("cStoreGUID", "");
		                rtn.put("cStoreName", ""); 
				 }else{
						rtn.put("cStoreGUID", storeinfo.get("cStoreGUID"));
		                rtn.put("cStoreName", storeinfo.get("cStoreName")); 
				 }

				//获取默认客户
				 String cCustGUID = Prefer.get("U3_SA_RetailDefaultCust");//默认客户
				 String CCustName=null;
				 String cDeptGUID=null;
				 String cDeptName=null;
				 String cEmpGUID=null;
				 String cEmpName=null;
				 
				 if(cCustGUID!="" && cCustGUID!=null){
					 bus.put("ccustguid", cCustGUID);
					 Map map = db.queryIdFirstRow("mobile_common.getempdeptbycustomer", bus);
					 CCustName = CollectionUtil.getStringFromMap(map, "CCustName");
					 cDeptGUID = CollectionUtil.getStringFromMap(map, "cDeptGUID");
					 cDeptName = CollectionUtil.getStringFromMap(map, "cDeptName");
					 cEmpGUID = CollectionUtil.getStringFromMap(map, "cEmpGUID");
					 cEmpName = CollectionUtil.getStringFromMap(map, "cEmpName");				 
				 }
				 rtn.put("cCustGUID",cCustGUID);
	             rtn.put("CCustName",CCustName); 
	             rtn.put("cDeptGUID",cDeptGUID); 
	             rtn.put("cDeptName",cDeptName); 
	             rtn.put("cEmpGUID",cEmpGUID); 
	             rtn.put("cEmpName",cEmpName); 
				 		
	             //获取单据编号
	            String cBillCode=new GetBillInfoUtil().getBillCode(db, bus, "business_sa_retail_form", "212");
	            rtn.put("cBillCode",cBillCode);
				form.setReturn(rtn);

			}else{
				throw new BusinessException("当前用户没有零售单新增权限!");
			}
			
		}else if("load".equals(action)){//修改查看时数据加载
			String cguid = bus.getString("cguid");
			Integer otherbill= (Integer) (billSetting==null?0:billSetting.getImodifybill());
			Integer zero = 0;
			Map mainMap = db.queryIdFirstRow("mobile_bill_saretail_sql.getSaRetailMain", new Object[]{cguid});
			String cCurGUID = db.getStringResult("select top 1 cguid from GL_Currency where iNative = 1 and iStatus = 1");
			String iRedFlag =  CollectionUtil.getStringFromMap(mainMap, "iRedFlag");	
			if(mainMap != null && mainMap.size() > 0){
				List detailMap = db.queryIdForList("mobile_bill_saretail_sql.getSaRetailDetail", new Object[]{cguid});
				if(detailMap != null && detailMap.size() > 0){
					List<Map> detail = new CommonUtil().getImgPath(detailMap);
					String iAuditStatus = String.valueOf(mainMap.get("iauditstatus")); //不可修改状态	
					String cCreatorCurrent = SessionHelper.getCurrentUserId();	
					if(otherbill.equals(zero)&& !cCreatorCurrent.equalsIgnoreCase(mainMap.get("cCreatorGUID").toString())){
					    mainMap.put("icanmodify", "n");	
					    mainMap.put("icannotreason", "不能修改他人单据");	
				    }else if(!"tsaved".equals(iAuditStatus) && !"saved".equals(iAuditStatus) && !"revise".equals(iAuditStatus)){
						    mainMap.put("icanmodify", "n");	
						    mainMap.put("icannotreason", "单据状态有控制，不能被修改，仅供查看");	
					}else if(!cCurGUID.equals(mainMap.get("cCurGUID"))){
						mainMap.put("icanmodify", "n");	
					    mainMap.put("icannotreason", "APP不支持外币修改，仅供查看");
				   }else if(iRedFlag.equals("1")){
					    mainMap.put("icanmodify", "n");	
					    mainMap.put("icannotreason", "APP不支持红字零售单修改，仅供查看");
				   }else{
						String editRescKey = "business_sa_retail_form.modifybf";
						boolean editRe = SecurityService.rescCheck(editRescKey); //校验是否有修改权限
						if(editRe){
							boolean flag=false;
							for(int i=0;i<detail.size();i++){
								Map row = (Map) detail.get(i);
								String iTaxQuotedPrice =row.get("iTaxQuotedPrice")!= null ? row.get("iTaxQuotedPrice").toString():"0E-9";
								String iQuotedPrice =row.get("iQuotedPrice")!= null ? row.get("iQuotedPrice").toString():"0E-9";	
								
								BigDecimal iDisRate = row.get("iDisRate") != null ? new BigDecimal(row.get("iDisRate").toString()) : new BigDecimal(0);
								iDisRate = iDisRate.setScale(2, RoundingMode.HALF_UP);
								if(row.get("isubunit").toString().equals("1") ||row.get("iguaranteeflag").toString().equals("1")||row.get("ibatchflag").toString().equals("1")
										|| !"0E-9".equalsIgnoreCase(iTaxQuotedPrice) || !"0E-9".equalsIgnoreCase(iQuotedPrice)|| new BigDecimal(100.00).compareTo(iDisRate)!=0){
									flag=true;
									break;
								}
							}
							if(flag){
								mainMap.put("icanmodify", "n");
								mainMap.put("icannotreason", "物品明细行存在或批次、保质期、浮动换算、返利、报价、折扣等情况的物品，手机端暂不支持修改");	
							}else{
								mainMap.put("icanmodify", "y");
							}		
						}else{
							mainMap.put("icanmodify", "n");
							mainMap.put("icannotreason", "没有修改单据权限，仅供查看");	
						}
					}					
					
					List paynowinfo = db.queryIdForList("mobile_bill_saretail_sql.getpayNowInfo", new Object[]{cguid});
					List file = db.queryIdForList("mobile_bill_saretail_sql.getFile", new Object[]{cguid});
					Map rtn = new HashMap();
					rtn.put("main", mainMap);
					rtn.put("detail", detail);
					rtn.put("paynowinfo", paynowinfo);
					rtn.put("file", file);
					form.setReturn(rtn);
				}else{
					throw new BusinessException("当前单据已被删除,请重新刷新列表!");
			}
			}else{
				throw new BusinessException("当前单据已被删除,请重新刷新列表!");
			}
		}else if("tosettle".equals(action)){//去结算
			Map settleInfo = new HashMap();
			//获取默认结算方式
			String settlemodeguid= Prefer.get("U3_SA_RetailSettlementMode");
			String settlemodename = null;
			if(settlemodeguid!="" && settlemodeguid!=null){
				settlemodename = db.getStringResult("select cname from CA_SettleMode where cguid=?", settlemodeguid);
			 }
			
			//获取应收金额和抹零金额
			BigDecimal iMolingAMT=new BigDecimal(0);
			BigDecimal iReceivableAMT=new BigDecimal(0);
			
			String U3_SA_RetailMaLingSet = Prefer.get("U3_SA_RetailMaLingSet");
	        String U3_SA_RetailMaLingDigit = Prefer.get("U3_SA_RetailMaLingDigit");
	        BigDecimal sumAMT = new BigDecimal(bus.getString("sumamt"));
	        if("one".equalsIgnoreCase(U3_SA_RetailMaLingSet)){
	            if("one".equalsIgnoreCase(U3_SA_RetailMaLingDigit)){
	                BigDecimal sum0 = sumAMT.setScale(-1, 1);
	                iMolingAMT = sumAMT.subtract(sum0);
	                iReceivableAMT = sum0;
	            }
	            if("two".equalsIgnoreCase(U3_SA_RetailMaLingDigit)){
	                BigDecimal sum1 = sumAMT.setScale(0, 1);;
	                iMolingAMT = sumAMT.subtract(sum1);
	                iReceivableAMT = sum1;
	            }
	            if("three".equalsIgnoreCase(U3_SA_RetailMaLingDigit)){
	                BigDecimal sum2 = sumAMT.setScale(1, 1);
	                iMolingAMT = sumAMT.subtract(sum2);
	                iReceivableAMT = sum2;
	            }
	        }
	        if("two".equalsIgnoreCase(U3_SA_RetailMaLingSet)) {
	            if("one".equalsIgnoreCase(U3_SA_RetailMaLingDigit)){
	                BigDecimal sum0 = sumAMT.setScale(-1, 4);
	                iMolingAMT = sumAMT.subtract(sum0);
	                iReceivableAMT = sum0;
	            }
	            if("two".equalsIgnoreCase(U3_SA_RetailMaLingDigit)){
	                BigDecimal sum1 = sumAMT.setScale(0, 4);
	                iMolingAMT = sumAMT.subtract(sum1);
	                iReceivableAMT = sum1;
	            }
	            if("three".equalsIgnoreCase(U3_SA_RetailMaLingDigit)){
	                BigDecimal sum2 = sumAMT.setScale(1, 4);
	                iMolingAMT = sumAMT.subtract(sum2);
	                iReceivableAMT = sum2;
	            }
	        }
	        if("three".equalsIgnoreCase(U3_SA_RetailMaLingSet) || U3_SA_RetailMaLingSet == null){  
                iReceivableAMT = sumAMT;
	        }

			settleInfo.put("settlemodeguid", settlemodeguid);
			settleInfo.put("settlemodename", settlemodename);
			settleInfo.put("imolingamt", iMolingAMT);
			settleInfo.put("ireceivableamt", iReceivableAMT);	
			form.setReturn(settleInfo);
				
		}else if("save".equals(action)||"tsave".equals(action)){//保存或暂存
			String alldata = bus.getString("alldata");
			alldata = alldata.substring(1, alldata.length()-1);
			Map bill = CsUtil.unserializeJson(alldata);
			Map head = (Map) bill.get("main");//主表
			List<Map> detail = (List) bill.get("detail");//明细
			List<Map> paylist = bill.get("paynowinfo")==null?new ArrayList():(List) bill.get("paynowinfo");//结算
			
			String cguid = CollectionUtil.getStringFromMap(head, "cguid");
			String dDate = CollectionUtil.getStringFromMap(head, "dDate");
			String cCustGUID = CollectionUtil.getStringFromMap(head, "cCustGUID");
			String cStoreGUID = CollectionUtil.getStringFromMap(head, "cStoreGUID");
			String cEmpGUID = CollectionUtil.getStringFromMap(head, "cEmpGUID");
			String cDeptGUID = CollectionUtil.getStringFromMap(head, "cDeptGUID");
			Map cus = new NoCaseMap();
			cus.put("cCustGUID", cCustGUID);
			Map cusinfo = db.queryIdFirstRow("a6_business_billtemplate.customerinfoload",cus);
			
			String cCurGUID = db.getStringResult("select top 1 cguid from GL_Currency where iNative = 1 and iStatus = 1");
			String cTemplateId = db.getStringResult("select top 1 cGuid from BILL_TEMPLATE where cFormID = 'business_sa_retail_form' and cDefault = '1' and iStatus = '1'");
		    String cCheckWay=billSetting==null?BillSetting.ManualCheck:billSetting.getCcheckway();
		     
			for(Map line : detail){
				String cguid_mx = CollectionUtil.getStringFromMap(line, "cguid");
				String cMatGUID = CollectionUtil.getStringFromMap(line, "cMatGUID");
				Map matinfo = db.queryIdFirstRow("a6_grid_materaltemplate.all_Material_refer",line);
				BigDecimal itaxrate = CollectionUtil.getBigDecimal(line, "itaxrate");
				String cTimeStamp = CollectionUtil.getStringFromMap(line, "cTimeStamp");
				line.putAll(matinfo);
				line.put("cguid", cguid_mx);
				line.put("cStoreGUID", cStoreGUID);
				line.put("cTimeStamp", cTimeStamp);
				
				line.put("iUnitPrice", CollectionUtil.getBigDecimal(line, "iUnitPrice_f"));
				line.put("iAMT", CollectionUtil.getBigDecimal(line, "iAMT_f"));
				line.put("iTaxPrice", CollectionUtil.getBigDecimal(line, "iTaxPrice_f"));
				line.put("iTotal", CollectionUtil.getBigDecimal(line, "iTotal_f"));
				line.put("iTax", CollectionUtil.getBigDecimal(line, "iTax_f"));
				line.put("iQuotedPrice", 0);
				line.put("iTaxQuotedPrice", 0);
				line.put("iQuotedTotal_F", 0);
				line.put("iDisRate", 100);
				line.put("iDisRateCon", 0);
				line.put("iDisAMT_F", 0);
				line.put("iDisAMT", 0);
				line.put("itaxrate", itaxrate);
				line.put("iUnitQTY", CollectionUtil.getBigDecimal(line, "iqty"));
				line.put("iRefePriceQTY", CollectionUtil.getBigDecimal(line, "iqty"));
				line.put("iMUnitPrice_F", CollectionUtil.getBigDecimal(line, "iUnitPrice_f"));
				line.put("iMUnitPrice", CollectionUtil.getBigDecimal(line, "iUnitPrice_f"));
				line.put("iIaFlag", "1");
				line.put("iAccDeliverGoods", "0");
				line.put("iMTaxPrice", CollectionUtil.getBigDecimal(line, "iTaxPrice_f"));
				line.put("iMUnitTaxPrice_F", CollectionUtil.getBigDecimal(line, "iTaxPrice_f"));
				//最高最低售价
				Map HighLowPrice = SaGetHighLowPricePlugin.get_quotedHLPrice(line, cCustGUID, dDate, db, form, bus);
				line.putAll(HighLowPrice);
				//最近采购价
				int U3_ST_PricePrecision = Integer.parseInt(Prefer.get("U3_ST_PricePrecision").toString()); // 获取发票精度
				// 获取采购发票上的采购计价单价，采购计价单位取法，采购计价单位，换算率
				Map map = db.getOneRecorder("select top 1 pl.iUnitPrice,pl.cPURefePriceUnitGUID,pl.cPURefePriceMethod,pl.iChangeRate FROM PU_InvoiceLine pl LEFT JOIN PU_Invoice p ON pl.cHeadGUID=p.cGUID WHERE p.iAuditStatus='checked' AND pl.cMatGUID = " + cMatGUID + "ORDER BY p.dInvDate DESC,p.dCreatorTime DESC");
				if (map != null && !map.isEmpty()) {
					String PU_RefePriceMethod = CollectionUtil.getStringFromMap(map, "cPURefePriceMethod");
					BigDecimal unitPrice = BigDecimal.ZERO;
					BigDecimal PU_unitPrice = CollectionUtil.getBigDecimal(map, "iUnitPrice");
					// 判断采购计价单位取法是辅，获取换算率
					if ("f".equalsIgnoreCase(PU_RefePriceMethod)) {
						BigDecimal PU_iChangeRate = CollectionUtil.getBigDecimal(map, "iChangeRate");
						unitPrice = PU_unitPrice.divide(PU_iChangeRate, U3_ST_PricePrecision, BigDecimal.ROUND_HALF_UP);
					} else {
						unitPrice = PU_unitPrice;
					}
					// 主单位单价换算成销售计价单位单价
					String SA_RefePriceMethod = CollectionUtil.getStringFromMap(line, "cSARefePriceMethod");
					BigDecimal SA_unitPrice = BigDecimal.ZERO;
					if ("f".equalsIgnoreCase(SA_RefePriceMethod)) {
						BigDecimal SA_iChangeRate = new BigDecimal(1);
						SA_unitPrice = unitPrice.multiply(SA_iChangeRate);
					} else {
						SA_unitPrice = unitPrice;
					}
					BigDecimal iCurRate2 = new BigDecimal(1);
					BigDecimal iRecPuPrice = SA_unitPrice.divide(iCurRate2, U3_ST_PricePrecision, BigDecimal.ROUND_HALF_UP);
					line.put("irecpuprice", iRecPuPrice);
				}
				
				
			}
			String cSettleTypeGUID = "";
			if(paylist!=null&&paylist.size()==1){
				cSettleTypeGUID = (String) paylist.get(0).get("csettletypeguid");
			}
			
			DataMsgBus newbus = new DataMsgBus();
			if("save".equals(action)){
				newbus.setNewAction("save");
			}else {
				newbus.setNewAction("tsave");
			}
			if(cguid!=null&&cguid!=""&&!cguid.isEmpty()){
				newbus.setBusFormState("editold");
				//修改时不处理发票类型
			}else{
				newbus.setBusFormState("editnew");
				//处理默认发票类型
				String invoiceType=Prefer.get("U3_SA_RetailDefaultInvoiceType");
				if("none".equalsIgnoreCase(invoiceType))
					invoiceType=null;
				newbus.put("invtype", invoiceType);
			}
			newbus.put("cEmpGUID", cEmpGUID);//取出来又放进去 是因为customerinfoload中的值会覆盖
			newbus.put("cDeptGUID", cDeptGUID);//取出来又放进去 是因为customerinfoload中的值会覆盖
			newbus.put("cCurGUID", cCurGUID);
			newbus.put("cTemplateId", cTemplateId);
			newbus.put("cSettleTypeGUID", cSettleTypeGUID);
			newbus.put("cCheckWay", cCheckWay);
			newbus.put("iRSFlag", "0");
			newbus.put("iRedFlag", "0");
			newbus.put("cBillType", "212");
			newbus.put("cBusCode", "212");
			newbus.put("cBusType", "s0004");
			newbus.put("cBusProcess", "s0004");
			newbus.put("iIaFlag", "1");
			newbus.put("iAccDeliverGoods", "0");
			newbus.put("cSysType", "SA");
			newbus.put("iCurRate", "1");
			newbus.put("iCurRate2", "1");

			
			if(cusinfo!=null &&(!cusinfo.isEmpty()) )
			    newbus.putAll(cusinfo);
			if(head!=null &&(!head.isEmpty()) )
				newbus.putAll(head);
			newbus.put("list", detail);
			newbus.put("paylist", paylist); //结算信息列表
			Map rtn = new HashMap(); //返回值
			StringBuffer msg=new StringBuffer();
			boolean savecheckflag=false; //保存检查是否通过	

	    	//不论哪种情况，都需将以下参数put到bus中返回：
	    	//msg：保存、审核成功或失败的提示信息；
	    	//icanmodify:操作后，单据的编辑状态；

	    	//保存校验情况下，还多返回一个savetmsg
	    	//审核校验情况下，还多返回一个submitmsg
			newbus.put("msg", null);
			newbus.put("icanmodify", "y");
			
			//保存或暂存			
			try{
				if("tsave".equals(action)){
					AbstractForm.doFormSubmit("business_sa_retail_form", newbus); //返回结果为objMsg，值为空
					newbus.put("msg", "暂存成功！");
					//删除返回的不必要信息，保留主表。
					newbus.remove("list");
					newbus.remove("paylist");					
					rtn.put("rtn", newbus);
					form.setReturn(rtn);
				}else { 
					//保存
					String firstsave = "true";	//最高最低价校验等不通过时，需要弹密码框等含2次交互的请求
					String firstsaveparam =bus.getString("firstsave")==null?"":bus.getString("firstsave").toString();
					if(!"".equals(firstsaveparam))
						firstsave=firstsaveparam;  //如果传了firstsave=false，则代表第二次提交，改成false			 				    	
			    	String savemsg=null;
			    	String savems=null;
			    	if("true".equals(firstsave)){ //要走保存前的各种校验
			    		    boolean f=false;
			    		    savems=sacheck.Check("business_sa_retail_form",newbus,"disauth_sa_save","保存"); 
				        	if(StringUtil.isNotBlank(savems)&&savems.length()>3){
				        		newbus.put("flagStr", savems);
				        		f=true;
				        	}					    	
					    	savemsg=sacheck.Check("business_sa_retail_form",newbus,"beforesave_sa","保存");
				        	if(savemsg!=null&&!savemsg.equals("{}")){					        										        		
				        		newbus.put("savemsg", savemsg);
				        		newbus.put("cguid", newbus.getString("cGUID"));
								//删除返回的不必要信息，保留主表。
								newbus.remove("list");
								newbus.remove("paylist");
								newbus.remove("matinfos");								
								rtn.put("rtn", newbus);
								form.setReturn(rtn);								
				        	}else{
				        		if(f){	 		
				        			newbus.put("savemsg", savemsg);
				        			newbus.put("cguid", newbus.getString("cGUID"));
									//删除返回的不必要信息，保留主表。
									newbus.remove("list");
									newbus.remove("paylist");
									newbus.remove("matinfos");
									rtn.put("rtn", newbus);
									form.setReturn(rtn);									
				        		}else{
				        			savecheckflag=true;  //保存检查通过，继续执行
				        		}
				        	}
				     }	
			    	
			    	//保存检查通过，继续执行。不通过，则直接停留在页面上
			    	//第二次提交跳过校验，也可以继续执行
			       if(savecheckflag||"false".equals(firstsave)){
			    	   try{
				    	    savemsg=sacheck.Check("business_sa_retail_form",newbus,"save","保存");
				    	    if(savemsg!=null&&!savemsg.equals("{}"))
				    	    	msg.append(savemsg);
							//如果保存成功，则继续审核				
							msg.append("保存成功，完成结算。");		
							newbus.put("msg", msg);
							db.commit();//将保存的结果提交
							//以下逻辑开始审核
							try{		
								//bus是请求过来包含的参数
								//newbus是组装后
								newbus=this.afterSaveAudit(db,bus,newbus,form);
								//删除返回的不必要信息，保留主表。
								newbus.remove("list");
								newbus.remove("paylist");
								newbus.remove("matinfos");
								rtn.put("rtn", newbus);
								form.setReturn(rtn);		
							    
							}catch (Exception e) {
					  		    Map  map = new HashMap();
				    		    map.put("errmsg","保存成功，但"+e.getMessage()+" 审核失败！");
				   				form.setReturn(map);
								e.printStackTrace();									
							}				
			    	   }catch (Exception e) {
							// TODO Auto-generated catch block		
			    		    Map  map = new HashMap();
			    		    map.put("errmsg",e.getMessage()+" 保存失败！");
			   				form.setReturn(map);
							e.printStackTrace();					
						}						
			       }	    	
				}	
			}catch (Exception e) {
				// TODO Auto-generated catch block
				 Map  map = new HashMap();
	    		 map.put("errmsg",e.getMessage());
	    		 form.setReturn(map);
				e.printStackTrace();
			}			
		}else if("submit".equals(action)){//非工作流第二次提交
			Map  map = new HashMap();
			StringBuffer sbf = new StringBuffer();
			String msg =bus.getString("msg")==null?"":bus.getString("msg");
			sbf.append(msg);
			
			map.put("cguid", bus.getString("cguid"));
			map.put("table", "ST_StkRecord");
			map.put("tableline", "ST_StkRecordLine");
			DataMsgBus newbus =dealfunc.getBusData(db, map);
			
			newbus.put("flagStr", bus.getString("flagstr")); //校验信息；
			newbus.put("cTimeStamp", bus.getString("ctimestamp"));//校验单据是否发生变化；
			
			String submitmsg=sacheck.Check("business_sa_retail_form",newbus,"docheck","审核");
  		    DaibanUtil.updateCheckWay("ST_StkRecord", bus.getString("cguid"));
  		    sbf.append("审核成功。");
  		    if(submitmsg!=null&&!submitmsg.equals("{}")) 							  		    	
  		    	sbf.append(submitmsg);	  		    
	    	String flag = dealfunc.canModifyCheck("checked","business_sa_retail_form.modifybf",newbus.getList("list"));	
	    	
	    	newbus.put("icanmodify", flag);
			newbus.put("msg",sbf);
			
	    	Map rtn=new HashMap();
			//删除返回的不必要信息，保留主表。
			newbus.remove("list");
			rtn.put("rtn", newbus);
			form.setReturn(rtn);
		}else if("wfsubmit".equals(action)){//工作流选择模板后，点提交	
			//组织数据，将submit需要的信息补充完整至newbus
			Map  map = new HashMap();
			
			map.put("cguid", bus.getString("cguid"));
			map.put("table", "ST_StkRecord");
			map.put("tableline", "ST_StkRecordLine");
			DataMsgBus newbus =dealfunc.getBusData(db, map);
			
			newbus.put("cTimeStamp", bus.getString("ctimestamp"));//校验单据是否发生变化；			
			newbus.send("wfProcessId", bus.getString("processcode"));
			
			dealfunc.WorkflowSubmit(form,newbus);	
			String flag = dealfunc.canModifyCheck(newbus.getString("iAuditStatus"),"business_sa_retail_form.modifybf",newbus.getList("list"));		
						
			newbus.put("icanmodify", flag);
			newbus.put("msg",bus.getString("msg")+"提交成功。");
			Map rtn=new HashMap();
			//删除返回的不必要信息，保留主表。
			newbus.remove("list");
			rtn.put("rtn", newbus);
			form.setReturn(rtn);
		}
	}
	

	public DataMsgBus afterSaveAudit(DbSvr db,DataMsgBus bus,DataMsgBus newbus,AbstractForm form){
		String cCheckWay=newbus.getString("cCheckWay");				
		String iAuditStatus=newbus.getString("iAuditStatus");
		String cGUID=newbus.getString("cGUID");

		boolean submitcheckflag=false;
		String flag=null;  //单据操作之后能否编辑；

		StringBuffer msg=new StringBuffer();
		 if(newbus.getString("msg")!=null)
 	    	msg.append(newbus.getString("msg"));

		List<Map> detail=newbus.getList("list");
		/* 获取审批方式
		 * 0手工审核 1自动审核 2 流程审批
		 */	
		
		//组织数据，将submit需要的信息补充完整至newbus 
	    List<Map> list=new  ArrayList<Map>();
	    Map m=db.getOneRecorder("select * from ST_StkRecord so where so.cGUID =?",cGUID);
	    m.put("table", "ST_StkRecord");
	    if (CollectionUtil.isBlankMap(m))
			 throw new BusinessException("单据不存在，可能已被并发删除");
	    list=db.getListResult("select * from ST_StkRecordLine sao where cheadguid=?",cGUID);
	    newbus.putAll(m);
	    newbus.send("list", list);	
	    newbus.send("billcguid",cGUID);	
		
	    if("0".equals(cCheckWay)){//手工审核
	    	msg.append("请到待办事项中审核。");
	    	flag = dealfunc.canModifyCheck(iAuditStatus,"business_sa_retail_form.modifybf",detail);
	    }else if("1".equals(cCheckWay)){  //自动审批，保存完自动审核。
			String firstsubmit="true";
			String firstsubmitparam =bus.getString("firstsubmit")==null?"":bus.getString("firstsubmit").toString();
			if(!"".equals(firstsubmitparam))
				firstsubmit=firstsubmitparam;  //如果传了firstSubmit=false，则代表第二次提交，改成false	
			
	    	String submitmsg=null;
	    	String submitms=null;
	    	if(firstsubmit.equals("true")){  //审核前的各种校验
	    		    boolean f=false;
	    		    submitms=sacheck.Check("business_sa_retail_form",newbus,"disauth_sa_check","审核");
		        	if(StringUtil.isNotBlank(submitms)&&submitms.length()>3){
		        		newbus.put("flagStr", submitms);
		        		f=true;
		        	}
		        	submitmsg=sacheck.Check("business_sa_retail_form",newbus,"beforecheck_sa","审核");
		        	if(submitmsg!=null&&!submitmsg.equals("{}")){
		        		flag = dealfunc.canModifyCheck(iAuditStatus,"business_sa_retail_form.modifybf",detail);
						newbus.put("submitmsg", submitmsg);			
		        	}else{
		        		if(f){
		        			submitmsg=JsUtil.toJsonObjectWithNull(sacheck.getZheKouCheck(submitms,"审核")).toString();
		        			flag = dealfunc.canModifyCheck(iAuditStatus,"business_sa_retail_form.modifybf",detail);
							newbus.put("submitmsg", submitmsg);									        
		        		}else{
		        			submitcheckflag=true;//审核校验通过，继续下一步
		        		}
		        	}
		        }
	    	
	    	//校验通过，继续下一步
	    	if(submitcheckflag){
	    		submitmsg=sacheck.Check("business_sa_retail_form",newbus,"docheck","审核");
	  		    DaibanUtil.updateCheckWay("ST_StkRecord",cGUID);
	  		    msg.append("审核成功。");
	  		    if(submitmsg!=null&&!submitmsg.equals("{}")) 							  		    	
	  		    	msg.append(submitmsg);	  		    
		    	iAuditStatus="checked";
		    	flag = dealfunc.canModifyCheck(iAuditStatus,"business_sa_retail_form.modifybf",detail);	
	    	}
	    	//第二次提交跳过校验，则应另发请求（不能再走保存了）
	    
	    }else  if("2".equals(cCheckWay)){  //工作流，点确认结算，直接保存加提交。此处处理提交。
	    	//自动执行action=submitChoose操作
	        if(!"saved".equals(iAuditStatus) && !"revise".equals(iAuditStatus)){
	        	throw new BusinessException("当前单据状态不能提交送审，请重新刷新列表数据!");
	        }
	        MS ms = new MS("AOS.GetBillFilteredProcessTemplate");
			List<Map> workflowTemps = (List<Map>)ms.doService("business_sa_retail_form",SessionHelper.getCurrentAdminOrgnId(),false?"change":null);
			if(CollectionUtil.isEmpty(workflowTemps)){
				throw new BusinessException("未找到可用的流程模板！");
			}
			if(workflowTemps.size()==1){//直接执行提交
				String processCode = (String)workflowTemps.get(0).get("code");										
				newbus.put("wfProcessId", processCode);										
				dealfunc.WorkflowSubmit(form,newbus);//newbus中需要包含：单据cGUID，单据主表表名table，如"ST_StkRecord"，单据时间戳cTimeStamp，
				flag = dealfunc.canModifyCheck(newbus.getString("iAuditStatus"),"business_sa_retail_form.modifybf",detail);
				msg.append("提交成功。");
			}else{//多个流程模板，先弹出模板选择框，返回chooseList
				if(BillCheckPlugin.State_Saved.equalsIgnoreCase(iAuditStatus)){//保存待提交
					List rtnList = new ArrayList();
					Map rtnMap = new HashMap();
					for(Map map : workflowTemps){//实际流程名称和id
						String processName = CollectionUtil.getStringFromMap(map, "name");
						String processCode=CollectionUtil.getStringFromMap(map, "code");
						//返回流程选项
						Map rtnprocess = new HashMap();
						rtnprocess.put("processCode", processCode);
						rtnprocess.put("processName", processName);
						rtnList.add(rtnprocess);
					}
					flag = dealfunc.canModifyCheck(iAuditStatus,"business_sa_retail_form.modifybf",detail);
					newbus.put("chooseList", rtnList);
				}
				
				if(BillCheckPlugin.State_Revise.equalsIgnoreCase(iAuditStatus)){//退回修改中
					String processtemplateId = "";
					MS s0 = new MS("AOS.BillQueryWorkFlow");
					Object wfVar = s0.doService(form, newbus);
					if(wfVar !=null){
						processtemplateId=CollectionUtil.getStringFromMap((Map)wfVar,"processtemplate");
					}else{
						throw new BusinessException("提交按钮初始化错误，未找到退回发起人单据再次提交的流程!");
					}
					
					newbus.send("wfProcessId", processtemplateId);
					dealfunc.WorkflowSubmit(form,newbus);
					flag = dealfunc.canModifyCheck(newbus.getString("iAuditStatus"),"business_sa_retail_form.modifybf",detail);
					msg.append("提交成功。");
				}
			}	
	    }
	    
		newbus.put("msg", msg);
		newbus.put("icanmodify", flag);
	    return newbus;
	}

}