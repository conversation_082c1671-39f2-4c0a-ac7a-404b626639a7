package com.aisino.app.a6.web.dotasklist.bill.plugin;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.a6.oa.common.util.OADateUtil;
import com.aisino.aos.preference.common.Prefer;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.db.Eso;
import com.aisino.platform.db.SqlInfo;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;

/**
 * 报销单预算控制
 * <AUTHOR>
 * @since 2017-08-29
 */
public class ExpenseAccountBillBudgetControlPlugin {
	/**
	 * 2013-01-30 mxs
	 * <p>Discription:校验报销明细行中金额是否超过部门预算(报销单预算校验)</p> 
	 *@param form
	 *@param bus
	 */
	public static void checkByBudegetSet4ExpenseLine(AbstractForm form, DataMsgBus bus){
		String strEnabledFlag = Prefer.get("A6_OA_BudegetEnabled");//2014-3-10 mxs 预算是否启用标识 1:启用 0:禁用
		if("1".equalsIgnoreCase(strEnabledFlag)){//启用
			List<Map> mergedList = getMergedList(bus);//报销明细行中如果有费用类别相同的行,将发生金额和实报金额分别合并
			if(mergedList!=null){
				String strFormState = bus.getString("curFormState");
				DataMsgBus newBus = new DataMsgBus();
				newBus.copy(bus);
				newBus.remove("list");
				//newBus.send("cLoaneeDeptGUID", newBus.get("cExpDeptGUID"));
				/*2014-04-23 sp08版本中增加费用发生部门列后,需要将报销明细中费用发生部门下的费用类别的预算查询出来(sp08版本再做相应的逻辑处理)
				 * 目前(sp07版本)只是查询表头的报销人部门
				 */
				List<Map> budList = new ArrayList<Map>();
				//String cExpDeptGUID = bus.getString("cExpDeptGUID");//费用报销部门
				for(Map map:mergedList){//遍历报销明细中的费用类别
					Map budMap = new HashMap();
					String cExpenseClassGUID = map.get("cexpenseclassguid")==null?"":map.get("cexpenseclassguid").toString();
					String cCostDeptGUID = map.get("ccostdeptguid")==null?"":map.get("ccostdeptguid").toString();
					budMap.put("cDeptGUID", cCostDeptGUID);
					budMap.put("cExpenseClassGUID", cExpenseClassGUID);
					budList.add(budMap);
				}
				DataMsgBus newBudBus = new DataMsgBus();
				newBudBus.send("deptClassInfo", budList);
				List<Map> budAMTList = getExpenseTypeBudgetAMTByDept(newBudBus);//获取指定部门及费用类别的预算值
				
				//2014-04-23 mxs 循环中出现了连接数据库进行查询结果的操作,后期有时间需要将该段代码重构,将多次数据库查询操作改为一次查询多个结果
				for(Map map:mergedList){
					String cExpenseClassGUID = map.get("cexpenseclassguid")==null?"":map.get("cexpenseclassguid").toString();
					String cCostDeptGUID = map.get("ccostdeptguid")==null?"":map.get("ccostdeptguid").toString();
					newBus.send("cExpenseClassGUID", cExpenseClassGUID);//费用类别
					newBus.send("cLoaneeDeptGUID", cCostDeptGUID);//报销部门
					Map budgetAmtMap = getAvailableBudgetAMT(form,newBus);//获取报销人所在部门,该费用类别可用预算金额
					if(budgetAmtMap != null){
						String strOverageAmount = budgetAmtMap.get("overageAmount")==null?"0":budgetAmtMap.get("overageAmount").toString();
						Double budgetAmt = Double.parseDouble(strOverageAmount);
						
						String cGUID = newBus.getString("cGUID");
						if(cGUID != null && !"".equalsIgnoreCase(cGUID)&&!"".equalsIgnoreCase(cExpenseClassGUID)){
							String sql = "select sum(iOpeningAMT) as iOpeningAMT from OA_ExpenseAccLine line left join OA_ExpenseAccountBill bill on line.cHeadGUID = bill.cguid "
									+ " where cHeadGUID = '"+cGUID+"' and cExpenseClassGUID='"+cExpenseClassGUID+"'"+" and '"+cCostDeptGUID+"' ="
									+ " case when line.cCostDeptGUID is null then bill.cExpDeptGUID else line.cCostDeptGUID end" ;
							String iLoanAMTOld = DbSvr.getDbService(null).getStringResult(sql);	
							if(iLoanAMTOld != null && !"".equals(iLoanAMTOld)) {
								budgetAmt += Double.parseDouble(iLoanAMTOld);
							}
						}
						String iActualAMT = map.get("iactualamt")==null?"0":map.get("iactualamt").toString();//实报金额
						String iLoanAMT = map.get("iopeningamt")==null?"0":map.get("iopeningamt").toString();//发生金额
						String cExpenseClassName = null;
						if(budgetAmtMap.get("cExpenseClassName")!=null){
							cExpenseClassName = "部门为："+budgetAmtMap.get("cDeptName").toString()+"费用类型为: "+budgetAmtMap.get("cExpenseClassName").toString()+"的 ";
						}
						//获取指定部门及费用类别的预算值,如果为0,则认为不进行预算控制
						if(budAMTList!=null){
							for(Map m:budAMTList){
								String code = (String)m.get("code");
								if((cCostDeptGUID+"_"+cExpenseClassGUID).equalsIgnoreCase(code)){//sp07版本是报销人部门_报销明细中费用类别,sp08版需要改为报销明细中费用发生部门_费用类别
									String budAmt = m.get("amount")==null?"0":m.get("amount").toString();
									if(Double.parseDouble(budAmt)!=0){
										validateBudgetAMT(strFormState,Double.parseDouble(iActualAMT),Double.parseDouble(iLoanAMT),budgetAmt,cExpenseClassName+"实报金额已超过预算",cExpenseClassName+"发生金额已超过预算");
									}
								}
							}
						}
					}
				}
			}
		}
	}
	
	/**
	 * 报销明细数据处理
	 * @param bus
	 * @return
	 */
	private static List<Map> getMergedList(DataMsgBus bus){
		String cExpDeptGUID = bus.getString("cExpDeptGUID"); //若子表中费用发生部门为空,则默认使用报销人部门
		List<Map> list = bus.getList("list");//报销明细行
		if(list!=null){
			List<String> expenseId_deptIdList = new ArrayList<String>(); //费用类别_部门 列表
			List<Map> mergedList = new ArrayList<Map>();
			String expenseId_deptId = new String();
			for(Map map:list){
				String strExpenseClassId = map.get("cexpenseclassguid")==null?"":map.get("cexpenseclassguid").toString();//费用类别
				String strCostDeptGUID = map.get("ccostdeptguid")==null?"":map.get("ccostdeptguid").toString();//报销部门
				if(StringUtil.isBlank(strCostDeptGUID)) {
					strCostDeptGUID = cExpDeptGUID;
				}
				if(!"".equalsIgnoreCase(strExpenseClassId) && !"".equalsIgnoreCase(strCostDeptGUID)){
					expenseId_deptId = strExpenseClassId+"_"+strCostDeptGUID;
					if(expenseId_deptIdList.contains(expenseId_deptId)){//报销明细行中存在重复的费用类别
						for(Map mergedMap:mergedList){
							String newExpenseClassId = mergedMap.get("cexpenseclassguid")==null?"":mergedMap.get("cexpenseclassguid").toString();//费用类别
							String newDeptId = mergedMap.get("ccostdeptguid")==null?"":mergedMap.get("ccostdeptguid").toString();//报销部门
							if(expenseId_deptId.equalsIgnoreCase(newExpenseClassId+"_"+newDeptId)){
								String newOpeningAMT = mergedMap.get("iopeningamt")==null?"0":mergedMap.get("iopeningamt").toString();
								String oldOpeningAMT = map.get("iopeningamt")==null?"0":map.get("iopeningamt").toString();
								mergedMap.put("iopeningamt", Double.parseDouble(newOpeningAMT)+Double.parseDouble(oldOpeningAMT));//合并计算发生金额
								String newActualAMT = mergedMap.get("iactualamt")==null?"0":mergedMap.get("iactualamt").toString();
								String oldActualAMT = map.get("iactualamt")==null?"0":map.get("iactualamt").toString();
								mergedMap.put("iactualamt", Double.parseDouble(newActualAMT)+Double.parseDouble(oldActualAMT));//合并计算实报金额
							}
						}
					}else{
						Map tempMap = new HashMap();
						tempMap.put("cexpenseclassguid", strExpenseClassId);
						tempMap.put("ccostdeptguid", strCostDeptGUID);
						tempMap.put("iopeningamt",map.get("iopeningamt"));
						tempMap.put("iactualamt",map.get("iactualamt"));
						mergedList.add(tempMap);
						expenseId_deptIdList.add(expenseId_deptId);
					}
				}
			}
			return mergedList;
		}
		return null;
	}
	
	/**
	 * 
	 * <p>Discription:获取指定部门及费用类别的预算值,如果为0,则认为不进行预算控制</p> 
	 *@param bus
	 *@return
	 */
	private static List<Map> getExpenseTypeBudgetAMTByDept(DataMsgBus bus){
		List<Map> list = bus.getList("deptClassInfo");
		if(list!=null){
			StringBuffer sb = new StringBuffer();
			for(Map m:list){
				sb.append("(bud.cDeptGUID='").append((String)m.get("cDeptGUID")).append("' and bud.cExpenseClassGUID='")
					.append((String)m.get("cExpenseClassGUID")).append(" ' ) or ");
			}
			sb = sb.delete(sb.lastIndexOf("or"), sb.length());
			DbSvr dbsvr = DbSvr.getDbService(null);
			SqlInfo sqlInfo = dbsvr.getSQLInfo("OA_FM_BudegetSearch.searchExpenseTypeBudgetAMTByDept");
			String sql = sqlInfo.getRawSql().replace("#deptClassFilter", sb.toString());
			SqlInfo newSqlInfo = new SqlInfo(sql);
			Eso eso = newSqlInfo.getEso(bus);
			return dbsvr.executeQuery(eso);
		}
		return null;
	}
	
	private static void validateBudgetAMT(String strFormState,Double iActualAMT,Double iLoanAMT, Double iAvailableBudgetAMT,String strCheckedMsg,String strSavedMsg){
		if("chked".equalsIgnoreCase(strFormState) || "chk".equalsIgnoreCase(strFormState)){
			if(iActualAMT > iAvailableBudgetAMT){
				throw new BusinessException(strCheckedMsg);
			}
		} else {
			if(iLoanAMT > iAvailableBudgetAMT){
				throw new BusinessException(strSavedMsg);
			}
		}
	}
	
	private static Map getAvailableBudgetAMT(AbstractForm form, DataMsgBus bus){
		if(!isBudEnabled()){//未启用预算控制
			DbSvr dbsvr = DbSvr.getDbService(null);
			bus.send("cDeptGUID", bus.getString("cLoaneeDeptGUID"));
			String dateString  = "";
			if("chked".equals(bus.get("curFormState"))){
				dateString = bus.getString("dDrawMoneyDate");
			}else{
				dateString = bus.getString("dAppDate");
			}
			if(!("".equals(dateString)) && dateString != null){
				OADateUtil oaDateUtil = new OADateUtil();
				Date dateFrom = oaDateUtil.thisMonth(dateString);
				Date dateTo = oaDateUtil.thisMonthEnd(dateString);
				bus.send("dateFrom", dateFrom);			
				bus.send("dateTo", dateTo);		
			}
			
			Map budgetAmtMap = dbsvr.queryIdFirstRow("OA_FM_BudegetSearch.searchByUnBudget", bus);
			return budgetAmtMap;
		}
		return null;
	}
	
	/**
	 * 是否启用财务预算控制
	 * 
	 * @return
	 */
	public static boolean isBudEnabled() {// FIXME:取预算处的参数
		return DbSvr.getDbService(null).queryIdHasResult("OA_FM_Common.getBudEnabled");
	}
}
