package com.aisino.app.a6.web.dotasklist.list.plugin;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.dotasklist.common.util.DotaskListUtil;
import com.aisino.app.web.report.util.ReportUtil;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.Plugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.db.SqlInfo;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.SessUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetApPaymentListPlugin extends Plugin implements FormCreateListener {
	/**
	 * 应收列表
	 * 2018-10-16
	 */
	private static final long serialVersionUID = 1L;
	@SuppressWarnings("rawtypes")
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		Map<String,Object> returnMap = new HashMap<String, Object>();
		String currUserGUID = SessionHelper.getCurrentUserId();
		String currOrgnID = SessionHelper.getCurrentAdminOrgnId(); 
		DbSvr db = DbSvr.getDbService(null);
		Map preiod = db.getOneRecorder("select * from co_sysinit where csubsyscode='AR'");
		if(preiod==null){
			//未启用应收模块时需要提示
			returnMap.put("errmsg", "未启用应收管理模块");
			form.setReturn(returnMap);
		}else{
			String orgnsql = "select DISTINCT r.cguid from aos_rms_resc r "
						+"	join AOS_RMS_PERM_RESC_REL pr on r.cGUID=pr.cRESC"
						+"	and pr.cPERM in ( select b.cpermid from"
						+"	(select uop.cPermID from AOS_RMS_USER_ORGN_PERM_REL uop where uop.cUserOrgnID="
						+"	(select cguid from AOS_RMS_USER_ORGN_REL uo where uo.cUserID=? and uo.cOrgnID=?)"
						+"	union all  select rp.cPermID"
						+"	from AOS_RMS_USER_ORGN_ROLE_REL uor , AOS_RMS_ROLE_PERM_REL rp"
						+"	where uor.cRoleID=rp.cRoleID and uor.cUserOrgnID="
						+"	(select cguid from AOS_RMS_USER_ORGN_REL uo where uo.cUserID=? and uo.cOrgnID=?))b"
						+"	join AOS_RMS_USERTYPE_PERM_REL_CFG upc on upc.cPermID=b.cPermID"
						+"	join AOS_RMS_USER u on u.cUserType=upc.cUserTypeID and u.cguid=?"
						+"	join AOS_RMS_ORGNTYPE_PERM_REL_CFG opc on opc.cPermID=b.cPermID"
						+"	join ( select * from AOS_ORGN_TYPE_REL union all select o.cGUID as cOrgnID,"
						+"	'ADMIN' as cTypeID from aos_orgn o where o.cGUID = o.cAdminOrgnID) otr on opc.cOrgnTypeID=otr.cTypeID and otr.cOrgnID=?"
						+"	) WHERE r.ckeys = 'bus_ar_report_ar_detail_form' ";
			String orgnflag = db.getStringResult(orgnsql, new Object[]{currUserGUID,currOrgnID,currUserGUID,currOrgnID,currUserGUID,currOrgnID});
			if(orgnflag==null){
				returnMap.put("errmsg", "该用户没有应收款明细表权限");
				form.setReturn(returnMap);
				return;
			}
			//查询条件
			String clockStart = null;
			int past = 0;
			String saleType=bus.getString("saleType")==null?"":bus.get("saleType").toString();;
			//如果saleType为空，即使用查询条件
			if("".equals(saleType)){
				if("0".equals(bus.getString("searchtype"))){
					if("".equals(bus.getString("yearstart"))){
						throw new BusinessException("请输入会计期间开始年。");
					}
					if("".equals(bus.getString("periodstart"))){
						throw new BusinessException("请输入会计期间开始期。");
					}
					if("".equals(bus.getString("yearend"))){
						throw new BusinessException("请输入会计期间结束年。");
					}
					if("".equals(bus.getString("periodend"))){
						throw new BusinessException("请输入会计期间期。");
					}
					//SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					String datestart = db.getStringResult("SELECT dBeginDate FROM GL_Period WHERE iYear = ? AND iMonth = ? AND iLeaf = '1' ", new Object[]{bus.getString("yearstart"),bus.getString("periodstart")});
					String dateend = db.getStringResult("SELECT dEndDate FROM GL_Period WHERE iYear = ? AND iMonth = ? AND iLeaf = '1' ", new Object[]{bus.getString("yearend"),bus.getString("periodend")});
					
					bus.put("startdate", datestart.substring(0, 19));
					bus.put("enddate", dateend.substring(0, 19));
					//期初余额查询字段
					bus.put("iInitDate", datestart.substring(0, 10));
					bus.put("iPreStartdate", preStrardate(datestart));
				}else if("1".equals(bus.getString("searchtype"))){
					if("".equals(bus.getString("startdate")))
						throw new BusinessException("请输入开始日期。");
					if("".equals(bus.getString("enddate")))
						throw new BusinessException("请输入结束日期。。");
					bus.put("startdate", bus.getString("startdate")+" 00:00:00");
					bus.put("enddate", bus.getString("enddate")+" 23:59:59");
					//期初余额查询字段
					bus.put("iInitDate", bus.getString("startdate").substring(0, 8)+"01");
					bus.put("iPreStartdate", preStrardate(bus.getString("startdate")+" 00:00:00"));
				}else{
					throw new BusinessException("查询方案不对，请从新传值。");
				}
					
				if("".equals(bus.getString("ccustguid")))
					bus.put("ccustguid", null);
				if("".equals(bus.getString("cdeptguid")))
					bus.put("cdeptguid", null);
				if("".equals(bus.getString("cempguid")))
					bus.put("cempguid", null);
			}else{
				if("today".equalsIgnoreCase(saleType)){
					past =0;
				}else if("week".equalsIgnoreCase(saleType)){
					past=6;
				}else if("month".equalsIgnoreCase(saleType)){
					past=29; //近30天
				}
				clockStart = DotaskListUtil.getPastDate(past);
				bus.put("startdate", clockStart+" 00:00:00");
				bus.put("enddate", null);
				//期初余额查询字段
				bus.put("iInitDate", clockStart.substring(0, 8)+"01");
				bus.put("iPreStartdate", preStrardate(clockStart+" 00:00:00"));
			}	
			//查询角度
			String searchangle = bus.getString("searchangle")==null?"":bus.getString("searchangle");
			//客户==供应商
			bus.setControlInfo("pt_control_currentpage",bus.getString("curpage"));
			bus.setControlInfo("pt_control_pagesize","100");
			//分页查列表
			List<Map> list = null;
			List<Map> listxj = null;
			String listsql = db.getSQL("mobile_list_ap_payment_sql.ApPaymentList");
			
			if("1".equals(searchangle)){//按部门
				String lsqlh = "SELECT TOP 100 PERCENT ROW_NUMBER () OVER ( ORDER BY cdeptcode,ccurname,dtallykdate ) AS rownum1,tempTable_2.* FROM ( "
						+ "SELECT SUM (CAST (tempTable_1.idebitamt AS NUMERIC (27, 9))) AS idebitamt, "
						+ "SUM (CAST (tempTable_1.icreditamt AS NUMERIC (27, 9))) AS icreditamt,"
						+ "SUM (CAST (tempTable_1.ibalanceamt AS NUMERIC (27, 9))) AS ibalanceamt,"
						+ "tempTable_1.iyear,tempTable_1.iperiod,tempTable_1.dtallykdate,"
						+ "tempTable_1.cdeptcode,tempTable_1.cdeptname,tempTable_1.cdeptguid,"
						+ "tempTable_1.ccurname,tempTable_1.cvoukind,tempTable_1.cvoutypename,tempTable_1.dvoudate,tempTable_1.cvoucode,"
						+ "tempTable_1.csettlename,tempTable_1.cremark,tempTable_1.ccheckcode,tempTable_1.cchecktype,tempTable_1.cvouguid FROM ( ";
						
				String lsqll = " ) tempTable_1 GROUP BY tempTable_1.iyear,tempTable_1.iperiod,tempTable_1.dtallykdate,"
						+ "tempTable_1.cdeptcode,tempTable_1.cdeptname,tempTable_1.cdeptguid,"
						+ "tempTable_1.ccurname,tempTable_1.cvoukind,tempTable_1.cvoutypename,tempTable_1.dvoudate,tempTable_1.cvoucode,"
						+ "tempTable_1.csettlename,tempTable_1.cremark,tempTable_1.ccheckcode,tempTable_1.cchecktype,tempTable_1.cvouguid "
						+ ") tempTable_2 ORDER BY cdeptcode,ccurname,dtallykdate ";
				SqlInfo listinfo = new SqlInfo(lsqlh+listsql+lsqll);
				list = db.getListResultByPage(bus, listinfo);
				
				String xjsqlh = "SELECT SUM (CAST (tempTable_1.idebitamt AS NUMERIC (27, 9))) AS idebitamt,SUM (CAST (tempTable_1.icreditamt AS NUMERIC (27, 9))) AS icreditamt,"
						+ "SUM (CAST (tempTable_1.ibalanceamt AS NUMERIC (27, 9))) AS ibalanceamt,tempTable_1.cdeptcode,tempTable_1.cdeptname FROM ( ";
				String xjsqll = " ) tempTable_1 GROUP BY tempTable_1.cdeptcode,tempTable_1.cdeptname ORDER BY tempTable_1.cdeptcode,tempTable_1.cdeptname";
				String xjsql = xjsqlh+listsql+xjsqll;
				listxj = db.executeQuery(new SqlInfo(xjsql).getEso(bus));
				
			}else if("2".equals(searchangle)){//按职员
				String lsqlh = "SELECT TOP 100 PERCENT ROW_NUMBER () OVER ( ORDER BY cempcode,ccurname,dtallykdate ) AS rownum1,tempTable_2.* FROM ( "
						+ "SELECT SUM (CAST (tempTable_1.idebitamt AS NUMERIC (27, 9))) AS idebitamt, "
						+ "SUM (CAST (tempTable_1.icreditamt AS NUMERIC (27, 9))) AS icreditamt,"
						+ "SUM (CAST (tempTable_1.ibalanceamt AS NUMERIC (27, 9))) AS ibalanceamt,"
						+ "tempTable_1.iyear,tempTable_1.iperiod,tempTable_1.dtallykdate,"
						+ "tempTable_1.cempcode,tempTable_1.cempname,"
						+ "tempTable_1.ccurname,tempTable_1.cvoukind,tempTable_1.cvoutypename,tempTable_1.dvoudate,tempTable_1.cvoucode,"
						+ "tempTable_1.csettlename,tempTable_1.cremark,tempTable_1.ccheckcode,tempTable_1.cchecktype,tempTable_1.cvouguid FROM ( ";
						
				String lsqll = " ) tempTable_1 GROUP BY tempTable_1.iyear,tempTable_1.iperiod,tempTable_1.dtallykdate,"
						+ "tempTable_1.cempcode,tempTable_1.cempname,"
						+ "tempTable_1.ccurname,tempTable_1.cvoukind,tempTable_1.cvoutypename,tempTable_1.dvoudate,tempTable_1.cvoucode,"
						+ "tempTable_1.csettlename,tempTable_1.cremark,tempTable_1.ccheckcode,tempTable_1.cchecktype,tempTable_1.cvouguid "
						+ ") tempTable_2 ORDER BY cempcode,ccurname,dtallykdate ";
				SqlInfo listinfo = new SqlInfo(lsqlh+listsql+lsqll);
				list = db.getListResultByPage(bus, listinfo);
				
				String xjsqlh = "SELECT SUM (CAST (tempTable_1.idebitamt AS NUMERIC (27, 9))) AS idebitamt,SUM (CAST (tempTable_1.icreditamt AS NUMERIC (27, 9))) AS icreditamt,"
						+ "SUM (CAST (tempTable_1.ibalanceamt AS NUMERIC (27, 9))) AS ibalanceamt,tempTable_1.cempcode,tempTable_1.cempname FROM ( ";
				String xjsqll = " ) tempTable_1 GROUP BY tempTable_1.cempcode,tempTable_1.cempname ORDER BY tempTable_1.cempcode,tempTable_1.cempname";
				String xjsql = xjsqlh+listsql+xjsqll;
				listxj = db.executeQuery(new SqlInfo(xjsql).getEso(bus));
				
			}else if("3".equals(searchangle)){//按项目
				String lsqlh = "SELECT TOP 100 PERCENT ROW_NUMBER () OVER ( ORDER BY citemname,ccurname,dtallykdate ) AS rownum1,tempTable_2.* FROM ( "
						+ "SELECT SUM (CAST (tempTable_1.idebitamt AS NUMERIC (27, 9))) AS idebitamt, "
						+ "SUM (CAST (tempTable_1.icreditamt AS NUMERIC (27, 9))) AS icreditamt,"
						+ "SUM (CAST (tempTable_1.ibalanceamt AS NUMERIC (27, 9))) AS ibalanceamt,"
						+ "tempTable_1.iyear,tempTable_1.iperiod,tempTable_1.dtallykdate,"
						+ "tempTable_1.citemname,"
						+ "tempTable_1.ccurname,tempTable_1.cvoukind,tempTable_1.cvoutypename,tempTable_1.dvoudate,tempTable_1.cvoucode,"
						+ "tempTable_1.csettlename,tempTable_1.cremark,tempTable_1.ccheckcode,tempTable_1.cchecktype,tempTable_1.cvouguid FROM ( ";
						
				String lsqll = " ) tempTable_1 GROUP BY tempTable_1.iyear,tempTable_1.iperiod,tempTable_1.dtallykdate,"
						+ "tempTable_1.citemname,"
						+ "tempTable_1.ccurname,tempTable_1.cvoukind,tempTable_1.cvoutypename,tempTable_1.dvoudate,tempTable_1.cvoucode,"
						+ "tempTable_1.csettlename,tempTable_1.cremark,tempTable_1.ccheckcode,tempTable_1.cchecktype,tempTable_1.cvouguid "
						+ ") tempTable_2 ORDER BY citemname,ccurname,dtallykdate ";
				SqlInfo listinfo = new SqlInfo(lsqlh+listsql+lsqll);
				list = db.getListResultByPage(bus, listinfo);
				
				String xjsqlh = "SELECT SUM (CAST (tempTable_1.idebitamt AS NUMERIC (27, 9))) AS idebitamt,SUM (CAST (tempTable_1.icreditamt AS NUMERIC (27, 9))) AS icreditamt,"
						+ "SUM (CAST (tempTable_1.ibalanceamt AS NUMERIC (27, 9))) AS ibalanceamt,tempTable_1.citemname FROM ( ";
				String xjsqll = " ) tempTable_1 GROUP BY tempTable_1.citemname ORDER BY tempTable_1.citemname ";
				String xjsql = xjsqlh+listsql+xjsqll;
				listxj = db.executeQuery(new SqlInfo(xjsql).getEso(bus));
				
			}else{//（默认）按客户				
				String lsqlh = "SELECT TOP 100 PERCENT ROW_NUMBER () OVER ( ORDER BY csupcode,ccurname,dtallykdate ) AS rownum1,tempTable_2.* FROM ( "
						+ "SELECT SUM (CAST (tempTable_1.idebitamt AS NUMERIC (27, 9))) AS idebitamt, SUM (CAST (tempTable_1.icreditamt AS NUMERIC (27, 9))) AS icreditamt,"
						+ "SUM (CAST (tempTable_1.ibalanceamt AS NUMERIC (27, 9))) AS ibalanceamt,tempTable_1.iyear,tempTable_1.iperiod,tempTable_1.dtallykdate,"
						+ "tempTable_1.csupclass,tempTable_1.csupcode,tempTable_1.csupname,tempTable_1.ccurname,tempTable_1.cvoukind,tempTable_1.cvoutypename,tempTable_1.cvouguid,"
						+ "tempTable_1.dvoudate,tempTable_1.cvoucode,tempTable_1.csettlename,tempTable_1.cremark,tempTable_1.ccheckcode,tempTable_1.cchecktype,"
						+ "tempTable_1.csupguid FROM ( ";
				
				String lsqll = " ) tempTable_1 GROUP BY tempTable_1.iyear,tempTable_1.iperiod,tempTable_1.dtallykdate,tempTable_1.csupclass,tempTable_1.csupcode,"
						+ "tempTable_1.csupname,tempTable_1.ccurname,tempTable_1.cvoukind,tempTable_1.cvoutypename,tempTable_1.dvoudate,tempTable_1.cvoucode,"
						+ "tempTable_1.csettlename,tempTable_1.cremark,tempTable_1.ccheckcode,tempTable_1.cchecktype,tempTable_1.cvouguid,tempTable_1.csupguid "
						+ ") tempTable_2 ORDER BY csupcode,ccurname,dtallykdate ";
				SqlInfo listinfo = new SqlInfo(lsqlh+listsql+lsqll);
				list = db.getListResultByPage(bus, listinfo);
				
				String xjsqlh = "SELECT SUM (CAST (tempTable_1.idebitamt AS NUMERIC (27, 9))) AS idebitamt,SUM (CAST (tempTable_1.icreditamt AS NUMERIC (27, 9))) AS icreditamt,"
						+ "SUM (CAST (tempTable_1.ibalanceamt AS NUMERIC (27, 9))) AS ibalanceamt,tempTable_1.csupcode,tempTable_1.csupname FROM (";
				String xjsqll = " ) tempTable_1 GROUP BY tempTable_1.csupcode,tempTable_1.csupname ORDER BY tempTable_1.csupcode,tempTable_1.csupname";
				String xjsql = xjsqlh+listsql+xjsqll;
				listxj = db.executeQuery(new SqlInfo(xjsql).getEso(bus));
				
			}
			if(list!=null){
				boolean addflag = this.bottomAddsum(list,listxj,searchangle);
				String initbalance = "0.00";
				String rowbalance = "0.00";
				//分角度查询判断客户，部门，职员，项目是否一致
				String angstr = "";
				if("1".equals(searchangle)){//部门deptcode
					angstr = "cdeptcode";
				}else if("2".equals(searchangle)){//职员
					angstr = "cempcode";
				}else if("3".equals(searchangle)){//项目
					angstr = "citemname";
				}else{
					angstr = "csupcode";
				}
				for(int i=0;i<list.size();i++){
					Map m = list.get(i);
					String angcode = m.get(angstr)==null?"":m.get(angstr).toString();
					if("0".equals(m.get("iperiod").toString())){
						if("0.00".equals(num2string(m.get("ibalanceamt")))){
							list.remove(m);
							i--;
						}else{
							dealretmap(m,searchangle);
							if(i<list.size()-1 && angcode.equals(list.get(i+1).get(angstr)==null?"":list.get(i+1).get(angstr).toString())){
								rowbalance = m.get("balabce").toString();
							}else{
								list.add(i+1, this.addxjh(listxj, searchangle, angcode));
								i=i+1;
							}
						}
						continue;
					}
					
					// 余额计算需要用公式计算
					if(!"0".equals(m.get("iperiod").toString())){
						String rbal = countrowbalance(rowbalance,num2string(m.get("idebitamt")),num2string(m.get("icreditamt")));
						rowbalance = rbal;
						m.put("ibalanceamt", rbal);
						dealretmap(m,searchangle);
					}
					
					if(i<list.size()-1){
						String nextangcode = list.get(i+1).get(angstr)==null?"":list.get(i+1).get(angstr).toString();
						if(!angcode.equals(nextangcode)){
							//客户/部门等改变时，添加对应客户/部门的小计行
							list.add(i+1, this.addxjh(listxj, searchangle, angcode));
							i=i+1;
							rowbalance = "0.00";
						}
					}else{//list最后一行时，判断是否在本行后添加合计行
						if(addflag){
							list.add(i+1, this.addxjh(listxj, searchangle, angcode));
							i=i+1;//不能删，退出循环						
						}
					}
					
				}
			}
			
			/* 应收、实收、余额 总金额的查询 */
			String countsqlh = "SELECT SUM (CAST (tempTable_1.idebitamt AS NUMERIC (27, 9))) AS idebitamt,SUM (CAST (tempTable_1.icreditamt AS NUMERIC (27, 9))) AS icreditamt,"
					+ "SUM (CAST (tempTable_1.ibalanceamt AS NUMERIC (27, 9))) AS ibalanceamt FROM (";
			String countsqll = " ) tempTable_1 ";
			String countsql = countsqlh+listsql+countsqll;
			List<Map> listcount = db.executeQuery(new SqlInfo(countsql).getEso(bus));
			
			//组装前台返回map
			Map map = new HashMap();
			map.put("list", list);
			map.put("debitamtsum", num2string(listcount.get(0).get("idebitamt")));
			map.put("creditamtsum", num2string(listcount.get(0).get("icreditamt")));
			String balacesum = countrowbalance(num2string(listcount.get(0).get("ibalanceamt")), num2string(listcount.get(0).get("idebitamt")), num2string(listcount.get(0).get("icreditamt")));	
			map.put("balabcesum", balacesum);
			form.setReturn(map);
		}
		
	}
	
	//公式计算余额
	public String countrowbalance(String initbal, String rdebit, String rcredit){
		BigDecimal biginitys = new BigDecimal(initbal.replace(",", ""));
		BigDecimal bigrowys = new BigDecimal(rdebit.replace(",", ""));
		BigDecimal bigrowss = new BigDecimal(rcredit.replace(",", ""));
		BigDecimal bigys = biginitys.add(bigrowys);
		BigDecimal bigrbal = bigys.subtract(bigrowss);
		return fmtMicrometer(bigrbal.toString());
	}
	
	//计算差
	public String countbalabce(String ys,String ss){
		BigDecimal bigys = new BigDecimal(ys.replace(",", ""));
		BigDecimal bigss = new BigDecimal(ss.replace(",", ""));
		BigDecimal bigye = bigys.subtract(bigss);
		return fmtMicrometer(bigye.toString());
	}
	
	//添加小计行
	public Map addxjh(List<Map> listxj,String searchangle,String custcode){
		Map xjmap = new HashMap();
		for(Map xjm : listxj){
			if("1".equals(searchangle)){
				if(custcode.equals(xjm.get("cdeptcode")==null?"":xjm.get("cdeptcode").toString())){
					xjmap.put("invoicetype", "部门小计");
					xjmap.put("custcode", xjm.get("cdeptcode"));
					xjmap.put("custname", xjm.get("cdeptname"));
					xjmap.put("closeflag", "1");
					xjmap.put("debitamt", num2string(xjm.get("idebitamt")));
					xjmap.put("creditamt", num2string(xjm.get("icreditamt")));
					String balabce = countrowbalance(num2string(xjm.get("ibalanceamt")), num2string(xjm.get("idebitamt")), num2string(xjm.get("icreditamt")));
					xjmap.put("balabce", balabce);
					break;
				}
			}else if("2".equals(searchangle)){
				if(custcode.equals(xjm.get("cempcode")==null?"":xjm.get("cempcode").toString())){
					xjmap.put("invoicetype", "职员小计");
					xjmap.put("custcode", xjm.get("cempcode"));
					xjmap.put("custname", xjm.get("cempname"));
					xjmap.put("closeflag", "1");
					xjmap.put("debitamt", num2string(xjm.get("idebitamt")));
					xjmap.put("creditamt", num2string(xjm.get("icreditamt")));
					String balabce = countrowbalance(num2string(xjm.get("ibalanceamt")), num2string(xjm.get("idebitamt")), num2string(xjm.get("icreditamt")));
					xjmap.put("balabce", balabce);
					break;
				}
			}else if("3".equals(searchangle)){
				if(custcode.equals(xjm.get("citemname")==null?"":xjm.get("citemname").toString())){
					xjmap.put("invoicetype", "项目小计");
					xjmap.put("custcode", xjm.get("citemcode"));
					xjmap.put("custname", xjm.get("citemname"));
					xjmap.put("closeflag", "1");
					xjmap.put("debitamt", num2string(xjm.get("idebitamt")));
					xjmap.put("creditamt", num2string(xjm.get("icreditamt")));
					String balabce = countrowbalance(num2string(xjm.get("ibalanceamt")), num2string(xjm.get("idebitamt")), num2string(xjm.get("icreditamt")));
					xjmap.put("balabce", balabce);
					break;
				}
			}else{
				if(custcode.equals(xjm.get("csupcode")==null?"":xjm.get("csupcode").toString())){
					xjmap.put("invoicetype", "客户小计");
					xjmap.put("custcode", xjm.get("csupcode").toString());
					xjmap.put("custname", xjm.get("csupname").toString());
					xjmap.put("closeflag", "1");
					xjmap.put("debitamt", num2string(xjm.get("idebitamt")));
					xjmap.put("creditamt", num2string(xjm.get("icreditamt")));
					String balabce = countrowbalance(num2string(xjm.get("ibalanceamt")), num2string(xjm.get("idebitamt")), num2string(xjm.get("icreditamt")));
					xjmap.put("balabce", balabce);
					break;
					//CollectionUtil.getStringFromMap(map, key);
				}
			}
			
		}
		return xjmap;
	}
	
	//计算同一客户/部门/职员/项目 当页应收，实收合计，判断是否在最后一行后添加合计
	public boolean bottomAddsum(List<Map> list, List<Map> listxj, String searchangle){
		boolean addflag = false;
		String codestr = "";
		BigDecimal yssum = new BigDecimal("0.00");
		BigDecimal sssum = new BigDecimal("0.00");
		if("1".equals(searchangle)){//部门deptcode
			codestr = "cdeptcode";
		}else if("2".equals(searchangle)){//职员
			codestr = "cempcode";
		}else if("3".equals(searchangle)){//项目
			codestr = "citemname";
		}else{//客户
			codestr = "csupcode";
		}
		//长度不足100表尾必须加小计
		if(list.size()<100){
			addflag=true;
			return addflag;
		}
		
		String anglecode = list.get(list.size()-1).get(codestr)==null?"":list.get(list.size()-1).get(codestr).toString();
		
		for(int i=list.size();i>0;i--){
			Map listm = list.get(i-1);
			if(anglecode.equals(listm.get(codestr)==null?"":listm.get(codestr).toString())){
				//是否考虑余额相等
				BigDecimal rowys = new BigDecimal(num2string(listm.get("idebitamt")).replace(",", ""));
				BigDecimal rowss = new BigDecimal(num2string(listm.get("icreditamt")).replace(",", ""));
				yssum = yssum.add(rowys);
				sssum = sssum.add(rowss);
				if(i==1){//当前页只有一个客户/部门...
					for(Map xjm : listxj){
						if(anglecode.equals(xjm.get(codestr)==null?"":xjm.get(codestr).toString())){
							BigDecimal xjys = new BigDecimal(num2string(xjm.get("idebitamt")).replace(",", ""));
							BigDecimal xjss = new BigDecimal(num2string(xjm.get("icreditamt")).replace(",", ""));
							if(yssum.compareTo(xjys)==0 && sssum.compareTo(xjss)==0){
								addflag = true;
							}
							break;
						}
					}
				}
			}else{//当前页不止一个客户/部门...
				for(Map xjm : listxj){
					if(anglecode.equals(xjm.get(codestr)==null?"":xjm.get(codestr).toString())){
						BigDecimal xjys = new BigDecimal(num2string(xjm.get("idebitamt")).replace(",", ""));
						BigDecimal xjss = new BigDecimal(num2string(xjm.get("icreditamt")).replace(",", ""));
						if(yssum.compareTo(xjys)==0 && sssum.compareTo(xjss)==0){
							addflag = true;
						}
						break;
					}
				}
				
			}
		}
		
		return addflag;
	}
	
	//显示开始日期的前一秒
	public String preStrardate(String datestring){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String preStart = "";
		try {
			Date date = sdf.parse(datestring);
			Calendar c = new GregorianCalendar();
			c.setTime(date);
			c.add(Calendar.SECOND, -1);
			date = c.getTime();
			preStart = sdf.format(date);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return preStart;
	}
	
	//处理查出来的数据0E-9,null,小数位
	public String num2string(Object num) {
		String rtn = "0.00";
		if(num==null || "".equals(num)){
			rtn = "0.00";
		}else if("0E-9".equals(num.toString())){
			rtn = "0.00";
		}else{
			String numstr = num.toString().replace(",", "");
			rtn = fmtMicrometer(numstr.substring(0, numstr.indexOf(".")+3));
		}
		
		return rtn;
		
	}
	
	//处理返回map
	public void dealretmap(Map m ,String searchangle){		
		if("0".equals(m.get("iperiod").toString())){
			m.put("invoicetype", "期初余额");
			m.put("debitamt", "0.0");
			m.put("creditamt", "0.0");
			m.put("balabce", num2string(m.get("ibalanceamt")));
			m.put("curtype", m.get("ccurname"));
			m.put("closeflag", "1");
			if("1".equals(searchangle)){
				m.put("custcode", m.get("cdeptcode"));
				m.put("custname", m.get("cdeptname"));
			}else if("2".equals(searchangle)){
				m.put("custcode", m.get("cempcode"));
				m.put("custname", m.get("cempname"));
			}else if("3".equals(searchangle)){
				m.put("custcode", m.get("citemcode"));
				m.put("custname", m.get("citemname"));
			}else{
				m.put("custcode", m.get("csupcode"));
				m.put("custname", m.get("csupname"));
			}
		}else{
			m.put("invoicetype", m.get("cvoutypename"));
			m.put("cbillcode", m.get("cvoucode"));
			m.put("debitamt", num2string(m.get("idebitamt")));
			m.put("creditamt", num2string(m.get("icreditamt")));
			m.put("balabce", num2string(m.get("ibalanceamt")));
			m.put("curtype", m.get("ccurname"));
			m.put("ddate", m.get("dvoudate"));
			m.put("hidden_cguid", m.get("cvouguid"));
			m.put("settletype", m.get("csettlename"));
			m.put("closeflag", "0");
			if("1".equals(searchangle)){
				m.put("custcode", m.get("cdeptcode"));
				m.put("custname", m.get("cdeptname"));
			}else if("2".equals(searchangle)){
				m.put("custcode", m.get("cempcode"));
				m.put("custname", m.get("cempname"));
			}else if("3".equals(searchangle)){
				m.put("custcode", m.get("citemcode"));
				m.put("custname", m.get("citemname"));
			}else{
				m.put("custcode", m.get("csupcode"));
				m.put("custname", m.get("csupname"));
			}
		}
	}
	
	//格式化数字为千分位显示 
	public static String fmtMicrometer(String text){  
	    DecimalFormat df = null;  
	    if(text.indexOf(".") > 0){
	        if(text.length() - text.indexOf(".")-1 == 0){  
	            df = new DecimalFormat("###,##0.");  
	        }else if(text.length() - text.indexOf(".")-1 == 1) {  
	            df = new DecimalFormat("###,##0.0");  
	        }else{  
	            df = new DecimalFormat("###,##0.00");  
	        }  
	    }else{
	        df = new DecimalFormat("###,##0");  
	    }  
	    double number = 0.0;  
	    try {  
	         number = Double.parseDouble(text);  
	    }catch (Exception e) {  
	        number = 0.0;  
	    }  
	    return df.format(number);  
	} 
	
	@Override
	public void setValue(String name, String value) {
		
	}

}
