package com.aisino.app.a6.web.dotasklist.yiban.service;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.aisino.aos.mobile.vo.ApproveBillField;
import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.dotasklist.common.plugin.BillDesignPlugin;
import com.aisino.app.web.dotasklist.daiban.plugin.DaibanUtil;
import com.aisino.app.web.notice.InsertCheckNoticePlugin;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.MS;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.JsUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;

public class YibanSAStkoutService  implements IService{
	@Override
	public Object doService(Object... param) {	
		String action = (String) param[0];
		String cguid=(String) param[1];
		DbSvr db=DbSvr.getDbService(null);	
		if("browse".equals(action)){			
			String cBusinessCode ="076";   
			Map m= BillDesignPlugin.getBill(cBusinessCode,cguid);   //调用接口传该单据的formid
			List<Map> main=(List<Map>) m.get("maindata");
			List<Map> detail=(List<Map>) m.get("detaildata");  //根据单据设计，获取将要显示的字段及对应的值
			
			//1.0版本要显示的字段（目的是：将千分位、或单据设置显示不出来的等特殊处理的字段加到单据设计的结果集中）
			//sql查出的字段必须都是要特殊处理的，其它不需处理的无需查出来；
			//sql中as的部分要与单据设计中段代码一致，另单据明细行sql必须要查出cguid
			List<Map> mainTemp =db.queryIdForList("mobile_daiban_pubill.puordermain", new Object[] {cguid}); 
			List<Map> detailTemp =db.queryIdForList("mobile_daiban_pubill.puorderdetail", new Object[] {cguid}); 
			
			//金额itotal权限
			boolean notHas=db.queryIdHasResult("mobile_common.checkIamtPUorder",SessionHelper.getCurrentUserId());
			Map map = new HashMap();
			
			//主表（将单据设置显示不出来的字段加到单据设计的结果集中,没有查看金额权限的，金额置空）
			if( main!=null&&!main.isEmpty()){
				Map jem = new HashMap();
				if(!notHas){									
					String je =(String) mainTemp.get(0).get("采购总金额");
					jem.put("name","采购总金额");
					jem.put("value",je);
					jem.put("code","pu_order.je");
					main.add(jem);					
				}
				map.put("main", main);	
			}
		
			
			//子表
			/*注意事项：
			 * a：没有特殊要处理的字段，不调用detailProcess该函数即可
			 * b: detailProcess第一个参数是单据设计结果集；第二个参数是1.0版本的sql部分
			 */
			if(detail!=null&&!detail.isEmpty()){
				detail=BillDesignPlugin.detailProcess(detail,detailTemp);//子表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("detail", detail);
			}
			
			//附件
			List<Map> files = db.queryIdForList("mobile_common.getFiles", new Object[] {cguid});
			if(CollectionUtil.isEmpty(main)||CollectionUtil.isEmpty(detail))
				 throw new BusinessException("并发删除,请重新查询");
			//附件权限
			String fileKey = "business_pu_puorder_billform.viewfile"; 
			boolean hasfiledown= SecurityService.rescCheck(fileKey); 
			if(!hasfiledown&&CollectionUtil.isNotEmpty(files)){
				Map m2=new HashMap();
				//此时app不允许下载附件
				m2.put("hasfiledown", "false");
				map.putAll(m2);
			}			
			if(files!=null&&!files.isEmpty())
		           map.put("file", files);
			return map;
		}else if("submit".equals(action)){					
			DataMsgBus nbus = new DataMsgBus();  
		    String firstSubmit="true";
		    if((String) param[2]!=null)
		    	firstSubmit=(String) param[2];
            List<Map>  main=new  ArrayList<Map>();
		    main=db.queryIdForList("mobile_daiban_pubill.puordermcheck", cguid);
		    if (CollectionUtil.isEmpty(main))
				 throw new BusinessException("单据不存在，可能已被并发删除");
		    Map ma=new HashMap<String,String>();
		    ma=main.get(0);
		    String cCreatorGUID=(String)ma.get("cCreatorGUID");
		    String str,q="";
	    	for(int i=0;i<ma.size();i++){
	    		str=ma.keySet().toArray()[i].toString();
	    		if(ma.get(str)!=null){
	    			q=ma.get(str).toString();
	    			nbus.send(str,q);
	    		}else{
	    			nbus.send(str,null);
	    		}
	    	}
	    	List<Map> list=new  ArrayList<Map>();
	    	list=db.queryIdForList("mobile_daiban_pubill.puorderdcheck", cguid);
	    	nbus.send("list", list);	
	    	nbus.send("billcguid", cguid);	
	    	String msg=null;
	        if(firstSubmit.equals("true")){
	        	msg=Check("business_pu_puorder_billform",nbus,"beforeCheck",cCreatorGUID);
	        	if(msg!=null){
	        		return msg;
	        	}
	        }
	     	msg=Check("business_pu_puorder_billform",nbus,"docheck",cCreatorGUID);
  		    DaibanUtil.updateCheckWay("PU_ORDER",cguid);
	    	return msg;
	    	
		}else  if("beforeWFAgree".equals(action)){//处理工作流审批的beforecheck
			DataMsgBus nbus = new DataMsgBus();  
		    String firstSubmit="true";
		    if((String) param[2]!=null)
		    	firstSubmit=(String) param[2];
            List<Map>  main=new  ArrayList<Map>();
		    main=db.queryIdForList("mobile_daiban_pubill.puordermcheck", cguid);
		    if (CollectionUtil.isEmpty(main))
				 throw new BusinessException("单据不存在，可能已被并发删除");
		    Map ma=new HashMap<String,String>();
		    ma=main.get(0);
		    String cCreatorGUID=(String)ma.get("cCreatorGUID");
		    String str,q="";
	    	for(int i=0;i<ma.size();i++){
	    		str=ma.keySet().toArray()[i].toString();
	    		if(ma.get(str)!=null){
	    			q=ma.get(str).toString();
	    			nbus.send(str,q);
	    		}else{
	    			nbus.send(str,null);
	    		}
	    	}
	    	List<Map> list=new  ArrayList<Map>();
	    	list=db.queryIdForList("mobile_daiban_pubill.puorderdcheck", cguid);
	    	nbus.send("list", list);	
	    	nbus.send("billcguid", cguid);	
	    	nbus.send("isworkflow",true);
	    	String msg=null;
	        if(firstSubmit.equals("true"))
	        msg=Check("business_pu_puorder_billform",nbus,"beforeCheck",cCreatorGUID);
	        return msg;
	        }else{
			   return null;
		    }
	}
	
	public String Check(String formid,DataMsgBus bus,String action,String cCreatorGUID){
		Map msg=new HashMap();
		String cbillcode=bus.getString("ccode");
		Object objMsg=null;
		String confirmmsg=null;
    	try {
    		 bus.setNewAction(action);
    		 //返回信息：采购单据的beforeCheck校验：包括采购最高最低进价控制。
    		/**此处跳出权限校验。否则会抛异常“没有[采购订单]功能的权限，不能进行操作”
    		 * 因为PC端的工作流审核不会考虑单据以及审核权限之类的**/
	    	if(bus.getBoolean("isworkflow", false)){
	    		Set set = new HashSet();
	 		    set.add("acs");
	 		    set.add("pt_sql_filter");
	 		    MS m2 = new MS("ACS.SessService");
	 		    m2.doService(new Object[] { "putRescInSession", formid });
	    	}
    		 objMsg=AbstractForm.callFormSubmitService(formid,bus,false);
    		 if(objMsg==null||objMsg=="")
    			 return null;
				if(objMsg instanceof Map) {
					Map map=(Map) objMsg;
					//当有这些异常时候才返回
					if(map!=null && map.get("msg")!=null && map.get("msg")!=""){
						Map m=getReturnBeforeCheck(map);
						String savemsg=null;
						if(StringUtil.isNotBlank((String) m.get("errmsg"))){
							savemsg=m.get("errmsg").toString();
						}
						//存储审批失败信息到PT_Notice
						if(savemsg!=null)
						new  InsertCheckNoticePlugin().InsertCheckNotice(cCreatorGUID,"单据号"+cbillcode+"："+savemsg);
						msg.putAll(m);
					}else{
						return null;
					}
				}
		} catch (Exception e) {
			DbSvr db=DbSvr.getDbService(null);	
			//回滚审核状态等等
			db.rollback();
			//存储审批失败信息到PT_Notice
			new  InsertCheckNoticePlugin().InsertCheckNotice(cCreatorGUID,e.getMessage());
			throw new  BusinessException(JsUtil.toJsonObjectWithNull("单据号"+cbillcode+"："+e.getMessage()));
				
		}
			return  JsUtil.toJsonObjectWithNull(msg).toString();
    	   
	}
	

	
	//beforecheck的返回值的处理
	public Map getReturnBeforeCheck(Map map){
		String msg="";
		Map ma=new HashMap();
		
		String rtnmsg=map.get("msg")==null?"":map.get("msg").toString();
		String isStrict=map.get("isStrict")==null?"":map.get("isStrict").toString();
		String superRight=map.get("superRight")==null?"":map.get("superRight").toString();
		
		if(!"".equals(rtnmsg)){
			msg="存在物品的价格低于或高于进价控制。";
		}
		
		if(!"".equals(isStrict) &&"n".equals(isStrict)){  //不严格控制
			ma.put("msg", msg+" 要继续审核吗？");
			ma.put("confirm", true);
		}else if("y".equals(isStrict)){ //严格控制
			if(!"".equals(superRight)){  //审核密码不为空
				ma.put("msg", msg+" 要继续审核吗？");
				ma.put("passWordConfirm", true);
				ma.put("password", superRight);	
			}else{
				ma.put("errmsg", msg+"不允许审核！");
			}
		}
		
		return ma;
		
	}


}
