package com.aisino.app.a6.web.dotasklist.common.plugin;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import com.aisino.aos.preference.common.Prefer;
import com.aisino.app.web.notice.InsertCheckNoticePlugin;
import com.aisino.platform.core.MS;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.JsUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;


/**
 * 销售模块保存审核校验
 * @since 2018-09-12
 */
public class SaCheckPlugin {

	public String Check(String formid,DataMsgBus bus,String action,String actiontype){
		//actiontype:为审核或保存，即为两个检查时点
		Map msg=new HashMap();
		String cbillcode=bus.getString("cBillCode");
		String cCreatorGUID=bus.getString("cCreatorGUID");
		String nextflagStr=bus.getString("flagStr");
		Object objMsg=null;
		String confirmmsg=null;
    	try {
    		 bus.setNewAction(action);
    		 //返回信息：销售单据的beforeCheck校验：包括最低售价，最高售价，销售折扣等。
    		 //零售单没有信用控制
    		 /**此处跳出权限校验。否则会抛异常“没有[零售单]功能的权限，不能进行操作”
     		 * 因为PC端的工作流审核不会考虑单据以及审核权限之类的**/
 	    	if(bus.getBoolean("isworkflow", false)){
 	    		Set set = new HashSet();
 	 		    set.add("acs");
 	 		    set.add("pt_sql_filter");
 	 		    MS m2 = new MS("ACS.SessService");
 	 		    m2.doService(new Object[] { "putRescInSession", formid });
 	    	}
    		 objMsg=AbstractForm.callFormSubmitService(formid,bus,false);
				if(objMsg instanceof Map) {
					Map map=(Map) objMsg;
					//当有这些异常时候才返回
					if(map.containsKey("msglow")||map.containsKey("msghigh")||map.containsKey("msgicostprice")||map.containsKey("msgcust")){
						map.put("nextflagStr", nextflagStr);
						Map m=getReturnBeforeCheck(map,actiontype);
						String savemsg=null;
						if(StringUtil.isNotBlank((String) m.get("errmsg"))){
							savemsg=m.get("errmsg").toString();
						}
						//存储审批失败信息到PT_Notice
						if(savemsg!=null)
						new  InsertCheckNoticePlugin().InsertCheckNotice(cCreatorGUID,"单据号"+cbillcode+"："+savemsg);
						msg.putAll(m);
					}else  if(map.containsKey("controltype")&&map.containsKey("msg")){
						/*销售折扣单独控制 2017年3月6日 19:17:38 */
						String flagStr="";
						if(StringUtil.isNotBlank((String) map.get("msg"))){
							flagStr =getdisauth_sa_check(map);
							new  InsertCheckNoticePlugin().InsertCheckNotice(cCreatorGUID,"单据号"+cbillcode+"："+"物品倒扣率超出限制");
						}
						return flagStr;
					}else{
						return null;
					}
				}
		} catch (Exception e) {
			DbSvr db=DbSvr.getDbService(null);	
			//回滚审核状态等等
			db.rollback();
			//存储审批失败信息到PT_Notice
			new  InsertCheckNoticePlugin().InsertCheckNotice(cCreatorGUID,e.getMessage());
			if(action.equals("docheck")){
				String emsg=getExceptiondoCheck(e.getMessage(),cbillcode);
				msg.put("errmsg",emsg);
				throw new  BusinessException(emsg);
			}
			else{
				msg.put("errmsg",e.getMessage());
				throw new  BusinessException("单据号"+cbillcode+"："+e.getMessage());
			}		
		}
			return  JsUtil.toJsonObjectWithNull(msg).toString();
    	   
	}
	
	
	
	
	public Map getReturnBeforeCheck(Map map,String actiontype){
		String nextflagStr=map.get("nextflagStr")==null?"":map.get("nextflagStr").toString();
		String msg="";
		if(StringUtil.isNotBlank(nextflagStr)){
			msg="物品倒扣率超出限制;";
		}
		Map ma=new HashMap(); 
		boolean isStrict=false;
		if(map.get("msglow")!=null){
			msg="物品销售价低于最低售价;";
			if(map.get("isStrictlow").equals("3"))
				isStrict=true;
		}
		if(map.get("msghigh")!=null){
			msg=msg+"物品销售价超出最高售价;";
			if(map.get("isStricthigh").equals("3"))
				isStrict=true;
		}
		if(map.get("msgicostprice")!=null){
			msg=msg+"物品无税价低于成本价;";
			if(map.get("isStricticostprice").equals("3"))
			    isStrict=true;
		}
		if(map.get("msgcust")!=null){
			msg=msg+"客户信用超出控制;";
			if(map.get("isStrictcust").equals("3"))
			    isStrict=true;
		}
		if(msg.charAt(msg.length()-1)==';')
			msg=msg.substring(0, msg.length()-1);
		if("yange".equals(nextflagStr)){
			isStrict=true;
		}
			
		//严格控制
		if(isStrict){
			ma.put("errmsg",actiontype+"失败："+msg);
			return ma;
		}
		else{
			//提示控制
			boolean confirm=false;
			//弹出授权密码
			boolean passWordConfirm=false;   
			if(map.get("isStrictlow")!=null&&map.get("isStrictlow").equals("2")&&map.get("msglow")!=null){
				passWordConfirm=true;
			}else if(map.get("isStricthigh")!=null&&map.get("isStricthigh").equals("2")&&map.get("msghigh")!=null){
				passWordConfirm=true;
			}else if(map.get("isStricticostprice")!=null&&map.get("isStricticostprice").equals("2")&&map.get("msgicostprice")!=null){
				passWordConfirm=true;
			}else if(map.get("isStrictcust")!=null&&map.get("isStrictcust").equals("2")&&map.get("msgcust")!=null){
				passWordConfirm=true;
			}else if("password".equals(nextflagStr))
				passWordConfirm=true;			
			else{
				confirm=true;
			}
			ma.put("msg", msg+"    要继续"+actiontype+"吗？");
			if(confirm){
				ma.put("confirm", true);
				return ma;
			}else if(passWordConfirm){
				ma.put("passWordConfirm", true);
				ma.put("password", Prefer.get("U3_SA_SuperRight"));
				return ma;
			}else{
				return ma;
			}
		}
		
	}
	
	
	
	
	public String  getdisauth_sa_check(Map m){
		
		Map ma=new HashMap(); 
		String flagStr=null;
		
		if("3".equals(m.get("controltype")))
			flagStr="yange";
		if("1".equals(m.get("controltype")))
			flagStr="tishi";
		if("2".equals(m.get("controltype")))
			flagStr="password";
		
		return flagStr;
	
	}
	
	//单独处理折扣率
	public Map getZheKouCheck(String controlFlag,String actiontype){			
		Map ma=new HashMap();
	    boolean isStrict=false;
		if("yange".equals(controlFlag)){
			isStrict=true;
		}
		//严格控制
		if(isStrict){
			ma.put("errmsg",actiontype+"失败：物品倒扣率超出限制");
			return ma;
		}
		else{
			//提示控制
			boolean confirm=false;
			//弹出授权密码
			boolean passWordConfirm=false;   
			 if("password".equals(controlFlag))
				passWordConfirm=true;
			else{
				confirm=true;
			}
			ma.put("msg", "物品倒扣率超出限制"+"    要继续"+actiontype+"吗？");
			if(confirm){
				ma.put("confirm", true);
				return ma;
			}else if(passWordConfirm){
				ma.put("passWordConfirm", true);
				ma.put("password", Prefer.get("U3_SA_SuperRight"));
				return ma;
			}else{
				return ma;
			}
		}
		
	}
	
	
	//对docheck的异常信息的处理。手机端要求提示信息简洁，不需要像web端那么具体详细。
	public String getExceptiondoCheck(String errmsg,String cbillcode){
		
		String msg[]=errmsg.split("。");
		if(msg.length==1){
			return "单据号"+cbillcode+"："+errmsg;
		}else{
			return "单据号"+cbillcode+"：物品超出可用量";
		}
	}
	
	

		
	}
	
	
	
	 
	
	
	