package com.aisino.app.a6.web.dotasklist.bill.plugin;

import java.util.Date;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.aisino.aos.bill.common.BillUtils;
import com.aisino.aos.bill.plugin.BillCheckLogUtils;
import com.aisino.aos.bill.vo.BillSetting;
import com.aisino.aos.bill.vo.BillVo;
import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.core.Guid;
import com.aisino.platform.core.MS;
import com.aisino.platform.db.Crud;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.DateUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;

/**
 * 报销单手工审核和自动审核
 * <AUTHOR>
 * @since 2017-08-31
 */
public class ExpenseAccountBillCheckPlugin {
	private static String H1 = "OA_ExpenseAccountBill";
	private static String statusField = "cStatusEnumGUID";//状态字段
	private static String auditorField = "cAuditorGuid";//审核人字段
	private static String auditTimeField = "dCheckTime";//审核时间字段
	private static String checkWayField = "cCheckWay";//审核方式字段
	private static final String pkField = "cguid";//主表的主键字段
	private static final String timeStampField = "cTimeStamp";//时间戳字段
	
	public static final String State_Revise = "revise";//退回修改中状态
	public static final String State_Saved = "saved";//保存待提交状态
	public static final String State_Tsaved = "tsaved";//暂存状态
	public static final String State_Checked = "checked";//单据审核完成状态
	public static final String State_Checking = "checking";//提交待审核状态
	public static final String State_Refuse = "refuse";//否决并终止状态
	
	public static final String BILL_WF_CHECK_FLAG = "bill_wf_check_flag";//有这个标志不发消息
	
	
	public static void checkExpenseAccountBill(AbstractForm form, DataMsgBus bus){
		DbSvr db = DbSvr.getDbService(null);
		Map billData = db.load(H1, pkField, bus.getString(pkField));
		if(CollectionUtil.isBlankMap(billData))
			throw new BusinessException("数据被并发删除，请刷新后重新操作！");
		String curState = CollectionUtil.getStringFromMap(billData, statusField);
		if(State_Tsaved.equalsIgnoreCase(curState))
			throw new BusinessException("单据是暂存状态，不能审核，如需审核请先保存单据。");
		if(State_Checked.equalsIgnoreCase(curState))
			throw new BusinessException("单据已审核，不能重复审核，如需重新审核请先反审核单据。");
		//获得审批方式
		BillSetting bs = BillUtils.getBillSettingVoByFormId("oa_fm_expenseAccountBill_edit_form");
		if(State_Saved.equalsIgnoreCase(curState) && bs!=null && bs.getCcheckway().equals(BillSetting.WFCheck))
			throw new BusinessException("单据未提交，不能审核！");
		if((State_Revise.equalsIgnoreCase(curState))&& bs!=null && bs.getCcheckway().equals(BillSetting.WFCheck))
			throw new BusinessException("单据已经被退回，不能再次审核！");
		if((State_Refuse.equalsIgnoreCase(curState))&& bs!=null && bs.getCcheckway().equals(BillSetting.WFCheck))
			throw new BusinessException("单据已经被终止，不能再次审核！");
		if((State_Saved).equalsIgnoreCase(curState)&&StringUtil.isNotBlank(bus.getString(pkField))){
			MS ms=new MS("AOS.BillQueryToBeSent");
			String processDoneTemplateId=(String) ms.doService(bus);
			if(StringUtil.isNotBlank(processDoneTemplateId))
				throw new BusinessException("单据是流程审批方式，不能审核！");
		}
		String checkWay = CollectionUtil.getStringFromMap(billData, checkWayField);
		if(BillSetting.isWFCheck(checkWay) && State_Checking.equalsIgnoreCase(curState)){
		}else{
			if(bs!=null)
				checkWay = bs.getCcheckway();
			else
				checkWay = BillSetting.AutoCheck;
			//自动或者手动审批
			//调用审核前接口
			//callBCI("beforeCheck", form, bus);
			verifyConcurrency(form, bus, db);
			Crud crud = new Crud(H1,db);
			String auditor = SessionHelper.getCurrentUserId();
			//设置自动审核人
			//只有在bus.getString("auto")为true时才取自动审核人
			if(bs!=null && bs.isAutoCheck() && StringUtils.isNotBlank(bs.getCautochecker()) && "true".equalsIgnoreCase(bus.getString("auto")))
				auditor = bs.getCautochecker();
			crud.define(statusField+","+auditorField+","+auditTimeField+","+timeStampField+","+checkWayField, 
					new Object[]{State_Checked, auditor, DateUtil.time2Str(new Date()), Guid.g(), checkWay});
			crud.defineCondition(pkField, new Object[]{bus.getString(pkField)});
			db.execute(crud.getUpdateEso());
			bus.put(auditorField, auditor);
			bus.put(BillVo.BusCodeField, CollectionUtil.getStringFromMap(billData, BillVo.BusCodeField));
			BillCheckLogUtils.appendLog(form,bus,"check");
			//调用审核后接口
			//callBCI("afterCheck", form, bus);
			//form.setReturn("PT.t('审核成功！');");
			/* 修正版调用审核后接口 */
			BillCheckInterface bci = new ExpenseAccountBillCheckImplPlugin();
			bci.afterCheck(bus);
		}
	}
	
	public static void verifyConcurrency(AbstractForm form, DataMsgBus bus, DbSvr db) {
		String cguid  = bus.getString(pkField);
		if(StringUtil.isBlank(cguid))
			return;
		if(!db.checkExists(H1, pkField+","+timeStampField,	new Object[]{cguid, bus.getString(timeStampField)}))
			throw new BusinessException("单据已被并发修改，请刷新后再试！");
	}
	
}
