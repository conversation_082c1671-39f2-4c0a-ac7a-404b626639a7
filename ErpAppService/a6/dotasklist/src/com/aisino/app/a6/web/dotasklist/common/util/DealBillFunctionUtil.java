package com.aisino.app.a6.web.dotasklist.common.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.aos.bill.common.BillUtils;
import com.aisino.aos.bill.plugin.BillCheckLogUtils;
import com.aisino.aos.bill.plugin.BillCheckPlugin;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.MS;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;

public class DealBillFunctionUtil {	
	/**
	 * <AUTHOR>
	 * @date sep 014.2018
	 */
	//单据工作流提交
	public void WorkflowSubmit(AbstractForm form, DataMsgBus bus) {
		//bus中需要包含：单据cguid，单据主表表名table，如"ST_StkRecord"，单据时间戳cTimeStamp，
		String cguid = bus.getString("cGUID");
		String table = bus.getString("table");
		DbSvr db = DbSvr.getDbService(null);
		MS oaMs = new MS("AOS.IfUseOAModule");
		Object hasOA = oaMs.doService(null);
		if(Boolean.FALSE.equals(hasOA)){
			throw new BusinessException("工作协同未启用，无法创建工作流！");
		}
		
		Map billData = BillUtils.getBillInfo(cguid, table);
		if(CollectionUtil.isBlankMap(billData)){
			throw new BusinessException("数据被并发删除，请刷新后重新操作！");
		}
		db.checkExists(table, "cGUID,cTimeStamp", new Object[]{cguid, bus.getString("cTimeStamp")});
			
		//校验单据状态
		String curState = CollectionUtil.getStringFromMap(billData, "iAuditStatus");
		if(BillCheckPlugin.State_Tsaved.equalsIgnoreCase(curState))
			throw new BusinessException("单据是暂存状态，不能审核，如需审核请先保存单据。");
		else if(BillCheckPlugin.State_Checking.equalsIgnoreCase(curState))
			throw new BusinessException("单据已经提交审批,不能重复提交！");
		else if(BillCheckPlugin.State_Checked.equalsIgnoreCase(curState))
			throw new BusinessException("单据已经审批结束，如需重新审批，请先执行反审批！");		
		//启动工作流
		new BillCheckPlugin().startWorkflow(form, bus, curState);
		//调用提交之后的接口
		BillCheckLogUtils.appendLog(form,bus,"commit");
		//发现bus中审核状态没有更新，重新查一次
	   String iAuditStatus = db.getStringResult("select iAuditStatus from "+table+" where cguid = ?", cguid);
	   bus.put("iAuditStatus", iAuditStatus);
	}
	
	
	//单据页面能否编辑
	public String canModifyCheck(String iAuditStatus,String editRescKey,List<Map> detail){
		String formid= editRescKey.split("\\.")[0]; //获取formid
		boolean flagrtn=false;			
		if(!"tsaved".equals(iAuditStatus) && !"saved".equals(iAuditStatus) && !"revise".equals(iAuditStatus)){
			
		}else{
			if("tsaved".equals(iAuditStatus)){
				flagrtn=true;
			}else{
				if(this.checkBillPower(editRescKey)){
					boolean hasspecial=false; //单据含有辅计量、批次、保质期等物品，也不让修改
					for(int i=0;i<detail.size();i++){
						Map row = (Map) detail.get(i);
						if("business_sa_retail_form".equals(formid)){  //零售单不支持辅计量
							if(row.get("isubunit").toString().equals("1") ||row.get("iguaranteeflag").toString().equals("1")||row.get("ibatchflag").toString().equals("1") ){
								hasspecial=true;
								break;
							}
						}else{ //其它单据不支持浮动换算
							if(row.get("irateflag").toString().equals("2") ||row.get("iguaranteeflag").toString().equals("1")||row.get("ibatchflag").toString().equals("1") ){
								hasspecial=true;
								break;
							}
						}
					}
					if(!hasspecial){
						flagrtn=true;
					}		
				}
			}		
		}
		if(flagrtn){
			return "y";
		}else{
			return "n";
		}
	}
	
	
	
	//是否有单据功能权限
	public boolean checkBillPower(String rescKey){
		boolean re = SecurityService.rescCheck(rescKey); 
		return re;
	}
	
	
	public DataMsgBus getBusData(DbSvr db,Map map){
		//组织数据，将submit需要的信息补充完整至bus 
		String cguid = CollectionUtil.getStringFromMap(map, "cguid");
		String table = CollectionUtil.getStringFromMap(map, "table");
		String tableline = CollectionUtil.getStringFromMap(map, "tableline");
		DataMsgBus bus = new DataMsgBus();
	    List<Map> list=new  ArrayList<Map>();
	    Map m = new HashMap();
	    bus.put("table", table);	    
	    bus.put("tableline", tableline);
	    m=db.getOneRecorder("select * from " + table +" so where so.cGUID =?",cguid);

	    if (CollectionUtil.isBlankMap(m))
    		 throw new BusinessException("单据不存在，可能已被并发删除");
	    list=db.getListResult("select sao.*,cs.irateflag from " + tableline +"  sao LEFT JOIN CM_Unit u on u.cGUID = sao.cUnitGUID left join CM_UnitClass cs on cs.cGUID=u.cClassGUID where cheadguid=?",cguid);
	    bus.putAll(m);
	    bus.send("list", list);	
	    bus.send("billcguid",cguid);	
		return bus;		
	}
	
}
