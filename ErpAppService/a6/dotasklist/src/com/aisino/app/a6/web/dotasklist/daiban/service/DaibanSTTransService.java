
package com.aisino.app.a6.web.dotasklist.daiban.service;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.aisino.app.web.dotasklist.common.plugin.BillDesignPlugin;
import com.aisino.app.web.dotasklist.daiban.plugin.DaibanUtil;
import com.aisino.app.web.notice.InsertCheckNoticePlugin;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.MS;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.JsUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;

public  class DaibanSTTransService  implements IService{
	

	@Override
	public Object doService(Object... param) {	
		String action = (String) param[0];
		String cguid=(String) param[1];
		DbSvr db=DbSvr.getDbService(null);	
		String firstSubmit="true";
		String cBusinessCode = db.getStringResult("SELECT cbilltype FROM ST_StkRecord where cGUID = ?", cguid);
		//库存调拨单不在出入库总表中，单独直接赋值后续增加单据需注意
		if(cBusinessCode == null){
			cBusinessCode ="072";
		}
		if("browse".equals(action)){
			//String cBusinessCode ="072";   
			Map m= BillDesignPlugin.getBill(cBusinessCode,cguid); //调用接口传该单据的formid
			List<Map> main=(List<Map>) m.get("maindata");
			List<Map> detail=(List<Map>) m.get("detaildata");  //根据单据设计，获取将要显示的字段及对应的值
			
			//查看明细的时候代办已办可以用同一套sql
			//List<Map> mainTemp = db.queryIdForList("mobile_daiban_stbill.sttransmain", cguid);
			List<Map> detailTemp = null;
			if("072".equals(cBusinessCode)){
				detailTemp = db.queryIdForList("mobile_daiban_stbill.sttransdetail",cguid);				
			}else{
				detailTemp = db.queryIdForList("mobile_daiban_stbill.ststkrecorddetail",cguid);
			}
			
			Map map = new HashMap();
			//主表
			if(main!=null&&!main.isEmpty()){
			//	main=BillDesignPlugin.mainProcess(main,mainTemp);//主表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("main", main);
			}
			//子表
			if(detail!=null&&!detail.isEmpty()){
				detail=BillDesignPlugin.detailProcess(detail,detailTemp);//子表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("detail", detail);
			}
			//附件
			List<Map> files =db.queryIdForList("mobile_common.getFiles",cguid);
			if(CollectionUtil.isEmpty(main)||CollectionUtil.isEmpty(detail))
				 throw new BusinessException("并发删除,请重新查询");
			//附件权限
			String fileKey = "business_st_StTrans.viewfile";			
			boolean hasfiledown= SecurityService.rescCheck(fileKey); 
			if(!hasfiledown&&CollectionUtil.isNotEmpty(files)){
				Map m2=new HashMap();
				//此时app不允许下载附件
				m2.put("hasfiledown", "false");
				map.putAll(m2);
			}
			if(files!=null&&!files.isEmpty())
		           map.put("file", files);
			return map;
		}else if("submit".equals(action)){
		    if((String) param[2]!=null)
		    	firstSubmit=(String) param[2];
			DataMsgBus nbus = new DataMsgBus();  
		    List<Map> list=new  ArrayList<Map>();
		    Map m = new HashMap(); 
		    if("072".equals(cBusinessCode)){
		    	m=db.getOneRecorder("select * from ST_StkTrans so where so.cGUID =?",cguid);
		    }else{
		    	m=db.getOneRecorder("select * from ST_StkRecord so where so.cGUID =?", cguid);
		    }
		    if (CollectionUtil.isBlankMap(m))
				 throw new BusinessException("单据不存在，可能已被并发删除");
		    if("072".equals(cBusinessCode)){
		    	list=db.getListResult("select * from ST_StkTransLine sao where cheadguid=?",cguid);
		    }else{
		    	list=db.getListResult("select * from ST_StkRecordLine sao where cheadguid=?",cguid);
		    }
		    nbus.putAll(m);
	    	nbus.send("list", list);	
	    	nbus.send("billcguid",cguid);
	    	String msg=null;
	   	    if(firstSubmit.equals("true")){
	   	    	if("072".equals(cBusinessCode)){
	   	    		msg=Check("business_st_StTrans",nbus,"beforeCheck");
	   	    	}else if("020".equals(cBusinessCode)){
	   	    		msg=Check("business_sa_sastkout",nbus,"beforecheck_sa");
	   	    	}else if("070".equals(cBusinessCode)){
	   	    		msg=Check("business_st_stinout_clckd",nbus,"beforeCheckByCurQty");
	   	    	}
	   	    	if(StringUtil.isNotBlank(msg)&&msg.length()>3)
	        		return msg;
	        }
	   	    if("072".equals(cBusinessCode)){
	   	    	msg=Check("business_st_StTrans",nbus,"docheck");
	  		    DaibanUtil.updateCheckWay("ST_StkTrans",cguid);
	   	    }else if("020".equals(cBusinessCode)){
	   	    	msg=Check("business_sa_sastkout",nbus,"docheck");
	  		    DaibanUtil.updateCheckWay("ST_StkRecord",cguid);
	   	    }else if("070".equals(cBusinessCode)){
	   	    	msg=Check("business_st_stinout_clckd",nbus,"docheck");
	   	    	DaibanUtil.updateCheckWay("ST_StkRecord",cguid);	   	    	
	   	    }
	    	return msg;
		}else  if("beforeWFAgree".equals(action)){//处理工作流审批的beforecheck
			if((String) param[2]!=null)
				firstSubmit=(String) param[2];
			DataMsgBus nbus = new DataMsgBus();  
		    List<Map> list=new  ArrayList<Map>();
		    Map m=new HashMap();
		    if("072".equals(cBusinessCode)){
		    	m=db.getOneRecorder("select * from ST_StkTrans so where so.cGUID =?",cguid);
		    }else{
		    	m=db.getOneRecorder("select * from ST_StkRecord so where so.cGUID =?",cguid);
		    }	
		    if (CollectionUtil.isBlankMap(m))
				 throw new BusinessException("单据不存在，可能已被并发删除");
		    if("072".equals(cBusinessCode)){
		    	list=db.getListResult("select * from ST_StkTransLine sao where cheadguid=?",cguid);
		    }else{
		    	list=db.getListResult("select * from ST_StkRecordLine sao where cheadguid=?",cguid);
		    }
		    nbus.putAll(m);
	    	nbus.send("list", list);	
	    	nbus.send("billcguid",cguid);
	    	String msg=null;
	   	    if(firstSubmit.equals("true")){
	   	    	if("072".equals(cBusinessCode)){
		   	    	msg=Check("business_st_StTrans",nbus,"beforeCheck");
		   	    }else if("020".equals(cBusinessCode)){
		   	    	msg=Check("business_sa_sastkout",nbus,"beforecheck_sa");
		   	    }else if("070".equals(cBusinessCode)){
		   	    	msg=Check("business_st_stinout_clckd",nbus,"beforeCheckByCurQty");
		   	    }
	        	if(StringUtil.isNotBlank(msg))
	        		return msg;
	        }
   	    	return msg;
		}else {
			return null;
		}
	}
	
		public String Check(String fromid,DataMsgBus bus,String action){
			Map msg=new HashMap();
			String cbillcode=bus.getString("cbillcode");
			String cCreatorGUID=bus.getString("cCreatorGUID");
			String nextflagStr=bus.getString("flagStr");
			Object objMsg=null;
			String confirmmsg=null;
	    	try {
	    		 bus.setNewAction(action);
	    		 /**此处跳出权限校验。否则会抛异常“没有[销售发票]功能的权限，不能进行操作”
	     		 * 因为PC端的工作流审核不会考虑单据以及审核权限之类的**/
	 	    	if(bus.getBoolean("isworkflow", false)){
	 	    		Set set = new HashSet();
	 	 		    set.add("acs");
	 	 		    set.add("pt_sql_filter");
	 	 		    MS m2 = new MS("ACS.SessService");
	 	 		    m2.doService(new Object[] { "putRescInSession", fromid });
	 	    	}
	 	    	if(action.equals("docheck")){/*此处在docheck中，模仿列表审批(加个billls)，以此或得某些提示信息*/
	 	    		List<Map> bills =new ArrayList();
	 	    		msg.put("cguid", bus.get("cguid"));
	 	    		bills.add(msg);
	 	    		bus.put("bills", bills);
	 	    	}
		 	     objMsg=AbstractForm.callFormSubmitService(fromid,bus,false);
		 	     if(objMsg instanceof Map) { /*beforecheck的校验*/ 
					 Map map=(Map) objMsg;
					 if(map.containsKey("billmsg")){
						/*仓库属性，0库存出库选择仅提示*/
						msg.put("msg",map.get("billmsg"));
						msg.put("confirm", true);
						return JsUtil.toJsonObjectWithNull(msg).toString();
					}
				}else if(msg.get("errorMsg")!=null&&msg.get("errorMsg")!=""){/*docheck的校验*/
						new  InsertCheckNoticePlugin().InsertCheckNotice(cCreatorGUID,"单据号"+cbillcode+"："+msg.get("errorMsg"));
						msg.remove("cguid");
						msg.put("errmsg", msg.get("errorMsg"));
						msg.remove("errorMsg");
						return JsUtil.toJsonObjectWithNull(msg).toString();
				}else{
					   return JsUtil.toJsonObjectWithNull(new HashMap()).toString();
				}
			} catch (Exception e) {
				DbSvr db=DbSvr.getDbService(null);	
				//回滚审核状态等等
				db.rollback();
				//存储审批失败信息到PT_Notice
				new  InsertCheckNoticePlugin().InsertCheckNotice(cCreatorGUID,e.getMessage());
			    throw new  BusinessException("单据号"+cbillcode+"："+e.getMessage());
			}
				return  null;
		}
     
}