package com.aisino.app.a6.web.dotasklist.common.plugin;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.a6.business.st.util.serialnumber.plugin.SerialNumberUtil;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.JsUtil;
import com.aisino.platform.util.MathUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

import edu.emory.mathcs.backport.java.util.Collections;

/**
 * 获取序列号并校验
 * @since 2018-09-12
 */
public class SerialNumberPlugin implements FormCreateListener{
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		// TODO Auto-generated method stub
		DbSvr db= DbSvr.getDbService(null);	
		Map map = new HashMap();
		Map snmap = new HashMap();
		List<Map> list = new ArrayList<Map>();
		String matmsg=null;
		
		String cMatGUID = bus.get("cmatguid").toString();
		String serialnumbers = bus.getString("serialnumbers");
		
		 if (serialnumbers != null) {
			 List<String> serialnumberlist = Arrays.asList(serialnumbers.split(","));  //要删除列表的cguid
			 Collections.sort(serialnumberlist); //对接收的序列号排序
			 //组装list
			 for (String serialnumber : serialnumberlist) {
				 Map temp = new HashMap();
				 temp.put("cMatGUID", cMatGUID);
				 temp.put("crowsninfo", serialnumber);
				 list.add(temp);
			 }
			 bus.send("list", list);
			 bus.send("cStoreGUID",bus.getString("cstoreguid")); //转化成大写
			 if("saretail".equalsIgnoreCase(bus.getString("billname"))){
				 bus.send("iRedFlag", 0);  //先写死
				 bus.send("iRSFlag", 0);  //先写死
				 bus.send("cBillType", "212");  //先写死
				 bus.send("cBusType", "s0004");  //先写死
			 }
			 try{
				matmsg =this.checkSerialNumber(form,bus);
				if(matmsg==null ||matmsg==""){
					snmap = this.matSnChoseList(form,bus);
				}
			 }catch(Exception e){
				throw new  BusinessException(e.getMessage());
			 }

	    }
		map.put("msg", matmsg);
		map.put("snmap", snmap);
		form.setReturn(map);		
	}
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	@SuppressWarnings("unchecked")
	public String checkSerialNumber(AbstractForm form, DataMsgBus bus){
		DbSvr db = DbSvr.getDbService(null);
		/*校验序列号是否可用*/
			List<Map> list = bus.getList("list");//获取扫描页面根据序列号找到的物品信息项
			String redblue = bus.getString("iRedFlag");//获取该明细行物品信息
			String iRSFlag = bus.getString("iRSFlag");//获取该明细行物品信息
			String cStoreGUID = bus.getString("cStoreGUID");//获取该明细行仓库信息
			List<String> showMsg=new ArrayList<String>();  //提示信息
			//获取单据类型
            //String cBillType=bus.getString("cBillType");
            //获取业务类型
            //String cBusType=bus.getString("cBusType");
            //是不是入库控制单据
            boolean isCheckRKBIll = SerialNumberUtil.isRkCtrlBill(bus);
			/*扫描序列号 点击确定校验序列号可用性*/
			for(Map map : list){
				String cStkGUID = "";
				String cMatGUID = map.get("cMatGUID").toString();//获取该明细行物品信息				
				String crowsninfo = map.get("crowsninfo")==null?"":map.get("crowsninfo").toString();//获取页面扫描的序列号
				/*
				 * 获取物品档案中 数量匹配标识
				 */
				String iQuntEqu = "";
				String iCkNo = "";
				String iRkNo ="";
				String iSNStart ="";
				String idigNoCtrl="" ;
				String idigno="";
				Map matmap = db.getOneRecorder("select * from CM_Material where cGUID=?", cMatGUID);
				if(matmap!=null){
					/*
					 * 获取物品档案中 出库序号控制标识
					 */
					iQuntEqu = matmap.get("iQuntEqu")==null?"":matmap.get("iQuntEqu").toString();
					/*
					 * 获取物品档案中 序号唯一性标识
					 */
					iCkNo = matmap.get("iCkNo")==null?"":matmap.get("iCkNo").toString(); 
					/*
					 * 6.0 新增需求 物品档案增加入库序号控制 
					 */
					iRkNo = matmap.get("iRkNo")==null?"":matmap.get("iRkNo").toString();
					iSNStart = matmap.get("iSNStart")==null?"":matmap.get("iSNStart").toString();
					idigNoCtrl = matmap.get("idigNoCtrl")==null?"":matmap.get("idigNoCtrl").toString();
					idigno = matmap.get("idigno")==null?"":matmap.get("idigno").toString();
				}
				//获取页面中和数据库重复的序列号
				List<Map> cfNumberList = null;
				if(!"".equals(crowsninfo)){
					if(!"1".equals(iSNStart)){//若该扫描行物品没有启用序列号
						showMsg.add(matmap.get("cMatName")+"未启用序列号,"+crowsninfo+"无效；");
						continue;
					}
					if("1".equals(idigNoCtrl) && crowsninfo.length()!= Integer.parseInt(idigno)){
						showMsg.add(crowsninfo+"长度与物品档案序列号长度控制不符；");
						continue;
					}
					String[] crowsninfos = crowsninfo.split(",");
					StringBuffer sb = new StringBuffer();
					for(int i=0;i<crowsninfos.length;i++){
						if(sb.length()>0){
							sb.append(",");
						}
						//将所有序列号页面上的序列号都存入到sb中
						sb.append("'"+crowsninfos[i]+"'");
					}
					String replaceStr = "";
					if(sb.length()>0){
						replaceStr = sb.toString();//获取该明细表物品所有序列号
					}else{
						replaceStr = "'"+"'";
					}
					String blueinsql=db.getSQLInfo("business_serialnumber_check_sql.check_BlueInCheck_isOnlyOne").getSql().replace("replaceStr",replaceStr);
					String redoutsql=db.getSQLInfo("business_serialnumber_check_sql.check_RedOut_isOnlyOne").getSql().replace("replaceStr", replaceStr);
					Map scanmap = new HashMap();
					scanmap.put("cMatGUID", cMatGUID);
					scanmap.put("crowsninfo", crowsninfo);
					if(!"".equals(cStoreGUID)&&cStoreGUID!=null){
						scanmap.put("cStoreGUID", cStoreGUID);
					}
					if("1".equals(iRSFlag)){
						if("0".equals(redblue)){
							/*
							 * 6.0 新增需求 蓝字入库单 判断是否勾选入库序号控制
							 */
							if("0".equals(iRkNo)){
								/*
								 * 没有勾选入库序号控制,可录入已出的序列号也可录入系统中不存在的序列号。录入系统中不存在的序列号时，对同一物品控制序列号不重复。
								 */
								cfNumberList = db.getListResult(redoutsql, new Object[]{cStkGUID,cMatGUID});
								if(cfNumberList==null||cfNumberList.size()==0) {
									
								}else{
									showMsg.add("序列号"+crowsninfo+"已被系统使用,请检查数据；");
									continue;
								}
							}
                            if("1".equals(iRkNo)&&isCheckRKBIll){
								/*
								 * 勾选入库序号控制,不可录入已出的序列号也可录入系统中不存在的序列号。录入系统中不存在的序列号时，对同一物品控制序列号不重复。
								 */
								cfNumberList = db.getListResult(blueinsql, new Object[]{cMatGUID});
								if(cfNumberList==null||cfNumberList.size()==0) {
									
								}else{
									showMsg.add("入库序号控制:序列号"+crowsninfo+"为已出状态，不能入库；");
									continue;
								}
							}
							
						}
						if("1".equals(redblue)){
							cfNumberList = db.queryIdForList("business_serialnumber_check_sql.scanincheck_RedIn_isOnlyOne", scanmap);
							if(cfNumberList==null){
								showMsg.add("序列号"+crowsninfo+"不为在库状态，不能出库；");
							}
						}
					}
					if("0".equals(iRSFlag)){
						if("0".equals(redblue)){
							if("1".equals(iQuntEqu)&& "1".equals(iCkNo)){
								cfNumberList = db.queryIdForList("business_serialnumber_check_sql.scanincheck_RedIn_isOnlyOne", scanmap);
								if(cfNumberList==null){
									showMsg.add("序列号"+crowsninfo+"不为在库状态，不能出库；");
								}
							}else{
								cfNumberList = db.queryIdForList("business_serialnumber_check_sql.scanincheck_BlueOut_isOnlyOne", scanmap);
								if(cfNumberList==null||cfNumberList.size()==0) {
									
								}else{
									showMsg.add("序列号"+crowsninfo+"已被系统使用,请检查数据；");
									continue;
								}
							}
						}
						if("1".equals(redblue)){
							cfNumberList = db.getListResult(redoutsql, new Object[]{cStkGUID,cMatGUID});
							if(cfNumberList==null||cfNumberList.size()==0) {
								
							}else{
								showMsg.add("序列号"+crowsninfo+"已被系统使用,请检查数据；");
								continue;
							}
						}
					}
				}
			}
			if(showMsg.size()>0){
				StringBuilder esMsg=new StringBuilder();
				for(int m=0;m<=showMsg.size()-1;m++){
					esMsg.append(showMsg.get(m));
				}
				return showMsg.size()+"条序列号错误!"+esMsg.toString();
			}else{
				return null;
			}
		}
		
	
	public Map matSnChoseList(AbstractForm form, DataMsgBus bus){
		//DbSvr db = DbSvr.getDbService(null);
		Map map = new HashMap();
		String iRedFlag = bus.getString("iRedFlag");
		List<Map> csnlist = bus.getList("list");//获取扫描页面根据序列号找到的物品信息项
		String cMatGUID =bus.getString("cmatguid");
		if(StringUtil.isNotBlank(cMatGUID)) {//若该明细行录入了物品信息 则只更新该行 序列号列
			String linecrowsninfo = "";
			String linecrowsninfonew = "";
			if(CollectionUtil.isNotEmpty(csnlist)) {
				BigDecimal iUnitQTY = MathUtil.toBigDecimal(csnlist.size());
				if("1".equals(iRedFlag)){
					iUnitQTY = iUnitQTY.negate();
				}
	
				String crowsninfo=null;
				for(Map csnmap:csnlist){
				    crowsninfo = csnmap.get("crowsninfo")==null?"":csnmap.get("crowsninfo").toString();
					/*处理显示序列号 同一规格的序列号显示成例子001--002*/
					String linecrowsninfos = "";
					if(linecrowsninfo.lastIndexOf(",") > linecrowsninfo.lastIndexOf("--")){
						linecrowsninfos= linecrowsninfo.split(",")[linecrowsninfo.split(",").length-1];
					}else if(linecrowsninfo.lastIndexOf("--") > linecrowsninfo.lastIndexOf(",")){
						linecrowsninfos= linecrowsninfo.split("--")[linecrowsninfo.split("--").length-1];
					}else{
						linecrowsninfos=linecrowsninfo;
					}
					String pipeiline = "";
					String pipei = "";
					String newlinecrowsninfos  = linecrowsninfos;
					String newcrowsninfo  = crowsninfo;
					int linenumber = 0;
					int number = 0;
					
					List oldlistnumber = new ArrayList();
					List oldliststring = new ArrayList();
					List listnumber = new ArrayList();
					List liststring = new ArrayList();
					for(int i=0;i<newlinecrowsninfos.length();i++){
						boolean flagnumber = String.valueOf(newlinecrowsninfos.charAt(i)).matches("^[0-9]*$");
						boolean flagstring = String.valueOf(newlinecrowsninfos.charAt(i)).matches("^[a-zA-Z]*$");
						if(flagnumber){
							oldlistnumber.add(i);
						}
						if(flagstring){
							oldliststring.add(i);
						}
					}
					if(oldlistnumber!=null && oldlistnumber.size()>0){
						String sss = "";
						for(int i = 0;i<oldlistnumber.size();i++){
							sss = sss+newlinecrowsninfos.charAt(Integer.parseInt(oldlistnumber.get(i).toString()));
						}
						linenumber = new BigInteger(sss).intValue();
					}
					if(oldliststring!=null && oldliststring.size()>0){
						for(int i = 0;i<oldliststring.size();i++){
							pipeiline = pipeiline+newlinecrowsninfos.charAt(Integer.parseInt(oldliststring.get(i).toString()));
						}
					}
					for(int j=0;j<newcrowsninfo.length();j++){
						boolean flagNumber = String.valueOf(newcrowsninfo.charAt(j)).matches("^[0-9]*$");
						boolean flagString = String.valueOf(newcrowsninfo.charAt(j)).matches("^[a-zA-Z]*$");
						if(flagNumber){
							listnumber.add(j);
						}
						if(flagString){
							liststring.add(j);
						}
					}
					if(listnumber!=null && listnumber.size()>0){
						String sss = "";
						for(int i = 0;i<listnumber.size();i++){
							sss = sss+newcrowsninfo.charAt(Integer.parseInt(listnumber.get(i).toString()));
						}
						number = new BigInteger(sss).intValue();
					}
					if(liststring!=null && liststring.size()>0){
						for(int i = 0;i<liststring.size();i++){
							pipei = pipei + newcrowsninfo.charAt(Integer.parseInt(liststring.get(i).toString()));
						}
					}
					
					if(pipeiline.equals(pipei) && linenumber+1==number && !linecrowsninfo.equals("")){
						if(linecrowsninfo.split("--").length==1){
							linecrowsninfo =  linecrowsninfo+"--"+crowsninfo;
						}else if(linecrowsninfo.split("--").length>1){
							if(linecrowsninfo.lastIndexOf("--")>linecrowsninfo.lastIndexOf(",")){
								linecrowsninfo =  linecrowsninfo.substring(0, linecrowsninfo.lastIndexOf("--"))+"--"+crowsninfo;
							}else{
								linecrowsninfo =  linecrowsninfo+"--"+crowsninfo;
							}
						}
					}else{
						if(linecrowsninfo.equals("")){
							linecrowsninfo = crowsninfo;
						}else{
							linecrowsninfo = linecrowsninfo+","+crowsninfo;
						}
					}
					
					if(linecrowsninfonew.equals("")){
						linecrowsninfonew = crowsninfo;
					}else{
						linecrowsninfonew = linecrowsninfonew+","+crowsninfo;
					}
					
				}
				map.put("crowsninfo", linecrowsninfo);
				map.put("crowsninfonew", linecrowsninfonew);
			}
		}
		return map; 		
	}

										
}