package com.aisino.app.a6.web.dotasklist.yiban.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.app.web.dotasklist.common.plugin.BillDesignPlugin;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;


public class YibanSTTransService  implements IService{
	@Override
	public Object doService(Object... param) {	
		String action = (String) param[0];
		String cguid=(String) param[1];
		DbSvr db=DbSvr.getDbService(null);
		String cBusinessCode = db.getStringResult("SELECT cbilltype FROM ST_StkRecord where cGUID = ?", cguid);
		//库存调拨单不在出入库总表中，单独直接赋值后续增加单据需注意
		if(cBusinessCode == null){
			cBusinessCode ="072";
		}
		if("browse".equals(action)){
//			String cBusinessCode ="072";   
			Map m= BillDesignPlugin.getBill(cBusinessCode,cguid);   //调用接口传该单据的formid
			List<Map> main=(List<Map>) m.get("maindata");
			List<Map> detail=(List<Map>) m.get("detaildata");  //根据单据设计，获取将要显示的字段及对应的值
			
			//查看明细的时候代办已办可以用同一套sql
	//		List<Map> mainTemp =db.queryIdForList("mobile_daiban_stbill.sttransmain", cguid);
			List<Map> detailTemp = null;
			if("072".equals(cBusinessCode)){
				detailTemp = db.queryIdForList("mobile_daiban_stbill.sttransdetail",cguid);
			}else{
				detailTemp = db.queryIdForList("mobile_daiban_stbill.ststkrecorddetail",cguid);
			}
			Map map = new HashMap();
			//主表
			if(main!=null&&!main.isEmpty()){
			//	main=BillDesignPlugin.mainProcess(main,mainTemp);//主表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("main", main);
			}
			//子表
			if(detail!=null&&!detail.isEmpty()){
				detail=BillDesignPlugin.detailProcess(detail,detailTemp);//子表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("detail", detail);
			}
			//附件
			List<Map> files =db.queryIdForList("mobile_common.getFiles",cguid);
			if(CollectionUtil.isEmpty(main)||CollectionUtil.isEmpty(detail))
				 throw new BusinessException("并发删除,请重新查询");
			//附件权限
			String fileKey = "business_st_StTrans.viewfile";			
			boolean hasfiledown= SecurityService.rescCheck(fileKey); 
			if(!hasfiledown&&CollectionUtil.isNotEmpty(files)){
				Map m2=new HashMap();
				//此时app不允许下载附件
				m2.put("hasfiledown", "false");
				map.putAll(m2);
			}
			if(files!=null&&!files.isEmpty())
		           map.put("file", files);
			return map;
		}else{
			return null;
		}
	}
	
}
