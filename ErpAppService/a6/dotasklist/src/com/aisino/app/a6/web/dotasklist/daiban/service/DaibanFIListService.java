package com.aisino.app.a6.web.dotasklist.daiban.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.a6.web.dotasklist.common.service.SecurityServiceRescCheck;
import com.aisino.app.a6.web.dotasklist.daiban.plugin.DaibanGetDeptRolesListPlugin;
import com.aisino.app.web.dotasklist.daiban.service.CommonGetUserAndDB;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

public class DaibanFIListService  implements IService{
	@Override
	public Object doService(Object... param) {
		

		String action = (String) param[0];  //操作类型：取list 或 取数目
		List<Map> l = (List<Map>) param[1];  //list
		Map m = (Map) param[2];    //key为listnum
		String keyword= (String) param[3];
		
		DataMsgBus bus=DataMsgBus.getCurrBus();
		/*DbSvr db=DbSvr.getDbService(null);
		String currUserGUID = SessionHelper.getCurrentUserId();
		String currUserEmpGUID = SessionHelper.getCurrentEmpId();*/
		DbSvr db=CommonGetUserAndDB.getDbSvr(param);
		String currUserGUID = CommonGetUserAndDB.getUserID(param);
		String currUserEmpGUID = CommonGetUserAndDB.getUserEmpID(param);
		
		bus.put("currUserGUID", currUserGUID);
		bus.put("currUserEmpGUID", currUserEmpGUID);
		bus.put("keyword", keyword);
		
		
		/* 获取当前用户所在部门角色分组等 */
		List deptGroupRoleIds = new DaibanGetDeptRolesListPlugin().getDeptRolesList(param);
		bus.put("deptRoleList", deptGroupRoleIds);
		
		//付款单获取审批方式
				String apFormId = "bus_ap_bill_form";
				String apcheckWay = db.queryIdForString("mobile_common_daiban.getCheckway", new Object[]{apFormId});

		//其它付款单获取审批方式
				String caFormId = "finance_ca_apvoucher_edit_form";
				String cacheckWay = db.queryIdForString("mobile_common_daiban.getCheckway", new Object[]{caFormId});
				
		/*付款单手工审核需要先校验当前用户是否有审核权限*/
				String rescKey = "bus_ap_bill_form.docheck";
				String billKey = "bus_ap_bill_form";
				String listKey = "bus_ap_billlist_form";
				/*boolean re = SecurityService.rescCheck(rescKey);  //判断当前登录人是否具有审核按钮的权限
				boolean bill= SecurityService.rescCheck(billKey); 
				boolean APlist = SecurityService.rescCheck(listKey); 	*/	
				boolean re = SecurityServiceRescCheck.RescCheck(rescKey);  //判断当前登录人是否具有审核按钮的权限
				boolean bill= SecurityServiceRescCheck.RescCheck(billKey); 
				boolean APlist = SecurityServiceRescCheck.RescCheck(listKey); 		
		/*其它付款单手工审核需要先校验当前用户是否有审核权限*/		
				boolean re1=db.queryIdHasResult("mobile_daiban_fibill.isCaapvoucherExist", currUserGUID);
		
		  /*付款单手工审批*/
			if(!"2".equals(apcheckWay)){
				   if(re&&bill||re&&APlist){  
					   bus.put("sgAP", "true");
				}
			}
			/*其它付款单手工审批*/
			if(!"2".equals(cacheckWay)){//其它付款单单手工审批
				//String rescKey = "finance_ca_apvoucher_edit_form.docheck";
				//boolean re = SecurityService.rescCheck(rescKey);  //判断当前登录人是否具有审核按钮的权限
				//因为其它付款单没有审核权限，只有列表和单据权限，所以只要其能看到就能审批。
				if(re1){
					 bus.put("sgCA", "true");
				}
			}
			
		/*查看该用户是否有协同-待办事项的菜单权限。*/
			//boolean hasworkflow = SecurityService.rescCheck("pt_workflow_worklist");
			boolean hasworkflow = SecurityServiceRescCheck.RescCheck("pt_workflow_worklist");
			if(hasworkflow){
				 bus.put("wfAP", "true");
				 bus.put("wfCA", "true");
		    }else{
		    	if(re&&bill)
				    bus.put("wffilterAP","true");
		    	if(re1)
				    bus.put("wffilterCA","true");
		    }  
		
		if("getlist".equals(action)) {  	
				/* 增加查询当前页数和每页条数 */
				bus.setControlInfo("pt_control_currentpage",param[4]);
				bus.setControlInfo("pt_control_pagesize",param[5]);
				List<Map> list = new ArrayList();
				list=db.queryIdForListByPage("mobile_daiban_filist.getallfilist",bus);
				if(list!=null&&!list.isEmpty()){
					checkIamt(db,currUserGUID,list);
					l.addAll(list);	
				}
		}else if("getnum".equals(action)){
			int num=0;
			num=Integer.parseInt(db.queryIdForString("mobile_daiban_filist.getallfinum",bus)); 
			m.put("listnum", num);
		}
 		return null;
	}
	
	///校验该用户是否有字段（金额）的权限，若没有该权限，将金额置为空。
	public void checkIamt(DbSvr db,String currUserGUID,List<Map> poList){
		
		boolean notHas=db.queryIdHasResult("mobile_common.checkIamtFK", currUserGUID);
		if(notHas){
			for(int i=0;i<poList.size();i++){
				if("082".equals(poList.get(i).get("hidden_cbilltype")))
				poList.get(i).put("je", "");
			}
			
		}
		
	}
	
	
	

}