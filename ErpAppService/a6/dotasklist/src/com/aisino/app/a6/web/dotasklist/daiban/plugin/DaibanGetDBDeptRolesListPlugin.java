package com.aisino.app.a6.web.dotasklist.daiban.plugin;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.dotasklist.daiban.service.CommonGetUserAndDB;
import com.aisino.platform.db.DbSvr;

/**
 * 获取当前登录用户的部门角色分组List
 * <AUTHOR>
 * @date 2016年12月18日
 *
 */
public class DaibanGetDBDeptRolesListPlugin {
	
	public static List getDBDeptRolesList(DbSvr db){
		List deptGroupRoleIds = new ArrayList();
		String currUserGUID = SessionHelper.getCurrentUserId();
		
		List<Map> userDeptRolesList = db.queryIdForList("mobile_daiban_common.getDeptRoleList", new Object[]{currUserGUID,currUserGUID,currUserGUID});
		if(userDeptRolesList != null && userDeptRolesList.size()>0){
			for(Map map: userDeptRolesList){
				deptGroupRoleIds.add(map.get("deptGroupRoleId"));
			}
		}
		
		return deptGroupRoleIds;
	}
}
