package com.aisino.app.a6.web.dotasklist.list.plugin;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

/**
 * 报销申请列表数据加载
 * <AUTHOR>
 * @since 2017-07-20
 */
public class GetExpenseAccountListPlugin implements FormCreateListener{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		// TODO Auto-generated method stub
		
		/* 校验是否有报销列表权限，若没有则返回空，否则继续 */
		String emListRescKey = "oa_fm_expenseAccountBill_list_form";
		boolean emListRe = SecurityService.rescCheck(emListRescKey);
		if(!emListRe){
			throw new BusinessException("当前用户没有报销申请列表权限.");
		}
		
		//审批状态：未提交wtj、审批中spz、已完成ywc
		String spzt = bus.getString("spzt");
		//查询条件
		String currUserGUID = SessionHelper.getCurrentUserId();
		String currUserEmpGUID = SessionHelper.getCurrentEmpId();
		bus.put("currUserGUID", currUserGUID);
		bus.put("currUserEmpGUID", currUserEmpGUID);
		
		DbSvr db = DbSvr.getDbService(null);
		/* 2017-08-03 quchj 增加对报销总金额的查询 */
		String zbxje = db.queryIdForString("mobile_list.expenseAccountBXJESum", bus);
		
		bus.setControlInfo("pt_control_currentpage",bus.getString("curpage"));
		bus.setControlInfo("pt_control_pagesize",bus.getString("pagenum"));
		
		/* 2017-08-04 quchj 修正按页查询 */
		List ret = db.queryIdForListByPage("mobile_list.expenseAccountList",bus);
		Map map = new HashMap();
		map.put("list", ret);
		map.put("zbxje", zbxje);
		form.setReturn(map);
	}
}