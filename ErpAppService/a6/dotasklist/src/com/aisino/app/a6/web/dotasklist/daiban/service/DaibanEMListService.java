package com.aisino.app.a6.web.dotasklist.daiban.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.a6.web.dotasklist.common.service.SecurityServiceRescCheck;
import com.aisino.app.a6.web.dotasklist.daiban.plugin.DaibanGetDeptRolesListPlugin;
import com.aisino.app.web.dotasklist.daiban.service.CommonGetUserAndDB;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

/**
 * 报销单代办列表相关查询
 * <AUTHOR>
 * @date 2016年12月10日
 *
 */
public class DaibanEMListService implements IService{

	@Override
	public Object doService(Object... param) {
		// TODO Auto-generated method stub
		DataMsgBus bus = new DataMsgBus();
		
		String action = (String) param[0];  //操作类型：取list 或 取数目
		List<Map> l = (List<Map>) param[1];  //list
		Map m = (Map) param[2];    //key为listnum
		
/*		DbSvr db = DbSvr.getDbService(null);
		
		String currUserGUID = SessionHelper.getCurrentUserId();
		String currUserEmpGUID = SessionHelper.getCurrentEmpId();
		*/
		DbSvr db=CommonGetUserAndDB.getDbSvr(param);
		String currUserGUID = CommonGetUserAndDB.getUserID(param);
		String currUserEmpGUID = CommonGetUserAndDB.getUserEmpID(param);
		
		//查询条件
		bus.put("currUserGUID", currUserGUID);
		bus.put("currUserEmpGUID", currUserEmpGUID);
		bus.put("keyword", param[3]);
		
		/* 获取当前用户所在部门角色分组等 */
		List deptGroupRoleIds = new DaibanGetDeptRolesListPlugin().getDeptRolesList(param);
		bus.put("deptRoleList", deptGroupRoleIds);
		
		/* 获取审批方式 */
		String elFormId = "oa_fm_loanBill_edit_form";
		String eaFormId = "oa_fm_expenseAccountBill_edit_form";
		//借款单审批方式
		String elcheckWay = db.queryIdForString("mobile_common_daiban.getCheckway", new Object[]{elFormId});
		//报销单审批方式
		String eacheckWay = db.queryIdForString("mobile_common_daiban.getCheckway", new Object[]{eaFormId});
		//手工审核单据列表和条数
		List<Map> sgList = new ArrayList();
		int sgNum = 0;
		
		//借款报销列表、单据权限
		String loanListRescKey = "oa_fm_loanBill_list_form";
		String emListRescKey = "oa_fm_expenseAccountBill_list_form";
/*		boolean loadListRe = SecurityService.rescCheck(loanListRescKey);
		boolean emListRe = SecurityService.rescCheck(emListRescKey);*/
		boolean loadListRe= SecurityServiceRescCheck.RescCheck(loanListRescKey); 
		boolean emListRe = SecurityServiceRescCheck.RescCheck(emListRescKey);
		
		//借款报销审核按钮权限
		String loanCheckRescKey = "oa_fm_loanBill_edit_form.docheck";
		String emCheckRescKey = "oa_fm_expenseAccountBill_edit_form.docheck";
		boolean loanCheckRe = SecurityServiceRescCheck.RescCheck(loanCheckRescKey); 
		boolean emCheckRe = SecurityServiceRescCheck.RescCheck(emCheckRescKey);
		//待办事项权限
		String dbRescKey = "pt_workflow_worklist";
		boolean dbRe = SecurityServiceRescCheck.RescCheck(dbRescKey);  
		
		/* 手工审核需要增加保存状态相关数据 */
		/* 手工审核需要校验是否具有列表或单据+审核按钮权限，有则查询显示，无则不显示 */
		if(!"2".equals(elcheckWay)){//借款单手工审批
			if(loadListRe && loanCheckRe){
				bus.send("sgspqxLoan", "sg");
			}
		}
		if(!"2".equals(eacheckWay)){//报销单手工审批
			if(emListRe && emCheckRe){
				bus.send("sgspqxEm", "sg");
			}
		}
		if(dbRe){//有待办事项权限，有待办事项权限则按照待办事项sql查询显示
			bus.send("spqxLoan", "workFlow");
			bus.send("spqxEm", "workFlow");
		}else{//判断是否有列表或单据权限+审核按钮权限，若没有待办事项权限，有列表或单据+审核按钮权限，则在原有待办事项sql中加上数据权限来展示
			if(loadListRe && loanCheckRe){
				bus.send("spqxLoan", "workFlowSjqx");
			}
			if(emListRe && emCheckRe){
				bus.send("spqxEm", "workFlowSjqx");
			}
		}
		// 获取借款报销单待办列表
		if("getlist".equals(action)) {
			/* 2017-02-28 qcj 增加查询当前页数和每页条数 */
			bus.setControlInfo("pt_control_currentpage",param[4]);
			bus.setControlInfo("pt_control_pagesize",param[5]);
			
			List<Map> allList = new ArrayList();
			allList = db.queryIdForListByPage("mobile_daiban_emList.getAllList",bus);
			/* 2017-03-01 qcj 校验返回list是否为空 */
			if(allList != null && allList.size() > 0)
				l.addAll(allList);
		}else if("getnum".equals(action)){//获取报销单待办数目
			int num = 0;
			num =Integer.parseInt(db.queryIdForString("mobile_daiban_emList.getAllListNum",bus));
			
			int listnum=(Integer) m.get("listnum");
			m.put("listnum", listnum+num);
		}
		
		return null;
	}
}