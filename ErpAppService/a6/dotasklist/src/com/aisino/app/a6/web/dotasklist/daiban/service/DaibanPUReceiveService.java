package com.aisino.app.a6.web.dotasklist.daiban.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.aisino.platform.core.MS;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;

public class DaibanPUReceiveService  implements IService{
	@Override
	public Object doService(Object... param) {	
		String cbilltype = (String) param[0];
		String cguid = (String) param[1];

		return null;
	}

}
