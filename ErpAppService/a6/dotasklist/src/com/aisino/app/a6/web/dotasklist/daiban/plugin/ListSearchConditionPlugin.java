package com.aisino.app.a6.web.dotasklist.daiban.plugin;

import java.util.List;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.a6.web.dotasklist.daiban.plugin.DaibanGetDBDeptRolesListPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

public class ListSearchConditionPlugin {	
	/**
	 * 处理查询条件
	 * <AUTHOR>
	 * @date Aug 26, 2018
	 */
	public static void dealCondition(DataMsgBus bus,DbSvr db,String keyword) {
		String currUserGUID = SessionHelper.getCurrentUserId();
		String currUserEmpGUID = SessionHelper.getCurrentEmpId();	
		bus.put("currUserGUID", currUserGUID);
		bus.put("currUserEmpGUID", currUserEmpGUID);
		bus.put("keyword", keyword);
		
		/* 获取当前用户所在部门角色分组等 */
		List deptGroupRoleIds = DaibanGetDBDeptRolesListPlugin.getDBDeptRolesList(db);
		bus.put("deptRoleList", deptGroupRoleIds);
	}


}
