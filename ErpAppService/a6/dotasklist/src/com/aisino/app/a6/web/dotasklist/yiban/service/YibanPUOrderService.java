package com.aisino.app.a6.web.dotasklist.yiban.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;








import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.dotasklist.common.plugin.BillDesignPlugin;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;


public class YibanPUOrderService  implements IService{
	@Override
	public Object doService(Object... param) {	
		String action = (String) param[0];
		String cguid=(String) param[1];
		DbSvr db=DbSvr.getDbService(null);	
		if("browse".equals(action)){
			String cBusinessCode ="076";   
			Map m= BillDesignPlugin.getBill(cBusinessCode,cguid);   //调用接口传该单据的formid
			List<Map> main=(List<Map>) m.get("maindata");
			List<Map> detail=(List<Map>) m.get("detaildata");  //根据单据设计，获取将要显示的字段及对应的值
			
			//1.0版本要显示的字段（目的是：将千分位、或单据设置显示不出来的等特殊处理的字段加到单据设计的结果集中）
			//sql查出的字段必须都是要特殊处理的，其它不需处理的无需查出来；
			//sql中as的部分要与单据设计中段代码一致，另单据明细行sql必须要查出cguid
			List<Map> mainTemp =db.queryIdForList("mobile_daiban_pubill.puordermain", new Object[] {cguid}); 
			List<Map> detailTemp =db.queryIdForList("mobile_daiban_pubill.puorderdetail", new Object[] {cguid}); 
			
			//金额itotal权限
			boolean notHas=db.queryIdHasResult("mobile_common.checkIamtPUorder",SessionHelper.getCurrentUserId());
			Map map = new HashMap();
			
			//主表（将单据设置显示不出来的字段加到单据设计的结果集中,没有查看金额权限的，金额置空）
			if( main!=null&&!main.isEmpty()){
				Map jem = new HashMap();
				if(!notHas){									
					String je =(String) mainTemp.get(0).get("采购总金额");
					jem.put("name","采购总金额");
					jem.put("value",je);
					jem.put("code","pu_order.je");
					main.add(jem);					
				}
				map.put("main", main);	
			}
		
			
			//子表
			/*注意事项：
			 * a：没有特殊要处理的字段，不调用detailProcess该函数即可
			 * b: detailProcess第一个参数是单据设计结果集；第二个参数是1.0版本的sql部分
			 */
			if(detail!=null&&!detail.isEmpty()){
				detail=BillDesignPlugin.detailProcess(detail,detailTemp);//子表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("detail", detail);
			}
			
			//附件
			List<Map> files = db.queryIdForList("mobile_common.getFiles", new Object[] {cguid});
			if(CollectionUtil.isEmpty(main)||CollectionUtil.isEmpty(detail))
				 throw new BusinessException("并发删除,请重新查询");
			//附件权限
			String fileKey = "business_pu_puorder_billform.viewfile"; 
			boolean hasfiledown= SecurityService.rescCheck(fileKey); 
			if(!hasfiledown&&CollectionUtil.isNotEmpty(files)){
				Map m2=new HashMap();
				//此时app不允许下载附件
				m2.put("hasfiledown", "false");
				map.putAll(m2);
			}			
			if(files!=null&&!files.isEmpty())
		           map.put("file", files);
			return map;
		}else{
			return null;
		}
	}
	
}
