package com.aisino.app.a6.web.dotasklist.yiban.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;











import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.dotasklist.common.plugin.BillDesignPlugin;
import com.aisino.app.web.dotasklist.common.util.PrecDealUtil;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;


public class YibanSARetailService  implements IService{
	@Override
	//还没写
	public Object doService(Object... param) {	
		String action = (String) param[0];
		String cguid=(String) param[1];
		DbSvr db=DbSvr.getDbService(null);	
		if("browse".equals(action)){			
			String cBusinessCode ="212";  
			Map m= BillDesignPlugin.getBill(cBusinessCode,cguid);   //调用接口传该单据的formid
			List<Map> main=(List<Map>) m.get("maindata");
			List<Map> detail=(List<Map>) m.get("detaildata");  //根据单据设计，获取将要显示的字段及对应的值
			
			List<Map> mainTemp =db.queryIdForList("mobile_daiban_sabill.saretailmain", cguid);
			List<Map> detailTemp =db.queryIdForList("mobile_daiban_sabill.saretaildetail",cguid);
			
						
		    /*2018年9月3日guodx  处理零售单的单价显示精度问题*/
			for(Map mm:detailTemp){
				if(!CollectionUtil.isBlankMap(mm)){
					int saPricePre=Integer.valueOf((String) mm.get("U3_ST_PricePrecision"));
					String st_stkrecordline_itaxprice=(String) mm.get("st_stkrecordline.itaxprice");
					String iTaxPrice=String.valueOf(mm.get("iTaxPrice"));
					String price = PrecDealUtil.precDeal(saPricePre, st_stkrecordline_itaxprice, iTaxPrice);
					mm.put("st_stkrecordline.itaxprice", price);
				}				
			}
			
			Map map = new HashMap();		
			//主表（将单据设置显示不出来的字段加到单据设计的结果集中,没有查看金额权限的，金额置空）
			if( main!=null&&!main.isEmpty()){
				Map jem = new HashMap();								
					String je =(String) mainTemp.get(0).get("零售总金额");
					jem.put("name","零售总金额");
					jem.put("value",je);
					jem.put("code","st_stkrecord.je");
					main.add(jem);					
				map.put("main", main);	
			}
			
			//子表
			/*注意事项：
			 * a：没有特殊要处理的字段，不调用detailProcess该函数即可
			 * b: detailProcess第一个参数是单据设计结果集；第二个参数是1.0版本的sql部分
			 */
			
			if(detail!=null&&!detail.isEmpty()){
				detail=BillDesignPlugin.detailProcess(detail,detailTemp);//子表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("detail", detail);
			}
			if(main!=null&&!main.isEmpty()){
				main=BillDesignPlugin.mainProcess(main,mainTemp);//主表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("main", main);
			}
			
			//附件
			List<Map> files =db.queryIdForList("mobile_common.getFiles",cguid);
			if(CollectionUtil.isEmpty(main)||CollectionUtil.isEmpty(detail))
				 throw new BusinessException("并发删除,请重新查询");
			//附件权限
			String fileKey = "business_sa_retail_form.viewfile";
			boolean hasfiledown= SecurityService.rescCheck(fileKey); 			
			if(!hasfiledown&&CollectionUtil.isNotEmpty(files)){
				Map m2=new HashMap();
				//此时app不允许下载附件
				m2.put("hasfiledown", "false");
				map.putAll(m2);
			}		
			if(files!=null&&!files.isEmpty())
		           map.put("file", files);
			return map;
		}else{
			return null;
		}
	}
	
}
