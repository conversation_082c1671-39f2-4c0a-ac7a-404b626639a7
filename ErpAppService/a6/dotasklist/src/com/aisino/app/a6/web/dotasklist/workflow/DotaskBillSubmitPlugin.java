package com.aisino.app.a6.web.dotasklist.workflow;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.jbpm.api.task.Task;
import org.jbpm.pvm.internal.history.model.HistoryTaskInstanceImpl;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.a6.web.dotasklist.daiban.plugin.DaibanGetDeptRolesListPlugin;
import com.aisino.platform.core.MS;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.workflow.eform.util.Keyword;
import com.aisino.platform.workflow.fetcher.ApprovalSelctionFetcher;
import com.aisino.platform.workflow.jbpmextention.helper.FreeProcessHelper;
import com.aisino.platform.workflow.jbpmextention.helper.ProcessInstanceHelper;
import com.aisino.platform.workflow.jbpmextention.helper.TaskHelper;
import com.aisino.platform.workflow.jbpmextention.helper.TaskTransHelper;
import com.aisino.platform.workflow.jbpmextention.model.HistoryStatusImpl;
import com.aisino.platform.workflow.management.approve.ApproveDealPlugin;

/**
 * 工作流审批提交
 * <AUTHOR>
 * @date 2016年12月15日
 *
 */
public class DotaskBillSubmitPlugin {
	public Object doSubmit(DataMsgBus bus){
		DbSvr db = DbSvr.getDbService(null);
		String cguid = bus.getString("cguid");
		String userId = SessionHelper.getCurrentUserId();
		//2016-12-18 qcj 修正获取taskid方法
		Map map = new HashMap();
		map.put("currUserGUID", userId);
		map.put("deptRoleList", new DaibanGetDeptRolesListPlugin().getDeptRolesList());
		map.put("cguid", cguid);
		String taskId = db.queryIdForString("mobile_daiban_common.getDaibanBillTaskId", map);
		
		if(taskId == null || StringUtils.isBlank(taskId)){
			DotaskWorkflowApprovePlugin wfap = new DotaskWorkflowApprovePlugin();
			taskId = wfap.getTaskId(cguid);
			if(taskId == null || StringUtils.isBlank(taskId)){
				throw new BusinessException("没有指定任务ID");
			}
		}
		TaskHelper t = new TaskHelper();
		if(!t.isTaskActive(taskId)){
			throw new BusinessException("任务已经暂停,请刷新待办事项列表!");
		}
		
		String cKey = cguid;
		bus.put("taskId",taskId);
		bus.put("businessKey",cKey);
		
		//判断流程状态
		this.validateSatus(bus);
		//初始化相关数据
		this.cshData(taskId,cKey,bus);
		
		String action = bus.getString("action");
		if("commit".equals(action)){ //指定下一审批人后的提交
			/* 2017-01-20 qcj 修正注释流程走向的取值 */
			String formId = new ApproveDealPlugin().getFormId(taskId,cKey);
			MS ms = new MS(formId+"_"+Keyword.COMMIT+"_before");
			ms.doService(cKey);
			
			new ApproveDealPlugin().commitThisTask(bus,formId);
			
			return "checkSuccess";
		}else if("agree".equals(action)){	//同意
			/* 判断提交分支 */
			boolean needChoose = new ApproveDealPlugin().needChoose(bus);
			if(needChoose){
				return "chooseUsers";
			}else{
				String formId = new ApproveDealPlugin().getFormId(taskId,cKey);
				MS ms = new MS(formId+"_"+Keyword.COMMIT+"_before");
				ms.doService(cKey);
				
				new ApproveDealPlugin().commitThisTask(bus,formId);
				
				return "checkSuccess";
			}
		}else if("backprev".equals(action)){//返回上一步		
			bus.put("canBeBack", "true");
			String formId =  new ApproveDealPlugin().getFormId(taskId,cKey);
			MS ms = new MS(formId+"_"+Keyword.ROLLBACKTOPRE+"_before");
			ms.doService(cKey);
			
			AbstractForm form = new AbstractForm(null);
			new ApproveDealPlugin().rollBackToPrevious(bus,form);
			
			return "backprevSuccess";
		}else if("backorigin".equals(action)){//返回发起人
			bus.put("canBeBack", "true");
			bus.put("exeWay", "1"); //1：按流程顺序执行
			String formId =  new ApproveDealPlugin().getFormId(taskId,cKey);
			MS ms = new MS(formId+"_"+Keyword.ROLLBACKTOSTART+"_before");
			ms.doService(cKey);
			
			new ApproveDealPlugin().rollBackToStart(bus);
			
			return "backoriginSuccess";
		}else if("terminate".equals(action)){//否决并终止
			String formId =  new ApproveDealPlugin().getFormId(taskId,cKey);
			MS ms = new MS(formId+"_"+Keyword.STOP+"_before");
			ms.doService(cKey);
			
			this.stopWorkflow(bus);
			return "terminateSuccess";
		}
		
		return null;
	}
	
	private void validateSatus(DataMsgBus bus) {
		String taskId = bus.getString("taskId");
		TaskHelper t = new TaskHelper();
		if(!t.isTaskActive(taskId)){
			throw new BusinessException("当前任务已完成或者已被挂起。");
		}
		if(!t.isUserParticipateTask(Keyword.getCurrentUserId(), taskId)){
			throw new BusinessException("当前任务不属于当前用户或者已委托给别人。");
		}
		String type = t.getTaskType(taskId);
		/**
		//判断当前任务状态
		if (!"check".equals(type) && !"free".equals(type)) {
			throw new BusinessException("流程当前不处于审批状态，无法审核。");
		}
		**/
		if (!t.isTaskActive(taskId)) {
			String status = t.getTaskState(taskId);

			if ("suspended".equalsIgnoreCase(status)) {
				throw new BusinessException("该流程已经被暂停，无法审核。");
			} else {
				String sql = "select ht.DBID_ from JBPM4_HIST_TASK ht left join JBPM4_HIST_DETAIL hd on ht.DBID_ = hd.HTASK_ "
						+ "where ht.DBID_ = ? and ht.STATE_ = 'completed' and hd.CLASS_ = 'status' "
						+ "and (hd.NEW_STR_ ='cancel' or hd.NEW_STR_ = 'retreat')";
				if (DbSvr.getDbService(null).isHasResult(sql, taskId)) {
					throw new BusinessException("该流程已经被撤销，无法审核。");
				} else {
					throw new BusinessException("该任务已经完成，无法审核。");
				}
			}
		}
		bus.send("task_type", type);
	}
	
	private void cshData(String taskId,String cKey,DataMsgBus bus){
		String pid = null;
		ProcessInstanceHelper pHelper = new ProcessInstanceHelper();
		TaskHelper tHelper = new TaskHelper();
		FreeProcessHelper fHelper = new FreeProcessHelper();
		boolean isFree = false;
		if(StringUtil.isNotBlank(taskId)){
			pid = tHelper.getProcessIdIncludeHistoty(taskId);
			if(StringUtil.isBlank(pid))throw new BusinessException("该任务所属流程实例已经被删除。");
			isFree = fHelper.isFreeProcessByTaskId(taskId);
			cKey = pHelper.getProcessKeyIncludeHistory(pid);
		}
		if(StringUtil.isNotBlank(cKey)&&StringUtil.isBlank(taskId)){
			pid = pHelper.getProcessIdByKey(cKey);
			if(pid == null || StringUtils.isBlank(pid)){
				throw new BusinessException("要查看的流程已被删除。");
			}
			isFree = fHelper.isFreeProcessByInstanceId(pid);
		}
		bus.put("isFree", isFree);
		bus.put("key",cKey);
	} 
	
	private void stopWorkflow(DataMsgBus bus) {
		bus.send(Keyword.USEROPERATION, Keyword.STOP);
		String taskId = bus.getString("taskId");
		if(StringUtil.isNotBlank(taskId)){
			TaskHelper taskHelper = new TaskHelper();
			Task task = taskHelper.e().getTaskService().getTask(taskId);
			if (task == null || !taskHelper.isTaskActive(taskId)) {
				throw new BusinessException("当前任务已完成或者已被挂起，请刷新。");
			}
		}
		String comment = bus.getString("idea");
		String attitude = bus.getString("attitude");
		if(StringUtil.isBlank(comment))
			comment=attitude;
		String businessKey = bus.getString("businessKey");
		TaskTransHelper taskTransHelper = new TaskTransHelper();
		HistoryStatusImpl hs = taskTransHelper.pauseFlow(taskId,comment,attitude,bus);
		String detailId = hs.getId();
		new ApproveDealPlugin().saveFileAndFlow(bus, taskId, businessKey, detailId);
	}
	
	private List<Map> getOptions(List<Map> outcomes) {
		List<Map> opts = new ArrayList<Map>();
		for (Map button : outcomes) {
			String name = button.get("dest") == null ? (String)button.get("name") : (String) button.get("dest")
					+ ("true".equals(button.get("condition")) ? "(条件)" : "");
			if (name.indexOf("-") > -1) {
				name = name.split("-")[1];
			}
			
			List operator = new ApprovalSelctionFetcher().getButtonName(button);
			Map opt = new HashMap();
			opt.put("code", button.get("name"));
			opt.put("preAssigned", button.get("preAssigned"));
			opt.put("name", name);
			opt.put("operator", operator);
			opts.add(opt);
		}
		return opts;
	}
}