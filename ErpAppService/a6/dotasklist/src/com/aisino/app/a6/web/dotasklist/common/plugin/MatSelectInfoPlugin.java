package com.aisino.app.a6.web.dotasklist.common.plugin;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.a6.business.sa.util.plugin.SAImportUtil;
import com.aisino.aos.preference.common.Prefer;
import com.aisino.app.web.dotasklist.common.util.CommonUtil;
import com.aisino.importer.common.BizException;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.CsUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

/**
 * 选择物品后获取相关信息
 * @since 2018-09-12
 */
public class MatSelectInfoPlugin implements FormCreateListener{
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		// TODO Auto-generated method stub
		DbSvr db= DbSvr.getDbService(null);	
		Map map = new HashMap();
		List<Map> list = new ArrayList<Map>();
		String matmsg=null;
		String action = bus.getString("action");
		String billname = bus.getString("billname");
		
		if("sgselect".equalsIgnoreCase(action)){
			//手工批量选择物品
			try {
				list= this.getMatInfo(bus,db);
			} catch (BizException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}else if("scanselect".equalsIgnoreCase(action)){
			//扫码直接带入物品
			String cbarcode = bus.getString("cbarcode")==null?"":bus.getString("cbarcode").toString();
			Map mat = db.queryIdFirstRow("mobile_common_func_sql.getscanmat", bus);
			if(mat==null || mat.isEmpty()){
				matmsg = "没有该条码的物品或没有该物品的数据权限。";
			}else{
				if("saretail".equalsIgnoreCase(billname) && (mat.get("isubunit").toString().equals("1") ||mat.get("iguaranteeflag").toString().equals("1")||mat.get("ibatchflag").toString().equals("1")||"1".equalsIgnoreCase(CollectionUtil.getStringFromMap(mat, "iServisFlag"))||"1".equalsIgnoreCase(CollectionUtil.getStringFromMap(mat, "iFreeFlag")) )){
					matmsg = "扫描的是辅计量或批次或保质期或劳务或启用辅助项类物品，手机端暂不支持录入。";
				}else{
					if(mat.get("irateflag").toString().equals("2")|| mat.get("iguaranteeflag").toString().equals("1")||mat.get("ibatchflag").toString().equals("1")||"1".equalsIgnoreCase(CollectionUtil.getStringFromMap(mat, "iServisFlag"))||"1".equalsIgnoreCase(CollectionUtil.getStringFromMap(mat, "iFreeFlag"))){
						matmsg = "扫描的是批次或保质期或劳务或启用浮动换算类物品，手机端暂不支持录入。";
					}else{
					    //取物品报价
						bus.send("cmatguid", mat.get("cguid"));
						bus.send("iqty", "1");
						BigDecimal iprice = new GetPricePlugin().getprice(form, bus);
						//获取物品信息
						Map temp = new HashMap();
						Map matdata = new HashMap();
						List<Map> matlist = new ArrayList<Map>();
						temp.put("cmatguid", mat.get("cguid"));
						temp.put("iqty", 1);
						temp.put("iprice", iprice);
						matlist.add(temp);
						matdata.put("matlist", CsUtil.serializeToJson(matlist));
						bus.send("matdata", matdata);
						try {
							list = this.getMatInfo(bus, db);
						} catch (BizException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
				}
			}			
		}
		map.put("matinfo", list);
		map.put("matmsg", matmsg);
		form.setReturn(map);		
	}
	
	
	public List<Map> getMatInfo(DataMsgBus bus,DbSvr db) throws BizException{
		
		List<Map> datals = new ArrayList<Map>();
		String  matdata =  bus.getString("matdata");
		//{"matlist":[{cmatguid:"677013494159421722",iqty:2.0,iprice:1.26},{cmatguid:"677013496415305112",iqty:1.0,iprice:26},{……}]} 
		Map p = CsUtil.unserializeJson(matdata);
		List<Map> list = (List<Map>) p.get("matlist");
		if(!CollectionUtil.isEmpty(list)){
			//取出所有物品cguid，存入idls，计算税率等其它信息
			List<Object> idls = new ArrayList<Object>();
			for(int i=0;i<list.size();i++){
				idls.add(list.get(i).get("cmatguid"));			
			}
			bus.put("idls", idls);
			
			String billname = bus.getString("billname");
			if(billname==null || billname==""){
			}else{					
				if("saretail".equalsIgnoreCase(billname.trim())){
					datals=db.queryIdForList("mobile_bill_saretail_sql.getMatSelectInfo", bus);
				}	
				if("saorder".equalsIgnoreCase(billname.trim())){
					datals=db.queryIdForList("mobile_bill_saorder_sql.getMatSelectInfo", bus);
				}
				for(Map newmap :datals){ //循环找到对应记录信息
					String dataid = newmap.get("cmatguid").toString();
					for(Map oldmap :list){
						if (dataid.equalsIgnoreCase((String) oldmap.get("cmatguid"))) {
							Map temp =this.calculatePrice(db,newmap,oldmap,billname); //把oldmap及换算的值都放入newmap中
							newmap.putAll(temp);
							break;
						}
					}
				}
			}
			
		}
		new CommonUtil().getImgPath(datals);
		return datals;
	}
	
	
	public Map calculatePrice(DbSvr db, Map newmap, Map oldmap, String billname) throws BizException{
		String module=""; //判断所属模块
		boolean checkredblue=false;//判断是否要进行红蓝单判断
		
		BigDecimal iTaxRate1 = newmap.get("itaxrate") != null ? new BigDecimal(newmap.get("itaxrate").toString()) : new BigDecimal(0);
		iTaxRate1 = iTaxRate1.setScale(2, RoundingMode.HALF_UP);
		newmap.remove("itaxrate");
		
		if("saretail".equalsIgnoreCase(billname)||"saorder".equalsIgnoreCase(billname))
			module="SA";
		
    	if("saorder".equalsIgnoreCase(billname)){
  		  	checkredblue=false; 
  		    newmap.put("ipertaxrate", iTaxRate1);
    	}

    	if("saretail".equalsIgnoreCase(billname)){
    		checkredblue=true; 
    	    newmap.put("itaxrate", iTaxRate1);
    	}
    		
		
		String itaxprice_f="";
		String iunitprice_f="";

		String cMatGUID = oldmap.get("cmatguid") != null ? oldmap.get("cmatguid").toString() : "";
		String iQTY = oldmap.get("iqty") != null ? oldmap.get("iqty").toString() : "";
		String cUnitGUID=newmap.get("cUnitGUID")==null?"":newmap.get("cUnitGUID").toString();//计量单位 
		       	
	    String iprice = CollectionUtil.getStringFromMap(oldmap, "iprice");
		String ipricetype =Prefer.get("U3_SA_PriceType");//价格是否含税 taxprice/price/空
	    if("price".equalsIgnoreCase(ipricetype)){
	    	iunitprice_f=iprice;
	    }else{
	    	itaxprice_f=iprice;
	    }
		
        BigDecimal irefepriceqty= new BigDecimal(iQTY); //因为APP单位=计价单位，所以计价数量=数量

        if(irefepriceqty == BigDecimal.ZERO)
            throw new BizException("\u6570\u91CF\u4E0D\u80FD\u4E3A\u96F6\uFF01");
        
    	if("SA".equalsIgnoreCase(module)){
    		this.onlyInputSaPrice(newmap, irefepriceqty, StringUtil.isBlank(itaxprice_f) ? null : new BigDecimal(itaxprice_f), StringUtil.isBlank(iunitprice_f) ? null : new BigDecimal(iunitprice_f), iTaxRate1);
    	}
    	newmap.put("iqty", iQTY);
    	return newmap;
		
	}		
	
	
	
    private void onlyInputSaPrice(Map row, BigDecimal irefepriceqty, BigDecimal itaxprice_f, BigDecimal iunitprice_f, BigDecimal iTaxRate)
            throws BizException {
            int iAmtPrecision = Integer.parseInt(Prefer.get("U3_ST_AMTPrecision"));
            int iPricePrecision = Integer.parseInt(Prefer.get("U3_ST_PricePrecision"));
            BigDecimal bai = new BigDecimal(100);
            BigDecimal newitaxprice_f;
            BigDecimal newiunitprice_f;
            BigDecimal iTotal_F;
            BigDecimal iAMT_F;
            BigDecimal iTax_F;
            if(itaxprice_f != null){
                newitaxprice_f = itaxprice_f.setScale(iPricePrecision, RoundingMode.HALF_UP);
                SAImportUtil.checkPricePositive(newitaxprice_f, "\u542B\u7A0E\u5355\u4EF7");
                iTotal_F = newitaxprice_f.multiply(irefepriceqty).setScale(iAmtPrecision, RoundingMode.HALF_UP);
                iAMT_F = iTotal_F.divide(bai.add(iTaxRate).divide(bai), iAmtPrecision, RoundingMode.HALF_UP);
                iTax_F = iTotal_F.subtract(iAMT_F).setScale(iAmtPrecision, RoundingMode.HALF_UP);
                newiunitprice_f = iAMT_F.divide(irefepriceqty, iPricePrecision, RoundingMode.HALF_UP);
            } else{
                newiunitprice_f = iunitprice_f.setScale(iPricePrecision, RoundingMode.HALF_UP);
                SAImportUtil.checkPricePositive(newiunitprice_f, "\u65E0\u7A0E\u5355\u4EF7");
                iAMT_F = newiunitprice_f.multiply(irefepriceqty).setScale(iAmtPrecision, RoundingMode.HALF_UP);
                iTax_F = iAMT_F.multiply(iTaxRate).divide(bai, iAmtPrecision, RoundingMode.HALF_UP);
                iTotal_F = iAMT_F.add(iTax_F).setScale(iAmtPrecision, RoundingMode.HALF_UP);
                newitaxprice_f = iTotal_F.divide(irefepriceqty, iPricePrecision, RoundingMode.HALF_UP);
            }

            row.put("itaxprice_f", newitaxprice_f);
            row.put("iunitprice_f", newiunitprice_f);
            row.put("itotal_f", iTotal_F);
            row.put("iamt_f", iAMT_F);
            row.put("itax_f", iTax_F);
            row.put("irefepriceqty", irefepriceqty); 
        }
	
	
}