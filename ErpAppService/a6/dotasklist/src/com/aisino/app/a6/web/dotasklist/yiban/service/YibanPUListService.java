package com.aisino.app.a6.web.dotasklist.yiban.service;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.a6.web.dotasklist.daiban.service.DaibanPUListService;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

public class YibanPUListService  implements IService{
	@Override
	public Object doService(Object... param) {
		
		String action = (String) param[0];  //操作类型：取list 或 取数目
		List<Map> l = (List<Map>) param[1];  //list
		Map m = (Map) param[2];    //key为listnum
		
		DbSvr db=DbSvr.getDbService(null);
		DataMsgBus bus=DataMsgBus.getCurrBus();
		bus.put("currUserGUID", SessionHelper.getCurrentUserId());
		bus.put("keyword", param[3]);	
		bus.put("today",  param[4]);

		if("getlist".equals(action)) {  	// 获取采购已办列表			
			 /* 增加查询当前页数和每页条数 */
			bus.setControlInfo("pt_control_currentpage",param[5]);
			bus.setControlInfo("pt_control_pagesize",param[6]);
		     List<Map> list= new ArrayList();
		     list=db.queryIdForListByPage("mobile_yiban_pulist.getallpulist",bus);
			if(list!=null && !list.isEmpty()){
				DaibanPUListService puservice = new DaibanPUListService();
				puservice.checkIamt(db,list);
			   l.addAll(list);		
			}
		}else if("getnum".equals(action)){ // 获取采购已办列表数目
			int num =Integer.parseInt(db.queryIdForString("mobile_yiban_pulist.getallpunum",bus));
			int listnum=(Integer) m.get("listnum");
			m.put("listnum", listnum+num);
		}
		return null;
	}
}
