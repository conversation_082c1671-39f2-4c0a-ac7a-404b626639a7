package com.aisino.app.a6.web.dotasklist.daiban.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.a6.web.dotasklist.common.service.SecurityServiceRescCheck;
import com.aisino.app.a6.web.dotasklist.daiban.plugin.DaibanGetDeptRolesListPlugin;
import com.aisino.app.web.dotasklist.daiban.service.CommonGetUserAndDB;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

/**
 * 协同办公待办列表服务
 * <AUTHOR>
 *
 */
public class DaibanOAListService implements IService{

	@Override
	public Object doService(Object... param) {
		// TODO Auto-generated method stub
		
		DataMsgBus bus = new DataMsgBus();
		
		String action = (String) param[0];  //操作类型：取list 或 取数目
		List<Map> l = (List<Map>) param[1];  //list
		Map m = (Map) param[2];    //key为listnum
	/*	
		DbSvr db = DbSvr.getDbService(null);
		
		String currUserGUID = SessionHelper.getCurrentUserId();
		String currUserEmpGUID = SessionHelper.getCurrentEmpId();*/
		
		DbSvr db=CommonGetUserAndDB.getDbSvr(param);
		String currUserGUID = CommonGetUserAndDB.getUserID(param);
		String currUserEmpGUID = CommonGetUserAndDB.getUserEmpID(param);
		//查询条件
		bus.put("currUserGUID", currUserGUID);
		bus.put("currUserEmpGUID", currUserEmpGUID);
		bus.put("keyword", param[3]);
		
		/* 获取当前用户所在部门角色分组等 */
		List deptGroupRoleIds = new DaibanGetDeptRolesListPlugin().getDeptRolesList(param);
		bus.put("deptRoleList", deptGroupRoleIds);
		
		//待办事项权限
		String dbRescKey = "pt_workflow_worklist";
		boolean dbRe = SecurityServiceRescCheck.RescCheck(dbRescKey); 
		//自定义单据列表权限
		String zdyListRescKey = "pt_eform_formlist";
		boolean zdyListRe = SecurityServiceRescCheck.RescCheck(zdyListRescKey);
		
		if(dbRe){//有待办事项权限，有待办事项权限则按照待办事项sql查询显示
			bus.send("spqxZdy", "workFlow");
		}else{//判断是否有列表权限，若没有待办事项权限，有列表权限，则在原有待办事项sql中加上数据权限来展示
			if(zdyListRe){
				bus.send("spqxZdy", "workFlowSjqx");
			}

		}
		
		// 获取协同办公待办列表
		if("getlist".equals(action)) {
			bus.setControlInfo("pt_control_currentpage",param[4]);
			bus.setControlInfo("pt_control_pagesize",param[5]);
			
			List<Map> allList = new ArrayList();
			allList = db.queryIdForListByPage("mobile_daiban_oaList.getAllList",bus);
			if(allList != null && allList.size() > 0)
				l.addAll(allList);
		}else if("getnum".equals(action)){//获取报销单待办数目
			int num = 0;
			num =Integer.parseInt(db.queryIdForString("mobile_daiban_oaList.getAllListNum",bus));
			
			int listnum=(Integer) m.get("listnum");
			m.put("listnum", listnum+num);
		}
		
		return null;
	}
}