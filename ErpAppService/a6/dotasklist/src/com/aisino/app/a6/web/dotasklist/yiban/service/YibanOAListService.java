package com.aisino.app.a6.web.dotasklist.yiban.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

/**
 * 已办协同办公列表
 * <AUTHOR>
 * @since 2017-07-12
 */
public class YibanOAListService implements IService{

	@Override
	public Object doService(Object... param) {
		// TODO Auto-generated method stub
		String action = (String) param[0];  //操作类型：取list 或 取数目
		List<Map> l = (List<Map>) param[1];  //list
		Map m = (Map) param[2];    //key为listnum
		
		DbSvr db = DbSvr.getDbService(null);
		
		DataMsgBus bus = new DataMsgBus();
		bus.put("currUserGUID", SessionHelper.getCurrentUserId());
		/* 2017-01-04 qcj 修正列表加载查询和筛选条件 */
		bus.put("keyword", param[3]);//查询条件
		bus.put("today", param[4]);//查询条件
		
		if("getlist".equals(action)) {  	// 获取已办列表
			/* 2017-02-28 qcj 增加查询当前页数和每页条数 */
			bus.setControlInfo("pt_control_currentpage",param[5]);
			bus.setControlInfo("pt_control_pagesize",param[6]);
			/* 2016-02-28 qcj 修正获取分页查询结果 */
			List<Map> allList = new ArrayList();
			allList = db.queryIdForListByPage("mobile_yiban_oaList.getAllList",bus);
			/* 2017-03-01 qcj 校验返回list是否为空 */
			if(allList != null && allList.size() > 0)
				l.addAll(allList);	
		}else if("getnum".equals(action)){//获取已办数目
			int num =Integer.parseInt(db.queryIdForString("mobile_yiban_oaList.getAllListNum",bus));
			int listnum=(Integer) m.get("listnum");
			m.put("listnum", listnum+num);
		}
		return null;
	}

}
