package com.aisino.app.a6.web.dotasklist.yiban.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.dotasklist.common.plugin.BillDesignPlugin;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;

public class YibanAPPaymentService  implements IService{
	

	@Override
	public Object doService(Object... param) {	
		String action = (String) param[0];
		String cguid=(String) param[1];
		DbSvr db=DbSvr.getDbService(null);	
		if("browse".equals(action)){
			String cBusinessCode ="007ap";   //查bill_bills表，不确定是007还是007ap
			Map m= BillDesignPlugin.getBill(cBusinessCode,cguid);   //调用接口传该单据的formid
			List<Map> main=(List<Map>) m.get("maindata");
			List<Map> detail=(List<Map>) m.get("detaildata");  //根据单据设计，获取将要显示的字段及对应的值
			
			List<Map> mainTemp =db.queryIdForList("mobile_daiban_fibill.appaymentmain", cguid);
			List<Map> detailTemp =db.queryIdForList("mobile_daiban_fibill.appaymentdetail",cguid);
			
			Map map = new HashMap();
			//主表
			if(main!=null&&!main.isEmpty()){
				main=BillDesignPlugin.mainProcess(main,mainTemp);//主表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("main", main);
			}
			//子表
			if(detail!=null&&!detail.isEmpty()){
				detail=BillDesignPlugin.detailProcess(detail,detailTemp);//子表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("detail", detail);
			}
			//附件
			List<Map> files = db.queryIdForList("mobile_common.getFiles", new Object[] {cguid});
			if(CollectionUtil.isEmpty(main))
				 throw new BusinessException("并发删除,请重新查询");
			//附件权限
			String fileKey = "bus_ap_bill_form.viewfile"; 
			boolean hasfiledown= SecurityService.rescCheck(fileKey); 
			if(!hasfiledown&&CollectionUtil.isNotEmpty(files)){
				Map m2=new HashMap();
				//此时app不允许下载附件
				m2.put("hasfiledown", "false");
				map.putAll(m2);
			}
/*			//在单据设计中已经把没有字段权限的字段过滤了
 * 			boolean notHas=db.queryIdHasResult("mobile_common.checkIamtFK",SessionHelper.getCurrentUserId());
			//金额权限
			if( main!=null&&!main.isEmpty()){
				if(notHas){
					for(int i=0;i<main.size();i++){
						main.get(i).put("付款金额", "");
					}
				}
				map.put("main", main);
			}
			if(detail!=null&&!detail.isEmpty()){
				if(notHas){
					for(int i=0;i<detail.size();i++){
						detail.get(i).put("本次付款金额", "");
					}
				}
				map.put("detail", detail);
			}*/
			if(files!=null&&!files.isEmpty())
			         map.put("file", files);
			return map;
		}else {
			return null;
		}
		
	}
	
}