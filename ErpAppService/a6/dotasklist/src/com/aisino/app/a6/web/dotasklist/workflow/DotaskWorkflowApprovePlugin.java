package com.aisino.app.a6.web.dotasklist.workflow;

import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.workflowinf.AbstractWorkflowPlugin;
/**
 * 工作流流程校验
 * <AUTHOR>
 * @date 2016年12月15日
 *
 */
public class DotaskWorkflowApprovePlugin extends AbstractWorkflowPlugin{
	public String getTaskId(String cguid){
		String taskid = service.get(0).getProcessCheckTask(cguid);
		if(taskid ==null) {
			throw new BusinessException("当前任务不处于审批状态，或者不可由当前用户审批。");
		}else{
			boolean isActive = service.get(0).isTaskActive(taskid);
			if(!isActive) {
				throw new BusinessException("当前任务处于非活动状态，无法继续。");
			}
		}
		
		return taskid;
	}
}