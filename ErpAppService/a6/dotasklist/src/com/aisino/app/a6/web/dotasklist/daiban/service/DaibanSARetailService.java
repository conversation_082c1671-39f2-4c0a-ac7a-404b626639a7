
package com.aisino.app.a6.web.dotasklist.daiban.service;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.aisino.aos.preference.common.Prefer;
import com.aisino.aos.system.SessionHelper;
import com.aisino.app.a6.web.dotasklist.common.plugin.SaCheckPlugin;
import com.aisino.app.web.dotasklist.common.plugin.BillDesignPlugin;
import com.aisino.app.web.dotasklist.common.util.PrecDealUtil;
import com.aisino.app.web.dotasklist.daiban.plugin.DaibanUtil;
import com.aisino.app.web.notice.InsertCheckNoticePlugin;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.MS;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.JsUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;

public  class DaibanSARetailService  implements IService{
	

	@Override
	public Object doService(Object... param) {	
		String action = (String) param[0];
		String cguid=(String) param[1];
		DbSvr db=DbSvr.getDbService(null);	
		String firstSubmit="true";
		SaCheckPlugin sacheck = new SaCheckPlugin();
		if("browse".equals(action)){
			String cBusinessCode ="212";  
			Map m= BillDesignPlugin.getBill(cBusinessCode,cguid);   //调用接口传该单据的formid
			List<Map> main=(List<Map>) m.get("maindata");
			List<Map> detail=(List<Map>) m.get("detaildata");  //根据单据设计，获取将要显示的字段及对应的值
			
			List<Map> mainTemp =db.queryIdForList("mobile_daiban_sabill.saretailmain", cguid);
			List<Map> detailTemp =db.queryIdForList("mobile_daiban_sabill.saretaildetail",cguid);
			
						
		    /*2018年9月3日guodx  处理零售单的单价显示精度问题*/
			for(Map mm:detailTemp){
				if(!CollectionUtil.isBlankMap(mm)){
					int saPricePre=Integer.valueOf((String) mm.get("U3_ST_PricePrecision"));
					String st_stkrecordline_itaxprice=(String) mm.get("st_stkrecordline.itaxprice");
					String iTaxPrice=String.valueOf(mm.get("iTaxPrice"));
					String price = PrecDealUtil.precDeal(saPricePre, st_stkrecordline_itaxprice, iTaxPrice);
					mm.put("st_stkrecordline.itaxprice", price);
				}				
			}
			
			Map map = new HashMap();		
			//主表（将单据设置显示不出来的字段加到单据设计的结果集中,没有查看金额权限的，金额置空）
			if( main!=null&&!main.isEmpty()){
				Map jem = new HashMap();								
					String je =(String) mainTemp.get(0).get("零售总金额");
					jem.put("name","零售总金额");
					jem.put("value",je);
					jem.put("code","st_stkrecord.je");
					main.add(jem);					
				map.put("main", main);	
			}
			
			//子表
			/*注意事项：
			 * a：没有特殊要处理的字段，不调用detailProcess该函数即可
			 * b: detailProcess第一个参数是单据设计结果集；第二个参数是1.0版本的sql部分
			 */
			
			if(detail!=null&&!detail.isEmpty()){
				detail=BillDesignPlugin.detailProcess(detail,detailTemp);//子表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("detail", detail);
			}
			if(main!=null&&!main.isEmpty()){
				main=BillDesignPlugin.mainProcess(main,mainTemp);//主表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("main", main);
			}
			
			//附件
			List<Map> files =db.queryIdForList("mobile_common.getFiles",cguid);
			if(CollectionUtil.isEmpty(main)||CollectionUtil.isEmpty(detail))
				 throw new BusinessException("UDP中移动应用配置的显示字段为空或单据被并发删除,请检查");
			//附件权限
			String fileKey = "business_sa_retail_form.viewfile";
			boolean hasfiledown= SecurityService.rescCheck(fileKey); 			
			if(!hasfiledown&&CollectionUtil.isNotEmpty(files)){
				Map m2=new HashMap();
				//此时app不允许下载附件
				m2.put("hasfiledown", "false");
				map.putAll(m2);
			}		
			if(files!=null&&!files.isEmpty())
		           map.put("file", files);
			return map;
		}else if("submit".equals(action)){					
			if((String) param[2]!=null)
				firstSubmit=(String) param[2];
			DataMsgBus nbus = new DataMsgBus();  
		    List<Map> list=new  ArrayList<Map>();
		    Map m=db.getOneRecorder("select * from ST_StkRecord so where so.cGUID =?",cguid);
		    if (CollectionUtil.isBlankMap(m))
				 throw new BusinessException("单据不存在，可能已被并发删除");
		    list=db.getListResult("select * from ST_StkRecordLine sao where cheadguid=?",cguid);
		    nbus.putAll(m);
	    	nbus.send("list", list);	
	    	nbus.send("billcguid",cguid);	
	    	String msg=null;
	    	String ms=null;
	    	 if(firstSubmit.equals("true")){
	    		    boolean f=false;
		        	ms=sacheck.Check("business_sa_retail_form",nbus,"disauth_sa_check","审核");
		        	if(StringUtil.isNotBlank(ms)&&ms.length()>3){
		        		nbus.put("flagStr", ms);
		        		f=true;
		        	}
		        	msg=sacheck.Check("business_sa_retail_form",nbus,"beforecheck_sa","审核");
		        	if(msg!=null&&!msg.equals("{}")){
		        		return msg;
		        	}else{
		        		if(f)
		        		return JsUtil.toJsonObjectWithNull(sacheck.getZheKouCheck(ms,"审核")).toString();
		        	}
		        }
	    	msg=sacheck.Check("business_sa_retail_form",nbus,"docheck","审核");
  		    DaibanUtil.updateCheckWay("ST_StkRecord",cguid);
	    	return msg;
		}else  if("beforeWFAgree".equals(action)){//处理工作流审批的beforecheck
			if((String) param[2]!=null)
				firstSubmit=(String) param[2];	
			DataMsgBus nbus = new DataMsgBus();  
		    List<Map> list=new  ArrayList<Map>();
		    Map m=db.getOneRecorder("select * from ST_StkRecord so where so.cGUID =?",cguid);
		    if (CollectionUtil.isBlankMap(m))
				 throw new BusinessException("单据不存在，可能已被并发删除");
		    list=db.getListResult("select * from ST_StkRecordLine sao where cheadguid=?",cguid);
		    nbus.putAll(m);
	    	nbus.send("list", list);	
	    	nbus.send("billcguid",cguid);
	    	nbus.send("isworkflow",true);
	    	String msg=null;
	    	String ms=null;
	        if(firstSubmit.equals("true")){
	        	 boolean f=false;
		        	ms=sacheck.Check("business_sa_retail_form",nbus,"disauth_sa_check","审核");
		        	if(StringUtil.isNotBlank(ms)&&ms.length()>3){
		        		nbus.put("flagStr", ms);
		        		f=true;
		        	}
		        	msg=sacheck.Check("business_sa_retail_form",nbus,"beforecheck_sa","审核");
		        	if("{}".equals(msg)){
		        		return null;
		        	}
		        	if(msg!=null){
		        		return msg;
		        	}else{
		        		if(f)
		        		return JsUtil.toJsonObjectWithNull(sacheck.getZheKouCheck(ms,"审核")).toString();
		        	}
	        }
	    	return msg;
		}else {
			return null;
		}
	}
 
}