package com.aisino.app.a6.web.dotasklist.daiban.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.aos.preference.common.Prefer;
import com.aisino.app.a6.web.dotasklist.daiban.plugin.DaibanGetDeptRolesListPlugin;
import com.aisino.app.a6.web.dotasklist.daiban.plugin.ListSearchCheckPlugin;
import com.aisino.app.web.dotasklist.daiban.service.CommonGetUserAndDB;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.db.Eso;
import com.aisino.platform.view.DataMsgBus;

public class DaibanSTListService  implements IService{
	@Override
	public Object doService(Object... param) {
		
		String action = (String) param[0];  //操作类型：取list 或 取数目
		List<Map> l = (List<Map>) param[1];  //list
		Map m = (Map) param[2];    //key为listnum
		String keyword= (String) param[3];
		
		DataMsgBus bus=DataMsgBus.getCurrBus();
		DbSvr db=CommonGetUserAndDB.getDbSvr(param);
		String currUserGUID = CommonGetUserAndDB.getUserID(param);
		String currUserEmpGUID = CommonGetUserAndDB.getUserEmpID(param);
		
		bus.put("currUserGUID", currUserGUID);
		bus.put("currUserEmpGUID", currUserEmpGUID);
		bus.put("keyword", keyword);
		
		/* 获取当前用户所在部门角色分组等 */
		List deptGroupRoleIds = new DaibanGetDeptRolesListPlugin().getDeptRolesList(param);
		bus.put("deptRoleList", deptGroupRoleIds);
		
		/**
		 *库存待办：库存调拨单，材料出库单，销售出库单 
		 *改用通用方法，设置参数,方便将来增加其他出入库单
		 * */
		/*库存调拨单*/
		Map transMap =new HashMap(); 
		transMap.put("formid", "business_st_StTrans"); 
		transMap.put("rescKey", "business_st_StTrans.docheck"); 
		transMap.put("billKey", "business_st_StTrans"); 
		transMap.put("listKey", "business_st_StTransList"); 
		transMap.put("table", "StTrans"); 
		ListSearchCheckPlugin.dealcheck(bus, db, transMap);
		
		/*材料出库单*/
		Map ccstkdMap =new HashMap(); 
		ccstkdMap.put("formid", "business_st_stinout_clckd"); 
		ccstkdMap.put("rescKey", "business_st_stinout_clckd.docheck"); 
		ccstkdMap.put("billKey", "business_st_stinout_clckd"); 
		ccstkdMap.put("listKey", "business_st_stinout_clckd_list"); 
		ccstkdMap.put("table", "stinout"); 
		ListSearchCheckPlugin.dealcheck(bus, db, ccstkdMap);
		
		/*销售出库单*/
		Map xcstkdMap =new HashMap();
		xcstkdMap.put("formid", "business_sa_sastkout"); 
		xcstkdMap.put("rescKey", "business_sa_sastkout.docheck"); 
		xcstkdMap.put("billKey", "business_sa_sastkout"); 
		xcstkdMap.put("listKey", "business_sa_sastkout_list_form"); 
		xcstkdMap.put("table", "sastkout"); 
		ListSearchCheckPlugin.dealcheck(bus, db, xcstkdMap);
		/*没加期初销售出库单，没啥权限*/
		
//		String sttrFormId = "business_st_StTrans";
//		String sttrcheckWay = db.queryIdForString("mobile_common_daiban.getCheckway", new Object[]{sttrFormId});
		
	    /*库存调拨单手工审核需要先校验当前用户是否有审核权限*/
//		String rescKey = "business_st_StTrans.docheck";
//		String billKey = "business_st_StTrans";
//		String listKey = "business_st_StTransList";
//		String test = "business_st_StTrans.check";
		
//		boolean trre = SecurityServiceRescCheck.RescCheck(rescKey);  
//		boolean trbill= SecurityServiceRescCheck.RescCheck(billKey); 
//		boolean trlist = SecurityServiceRescCheck.RescCheck(listKey); 
//		boolean tesr1 = SecurityServiceRescCheck.RescCheck(test);
		
	    /*库存调拨单单手工审批*/
//		if(!"2".equals(sttrcheckWay)){
//			   if(trre&&trbill||trre&&trlist){  
//				   bus.put("sgStTrans", "true");
//			}
//		}
	
		/*查看该用户是否有协同-待办事项的菜单权限。*/
//		boolean hasworkflow = SecurityServiceRescCheck.RescCheck("pt_workflow_worklist");
//		
//		if(hasworkflow){
//			 bus.put("wfStTrans", "true");
//	    }else{
//		    if(trre&&trbill)
//		    bus.put("wffilterStTrans","true");	    
//	    }	
		
		if("getlist".equals(action)) {  	
			/* 增加查询当前页数和每页条数 */
			bus.setControlInfo("pt_control_currentpage",param[4]);
			bus.setControlInfo("pt_control_pagesize",param[5]);
			List<Map> list = new ArrayList();
			
			//list=db.queryIdForListByPage("mobile_daiban_stlist.getallstlist",bus);
			//2017年8月26日 10:03:27 调拨仓库权限处理
			Eso eso= Eso.make(SttransStoreFilter("mobile_daiban_stlist.getallstlist",db), bus);
			list= db.executeQueryByPage(eso,bus);
			if(list!=null&&!list.isEmpty()){
			    //checkIamt(db,currUserGUID,list);
				l.addAll(list);	
			}
		}else if("getnum".equals(action)){
			int num=0;
			//num=Integer.parseInt(db.queryIdForString("mobile_daiban_stlist.getallstnum",bus)); 
			//2017年8月26日 10:03:27  调拨仓库权限处理
			Eso eso= Eso.make(SttransStoreFilter("mobile_daiban_stlist.getallstnum",db), bus);
			num=Integer.parseInt(db.exeEsoForString(eso));
			m.put("listnum", num);
		}
 		return null;
	}
	
	//校验该用户是否有字段（金额）的权限，若没有该权限，将金额置为空。
	/*public void checkIamt(DbSvr db,String currUserGUID,List<Map> poList){
		boolean notHas=db.queryIdHasResult("mobile_common.checkIamtSAorder", currUserGUID);
		if(notHas){
			for(int i=0;i<poList.size();i++){
				poList.get(i).put("je", "");
			}
			
		}*/

		public String  SttransStoreFilter(String sqlid,DbSvr db){
			
			String A6_ST_TRANS_DATARIGHT = Prefer.get("A6_ST_TRANS_DATARIGHT");
			String sql=db.getSQL(sqlid);
			String nsql="";
			//根据参数替换权限配置的sql
			if ( "indataright".equals(A6_ST_TRANS_DATARIGHT) ) {//仅控制调入
	            String replace_in="ST_StkTrans.cInDeptGUID=cm_department.cguid,"+
	            		"ST_StkTrans.cInEmpGUID=CM_Employee.cGUID,"+"ST_StkTrans.cInstoreGUID=CM_Storehouse.cguid,ST_StkTransLine.cInstoreGUID=CM_StoreHouse.cguid";
	             nsql=sql.replaceAll("STDateDataRightSetBeforeQueryPlugin_feild", replace_in);
			}else if ("outdataright".equals(A6_ST_TRANS_DATARIGHT)) {//仅控制调出
				String replace_out="ST_StkTrans.cOutDeptGUID=cm_department.cguid,"+
	    				"ST_StkTrans.cOutEmpGUID=CM_Employee.cGUID,"+"ST_StkTrans.cOutstoreGUID=CM_Storehouse.cguid,ST_StkTransLine.cOutstoreGUID=CM_StoreHouse.cguid";
	             nsql=sql.replaceAll("STDateDataRightSetBeforeQueryPlugin_feild", replace_out);
			}else {//控制调入调出
				String replace_all="ST_StkTrans.cInDeptGUID=cm_department.cguid,ST_StkTrans.cOutDeptGUID=cm_department.cguid,"+
	    				"ST_StkTrans.cInEmpGUID=CM_Employee.cGUID,ST_StkTrans.cOutEmpGUID=CM_Employee.cGUID,"+"ST_StkTrans.cInstoreGUID=CM_Storehouse.cguid,ST_StkTransLine.cInstoreGUID=CM_StoreHouse.cguid,ST_StkTrans.cOutstoreGUID=CM_Storehouse.cguid,ST_StkTransLine.cOutstoreGUID=CM_StoreHouse.cguid";
	            nsql=sql.replaceAll("STDateDataRightSetBeforeQueryPlugin_feild", replace_all);
			}
			return nsql;
		}
}
