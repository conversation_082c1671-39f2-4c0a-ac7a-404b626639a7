package com.aisino.app.a6.web.dotasklist.common.util;

import com.aisino.aos.bill.keygen.KeygenUtil;
import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

public class GetBillInfoUtil {	
	/**
	 * <AUTHOR>
	 * @date sep 014.2018
	 */
	public String getBillCode(DbSvr db,DataMsgBus bus,String cFormId,String cBusinessCode) {
		//APP端默认都是自动编号
		String cAdminOrgnId = SessionHelper.getCurrentAdminOrgnId();
		String cOrgnId = SessionHelper.getCurrentOrgnId();
		//自动编号规则ID
		String billSequenceId = db.queryIdForString("mobile_bill_common_sql.getBillCode", new Object[]{cAdminOrgnId,cOrgnId,cAdminOrgnId,cOrgnId,cFormId,cBusinessCode});
		String ccode = KeygenUtil.getNextKeyBySequencecGuid(billSequenceId, bus);
		return ccode;
	}
}
