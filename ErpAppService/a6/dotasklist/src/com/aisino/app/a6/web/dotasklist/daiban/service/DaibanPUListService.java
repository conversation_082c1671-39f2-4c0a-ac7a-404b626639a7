package com.aisino.app.a6.web.dotasklist.daiban.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.a6.web.dotasklist.daiban.plugin.ListSearchCheckPlugin;
import com.aisino.app.a6.web.dotasklist.daiban.plugin.ListSearchConditionPlugin;
import com.aisino.app.web.dotasklist.daiban.service.CommonGetUserAndDB;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

public class DaibanPUListService  implements IService{
	@Override
	public Object doService(Object... param) {
		String action = (String) param[0];  //操作类型：取list 或 取数目
		List<Map> l = (List<Map>) param[1];  //list
		Map m = (Map) param[2];    //key为listnum
		String keyword= (String) param[3];
		
		DataMsgBus bus=DataMsgBus.getCurrBus();
		DbSvr db=CommonGetUserAndDB.getDbSvr(param);	
		ListSearchConditionPlugin.dealCondition(bus,db,keyword);
		
		  /*采购计划*/ 
		Map planMap =new HashMap(); 
		planMap.put("formid", "business_pu_purPlan_form"); 
		planMap.put("rescKey", "business_pu_purPlan_form.docheck"); 
		planMap.put("billKey", "business_pu_purPlan_form"); 
		planMap.put("listKey", "business_pu_planlist_form"); 
		planMap.put("table", "PuPlan"); 
		ListSearchCheckPlugin.dealcheck(bus, db, planMap);
		
		  /*采购订单*/ 
		Map orderMap =new HashMap(); 
		orderMap.put("formid", "business_pu_puorder_billform"); 
		orderMap.put("rescKey", "business_pu_puorder_billform.docheck"); 
		orderMap.put("billKey", "business_pu_puorder_billform"); 
		orderMap.put("listKey", "business_PU_puorder_listform"); 
		orderMap.put("table", "PuOrder"); 
		ListSearchCheckPlugin.dealcheck(bus, db, orderMap);
		
		if("getlist".equals(action)) {  	
				/* 增加查询当前页数和每页条数 */
				bus.setControlInfo("pt_control_currentpage",param[4]);
				bus.setControlInfo("pt_control_pagesize",param[5]);
				List<Map> list = new ArrayList();
				list=db.queryIdForListByPage("mobile_daiban_pulist.getallpulist",bus);
				if(list!=null&&!list.isEmpty()){
					checkIamt(db,list);
					l.addAll(list);	
				}
		}else if("getnum".equals(action)){
			int num=0;
			num=Integer.parseInt(db.queryIdForString("mobile_daiban_pulist.getallpunum",bus)); 
			m.put("listnum", num);
		}
 		return null;
	}
	

	//校验该用户是否有字段（金额）的权限，若没有该权限，将金额置为空。
	public void checkIamt(DbSvr db,List<Map> list){
		String currUserGUID = SessionHelper.getCurrentUserId();
		boolean notHasPo=db.queryIdHasResult("mobile_common.checkIamtPUorder", currUserGUID);
		if(notHasPo){
			for(int i=0;i<list.size();i++){
				if("076".equals(list.get(i).get("hidden_cbilltype"))){
					list.get(i).put("je", "");
				}
			}
		}		
	}

}
