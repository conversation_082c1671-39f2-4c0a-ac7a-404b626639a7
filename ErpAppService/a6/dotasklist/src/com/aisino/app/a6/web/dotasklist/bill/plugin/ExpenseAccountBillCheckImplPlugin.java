package com.aisino.app.a6.web.dotasklist.bill.plugin;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.mail.MessagingException;

import com.aisino.a6.oa.common.dao.OADataOpt;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.db.Eso;
import com.aisino.platform.mail.Mail;
import com.aisino.platform.view.DataMsgBus;

/**
 * 报销单审核校验
 * <AUTHOR>
 *
 */
public class ExpenseAccountBillCheckImplPlugin implements BillCheckInterface{

	@Override
	public void afterCheck(DataMsgBus bus) {
		// TODO Auto-generated method stub
		
		OADataOpt dao = new OADataOpt();
		Eso esoUpdateAMT = dao.getEsoById("OA_FM_ExpenseAccountBill.updateExpenseActualAMT", bus);//修改报销单主表实际收款金额
		Eso esoUpdateLineAMT = dao.getEsoById("OA_FM_ExpenseAccountBill.updateExpenseLineActualAMT", bus);//修改报销单子表实际收款金额
	
		List<Eso> list = new ArrayList<Eso>();
		if(esoUpdateAMT!=null){
			list.add(esoUpdateAMT);
		}
		if(esoUpdateLineAMT!=null){
			list.add(esoUpdateLineAMT);
		}
		boolean boo = dao.executeAllEsosInTransaction(list);//执行更新事务
		if(boo){
			sendEmail(bus);//发送邮件通知
		}
	}
	
	private void sendEmail(DataMsgBus bus) {
		DbSvr svr = DbSvr.getDbService(null);
		
		String getMainSQL = "SELECT eab.cGUID cguid,eab.cCode ccode,se.cName statusName,eab.iTotalAMT iTotalAMT,"
				+ " eab.cUserGUID cUserGUID "
				+ " FROM OA_ExpenseAccountBill eab"
				+ " LEFT JOIN OA_StaticEnum se on se.cGUID=eab.cStatusEnumGUID"
				+ " WHERE eab.cGUID=?";
		
		Map dataMap = svr.getOneRecorder(getMainSQL, new Object[]{bus.getString("cguid")});
		if(dataMap!=null){
			DataMsgBus emailBus = new DataMsgBus();
			emailBus.send("cCode", dataMap.get("cCode"));
			emailBus.send("statusName", dataMap.get("statusName"));
			emailBus.send("iTotalAMT", dataMap.get("iTotalAMT"));
			
			String empMail = svr.getStringResult("select e.cEmail from AOS_RMS_USER u " +
					"LEFT JOIN CM_Employee e on e.cGUID = u.cEMP where u.cGUID=?", new Object[]{dataMap.get("cUserGUID")});
			
			if(empMail != null && !"".equals(empMail)) {
				Mail expenseMail = new Mail("oa_expense", empMail, null, null, emailBus);
				try {
					expenseMail.send();
				} catch (MessagingException e) {
					e.printStackTrace();
				}
			}
		}
	}
}