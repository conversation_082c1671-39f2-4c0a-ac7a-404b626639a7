
package com.aisino.app.a6.web.dotasklist.daiban.service;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.app.web.dotasklist.common.plugin.BillDesignPlugin;
import com.aisino.app.web.dotasklist.daiban.plugin.DaibanUtil;
import com.aisino.app.web.notice.InsertCheckNoticePlugin;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.JsUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;

public  class DaibanCAApvoucherService  implements IService{
	

	@Override
	public Object doService(Object... param) {	
		String action = (String) param[0];
		String cguid=(String) param[1];
		DbSvr db=DbSvr.getDbService(null);	
		if("browse".equals(action)){
			String cBusinessCode ="105";   
			Map m= BillDesignPlugin.getBill(cBusinessCode,cguid);   //调用接口传该单据的formid
			List<Map> main=(List<Map>) m.get("maindata");
			List<Map> detail=(List<Map>) m.get("detaildata");  //根据单据设计，获取将要显示的字段及对应的值
			
			List<Map> mainTemp =db.queryIdForList("mobile_daiban_fibill.caapvouchermain", cguid);
			List<Map> detailTemp =db.queryIdForList("mobile_daiban_fibill.caapvoucherdetail",cguid);
			
			Map map = new HashMap();
			//主表
			if(main!=null&&!main.isEmpty()){
				main=BillDesignPlugin.mainProcess(main,mainTemp);//主表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("main", main);
			}
			//子表
			if(detail!=null&&!detail.isEmpty()){
				detail=BillDesignPlugin.detailProcess(detail,detailTemp);//子表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("detail", detail);
			}
			
			List<Map> files = db.queryIdForList("mobile_common.getFiles", new Object[] {cguid});
			if(CollectionUtil.isEmpty(main))
				 throw new BusinessException("并发删除,请重新查询");
			if(files!=null&&!files.isEmpty())
		           map.put("file", files);
			return map;
		}else if("submit".equals(action)){					
			
			DataMsgBus nbus = new DataMsgBus();  
			String msg=null;
		    List<Map> list=new  ArrayList<Map>();
		    /*Map m=db.getOneRecorder(db.getSQL("mobile_daiban_fibill.caapvouchermain"), cguid);*/
		    Map m=db.getOneRecorder("select * from CA_OtherPayment so where so.cGUID =?",cguid);
		    if (CollectionUtil.isBlankMap(m))
				 throw new BusinessException("单据不存在，可能已被并发删除");
		    nbus.putAll(m);
	    	/*list=db.queryIdForList("mobile_daiban_fibill.caapvoucherdetail", cguid);*/
		    list=db.getListResult("select * from CA_OtherPaymentLine sao where cHeadGUID=?",cguid);
	    	nbus.send("list", list);	
	    	nbus.send("billcguid", cguid);	
	    	msg=Check("finance_ca_apvoucher_edit_form",nbus,"docheck");
	    	return JsUtil.toJsonObjectWithNull(new HashMap()).toString();
		}else{
			return null;
		}
		
		
		}
	
		public String Check(String fromid,DataMsgBus bus,String action){
			String cbillcode=bus.getString("cVouCode");
			String cCreatorGUID=bus.getString("cCreatorGUID");
			DbSvr db=DbSvr.getDbService(null);	
	    	try {
	    		  bus.setNewAction(action);
	    		  AbstractForm.callFormSubmitService(fromid,bus,false);
	    		  DaibanUtil.updateCheckWay("CA_OtherPayment",bus.getString("cguid"));
			} catch (Exception e){
				//回滚审核状态等等
				db.rollback();
				//存储审批失败信息到PT_Notice
				new  InsertCheckNoticePlugin().InsertCheckNotice(cCreatorGUID,e.getMessage());
				throw new  BusinessException("单据号"+cbillcode+"："+e.getMessage());
					
			}
				return  null;
		}
		
	
	
		
     
}