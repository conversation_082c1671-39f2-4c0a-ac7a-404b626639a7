package com.aisino.app.a6.web.dotasklist.common.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.workflow.jbpmextention.helper.ProcessInstanceHelper;
import com.aisino.platform.workflow.management.history.HistDataLoader;

/**
 * 返回流程日志
 * <AUTHOR>
 * @date 2016年12月26日
 *
 */
public class FlowlogService  implements IService{
	@Override
	/*查看流程日志服务*/
	public Object doService(Object... param) {	
		String cguid=(String) param[0]; //cguid 
		String cbilltype=(String) param[1]; //cguid 
		DbSvr db = DbSvr.getDbService(null);	
		DataMsgBus bus = new DataMsgBus();
		bus.send("key", cguid);
		//返回流程日志list
		List<Map> returnList = new ArrayList<Map>();
		
		HistDataLoader handler = new HistDataLoader();
		List<Map> historyList = handler.loadHistoryList(db, bus);
		//增加操作相关
		String pId = new ProcessInstanceHelper().getProcessIdByKey(cguid);
		historyList = handler.process(historyList,pId,null);
		
		/*
		 * 返回List<Map>样式：
		 * 操作人员，操作：审批意见
		 */
		if(historyList != null && historyList.size() > 0){
			for(Map map : historyList){
				/* 2017-03-13 qcj 校验操作为空的处理 */
				String op = map.get("op")==null?"":String.valueOf(map.get("op"));
				/* 2017-01-16 qcj 修正审批意见取值 */
				String approveinfo = map.get("approveinfo")==null?"":String.valueOf(map.get("approveinfo"));
				if(!"".equals(op)){
					op = op + "。"+ approveinfo;
				}
				Map rtnMap = new HashMap();
				rtnMap.put("operatorname", map.get("operatorname"));
				rtnMap.put("op", op);
				/* 2017-01-09 qcj 增加审核时间列 */
				rtnMap.put("endtime", map.get("endtime"));
				returnList.add(rtnMap);
			}
		}else{
			/*
			 * 非工作流加入流程日志：
			 * */
			  if("066".equals(cbilltype)){//销售订单
				 returnList = db.queryIdForList("mobile_common_liuChengRizhi.saorder",cguid,cguid,cguid); 	
			  }else if("076".equals(cbilltype)){//采购订单
				  returnList = db.queryIdForList("mobile_common_liuChengRizhi.puorder",cguid,cguid,cguid); 	
			  }else if("082".equals(cbilltype)){//付款单
				  returnList = db.queryIdForList("mobile_common_liuChengRizhi.AP_Payment",cguid,cguid,cguid); 	
			  }else if("105".equals(cbilltype)){//其它付款单
				  returnList = db.queryIdForList("mobile_common_liuChengRizhi.CA_OtherPayment",cguid,cguid); 	
			  }else if("072".equals(cbilltype)){//加入库存调拨单，销售发票流程日志  20170712  zrc
				  returnList = db.queryIdForList("mobile_common_liuChengRizhi.sttrans",cguid,cguid,cguid); 	
			  }else if("085".equals(cbilltype)){//销售发票
				  returnList = db.queryIdForList("mobile_common_liuChengRizhi.sainvoice",cguid,cguid,cguid); 	
			  }else if("oa_008".equals(cbilltype)){//借款单
				  returnList = db.queryIdForList("mobile_common_liuChengRizhi.Loan",cguid,cguid,cguid); 	
			  }else if("oa_010".equals(cbilltype)){//报销单
				  returnList = db.queryIdForList("mobile_common_liuChengRizhi.ExpenseAccount",cguid,cguid,cguid); 	
			  }else if("220".equals(cbilltype)){//采购计划
				  returnList = db.queryIdForList("mobile_common_liuChengRizhi.puplan",cguid,cguid,cguid); 	
			  }else if("212".equals(cbilltype)){//零售单
				  returnList = db.queryIdForList("mobile_common_liuChengRizhi.saretail",cguid,cguid,cguid); 	
			  }else if("020".equals(cbilltype)){//销售出库单
				  returnList = db.queryIdForList("mobile_common_liuChengRizhi.stkreronds",cguid,cguid,cguid); 	
			  }else if("070".equals(cbilltype)){//材料出库单
				  returnList = db.queryIdForList("mobile_common_liuChengRizhi.stkreronds",cguid,cguid,cguid); 	
			  }
			  
			
		}
		
		return returnList;
	}
}