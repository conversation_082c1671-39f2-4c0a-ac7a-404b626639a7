package com.aisino.app.a6.web.dotasklist.daiban.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.app.web.dotasklist.common.plugin.BillDesignPlugin;
import com.aisino.app.web.dotasklist.daiban.plugin.DaibanUtil;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;

/**
 * 报销单单据详情
 * <AUTHOR>
 * @date 2016年12月10日
 *
 */
public class DaibanEMExpenseAccountService implements IService{

	@Override
	public Object doService(Object... param) {
		// TODO Auto-generated method stub
		String action = (String) param[0];
		String cguid=(String) param[1];
		DbSvr db=DbSvr.getDbService(null);	
		if("browse".equals(action)){
			/* 2017-08-01 quchj 修正报销单取值来自于单据设计 */
			String cBusinessCode ="oa_010";   
			Map m= BillDesignPlugin.getBill(cBusinessCode,cguid);   //调用接口传该单据的formid
			List<Map> main=(List<Map>) m.get("maindata");
			List<Map> detail=(List<Map>) m.get("detaildata");
			
			//List<Map> main =db.queryIdForList("mobile_daiban_emBill.expenseAccountMain", new Object[] {cguid});
			/* 2016-12-30 qcj 增加流程审批标题校验处理 */
			/* 2016-12-30 qcj 去除流程审批标题校验处理
			if(main != null && main.size() > 0){
				for(Map m : main){
					if(m.get("hidden_proName") != null){
						String nameArray[] = String.valueOf(m.get("hidden_proName")).split(",");
						m.put("标题", nameArray[0]);
					}
				}
				
			}
			*/
			
			/* 2017-03-13 qcj 增加并发删除校验 */
			if(CollectionUtil.isEmpty(main) || CollectionUtil.isEmpty(detail))
				 throw new BusinessException("并发删除,请重新查询");
			
			List<Map> mainTemp =db.queryIdForList("mobile_daiban_emBill.expenseAccountMain", new Object[] {cguid});
			List<Map> detailTemp =db.queryIdForList("mobile_daiban_emBill.expenseAccountDetail", new Object[] {cguid});
			/*注意事项：
			 * a：没有特殊要处理的字段，不调用detailProcess该函数即可
			 * b: detailProcess第一个参数是单据设计结果集；第二个参数是1.0版本的sql部分
			 */
			main=BillDesignPlugin.mainProcess(main,mainTemp);//主表（将1.0版本的特殊处理字段合并到单据设计结果中）
			detail=BillDesignPlugin.detailProcess(detail,detailTemp);//子表（将1.0版本的特殊处理字段合并到单据设计结果中）			
			
			/* 2016-12-30 qcj 增加附件查询 */
			List<Map> files = db.queryIdForList("mobile_common.getFiles", new Object[] {cguid});
			Map map = new HashMap();
			map.put("main", main);
			map.put("detail", detail);
			map.put("file", files);
			return map;
		}else if("submit".equals(action)){
			String formid="oa_fm_expenseAccountBill_edit_form";
			DataMsgBus nbus = new DataMsgBus();
			//nbus.put("cGuid" , "619153736316762327");
			//cguid = "619153736316762327";
			nbus.setNewAction("docheck");
			List<Map>  main=new  ArrayList<Map>();
			String sql ="select * from OA_ExpenseAccountBill where cguid = ?";
		    main=db.getListResult(sql, cguid);
		    /* 2017-03-02 qcj 增加对并发删除的校验 */
		    if(main == null || main.size() < 1){
		    	throw new BusinessException("当前单据已被并发删除，请重新刷新列表.");
		    }
		    Map ma=new HashMap<String,String>();
		    ma=main.get(0);
		    //String cCreatorGUID=(String)ma.get("cCreatorGUID");
		    String str,q="";
	    	for(int i=0;i<ma.size();i++){
	    		str=ma.keySet().toArray()[i].toString();
	    		if(ma.get(str)!=null){
	    			q=ma.get(str).toString();
	    			nbus.send(str,q);
	    		}else{
	    			nbus.send(str,null);
	    		}
	    	}
	    	List<Map> list=new  ArrayList<Map>();
	    	String listSql = "select * from OA_ExpenseAccLine where cheadguid = ?";
	    	list=db.getListResult(listSql, cguid);
	    	nbus.send("list", list);	
	    	nbus.send("billcguid", cguid);
	    	
	    	/* 2017-03-03 qcj 修正手工审核返回值 */
			Object objMsg=AbstractForm.callFormSubmitService(formid,nbus,false);
			String objMsgStr = objMsg.toString().split("'")[1];
			Map msg=new HashMap();
			if(objMsgStr.indexOf("成功")>0){
				/* 2017-03-02 qcj app手工审核成功更新单据已保存的审核方式 */
				String checkSQL = "select top 1 1 from OA_ExpenseAccountBill where cStatusEnumGUID='checked' and cCheckWay='1' and cguid=?";
				if(db.isHasResult(checkSQL, new Object[]{cguid})){
					DaibanUtil.updateCheckWay("OA_ExpenseAccountBill",cguid);
				}
			}else{
				msg.put("msg", objMsgStr);
			}
			
			return msg.toString();
		}else{
			return null;
		}
	}

}
