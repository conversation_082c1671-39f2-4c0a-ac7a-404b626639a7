package com.aisino.app.a6.web.dotasklist.bill.plugin;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.aisino.a6.business.sa.price.plugin.SaGetHighLowPricePlugin;
import com.aisino.a6Common.busutil.MaterialFreeNameInfo;
import com.aisino.app.web.dotasklist.common.util.AppendixUtil;
import com.aisino.aos.bill.common.BillUtils;
import com.aisino.aos.bill.plugin.BillCheckPlugin;
import com.aisino.aos.bill.vo.BillSetting;
import com.aisino.aos.preference.common.Prefer;
import com.aisino.aos.system.SessionHelper;
import com.aisino.app.a6.web.dotasklist.common.plugin.GetPricePlugin;
import com.aisino.app.a6.web.dotasklist.common.plugin.SaCheckPlugin;
import com.aisino.app.a6.web.dotasklist.common.util.DealBillFunctionUtil;
import com.aisino.app.a6.web.dotasklist.common.util.GetBillInfoUtil;
import com.aisino.app.web.dotasklist.common.plugin.PrepareParamPlugin;
import com.aisino.app.web.dotasklist.common.util.CommonUtil;
import com.aisino.app.web.dotasklist.daiban.plugin.DaibanUtil;
import com.aisino.importer.common.BizException;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.MS;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.CsUtil;
import com.aisino.platform.util.DateUtil;
import com.aisino.platform.util.JsUtil;
import com.aisino.platform.util.NoCaseMap;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.veng.json.dauglas.JSONArray;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

/**
 * 销售订单新增相关
 * @since 2018-10-23
 */
public class SAOrderBillPlugin implements FormCreateListener{
	private static final long serialVersionUID = 1L;
	DealBillFunctionUtil dealfunc = new DealBillFunctionUtil();
	SaCheckPlugin sacheck= new SaCheckPlugin();
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		// TODO Auto-generated method stub
		DbSvr db = DbSvr.getDbService(null);
		String action = bus.getString("action");
		BillSetting billSetting = BillUtils.getBillSettingVoByFormId("business_sa_saorder");
		if("new".equals(action)){ /*新增是数据加载*/
			Map rtn = new HashMap();
			String newRescKey = "business_sa_saorder.new";
			if(dealfunc.checkBillPower(newRescKey) == true){
				//获取单据默认日期
				Date today =DateUtil.today();
				String dDate = DateUtil.date2Str(today);
				rtn.put("dDate", dDate);
				
				//获取默认业务类型
				PrepareParamPlugin param = new PrepareParamPlugin();
				param.prepareReferParam(bus, "saorder");
				Map cbustypeinfo = db.queryIdFirstRow("mobile_common_reference_sql.referCbustype", bus);
				String cbustypeguid = cbustypeinfo.get("code").toString();
				rtn.put("cbustypeguid",cbustypeguid);
                rtn.put("cbustypename", cbustypeinfo.get("name")); 
				
				//获取默认业务流程
				bus.put("cBusType", cbustypeguid);
				Map cbusprocessinfo = db.queryIdFirstRow("mobile_common_reference_sql.referCbusprocess", bus);
				rtn.put("cbusprocessguid",cbusprocessinfo.get("code"));
                rtn.put("cbusprocessname", cbusprocessinfo.get("name")); 
				 		
	             //获取单据编号
	            String cBillCode=new GetBillInfoUtil().getBillCode(db, bus, "business_sa_saorder", "066");
	            rtn.put("cBillCode",cBillCode);
				form.setReturn(rtn);

			}else{
				throw new BusinessException("当前用户没有销售订单新增权限!");
			}
			
		}else if("load".equals(action)){//修改查看时数据加载
			Integer otherbill= (Integer) (billSetting==null?0:billSetting.getImodifybill());	
			Integer zero = 0;
			String cguid = bus.getString("cguid");
			Map mainMap = db.queryIdFirstRow("mobile_bill_saorder_sql.getSaOrderMain", new Object[]{cguid});
			String cCurGUID = db.getStringResult("select top 1 cguid from GL_Currency where iNative = 1 and iStatus = 1");
			if(mainMap != null && mainMap.size() > 0){
				List detailMap = db.queryIdForList("mobile_bill_saorder_sql.getSaOrderDetail", new Object[]{cguid});
				if(detailMap != null && detailMap.size() > 0){
					List<Map> detail = new CommonUtil().getImgPath(detailMap);
					String iAuditStatus = String.valueOf(mainMap.get("iauditstatus")); //不可修改状态	
					String cCreatorCurrent = SessionHelper.getCurrentUserId();	
					if(otherbill.equals(zero) && !cCreatorCurrent.equalsIgnoreCase(mainMap.get("cCreatorGUID").toString())){
					    mainMap.put("icanmodify", "n");	
					    mainMap.put("icannotreason", "不能修改他人单据");	
				    }else if(!"tsaved".equals(iAuditStatus) && !"saved".equals(iAuditStatus) && !"revise".equals(iAuditStatus)){
						    mainMap.put("icanmodify", "n");	
						    mainMap.put("icannotreason", "单据状态有控制，不能被修改，仅供查看");	
					}else if(!cCurGUID.equals(mainMap.get("cCurGUID"))){
							mainMap.put("icanmodify", "n");	
						    mainMap.put("icannotreason", "APP不支持外币修改，仅供查看");
					}else{
						String editRescKey = "business_sa_saorder.modifybf";
						boolean editRe = SecurityService.rescCheck(editRescKey); //校验是否有修改权限
						if(editRe){
							boolean flag=false;
							for(int i=0;i<detail.size();i++){
								Map row = (Map) detail.get(i);
								//不支持情况的校验和判断
								String iUsedRebateAMT = row.get("iUsedRebateAMT")!= null ? row.get("iUsedRebateAMT").toString():"0E-9";
								String iTaxQuotedPrice =row.get("iTaxQuotedPrice")!= null ? row.get("iTaxQuotedPrice").toString():"0E-9";
								String iQuotedPrice =row.get("iQuotedPrice")!= null ? row.get("iQuotedPrice").toString():"0E-9";	
								
								BigDecimal iDisRate = row.get("iDisRate") != null ? new BigDecimal(row.get("iDisRate").toString()) : new BigDecimal(0);
								iDisRate = iDisRate.setScale(2, RoundingMode.HALF_UP);
								
								if(row.get("irateflag").toString().equals("2")||row.get("iguaranteeflag").toString().equals("1")||row.get("ibatchflag").toString().equals("1") || !"0E-9".equalsIgnoreCase(iUsedRebateAMT)
									|| !"0E-9".equalsIgnoreCase(iTaxQuotedPrice) || !"0E-9".equalsIgnoreCase(iQuotedPrice)|| new BigDecimal(100.00).compareTo(iDisRate)!=0){
									flag=true;
									break;
								}
							}
							if(flag){
								mainMap.put("icanmodify", "n");
								mainMap.put("icannotreason", "物品明细行存在或批次、保质期、浮动换算、返利、报价、折扣等情况的物品，手机端暂不支持修改");	
							}else{
								mainMap.put("icanmodify", "y");
							}		
						}else{
							mainMap.put("icanmodify", "n");
							mainMap.put("icannotreason", "没有修改单据权限，仅供查看");	
						}
					}					
									
					List file = db.queryIdForList("mobile_bill_saorder_sql.getFile", new Object[]{cguid});
					Map rtn = new HashMap();
					rtn.put("main", mainMap);
					rtn.put("detail", detail);
					rtn.put("file", file);
					form.setReturn(rtn);
				}else{
					throw new BusinessException("当前单据已被删除,请重新刷新列表!");
			}
			}else{
				throw new BusinessException("当前单据已被删除,请重新刷新列表!");
			}
		}else if("save".equals(action)||"tsave".equals(action)){//保存或暂存			
			String alldata = bus.getString("alldata");
			alldata = alldata.substring(1, alldata.length()-1);
			Map data = CsUtil.unserializeJson(alldata);
			Map head = (Map) data.get("main");//主表
			List<Map> detail = (List) data.get("detail");//明细
			String cguid = CollectionUtil.getStringFromMap(head, "cguid");
			String dDate = CollectionUtil.getStringFromMap(head, "dDate");
			String cCustGUID = CollectionUtil.getStringFromMap(head, "cCustGUID");
			String cEmpGUID = CollectionUtil.getStringFromMap(head, "cEmpGUID");
			String cDeptGUID = CollectionUtil.getStringFromMap(head, "cDeptGUID");
			String cCurGUID = db.getStringResult("select top 1 cguid from GL_Currency where iNative = 1 and iStatus = 1");
			String cTemplateId = db.getStringResult("select top 1 cGuid from BILL_TEMPLATE where cFormID = 'business_sa_saorder' and cDefault = '1' and iStatus = '1'");
		    String cCheckWay=billSetting==null?BillSetting.ManualCheck:billSetting.getCcheckway();
		    
			Map cus = new NoCaseMap();
			cus.put("cCustGUID", cCustGUID);
			Map cusinfo = db.queryIdFirstRow("a6_business_billtemplate.customerinfoload",cus);
						
			DataMsgBus newbus = new DataMsgBus();			
			if(cusinfo!=null &&(!cusinfo.isEmpty()) )
			    newbus.putAll(cusinfo);
			if(head!=null &&(!head.isEmpty()) )
				newbus.putAll(head);
			
			/* 2018-10-30 guodxj 增加对附件的处理 */
			List paramFileList = new ArrayList();
			
			if("save".equals(action)){
				newbus.setNewAction("save");
			}else {
				newbus.setNewAction("tsave");
			}
			
			if(cguid!=null&&cguid!=""&&!cguid.isEmpty()){
				newbus.setBusFormState("editold");
			}else{
				newbus.setBusFormState("editnew");
				//如果已事先上传附件
				String cGroupGuid = bus.getString("cgroupguid");
				if(StringUtils.isNotBlank(cGroupGuid) && StringUtils.isBlank(cguid))
					cguid = cGroupGuid;
			}
			
			//修改保存时，需要对旧的附件做特殊处理。
			List<String> filedata = (List<String>) data.get("filedata");
			if(filedata != null && !filedata.isEmpty()){
				for(String s :filedata){
					String fileGUID =s;
					Object[] param = {fileGUID};
					paramFileList.add(param);
				}
			}
			newbus.put("cGUID", cguid);
			newbus.put("cEmpGUID", cEmpGUID);//取出来又放进去 是因为customerinfoload中的值会覆盖
			newbus.put("cDeptGUID", cDeptGUID);//取出来又放进去 是因为customerinfoload中的值会覆盖
			newbus.put("cBusType", CollectionUtil.getStringFromMap(head, "cbustypeguid"));
			newbus.put("cBusProcess", CollectionUtil.getStringFromMap(head, "cbusprocessguid"));
			newbus.put("cPayTime", dDate);
			newbus.put("cReObject", "4"); //返利对象

			newbus.put("cCurGUID", cCurGUID);
			newbus.put("cTemplateId", cTemplateId);
			newbus.put("cCheckWay", cCheckWay);
			newbus.put("cBillType", "066");
			newbus.put("cBusCode", "066");
			newbus.put("cSysType", "SA");
			newbus.put("iCurRate", "1");
			newbus.put("iCurRate2", "1");
			newbus.put("defultunit", "SA");//有SA、PU、ST;		

			
		    int inumber=1; //存行号
			for(Map line : detail){
				//前端获取的数据
				String cguid_mx = CollectionUtil.getStringFromMap(line, "cguid");
				String cmatguid = CollectionUtil.getStringFromMap(line, "cmatguid");
				String iunitprice_f = CollectionUtil.getStringFromMap(line, "iunitprice_f"); 
				String itaxprice_f = CollectionUtil.getStringFromMap(line, "itaxprice_f");
				String iamt_f = CollectionUtil.getStringFromMap(line, "iamt_f");  //无税金额
				String itotal_f = CollectionUtil.getStringFromMap(line, "itotal_f"); //含税金额
				String ipertaxrate = CollectionUtil.getStringFromMap(line, "ipertaxrate"); //含税金额
				String itax_f = CollectionUtil.getStringFromMap(line, "itax_f"); //税额
				String iqty = CollectionUtil.getStringFromMap(line, "iqty"); 
				String cunitguid = CollectionUtil.getStringFromMap(line, "cunitguid"); //单位，实际取物品的销售计价单位
				String ctimestamp = CollectionUtil.getStringFromMap(line, "ctimestamp");
				String cstoreguid = CollectionUtil.getStringFromMap(line, "cstoreguid");
				Map matinfo = db.queryIdFirstRow("a6_grid_materaltemplate.all_Material_refer",line);
				line.putAll(matinfo);
				//导致凡是matinfo查出来的字段与APP界面字段重复的，都需要重新置值。
			    
				String cmatname = CollectionUtil.getStringFromMap(matinfo, "cmatname");  //物品名称
				String cmunitguid = CollectionUtil.getStringFromMap(matinfo, "cmunitguid");  //主计量单位cguid
				String irateflag = CollectionUtil.getStringFromMap(matinfo, "irateflag");  //单位是否是浮动
				String ichangerate = null; //求换算率
				if(!"2".equals(irateflag)){
		            String sql2 = "select ur.iChangeRate from CM_Material m,CM_MatUnitRate ur where m.cGUID=? and ur.cMatGUID=m.cGUID and ur.cUnitGUID=?";
		            ichangerate = db.getStringResult(sql2, new Object[] {cmatguid, cunitguid});
		            if(StringUtil.isBlank(ichangerate))
		            	throw new BusinessException("换算率不能为空");
		        } else{
		        	 throw new BusinessException(cmatname+"是浮动换算物品，暂不支持录入。");
		        }
		        int iPricePrecision = Integer.parseInt(Prefer.get("U3_ST_PricePrecision"));
				BigDecimal iunitqty = (new BigDecimal(iqty)).multiply(new BigDecimal(ichangerate));
				BigDecimal imunittaxprice_f = new BigDecimal(itotal_f).divide(iunitqty, iPricePrecision, RoundingMode.HALF_UP);
			    BigDecimal imunitprice_f = new BigDecimal(iamt_f).divide(iunitqty, iPricePrecision, RoundingMode.HALF_UP);
			    line.put("cguid", cguid_mx); //因为把整个mat都放到line中，导致有多个cguid，所以要取出来再放进去
			    line.put("ctimestamp", ctimestamp); 
			    line.put("cstoreguid", cstoreguid); 
			    line.put("cunitguid", cunitguid); 
			    line.put("iqty", iqty); 
			    line.put("iunitprice_f",iunitprice_f);
				line.put("iunitprice",iunitprice_f);
				line.put("itaxprice_f",itaxprice_f);
				line.put("itaxprice",itaxprice_f);
				line.put("iamt_f", iamt_f);
				line.put("iamt", iamt_f);
				line.put("itotal_f", itotal_f);
				line.put("itotal", itotal_f);
				line.put("itotal", itotal_f);
				line.put("ipertaxrate", ipertaxrate);
				line.put("itax_f", itax_f);
				line.put("itax", itax_f);
				line.put("iquotedprice", 0);
				line.put("itaxquotedprice", 0);
				line.put("iquotedtotal_f", 0);
				line.put("idisrate", 100);
				line.put("idisratecon", 0);
				line.put("idisamt_f", 0);
				line.put("idisamt", 0);
				line.put("idispatchqty", 0);
				
				line.put("irefepriceqty",iqty);
				line.put("iunitqty",iunitqty);
				line.put("cmunitguid",cmunitguid);
				line.put("imunitprice_f",imunitprice_f);
				line.put("imunitprice",imunitprice_f);
				line.put("ichangerate",ichangerate);
				line.put("ibefrebateamt",itotal_f);
				line.put("ibefrebateprice",itaxprice_f);
				line.put("iusedrebateamt",0);
				line.put("inumber",inumber++);
				line.put("iMUnitTaxPrice_F", imunittaxprice_f);
			
				//最高最低售价
		//		Map HighLowPrice = SaGetHighLowPricePlugin.get_quotedHLPrice(line, cCustGUID, dDate, db, form, newbus);
		//		line.putAll(HighLowPrice);
				MaterialFreeNameInfo.makecFreeInfo(db, line); //处理辅助项
				
				//获取cpriceinfo,及最高最低价
				 Map result = new HashMap();    
			     bus.send("formid", "business_sa_saorder");
		         result = new GetPricePlugin().getquotedprice(line, cCustGUID, "", db, form, newbus);
		         if(!CollectionUtil.isBlankMap(result)){ 
		        	 String cpriceinfo = (String) result.get("cpriceinfo");
			         line.put("ihighprice", result.get("ihighprice"));
			         line.put("ilowprice", result.get("ilowprice"));
			         line.put("cpriceinfo", result.get("cpriceinfo"));
		         }
			}
			
			newbus.put("list", detail);
			Map rtn = new HashMap(); //返回值
			StringBuffer msg=new StringBuffer();
			boolean savecheckflag=false; //保存检查是否通过	

	    	//不论哪种情况，都需将以下参数put到bus中返回：
	    	//msg：保存、审核成功或失败的提示信息；
	    	//icanmodify:操作后，单据的编辑状态；

	    	//保存校验情况下，还多返回一个savetmsg
	    	//审核校验情况下，还多返回一个submitmsg
			newbus.put("msg", null);
			newbus.put("icanmodify", "y");
			
			//保存或暂存			
			try{
				if("tsave".equals(action)){
					AbstractForm.doFormSubmit("business_sa_saorder", newbus); //返回结果为objMsg，值为空
					newbus.put("msg", "暂存成功！");
					/*如果有需要删除的附件则执行附件删除操作 */
					if(paramFileList != null && paramFileList.size() > 0){
						String fileSQL = "DELETE FROM AOS_FILE_FILES WHERE cGuid=?";
						db.batchUpdate(fileSQL, paramFileList);
					}
					//删除返回的不必要信息，保留主表。
					newbus.remove("list");				
					rtn.put("rtn", newbus);
					form.setReturn(rtn);
				}else { 
					//保存
					String firstsave = "true";	//最高最低价校验等不通过时，需要弹密码框等含2次交互的请求
					String firstsaveparam =bus.getString("firstsave")==null?"":bus.getString("firstsave").toString();
					if(!"".equals(firstsaveparam))
						firstsave=firstsaveparam;  //如果传了firstsave=false，则代表第二次提交，改成false			 				    	
			    	String savemsg=null;
			    	String savems=null;
			    	if("true".equals(firstsave)){ //要走保存前的各种校验
			    		    boolean f=false;
			    		    savems=sacheck.Check("business_sa_saorder",newbus,"disauth_sa_save","保存"); 
				        	if(StringUtil.isNotBlank(savems)&&savems.length()>3){
				        		newbus.put("flagStr", savems);
				        		f=true;
				        	}					    	
					    	savemsg=sacheck.Check("business_sa_saorder",newbus,"beforesave_sa","保存");
				        	if(savemsg!=null&&!savemsg.equals("{}")){					        										        		
				        		newbus.put("savemsg", savemsg);
				        		newbus.put("cguid", newbus.getString("cGUID"));
								//删除返回的不必要信息，保留主表。
								newbus.remove("list");
								newbus.remove("matinfos");								
								rtn.put("rtn", newbus);
								form.setReturn(rtn);								
				        	}else{
				        		if(f){	 		
				        			newbus.put("savemsg", savemsg);
				        			newbus.put("cguid", newbus.getString("cGUID"));
									//删除返回的不必要信息，保留主表。
									newbus.remove("list");
									newbus.remove("matinfos");
									rtn.put("rtn", newbus);
									form.setReturn(rtn);									
				        		}else{
				        			savecheckflag=true;  //保存检查通过，继续执行
				        		}
				        	}
				     }	
			    	
			    	//保存检查通过，继续执行。不通过，则直接停留在页面上
			    	//第二次提交跳过校验，也可以继续执行
			       if(savecheckflag||"false".equals(firstsave)){
			    	   try{
				    	    savemsg=sacheck.Check("business_sa_saorder",newbus,"save","保存");
				    	    if(savemsg!=null&&!savemsg.equals("{}"))
				    	    	msg.append(savemsg);
							//如果保存成功，则继续审核				
							msg.append("保存成功。");		
							newbus.put("msg", msg);
							
							//将单据附件存为永久附件
							AppendixUtil.updateToPermanent(newbus.getString("cGUID"), db);
							/*如果有需要删除的附件则执行附件删除操作 */
							if(paramFileList != null && paramFileList.size() > 0){
								String fileSQL = "DELETE FROM AOS_FILE_FILES WHERE cGuid=?";
								db.batchUpdate(fileSQL, paramFileList);
							}
							db.commit();//将保存的结果提交
							
							//以下逻辑开始审核
							try{		
								//bus是请求过来包含的参数
								//newbus是组装后
								newbus=this.afterSaveAudit(db,bus,newbus,form);
								//删除返回的不必要信息，保留主表。
								newbus.remove("list");
								newbus.remove("matinfos");
								rtn.put("rtn", newbus);
								form.setReturn(rtn);		
							    
							}catch (Exception e) {
								Map  map = new HashMap();
				    		    map.put("errmsg","保存成功，但"+e.getMessage()+" 审核失败！");
				   				form.setReturn(map);	
								e.printStackTrace();									
							}				
			    	   }catch (Exception e) {
							// TODO Auto-generated catch block
			    		    Map  map = new HashMap();
			    		    map.put("errmsg",e.getMessage()+" 保存失败！");
			   				form.setReturn(map);
							e.printStackTrace();
						}						
			       }	    	
				}	
			}catch (Exception e) {
				// TODO Auto-generated catch block
				 Map  map = new HashMap();
	    		 map.put("errmsg",e.getMessage());
	    		 form.setReturn(map);
				e.printStackTrace();
			}			
		}else if("submit".equals(action)){//非工作流第二次提交
			Map  map = new HashMap();
			StringBuffer sbf = new StringBuffer();
			String msg =bus.getString("msg")==null?"":bus.getString("msg");
			sbf.append(msg);
			
			map.put("cguid", bus.getString("cguid"));
			map.put("table", "SA_Order");
			map.put("tableline", "SA_OrderLine");
			DataMsgBus newbus =dealfunc.getBusData(db, map);
			
			newbus.put("flagStr", bus.getString("flagstr")); //校验信息；
			newbus.put("cTimeStamp", bus.getString("ctimestamp"));//校验单据是否发生变化；
			
			String submitmsg=sacheck.Check("business_sa_saorder",newbus,"docheck","审核");
  		    DaibanUtil.updateCheckWay("SA_Order", bus.getString("cguid"));
  		    sbf.append("审核成功。");
  		    if(submitmsg!=null&&!submitmsg.equals("{}")) 							  		    	
  		    	sbf.append(submitmsg);	  		    
	    	String flag = dealfunc.canModifyCheck("checked","business_sa_saorder.modifybf",newbus.getList("list"));	
	    	
	    	newbus.put("icanmodify", flag);
			newbus.put("msg",sbf);
			
	    	Map rtn=new HashMap();
			//删除返回的不必要信息，保留主表。
			newbus.remove("list");
			rtn.put("rtn", newbus);
			form.setReturn(rtn);
		}else if("wfsubmit".equals(action)){//工作流选择模板后，点提交	
			//组织数据，将submit需要的信息补充完整至newbus
			Map  map = new HashMap();
			map.put("cguid", bus.getString("cguid"));
			map.put("table", "SA_Order");
			map.put("tableline", "SA_OrderLine");
			DataMsgBus newbus =dealfunc.getBusData(db, map);
			
			newbus.put("cTimeStamp", bus.getString("ctimestamp"));//校验单据是否发生变化；			
			newbus.send("wfProcessId", bus.getString("processcode"));
			
			dealfunc.WorkflowSubmit(form,newbus);	
			String flag = dealfunc.canModifyCheck(newbus.getString("iAuditStatus"),"business_sa_saorder.modifybf",newbus.getList("list"));		
						
			newbus.put("icanmodify", flag);
			newbus.put("msg",bus.getString("msg")+"提交成功。");
			Map rtn=new HashMap();
			//删除返回的不必要信息，保留主表。
			newbus.remove("list");
			rtn.put("rtn", newbus);
			form.setReturn(rtn);
		}else if("delete".equals(action)){	
			String cguid = bus.getString("cguid");
			bus.setNewAction(action);
			String msg=null;
			Object objmap;
			Map mainMap = db.queryIdFirstRow("mobile_bill_saorder_sql.getSaOrderMain", new Object[]{cguid});
			String iAuditStatus = CollectionUtil.getStringFromMap(mainMap, "iAuditStatus");
			if(mainMap != null && mainMap.size() > 0){
				String editRescKey = "business_sa_saorder.delete";
				boolean editRe = SecurityService.rescCheck(editRescKey); //校验是否有删除权限
				if(editRe){
					if("tsaved".equals(iAuditStatus) ||  "saved".equals(iAuditStatus) || "revise".equals(iAuditStatus)){
						objmap =AbstractForm.callFormSubmitService("business_sa_saorder",bus,false);
						msg="删除成功。";
					}else{
						throw new BusinessException("单据已审核或正在审核中,无法删除！");
					}
				}else{
					throw new BusinessException("本用户没有删除权限,无法删除！");
				}
			}else{
				throw new BusinessException("当前单据已不存在,无法删除！");
			}
		
			Map rtn=new HashMap();
			rtn.put("rtn", msg);
			form.setReturn(rtn);
		}
	}
	

	public DataMsgBus afterSaveAudit(DbSvr db,DataMsgBus bus,DataMsgBus newbus,AbstractForm form){
		String cCheckWay=newbus.getString("cCheckWay");				
		String iAuditStatus=newbus.getString("iAuditStatus");
		String cGUID=newbus.getString("cGUID");

		boolean submitcheckflag=false;
		String flag=null;  //单据操作之后能否编辑；

		StringBuffer msg=new StringBuffer();
		 if(newbus.getString("msg")!=null)
 	    	msg.append(newbus.getString("msg"));

		List<Map> detail=newbus.getList("list");
		/* 获取审批方式
		 * 0手工审核 1自动审核 2 流程审批
		 */	
		
		//组织数据，将submit需要的信息补充完整至newbus 
	    List<Map> list=new  ArrayList<Map>();
	    Map m=db.getOneRecorder("select * from SA_Order so where so.cGUID =?",cGUID);
	    m.put("table", "SA_Order");
	    if (CollectionUtil.isBlankMap(m))
			 throw new BusinessException("单据不存在，可能已被并发删除");
	    list=db.getListResult("select * from SA_OrderLine sao where cheadguid=?",cGUID);
	    newbus.putAll(m);
	    newbus.send("list", list);	
	    newbus.send("billcguid",cGUID);	
		
	    if("0".equals(cCheckWay)){//手工审核
	    	msg.append("请到待办事项中审核。");
	    	flag = dealfunc.canModifyCheck(iAuditStatus,"business_sa_saorder.modifybf",detail);
	    }else if("1".equals(cCheckWay)){  //自动审批，保存完自动审核。
			String firstsubmit="true";
			String firstsubmitparam =bus.getString("firstsubmit")==null?"":bus.getString("firstsubmit").toString();
			if(!"".equals(firstsubmitparam))
				firstsubmit=firstsubmitparam;  //如果传了firstSubmit=false，则代表第二次提交，改成false	
			
	    	String submitmsg=null;
	    	String submitms=null;
	    	if(firstsubmit.equals("true")){  //审核前的各种校验
	    		    boolean f=false;
	    		    submitms=sacheck.Check("business_sa_saorder",newbus,"disauth_sa_check","审核");
		        	if(StringUtil.isNotBlank(submitms)&&submitms.length()>3){
		        		newbus.put("flagStr", submitms);
		        		f=true;
		        	}
		        	submitmsg=sacheck.Check("business_sa_saorder",newbus,"beforecheck_sa","审核");
		        	if(submitmsg!=null&&!submitmsg.equals("{}")){
		        		flag = dealfunc.canModifyCheck(iAuditStatus,"business_sa_saorder.modifybf",detail);
						newbus.put("submitmsg", submitmsg);			
		        	}else{
		        		if(f){
		        			submitmsg=JsUtil.toJsonObjectWithNull(sacheck.getZheKouCheck(submitms,"审核")).toString();
		        			flag = dealfunc.canModifyCheck(iAuditStatus,"business_sa_saorder.modifybf",detail);
							newbus.put("submitmsg", submitmsg);									        
		        		}else{
		        			submitcheckflag=true;//审核校验通过，继续下一步
		        		}
		        	}
		        }
	    	
	    	//校验通过，继续下一步
	    	if(submitcheckflag){
	    		submitmsg=sacheck.Check("business_sa_saorder",newbus,"docheck","审核");
	  		    DaibanUtil.updateCheckWay("SA_Order",cGUID);
	  		    msg.append("审核成功。");
	  		    if(submitmsg!=null&&!submitmsg.equals("{}")) 							  		    	
	  		    	msg.append(submitmsg);	  		    
		    	iAuditStatus="checked";
		    	flag = dealfunc.canModifyCheck(iAuditStatus,"business_sa_saorder.modifybf",detail);	
	    	}
	    	//第二次提交跳过校验，则应另发请求（不能再走保存了）
	    
	    }else  if("2".equals(cCheckWay)){  //直接保存加提交。此处处理提交。
	    	//自动执行action=submitChoose操作
	        if(!"saved".equals(iAuditStatus) && !"revise".equals(iAuditStatus)){
	        	throw new BusinessException("当前单据状态不能提交送审，请重新刷新列表数据!");
	        }
	        MS ms = new MS("AOS.GetBillFilteredProcessTemplate");
			List<Map> workflowTemps = (List<Map>)ms.doService("business_sa_saorder",SessionHelper.getCurrentAdminOrgnId(),false?"change":null);
			if(CollectionUtil.isEmpty(workflowTemps)){
				throw new BusinessException("未找到可用的流程模板！");
			}
			if(workflowTemps.size()==1){//直接执行提交
				String processCode = (String)workflowTemps.get(0).get("code");										
				newbus.put("wfProcessId", processCode);										
				dealfunc.WorkflowSubmit(form,newbus);//newbus中需要包含：单据cGUID，单据主表表名table，如"ST_StkRecord"，单据时间戳cTimeStamp，
				flag = dealfunc.canModifyCheck(newbus.getString("iAuditStatus"),"business_sa_saorder.modifybf",detail);
				msg.append("提交成功。");
			}else{//多个流程模板，先弹出模板选择框，返回chooseList
				if(BillCheckPlugin.State_Saved.equalsIgnoreCase(iAuditStatus)){//保存待提交
					List rtnList = new ArrayList();
					Map rtnMap = new HashMap();
					for(Map map : workflowTemps){//实际流程名称和id
						String processName = CollectionUtil.getStringFromMap(map, "name");
						String processCode=CollectionUtil.getStringFromMap(map, "code");
						//返回流程选项
						Map rtnprocess = new HashMap();
						rtnprocess.put("processCode", processCode);
						rtnprocess.put("processName", processName);
						rtnList.add(rtnprocess);
					}
					flag = dealfunc.canModifyCheck(iAuditStatus,"business_sa_saorder.modifybf",detail);
					newbus.put("chooseList", rtnList);
				}
				
				if(BillCheckPlugin.State_Revise.equalsIgnoreCase(iAuditStatus)){//退回修改中
					String processtemplateId = "";
					MS s0 = new MS("AOS.BillQueryWorkFlow");
					Object wfVar = s0.doService(form, newbus);
					if(wfVar !=null){
						processtemplateId=CollectionUtil.getStringFromMap((Map)wfVar,"processtemplate");
					}else{
						throw new BusinessException("提交按钮初始化错误，未找到退回发起人单据再次提交的流程!");
					}
					
					newbus.send("wfProcessId", processtemplateId);
					dealfunc.WorkflowSubmit(form,newbus);
					flag = dealfunc.canModifyCheck(newbus.getString("iAuditStatus"),"business_sa_saorder.modifybf",detail);
					msg.append("提交成功。");
				}
			}	
	    }
	    
		newbus.put("msg", msg);
		newbus.put("icanmodify", flag);
	    return newbus;
	}

}