package com.aisino.app.a6.web.dotasklist.daiban.service;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.platform.core.ProductInfo;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;

/**
 * 自定义表单单据加载
 * <AUTHOR>
 * @since 2017-07-10
 */
public class DaibanOAZdyService implements IService{

	@Override
	public Object doService(Object... param) {
		// TODO Auto-generated method stub
		
		String action = (String) param[0];
		String cguid=(String) param[1];
		DbSvr db=DbSvr.getDbService(null);	
		if("browse".equals(action)){
			/* 2017-07-17 quchj 增加对服务器上htm文件是否存在的判断 */
			String filePath = ProductInfo.getWebRealPath() + "/oa/html/" + cguid + ".htm";
			File file = new File(filePath);
			boolean existsFile = file.exists();
			
			List<Map> main =db.queryIdForList("mobile_daiban_oaBill.loadZdyMain", new Object[] {cguid});
			
			/* 增加并发删除校验 */
			if(CollectionUtil.isEmpty(main))
				 throw new BusinessException("并发删除,请重新查询");
			
			/* 2017-07-12 quchj 增加附件查询 */
			List<Map> files = db.queryIdForList("mobile_daiban_oaBill.getWfFiles", new Object[] {cguid});
			
			Map map = new HashMap();
			map.put("main", main);
			map.put("file", files);
			map.put("zdyExists", existsFile);
			return map;
		}else{
			return null;
		}
	}

}
