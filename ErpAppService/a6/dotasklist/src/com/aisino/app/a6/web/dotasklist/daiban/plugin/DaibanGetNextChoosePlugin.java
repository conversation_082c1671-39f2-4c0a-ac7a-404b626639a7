package com.aisino.app.a6.web.dotasklist.daiban.plugin;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.aisino.app.a6.web.dotasklist.workflow.DotaskWorkflowApprovePlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;
import com.aisino.platform.workflow.eform.util.RmsUtil;
import com.aisino.platform.workflow.fetcher.ApprovalSelctionFetcher;
import com.aisino.platform.workflow.jbpmextention.countersign.CountersignInfo;
import com.aisino.platform.workflow.jbpmextention.helper.TaskHelper;
import com.aisino.platform.workflow.management.approve.NextApproverInitPlugin;
import com.aisino.platform.workflow.management.definition.UserListLoader;

/**
 * 获取指定下一节点审批人数据
 * <AUTHOR>
 * @date 2016年12月17日
 *
 */
public class DaibanGetNextChoosePlugin implements FormCreateListener{

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		// TODO Auto-generated method stub
		String cguid = bus.getString("cguid");	//单据cguid  
		
		//获取taskid
		DotaskWorkflowApprovePlugin wfap = new DotaskWorkflowApprovePlugin();
		String taskId = wfap.getTaskId(cguid);
		/* 2017-01-20 qcj 修正指定下一审批节点和审批人相关设置 */
		String getType = bus.getString("getType");
		if("getOptions".equals(getType)){
			TaskHelper h = new TaskHelper();
			List<Map> outcomes = h.getCurrentOutcomes(taskId, null, null);
			List<Map> options = getOptions(outcomes);
			
			//判断节点是不是会签，如果是会签节点
			//1.如果该会签节点还没有被处理过，则可以选择出口；
			//2.如果该会签节点已经被处理过，则以第一个选择的人为准，不选择出口；
			TaskHelper helper = new TaskHelper();
			String destinationName = null,lastTransition = null;
			CountersignInfo m = helper.getCountersignInfo(taskId);
			if(m != null){
				for (Map.Entry<String, String> entry : m.getConclusions().entrySet()) {
					String trans = entry.getValue();
					if(trans!=null) {
						lastTransition = trans;
						break;
					}
					
				}
				
				List<Map> rtnOptions = new ArrayList();
				if(lastTransition != null){//已处理节点，流程走向不可编辑
					for(Map map : options){
						if(lastTransition.equals(map.get("code"))){
							map.put("isContersign", true);
							rtnOptions.add(map);
						}
					}
				}else{//非已处理节点，默认显示第一个
					/* 2017-03-17 qcj 修正非已处理节点正常显示审批多出口 */
					rtnOptions.addAll(options);
				}
				
				form.setReturn(rtnOptions);
			}else{
				form.setReturn(options);
			}
		}else{
			String selectedActName = bus.getString("selectedActName");
			
			ArrayList<Map> users = new ArrayList<Map>();
			String assignDealWay=null;
			ArrayList existUsers = new ArrayList();
			TaskHelper helper = new TaskHelper();
			
			Map actApprovers = new NextApproverInitPlugin().getApproverByTaskIdAndActName(taskId,selectedActName,null,null);
		
			List<String> approvers = (List<String>) actApprovers.get("userList");
			String preAssign = String.valueOf(actApprovers.get("preAssigned"));
			
			String pId = null;
			if(StringUtil.isNotBlank(taskId))
				pId = helper.getProcessIdByTaskId(taskId);
			
			//如果是指派节点，判断以前是否办理过
			if("true".equals(preAssign)&&StringUtil.isNotBlank(taskId)){
				UserListLoader loader = new UserListLoader();
				//判断该task是否是正在办理中的会签节点
				String isContersign = bus.getString("isContersign");
				if("true".equals(isContersign)){
					List<Map> list = new ArrayList<Map>();
					
					String assingUser = helper.getCountersignPreAssignUser(taskId);
					assignDealWay = helper.getAssignDealWay(taskId);
					if(assingUser!=null){
						list = loader.getUserlistByStr(assingUser);
						if(list != null && list.size() >0){
							for(Map m : list){
								String code = String.valueOf(m.get("code"));
								
								/* 2017-03-14 qcj 修正已办理会签节点办理人的查询和处理 */
								TaskHelper taskhelper =new TaskHelper();
								Set<String> u = taskhelper.parseOneUser(code, pId, null, null);
								if(u != null && u.size() >0){
									for(String str : u){
										if(!existUsers.contains(str)){
											existUsers.add(str);
											Map map = new HashMap();
											map.put("code", "U_"+str);
											map.put("name", "【"+ this.getDeptNameById(str)+"】"+RmsUtil.getUserNameById(str)+"("+RmsUtil.getLoginNameById(str)+")");
											users.add(map);
										}
									}
								}
								
								/*  
								String str = code.substring((code.indexOf('_')+1),code.length());
								
								Map map = new HashMap();
								map.put("code", "U_"+str);
								map.put("name", "【"+ this.getDeptNameById(str)+"】"+RmsUtil.getUserNameById(str)+"("+RmsUtil.getLoginNameById(str)+")");
								users.add(map);
								*/
							}
						}
					}
				}else{
					if(approvers != null && approvers.size() > 0){
						for(int i=0; i<approvers.size(); i++){
							String selId = approvers.get(i);
							TaskHelper taskhelper =new TaskHelper();
							Set<String> u = taskhelper.parseOneUser(selId, pId, null, null);
							if(u != null && u.size() > 0){
								for(String str : u){
									if(!existUsers.contains(str)){
										existUsers.add(str);
										Map map = new HashMap();
										map.put("code", "U_"+str);
										map.put("name", "【"+ this.getDeptNameById(str)+"】"+RmsUtil.getUserNameById(str)+"("+RmsUtil.getLoginNameById(str)+")");
										users.add(map);
									}
								}
							}
						}
					}
				}
			}else{
				if(approvers != null && approvers.size() > 0){
					for(int i=0; i<approvers.size(); i++){
						String selId = approvers.get(i);
						TaskHelper taskhelper =new TaskHelper();
						Set<String> u = taskhelper.parseOneUser(selId, pId, null, null);
						if(u != null && u.size() > 0){
							for(String str : u){
								if(!existUsers.contains(str)){
									existUsers.add(str);
									Map map = new HashMap();
									map.put("code", "U_"+str);
									map.put("name", "【"+ this.getDeptNameById(str)+"】"+RmsUtil.getUserNameById(str)+"("+RmsUtil.getLoginNameById(str)+")");
									users.add(map);
								}
							}
						}
					}
				}
			}
			
			Map rtn = new HashMap();
			rtn.put("assignDealWay", assignDealWay);
			rtn.put("users", users);
			form.setReturn(rtn);
		}
	}

	/* 通过userid获取部门 */
	private String getDeptNameById(String userGuid){
		String sql = "select d.cname as name from AOS_RMS_USER u "
				+ " left join AOS_RMS_EMPL e on u.cEMP = e.cGUID "
				+ " LEFT JOIN CM_Department d on e.cdeptguid = d.cGUID "
				+ "	where u.cGUID= ?";
		String dname = DbSvr.getDbService(null).getStringResult(sql, new Object[]{userGuid});
		return dname;
	}
	
	/* 2017-01-11 qcj 查询下一办理节点信息 */
	private List<Map> getOptions(List<Map> outcomes) {
		List<Map> opts = new ArrayList<Map>();
		for (Map button : outcomes) {
			String name = button.get("dest") == null ? (String)button.get("name") : (String) button.get("dest")
					+ ("true".equals(button.get("condition")) ? "(条件)" : "");
			if (name.indexOf("-") > -1) {
				name = name.split("-")[1];
			}
			
			List operator = new ApprovalSelctionFetcher().getButtonName(button);
			Map opt = new HashMap();
			opt.put("code", button.get("name"));
			opt.put("preAssigned", button.get("preAssigned"));
			opt.put("name", name);
			opt.put("operator", operator);
			opts.add(opt);
		}
		return opts;
	}
}