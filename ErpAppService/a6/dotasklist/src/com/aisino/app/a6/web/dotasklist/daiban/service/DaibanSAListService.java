package com.aisino.app.a6.web.dotasklist.daiban.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.a6.web.dotasklist.daiban.plugin.ListSearchCheckPlugin;
import com.aisino.app.a6.web.dotasklist.daiban.plugin.ListSearchConditionPlugin;
import com.aisino.app.web.dotasklist.daiban.service.CommonGetUserAndDB;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

public class DaibanSAListService  implements IService{
	@Override
	public Object doService(Object... param) {
		

		String action = (String) param[0];  //操作类型：取list 或 取数目
		List<Map> l = (List<Map>) param[1];  //list
		Map m = (Map) param[2];    //key为listnum
		String keyword= (String) param[3];
		
		DataMsgBus bus=DataMsgBus.getCurrBus();		
		DbSvr db=CommonGetUserAndDB.getDbSvr(param);
		ListSearchConditionPlugin.dealCondition(bus,db,keyword);
		
		/*销售订单*/
		Map saorderMap =new HashMap(); 
		saorderMap.put("formid", "business_sa_saorder"); 
		saorderMap.put("rescKey", "business_sa_saorder.docheck"); 
		saorderMap.put("billKey", "business_sa_saorder"); 
		saorderMap.put("listKey", "business_sa_saorder_list"); 
		saorderMap.put("table", "SaOrder"); 
		ListSearchCheckPlugin.dealcheck(bus, db, saorderMap);
		
		
		/*销售发票（含期初）*/
		Map saInvMap =new HashMap(); 
		saInvMap.put("formid", "business_sa_sainvoice"); 
		saInvMap.put("rescKey", "business_sa_sainvoice.docheck"); 
		saInvMap.put("billKey", "business_sa_sainvoice"); 
		saInvMap.put("listKey", "business_sa_sainvoice_list"); 
		saInvMap.put("initRescKey", "business_sa_initInvoice.docheck"); 
		saInvMap.put("initBillKey", "business_sa_initInvoice"); 
		saInvMap.put("table", "SaInvoice"); 
		ListSearchCheckPlugin.dealcheck(bus, db, saInvMap);
				
		
		/*零售单*/
		Map saretailMap =new HashMap(); 
		saretailMap.put("formid", "business_sa_retail_form"); 
		saretailMap.put("rescKey", "business_sa_retail_form.docheck"); 
		saretailMap.put("billKey", "business_sa_retail_form"); 
		saretailMap.put("listKey", "business_sa_retail_list_form"); 
		saretailMap.put("table", "SaRetail"); 
		ListSearchCheckPlugin.dealcheck(bus, db, saretailMap);
		
		if("getlist".equals(action)) {  	
				/* 增加查询当前页数和每页条数 */
				bus.setControlInfo("pt_control_currentpage",param[4]);
				bus.setControlInfo("pt_control_pagesize",param[5]);
				List<Map> list = new ArrayList();
				list=db.queryIdForListByPage("mobile_daiban_salist.getallsalist",bus);
				if(list!=null&&!list.isEmpty()){
					checkIamt(db,list);
					l.addAll(list);	
				}
		}else if("getnum".equals(action)){
			int num=0;
			num=Integer.parseInt(db.queryIdForString("mobile_daiban_salist.getallsanum",bus)); 
			m.put("listnum", num);
		}
 		return null;
	}
	
	//校验该用户是否有字段（金额）的权限，若没有该权限，将金额置为空。
	public void checkIamt(DbSvr db,List<Map> list){
		String currUserGUID = SessionHelper.getCurrentUserId();
		boolean notHas=db.queryIdHasResult("mobile_common.checkIamtSAorder", currUserGUID);
		boolean notHasSi=db.queryIdHasResult("mobile_common.checkIamtSAinvoice", currUserGUID);
		if(notHas||notHasSi){
			for(int i=0;i<list.size();i++){
				if("066".equals(list.get(i).get("hidden_cbilltype"))&&notHas)
					list.get(i).put("je", "");
				if("085".equals(list.get(i).get("hidden_cbilltype"))&&notHasSi)
					list.get(i).put("je", "");
				//零售单没有金额不可见权限
			}
		}
				
		
		
	}
	
	

}
