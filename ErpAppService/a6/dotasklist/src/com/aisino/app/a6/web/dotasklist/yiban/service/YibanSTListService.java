package com.aisino.app.a6.web.dotasklist.yiban.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.aisino.aos.preference.common.Prefer;
import com.aisino.aos.system.SessionHelper;
import com.aisino.app.a6.web.dotasklist.daiban.plugin.DaibanGetDeptRolesListPlugin;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.db.Eso;
import com.aisino.platform.db.SqlInfo;
import com.aisino.platform.view.DataMsgBus;

public class YibanSTListService  implements IService{
	@Override
	public Object doService(Object... param) {
		
		String action = (String) param[0];  //操作类型：取list 或 取数目
		List<Map> l = (List<Map>) param[1];  //list
		Map m = (Map) param[2];    //key为listnum
		String keyword= (String) param[3]; 
		String today= (String) param[4]; 
		
		DataMsgBus bus=DataMsgBus.getCurrBus();
		DbSvr db=DbSvr.getDbService(null);
		bus.put("currUserGUID", SessionHelper.getCurrentUserId());
		bus.put("today", today);
		bus.put("keyword",keyword);
		if("getlist".equals(action)) {  	
				/* 增加查询当前页数和每页条数 */
				bus.setControlInfo("pt_control_currentpage",param[5]);
				bus.setControlInfo("pt_control_pagesize",param[6]);
				List<Map> list = new ArrayList();
				//list=db.queryIdForListByPage("mobile_yiban_stlist.getallstlist",bus);
				SqlInfo info=new SqlInfo(SttransStoreFilter("mobile_yiban_stlist.getallstlist",db));
				list= db.getListResultByPage(bus, info);
				if(list!=null&&!list.isEmpty()){
				    //checkIamt(db,currUserGUID,list);
					l.addAll(list);	
				}
		}else if("getnum".equals(action)){
			
			int num=0;
			//num=Integer.parseInt(db.queryIdForString("mobile_yiban_stlist.getallstnum",bus)); 
			 Eso eso= Eso.make(SttransStoreFilter("mobile_yiban_stlist.getallstnum",db), bus);
			 num=Integer.parseInt(db.exeEsoForString(eso));
			m.put("listnum", num);
		}
 		return null;
	}
	
	//校验该用户是否有字段（金额）的权限，若没有该权限，将金额置为空。
	/*public void checkIamt(DbSvr db,String currUserGUID,List<Map> poList){
		
		boolean notHas=db.queryIdHasResult("mobile_common.checkIamtSAorder", currUserGUID);
		if(notHas){
			for(int i=0;i<poList.size();i++){
				poList.get(i).put("je", "");
			}
			
		}*/
	public String  SttransStoreFilter(String sqlid,DbSvr db){
		
		String A6_ST_TRANS_DATARIGHT = Prefer.get("A6_ST_TRANS_DATARIGHT");
		String sql=db.getSQL(sqlid);
		String nsql="";
		//根据参数替换权限配置的sql
		if ( "indataright".equals(A6_ST_TRANS_DATARIGHT) ) {//仅控制调入
            String replace_in="ST_StkTrans.cInDeptGUID=cm_department.cguid,"+
            		"ST_StkTrans.cInEmpGUID=CM_Employee.cGUID,"+"ST_StkTrans.cInstoreGUID=CM_Storehouse.cguid,ST_StkTransLine.cInstoreGUID=CM_StoreHouse.cguid";
             nsql=sql.replaceAll("STDateDataRightSetBeforeQueryPlugin_feild", replace_in);
		}else if ("outdataright".equals(A6_ST_TRANS_DATARIGHT)) {//仅控制调出
			String replace_out="ST_StkTrans.cOutDeptGUID=cm_department.cguid,"+
    				"ST_StkTrans.cOutEmpGUID=CM_Employee.cGUID,"+"ST_StkTrans.cOutstoreGUID=CM_Storehouse.cguid,ST_StkTransLine.cOutstoreGUID=CM_StoreHouse.cguid";
             nsql=sql.replaceAll("STDateDataRightSetBeforeQueryPlugin_feild", replace_out);
		}else {//控制调入调出
			String replace_all="ST_StkTrans.cInDeptGUID=cm_department.cguid,ST_StkTrans.cOutDeptGUID=cm_department.cguid,"+
    				"ST_StkTrans.cInEmpGUID=CM_Employee.cGUID,ST_StkTrans.cOutEmpGUID=CM_Employee.cGUID,"+"ST_StkTrans.cInstoreGUID=CM_Storehouse.cguid,ST_StkTransLine.cInstoreGUID=CM_StoreHouse.cguid,ST_StkTrans.cOutstoreGUID=CM_Storehouse.cguid,ST_StkTransLine.cOutstoreGUID=CM_StoreHouse.cguid";
            nsql=sql.replaceAll("STDateDataRightSetBeforeQueryPlugin_feild", replace_all);
		}
		return nsql;
		
	}
		
		
		
		
      
	
	

}
