package com.aisino.app.a6.web.dotasklist.yiban.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.app.web.dotasklist.common.plugin.BillDesignPlugin;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;

/**
 * 已办借款单据详情
 * <AUTHOR>
 * @date 2016年12月27日
 *
 */
public class YibanEMLoanService implements IService{

	@Override
	public Object doService(Object... param) {
		// TODO Auto-generated method stub
		String action = (String) param[0];
		String cguid=(String) param[1];
		DbSvr db=DbSvr.getDbService(null);	
		if("browse".equals(action)){
			/* 2017-08-01 quchj 修正借款单取值来自于单据设计 */
			String cBusinessCode ="oa_008";   
			Map m= BillDesignPlugin.getBill(cBusinessCode,cguid);   //调用接口传该单据的formid
			List<Map> main=(List<Map>) m.get("maindata");
			
			//List<Map> main =db.queryIdForList("mobile_daiban_emBill.LoanMain", new Object[] {cguid});
			/* 2017-03-13 qcj 增加并发删除校验 */
			if(CollectionUtil.isEmpty(main))
				 throw new BusinessException("并发删除,请重新查询");
			
			/* 特殊处理字段 */
			List<Map> mainTemp =db.queryIdForList("mobile_daiban_emBill.loanMain", new Object[] {cguid});
			main=BillDesignPlugin.mainProcess(main,mainTemp);//主表（将1.0版本的特殊处理字段合并到单据设计结果中）
			
			/* 2017-01-03 qcj 增加附件查询 */
			List<Map> files = db.queryIdForList("mobile_common.getFiles", new Object[] {cguid});
			Map map = new HashMap();
			map.put("main", main);
			map.put("file", files);
			return map;
		}
		return null;
	}

}
