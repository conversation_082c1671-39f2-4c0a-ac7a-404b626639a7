package com.aisino.app.a6.web.dotasklist.yiban.service;

import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

public class YibanSAListService implements IService{

	@Override
	public Object doService(Object... param) {
		String action = (String) param[0];  //操作类型：取list 或 取数目
		List<Map> l = (List<Map>) param[1];  //list
		Map m = (Map) param[2];    //key为listnum
		String keyword= (String) param[3]; 
		String today= (String) param[4]; 
		
		DataMsgBus bus=DataMsgBus.getCurrBus();
		DbSvr db=DbSvr.getDbService(null);
		bus.put("currUserGUID", SessionHelper.getCurrentUserId());
		bus.put("today", today);
		bus.put("keyword",keyword);
		if("getlist".equals(action)) {  	// 获销售已办列表
			 String s=db.getSQL("mobile_yiban_salist.saordernum");
			 /* 增加查询当前页数和每页条数 */
				bus.setControlInfo("pt_control_currentpage",param[5]);
				bus.setControlInfo("pt_control_pagesize",param[6]);
		     List<Map> list=db.queryIdForListByPage("mobile_yiban_salist.saorderlist",bus);
		     if(list!=null&&!list.isEmpty()){
		    	  checkIamt(db,SessionHelper.getCurrentUserId(),list);
			      l.addAll(list);	
			 }
		}else if("getnum".equals(action)){
			int num =Integer.parseInt(db.queryIdForString("mobile_yiban_salist.saordernum",bus)); //获取销售订单待办数目
			int listnum=(Integer) m.get("listnum");
			m.put("listnum", listnum+num);
		}
 		return null;
	}

	//校验该用户是否有字段（金额）的权限，若没有该权限，将金额置为空。
		public void checkIamt(DbSvr db,String currUserGUID,List<Map> poList){
			
			boolean notHas=db.queryIdHasResult("mobile_common.checkIamtSAorder", currUserGUID);
			boolean notHasSi=db.queryIdHasResult("mobile_common.checkIamtSAinvoice", currUserGUID);
			if(notHas||notHasSi){
				for(int i=0;i<poList.size();i++){
					if("066".equals(poList.get(i).get("hidden_cbilltype"))&&notHas)
					poList.get(i).put("je", "");
					if("085".equals(poList.get(i).get("hidden_cbilltype"))&&notHasSi)
					poList.get(i).put("je", "");
				}
			}
	   }


}


