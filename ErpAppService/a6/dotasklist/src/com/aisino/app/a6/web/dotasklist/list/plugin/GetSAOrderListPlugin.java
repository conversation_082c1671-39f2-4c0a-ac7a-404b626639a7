package com.aisino.app.a6.web.dotasklist.list.plugin;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.dotasklist.common.util.DotaskListUtil;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

/**
 * 销售订单列表数据加载
 * <AUTHOR>
 * @since 2018-10-22
 */
public class GetSAOrderListPlugin implements FormCreateListener{
	private static final long serialVersionUID = 1L;

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		// TODO Auto-generated method stub
		
		/* 校验是否有销售订单列表权限，若没有则返回空，否则继续 */
		String saRetailListRescKey = "business_sa_saorder_list";
		boolean saRetailListRe = SecurityService.rescCheck(saRetailListRescKey);
		if(!saRetailListRe){
			throw new BusinessException("当前用户没有销售订单列表权限。");
		}
		
		//查询条件
		String currUserGUID = SessionHelper.getCurrentUserId();
		String currUserEmpGUID = SessionHelper.getCurrentEmpId();
		bus.put("currUserGUID", currUserGUID);
		bus.put("currUserEmpGUID", currUserEmpGUID);
		
		String clockStart = null;
		int past = 0;
		String saleType=bus.getString("saleType")==null?"":bus.get("saleType").toString();;
		//如果saleType为空，即使用查询条件，则clockStart为空，查所有；
		if("".equals(saleType)){
			if("".equals(bus.getString("cbillcode")))
				bus.put("cbillcode", null);
			if("".equals(bus.getString("startdate")))
				bus.put("startdate", null);
			if("".equals(bus.getString("enddate")))
				bus.put("enddate", null);
			if("".equals(bus.getString("ccustguid")))
				bus.put("ccustguid", null);
			if("".equals(bus.getString("cdeptguid")))
				bus.put("cdeptguid", null);
			if("".equals(bus.getString("cempguid")))
				bus.put("cempguid", null);
			if("".equals(bus.getString("cbustypeguid")))
				bus.put("cbustypeguid", null);
			if("".equals(bus.getString("cbusprocessguid")))
				bus.put("cbusprocessguid", null);			
		}else{
			if("default".equalsIgnoreCase(saleType)){
				//本月第一天至今
				Calendar calendar = Calendar.getInstance();
				calendar.add(Calendar.MONTH, 0);  
				calendar.set(Calendar.DAY_OF_MONTH, 1);  
				SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
				clockStart= format.format(calendar.getTime());  
			}else{ 
				if("today".equalsIgnoreCase(saleType)){
					past =0;
				}else if("week".equalsIgnoreCase(saleType)){
					past=6;
				}else if("month".equalsIgnoreCase(saleType)){
					past=29; //近30天
				}
				clockStart = DotaskListUtil.getPastDate(past);
			}
		}			
		bus.put("clockStart", clockStart);
		
		DbSvr db = DbSvr.getDbService(null);
		
		bus.setControlInfo("pt_control_currentpage",bus.getString("curpage"));
		bus.setControlInfo("pt_control_pagesize",DotaskListUtil.pagenum);
		//分页查列表
		List list = db.queryIdForListByPage("mobile_list_saorder_sql.saOrderList",bus);
		/* 总金额的查询 */
		String salemoney = db.queryIdForString("mobile_list_saorder_sql.saOrderAmtSum", bus);
		/* 总笔数的查询 */
		String salenum= db.queryIdForString("mobile_list_saorder_sql.saOrderSum", bus);
		Map map = new HashMap();
		map.put("list", list);
		map.put("salemoney", salemoney);
		map.put("salenum", salenum);
		form.setReturn(map);
	}
}