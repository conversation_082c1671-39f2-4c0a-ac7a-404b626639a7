package com.aisino.app.a6.web.dotasklist.bill.plugin;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.aisino.a6.finance.business.bm.BMUtil;
import com.aisino.aos.bill.common.BillUtils;
import com.aisino.aos.bill.keygen.KeygenUtil;
import com.aisino.aos.bill.plugin.BillCheckLogUtils;
import com.aisino.aos.bill.plugin.BillCheckPlugin;
import com.aisino.aos.bill.vo.BillSetting;
import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.Guid;
import com.aisino.platform.core.MS;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.veng.json.dauglas.JSONArray;
import com.aisino.platform.veng.json.dauglas.JSONException;
import com.aisino.platform.veng.json.dauglas.JSONObject;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

/**
 * 报销单新增相关
 * <AUTHOR>
 * @since 2017-08-14
 */
public class ExpenseAccountBillPlugin implements FormCreateListener{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		// TODO Auto-generated method stub
		
		String action = bus.getString("action");
		if("new".equals(action)){ /*新增是数据加载*/
			Map rtn = new HashMap();
			if(this.checkBillNewPower() == true){
				/* 获取报销人、报销部门 */
				String currEmpId = SessionHelper.getCurrentEmpId();
				if(currEmpId != null && StringUtils.isNotEmpty(currEmpId)){
					String currDeptId = SessionHelper.getCurrentDeptId();
					String currDeptName = SessionHelper.getCurrentDeptName();
					DbSvr db = DbSvr.getDbService(null);
					String getEmpNameSQL= "select cName from CM_Employee where cGUID= ?";
					String currEmpName = db.getStringResult(getEmpNameSQL, new Object[]{currEmpId});
					
					rtn.put("currEmpId", currEmpId);
					rtn.put("currEmpName", currEmpName);
					rtn.put("currDeptId", currDeptId);
					rtn.put("currDeptName", currDeptName);
					
					//获取业务类型列表
					List ywlx = db.queryIdForList("mobile_bill_em.getAllYwlx");
					rtn.put("ywlx", ywlx);
					//获取职员部门列表
					Map param = new HashMap();
					param.put("queryName", "");
					List bxrList = db.queryIdForList("mobile_bill_em.getAllEmpDeptList",param);
					rtn.put("bxrList", bxrList);
					//获取项目列表
					//List xmList = db.queryIdForList("mobile_bill_em.getAllProjectList", param);
					//rtn.put("xmList", xmList);
					//报销明细和发票明细
					rtn.put("bxLine", null);
					rtn.put("fpLine", null);
					
					form.setReturn(rtn);
				}else{
					throw new BusinessException("非职员用户不能新增报销单!");
				}
			}else{
				throw new BusinessException("当前用户没有报销单新增权限!");
			}
			
		}else if("queryBxr".equals(action)){//报销人查询
			String queryName = bus.getString("queryName");
			//获取职员部门列表
			DbSvr db = DbSvr.getDbService(null);
			Map param = new HashMap();
			param.put("queryName", queryName);
			List bxrList = db.queryIdForList("mobile_bill_em.getAllEmpDeptList", param);
			Map rtn = new HashMap();
			rtn.put("bxrList", bxrList);
			
			form.setReturn(rtn);
		}else if("queryAllXm".equals(action)){//项目查询
			String queryName = bus.getString("queryName");
			//获取项目列表
			DbSvr db = DbSvr.getDbService(null);
			Map param = new HashMap();
			param.put("queryName", queryName);
			List xmList = db.queryIdForList("mobile_bill_em.getAllProjectList", param);
			Map rtn = new HashMap();
			rtn.put("xmList", xmList);
			
			form.setReturn(rtn);
		}else if("queryCommonXm".equals(action)){//常用项目查询
			//获取当前职员
			String currEmpId = SessionHelper.getCurrentEmpId();
			//获取当前年
			Calendar date = Calendar.getInstance();
			String year = String.valueOf(date.get(Calendar.YEAR));
			bus.send("year", year);
			bus.send("currEmpId", currEmpId);
			//获取常用项目列表
			DbSvr db = DbSvr.getDbService(null);
			List coXmList = db.queryIdForList("mobile_bill_em.getCommonProjectList", bus);
			Map rtn = new HashMap();
			rtn.put("coXmList", coXmList);
			
			form.setReturn(rtn);
		}else if("queryFylb".equals(action)){//费用类别查询
			String ywlxId = bus.getString("ywlxId");
			if(StringUtils.isBlank(ywlxId)){
				throw new BusinessException("请先选择业务类型!");
			}else{
				DbSvr db = DbSvr.getDbService(null);
				List fylb = db.queryIdForList("mobile_bill_em.getAllFylb", new Object[]{ywlxId});
				Map rtn = new HashMap();
				rtn.put("fylb", fylb);
				form.setReturn(rtn);
			}
		}else if("checkEditPower".equals(action)){//未提交列表打开校验修改权限
			String emEditRescKey = "oa_fm_expenseAccountBill_edit_form.modifybf";
			boolean emEditRe = SecurityService.rescCheck(emEditRescKey); 
			
			form.setReturn(emEditRe);
		}else if("delete".equals(action)){//删除
			String cguid = bus.getString("cguid");
			DbSvr dbSvr = DbSvr.getDbService(null);
			
			/* 2017-10-09 quchj 增加删除权限校验 */
			String emDelRescKey = "oa_fm_expenseAccountBill_edit_form.delete";
			boolean emDelRe = SecurityService.rescCheck(emDelRescKey);
			if(!emDelRe){
				throw new BusinessException("没有删除权限，不能删除当前报销单!");
			}
			
			Map existMap = dbSvr.queryIdFirstRow("mobile_bill_em.getExpenseAccountMain", new Object[]{cguid});
			if(existMap != null && existMap.size() > 0){//当前单据存在
				String state = String.valueOf(existMap.get("zt"));
				if("tsaved".equals(state) || "saved".equals(state) || "revise".equals(state)){//可以删除状态
					DataMsgBus newBus = new DataMsgBus();
					newBus.put("cguid", cguid);
					
					/* 2017-09-02 quchj 获取子表数据用于预算数据清理 */
					List<Map> lineList = dbSvr.queryIdForList("mobile_bill_em.getExpenseAccountBxmx", new Object[]{cguid});
					newBus.put("list", lineList);
					
					String mainDelSQL = "DELETE FROM OA_ExpenseAccountBill WHERE cGUID=?";
					String bxmxDelSQL = "DELETE FROM OA_ExpenseAccLine WHERE cHeadGUID=?";
					String fpmxDelSQL = "DELETE FROM OA_ExpenseAccInvInfo WHERE cHeadGUID=?";
					
					String fileDelSQL = "DELETE FROM AOS_FILE_FILES WHERE cGroupGuid=?";
					//删除主子表
					dbSvr.update(bxmxDelSQL, new Object[]{cguid});
					dbSvr.update(fpmxDelSQL, new Object[]{cguid});
					dbSvr.update(mainDelSQL, new Object[]{cguid});
					//删除附件
					dbSvr.update(fileDelSQL, new Object[]{cguid});
					//校验是否存在流程，存在则删除
					MS ms = new MS("AOS.BillQueryWorkFlow");
				    boolean hasWorkflow = (ms.doService(form, bus)!=null);
					if(hasWorkflow){//存在流程，同时删除流程
						MS s = new MS("AOS.BillDeleteWorkFlow");
						s.doService(form, bus);
					}
					
					/* 2017-09-02 quchj 删除预算执行数 */
					newBus.put("listname", "list");		
					
					BMUtil util = new BMUtil();
					util.deleteBMBook(dbSvr, newBus);
					
					//返回删除成功
					Map rtn = new HashMap();
					rtn.put("msg", "删除成功!");
					form.setReturn(rtn);
				}else{
					throw new BusinessException("当前单据状态已更新不可删除,请重新刷新数据!");
				}
			}else{
				throw new BusinessException("当前单据已被删除,请重新刷新列表!");
			}
		}else if("load".equals(action)){//修改查看时数据加载
			String cguid = bus.getString("cguid");
			//审批状态，如果是未提交进来则是修改，其他则是查看
			String spzt = bus.getString("spzt");
			
			DbSvr dbSvr = DbSvr.getDbService(null);
			Map mainMap = dbSvr.queryIdFirstRow("mobile_bill_em.getExpenseAccountMain", new Object[]{cguid});
			if(mainMap != null || mainMap.size() > 0){
				if("wtj".equals(spzt)){//修改时校验当前单据状态
					String zt = String.valueOf(mainMap.get("zt"));
					//不可修改状态
					if(!"tsaved".equals(zt) && !"saved".equals(zt) && !"revise".equals(zt)){
						throw new BusinessException("当前单据状态已更新不可修改,请重新刷新数据!");
					}
				}
				
				List bxmxList = dbSvr.queryIdForList("mobile_bill_em.getExpenseAccountBxmx", new Object[]{cguid});
				List fpmxList = dbSvr.queryIdForList("mobile_bill_em.getExpenseAccountFpmx", new Object[]{cguid});
				List fileList = dbSvr.queryIdForList("mobile_bill_em.getExpenseAccountFile", new Object[]{cguid});
				Map rtn = new HashMap();
				rtn.put("main", mainMap);
				rtn.put("bxmxList", bxmxList);
				rtn.put("fpmxList", fpmxList);
				rtn.put("fileList", fileList);
				form.setReturn(rtn);
			}else{
				throw new BusinessException("当前单据已被删除,请重新刷新列表!");
			}
			
		}else if("save".equals(action)){//保存
			DbSvr dbSvr = DbSvr.getDbService(null);
			String zt;
			String alldata = bus.getString("alldata");
			alldata = alldata.substring(1, alldata.length()-1);
			try {
				JSONObject objAllData = new JSONObject(alldata);
				//主表
				String mainStr = objAllData.getString("main");
				//报销明细
				JSONArray bxmxdata = objAllData.getJSONArray("bxmxdata");
				//发票明细
				JSONArray fpmxdata = objAllData.getJSONArray("fpmxdata");
				
				//执行SQL
				String mainSQL;
				//主表执行参数List
				List paramList = new ArrayList();
				//子表执行参数List
				List paramBxmxList = new ArrayList();
				List paramFpmxList = new ArrayList();
				//自动编号规则ID
				String billSequenceId="";
				
				//解析主表数据
				JSONObject mainJ = new JSONObject(mainStr);
				String cguid = mainJ.getString("cguid");//cguid
				String ccode = mainJ.getString("ccode");//cguid
				String cExpUserGUID = mainJ.getString("currEmpId");//报销人
				String cExpDeptGUID = mainJ.getString("currDeptId");//报销人部门
				String cGroupTypeGUID = mainJ.getString("ywlx");//业务类型
				String cRefundDate = mainJ.getString("bxrq");//报销日期
				String cReason = mainJ.getString("bxyy");//报销原因
				String cAccItemTypeGUID = mainJ.getString("xmid");//项目
				String iTotalAMT = mainJ.getString("summoney");//发生总额
				String cTimeStamp;
				String cStatusEnumGUID;
				
				String cCreatorGUID = SessionHelper.getCurrentUserId();//创建人or修改人
				//创建or修改时间
				Date now = new Date();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				String dCreateDate = sdf.format(now);
				
				//预算控制BUS
				DataMsgBus newBus = new DataMsgBus();
				newBus.put("cguid", cguid);
				newBus.put("itotalamt", iTotalAMT);
				newBus.put("crefunddate", cRefundDate);
				
				if(StringUtils.isNotBlank(cguid)){//修改
					String sjc = mainJ.getString("sjc");
					/* 校验时间戳 */
					Map sjcMap = dbSvr.queryIdFirstRow("mobile_bill_em.checkEditState", new Object[]{cguid});
					String existSjc = String.valueOf(sjcMap.get("sjc"));
					String existZt = String.valueOf(sjcMap.get("zt"));
					if(!sjc.equals(existSjc)){
						throw new BusinessException("当前单据数据已更新,请重新刷新数据!");
					}if(!"tsaved".equals(existZt) && !"saved".equals(existZt) && !"revise".equals(existZt)){
						throw new BusinessException("当前单据状态已更新不可修改,请重新刷新数据!");
					}
					
					zt = "editold";
					cTimeStamp = Guid.g();//时间戳
					cStatusEnumGUID = mainJ.getString("zt");//状态
					mainSQL = "UPDATE OA_ExpenseAccountBill SET cExpUserGUID=?,cExpDeptGUID=?,"
							+ " cGroupTypeGUID=?,cRefundDate=?,cAccItemTypeGUID=?,iTotalAMT=?,cReason=?,"
							+ " cModifierGUID=?,dModifyTime=?,cTimeStamp=? WHERE cGUID=?";
					
					Object[] paramMain = {cExpUserGUID,cExpDeptGUID,cGroupTypeGUID,cRefundDate,cAccItemTypeGUID,
							iTotalAMT,cReason,cCreatorGUID,dCreateDate,cTimeStamp,cguid};
					paramList.add(paramMain);
				}else{//新增
					zt = "editnew";
					//获取组织ID
					String cAdminOrgnId = SessionHelper.getCurrentAdminOrgnId();
					String cOrgnId = SessionHelper.getCurrentOrgnId();
					//自动编号规则ID
					billSequenceId = dbSvr.queryIdForString("mobile_bill_em.getEMBILLSEQUENCESQL", new Object[]{cAdminOrgnId,cOrgnId,cAdminOrgnId,cOrgnId});
					ccode = KeygenUtil.getNextKeyBySequencecGuid(billSequenceId, bus);//自动编号
					
					String cUserGUID = SessionHelper.getCurrentEmpId();//申请人
					String cUserDeptGUID = SessionHelper.getCurrentDeptId();//申请人部门
					Map bzMap = dbSvr.queryIdFirstRow("OA_FM_VFSql.currency");//默认币种
					String cCurrencyGUID = String.valueOf(bzMap.get("code"));
					cTimeStamp = Guid.g();//时间戳
					cStatusEnumGUID = "saved";
					
					mainSQL = "INSERT INTO OA_ExpenseAccountBill(cGUID,cCode,cUserGUID,cUserDeptGUID,"
							+ "	cExpUserGUID,cExpDeptGUID,cRefundDate,iTotalAMT,cReason,cGroupTypeGUID,cAccItemTypeGUID,"
							+ " cCurrencyGUID,cCreatorGUID,dCreateDate,cTimeStamp,cStatusEnumGUID) "
							+ " VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
					cguid = Guid.g();
					Object[] paramMain = {cguid,ccode,cUserGUID,cUserDeptGUID,cExpUserGUID,cExpDeptGUID,cRefundDate,iTotalAMT,
							cReason,cGroupTypeGUID,cAccItemTypeGUID,cCurrencyGUID,cCreatorGUID,dCreateDate,cTimeStamp,cStatusEnumGUID};
					paramList.add(paramMain);
				}
				
				
				//解析发票明细,校验是否已报销过
				if(fpmxdata != null && fpmxdata.length()>0){
					String info = "不允许重复报销!\n";
					int existNum = 0;
					List existMx = new ArrayList();
					Map existMxMap = new HashMap();
					for(int i=0; i<fpmxdata.length(); i++){
						String fpmxStr = fpmxdata.getString(i);
						JSONObject fpmx = new JSONObject(fpmxStr);
						
						String fpguid = fpmx.getString("fpguid");//发票GUID
						String FP_DM = fpmx.getString("FPDM");//发票代码
						String FP_HM = fpmx.getString("FPHM");//发票号码
						/* 2017-10-10 quchj 增加发票金额是否为空校验处理 */
						String iAMT = fpmx.getString("FPSUM").equals("")?null:fpmx.getString("FPSUM");//发票金额
						String cremark = fpmx.getString("BZ");//备注
						
						/* 校验明细重复性 */
						if(existMx.contains(FP_DM)){
							String existHm = String.valueOf(existMxMap.get(FP_DM));
							if(existHm.equals(FP_HM)){
								existNum++;
								info = info+"发票代码"+"["+FP_DM+"]，发票号码["+FP_HM+"]的发票重复.\n";
								break;
							}
						}else{
							existMx.add(FP_DM);
							existMxMap.put(FP_DM, FP_HM);
						}
						
						boolean isExist = dbSvr.queryIdHasResult("mobile_bill_em.checkInv", new Object[]{FP_DM,FP_HM,cguid});
						if(isExist){
							existNum++;
							info = info+"发票代码"+"["+FP_DM+"]，发票号码["+FP_HM+"]的发票已报销过.\n";
							break;
						}else{
							fpguid = Guid.g();
							Object[] param = {fpguid,cguid,FP_DM,FP_HM,iAMT,cremark};
							paramFpmxList.add(param);
						}
					}
					
					if(existNum > 0){
						throw new BusinessException(info);
					}
				}
				
				List mxList = new ArrayList();
				//解析报销明细数据
				if(bxmxdata != null && bxmxdata.length()>0){
					for(int i=0;i<bxmxdata.length();i++){
						String bxmxStr = bxmxdata.getString(i);
						JSONObject bxmx = new JSONObject(bxmxStr);
						
						String bxguid = bxmx.getString("bxguid");//guid
						String cExpenseClassGUID = bxmx.getString("FYradio");//费用类别
						String iOpeningAMT = bxmx.getString("BXMoney");//发生金额
						String cStartDate = bxmx.getString("startTime");//起始日期
						String cOverDate = bxmx.getString("endTime");//结束日期
						String cStarting = bxmx.getString("STARTPLACE");//始发地
						String cFinishing = bxmx.getString("ENDPLACE");//目的地
						String iDays = bxmx.getString("SUMDAY");//天数
						String cRemark = bxmx.getString("BZ");//备注
						
						/* 2017-10-10 quchj 增加业务类型和费用类别一致性校验：费用类别必须是属于对应的业务类型 */
						String existSQL = "SELECT TOP 1 1 FROM OA_ExpenseClass WHERE cGUID=? AND cGroupTypeGUID=?";
						boolean existGl = dbSvr.isHasResult(existSQL, new Object[]{cExpenseClassGUID,cGroupTypeGUID});
						if(!existGl){
							throw new BusinessException("费用类别不属于该业务类型，请重新选择!");
						}
						
						bxguid = Guid.g();
						
						/* 用于预算控制 */
						Map m = new HashMap();
						m.put("cexpenseclassguid", cExpenseClassGUID);
						m.put("iOpeningAMT", iOpeningAMT);
						m.put("iactualamt", 0);
						m.put("cguid", bxguid);
						mxList.add(m);
						
						Object[] param = {bxguid,cguid,cExpenseClassGUID,iOpeningAMT,cStartDate,cOverDate,cStarting,
								cFinishing,iDays,cExpDeptGUID,cAccItemTypeGUID,cRemark};
						paramBxmxList.add(param);
					}
				}else{
					throw new BusinessException("报销明细不能为空!");
				}
				
				/* oa预算控制预算控制校验其他参数 */
				newBus.put("cExpDeptGUID", cExpDeptGUID);
				newBus.put("cExpUserGUID", cExpUserGUID);
				newBus.put("list", mxList);
				newBus.put("curFormState", zt);
				
				this.checkByBudegetSet(form, newBus);
				//财务预算控制
				newBus.put("cBusCode", "oa_010");
				newBus.put("actionState", "saved");
				String isControl = ExpenseAccountBillBMControlPlugin.checkBill(newBus, "Bill");
				if("y".equals(isControl)){//需要预算控制
					ExpenseAccountBillBMControlPlugin.bmControl(form, newBus);
				}
				
				/* 2017-09-03 quchj 增加对附件删除的处理 */
				List paramFileList = new ArrayList();
				/* 2017-09-04 quchj 修正附件删除的处理在修改时才执行 */
				if("editold".equals(zt)){
					JSONArray filedata = objAllData.getJSONArray("filedata");
					if(filedata != null && filedata.length() > 0){
						for(int i=0;i<filedata.length();i++){
							String fileGUID = filedata.getString(i);
							Object[] param = {fileGUID};
							paramFileList.add(param);
						}
					}
				}
				
				String bxmxSQL = "INSERT INTO OA_ExpenseAccLine(cGUID,cHeadGUID,cExpenseClassGUID,iOpeningAMT,"
						+ " cStartDate,cOverDate,cStarting,cFinishing,iDays,cCostDeptGUID,cAccItemTypeGUID,cRemark) "
						+ " VALUES(?,?,?,?,?,?,?,?,?,?,?,?)";
				String fpmxSQL = "INSERT INTO OA_ExpenseAccInvInfo(cGUID,cHeadGuid,FP_DM,FP_HM,iAMT,cremark) "
						+ "	VALUES(?,?,?,?,?,?)";
				
				//执行SQL
				dbSvr.batchUpdate(mainSQL, paramList);
				if("editold".equals(zt)){
					String bxmxDelSQL = "DELETE FROM OA_ExpenseAccLine WHERE cHeadGUID='"+cguid+"'";
					dbSvr.execute(bxmxDelSQL);
				}	
				dbSvr.batchUpdate(bxmxSQL, paramBxmxList);
				
				if(paramFpmxList != null && paramFpmxList.size() > 0){
					if("editold".equals(zt)){
						String fpmxDelSQL = "DELETE FROM OA_ExpenseAccInvInfo WHERE cHeadGUID='"+cguid+"'";
						dbSvr.execute(fpmxDelSQL);
					}
					dbSvr.batchUpdate(fpmxSQL, paramFpmxList);
				}
				/* 2017-09-03 如果有需要删除的附件则执行附件删除操作 */
				if(paramFileList != null && paramFileList.size() > 0){
					String fileSQL = "DELETE FROM AOS_FILE_FILES WHERE cGuid=?";
					dbSvr.batchUpdate(fileSQL, paramFileList);
				}
				
				/* 2017-09-02 quchj 增加保存预算执行数 */
				newBus.put("cguid", cguid);
				newBus.put("listname", "list");
				newBus.put("StatusField", "cStatusEnumGUID");
				newBus.put("cStatusEnumGUID", cStatusEnumGUID);
				
				newBus.send("dBillDataBMUsed", "cRefundDate");
				newBus.send("currate.payAMT", "iTotalAMT");
				newBus.send("currate.payAMT_F", "");
				newBus.send("currate.payAMTCol", "iOpeningAMT");
				
				BMUtil util = new BMUtil();
				util.addBMBook(dbSvr, newBus);
				
				if("editnew".equals(zt)){
					KeygenUtil.getKeyBySequencecGuid(billSequenceId, bus);//自动编号流水表中更新流水号
				}
				
				//是否自动审核		
				BillSetting billSetting = BillUtils.getBillSettingVoByFormId("oa_fm_expenseAccountBill_edit_form");
				String cCheckWay=billSetting==null?BillSetting.ManualCheck:billSetting.getCcheckway();
				if("1".equals(cCheckWay)){//自动审核
					if(!"revise".equals(cStatusEnumGUID)){//退回修改中不能走自动审核
						DataMsgBus newB = new DataMsgBus();
						newB.put("cguid", cguid);
						newB.put("ctimestamp", cTimeStamp);
						newB.put("auto", true);
						ExpenseAccountBillCheckPlugin.checkExpenseAccountBill(form, newB);
						cStatusEnumGUID = "checked";
					}
				}
				
				//返回保存成功
				Map rtn = new HashMap();
				rtn.put("msg", "保存成功!");
				//保存成功返回cguid和时间戳
				rtn.put("cguid", cguid);
				rtn.put("ccode", ccode);
				rtn.put("ctimestamp", cTimeStamp);
				rtn.put("cstatusenumguid", cStatusEnumGUID);
				/* 2017-10-21 quchj 增加审批方式返回值 */
				rtn.put("ccheckway", cCheckWay);
				form.setReturn(rtn);
				
			} catch (JSONException e) {
				// TODO Auto-generated catch block
				System.out.println("String转JSONObject报错失败!");
				e.printStackTrace();
			}	
		}else if("submitChoose".equals(action)){//提交送审选择
			String cguid = bus.getString("cguid");
			DbSvr dbSvr = DbSvr.getDbService(null);
			//查找主表数据
			Map main = dbSvr.queryIdFirstRow("mobile_bill_em.getExpenseAccountMain", new Object[]{cguid});
			if(main == null || main.size() < 1){
				throw new BusinessException("当前单据已被删除,请重新刷新列表!");
			}
			/* 获取审批方式
			 * 0手工审核 1自动审核 2 流程审批
			 */
			 BillSetting billSetting = BillUtils.getBillSettingVoByFormId("oa_fm_expenseAccountBill_edit_form");
		     String cCheckWay=billSetting==null?BillSetting.ManualCheck:billSetting.getCcheckway();
		     if("0".equals(cCheckWay)){//手工审核
		    	 /* 2017-10-18 quchj 手工审核增加审核权限校验 */
				String emCheckRescKey = "oa_fm_expenseAccountBill_edit_form.docheck";
				boolean emCheckRe = SecurityService.rescCheck(emCheckRescKey);
				if(!emCheckRe){
					throw new BusinessException("没有审核权限，不能进行操作!");
				}
		    	 
		    	 
		    	 ExpenseAccountBillCheckPlugin.checkExpenseAccountBill(form, bus);
		    	 
		    	//返回提交送审成功
				Map rtn = new HashMap();
				rtn.put("msg", "提交送审成功!");
				form.setReturn(rtn);	
		     }else if("2".equals(cCheckWay)){//工作流审批
		    	 /* 校验是否有财务预算控制 */
				DataMsgBus newBus = new DataMsgBus();
				newBus.put("cguid", cguid);
				newBus.put("cbuscode", "oa_010");
				newBus.put("crefunddate", main.get("bxrq"));
				newBus.put("itotalamt", main.get("bxje"));
				newBus.put("actionState", "checking");
				newBus.put("cStatusEnumGUID", main.get("zt"));
				newBus.put("cExpDeptGUID", main.get("bxrbmId"));
				String isControl = ExpenseAccountBillBMControlPlugin.checkBill(newBus, "Bill");
				if("y".equals(isControl)){
					List<Map> mxLine = dbSvr.queryIdForList("mobile_bill_em.getExpenseAccountBxmx", new Object[]{cguid});
					List rtnMxLine = new ArrayList();
					for(Map m:mxLine){
						/* 用于预算控制 */
						Map rtnM = new HashMap();
						rtnM.put("cexpenseclassguid", m.get("fylbId"));
						rtnM.put("iopeningamt", m.get("bxje"));
						rtnM.put("iactualamt", 0);
						rtnMxLine.add(rtnM);
					}
					
					newBus.put("list",rtnMxLine);
					ExpenseAccountBillBMControlPlugin.bmControl(form, newBus);
				}
				
				//单据状态
		        String cStatus = String.valueOf(main.get("zt"));
		        if(!"saved".equals(cStatus) && !"revise".equals(cStatus)){
		        	throw new BusinessException("当前单据状态不能提交送审，请重新刷新列表数据!");
		        }
		       
		        MS m = new MS("AOS.GetBillFilteredProcessTemplate");
				List<Map> workflowTemps = (List<Map>)m.doService("oa_fm_expenseAccountBill_edit_form",SessionHelper.getCurrentAdminOrgnId(),false?"change":null);
				if(CollectionUtil.isEmpty(workflowTemps)){
					throw new BusinessException("未找到可用的流程模板！");
				}
				if(workflowTemps.size()==1){//直接执行提交
					String processCode = (String)workflowTemps.get(0).get("code");
					
					bus.send("wfProcessId", processCode);
					
					this.submitEmBill(form,bus);
				}else{//返回选择流程
					if(BillCheckPlugin.State_Saved.equalsIgnoreCase(cStatus)){//保存待提交
						List rtnList = new ArrayList();
						Map rtnMap = new HashMap();
						for(Map map : workflowTemps){//实际流程名称和id
							String processName = CollectionUtil.getStringFromMap(map, "name");
							String processCode=CollectionUtil.getStringFromMap(map, "code");
							//返回流程选项
							Map rtn = new HashMap();
							rtn.put("processCode", processCode);
							rtn.put("processName", processName);
							rtnList.add(rtn);
						}
						
						rtnMap.put("chooseList", rtnList);
						form.setReturn(rtnMap);
					}
					if(BillCheckPlugin.State_Revise.equalsIgnoreCase(cStatus)){//退回修改中
						String processtemplateId = "";
						MS s0 = new MS("AOS.BillQueryWorkFlow");
						Object wfVar = s0.doService(form, bus);
						if(wfVar !=null){
							processtemplateId=CollectionUtil.getStringFromMap((Map)wfVar,"processtemplate");
						}else{
							throw new BusinessException("提交按钮初始化错误，未找到退回发起人单据再次提交的流程!");
						}
						
						bus.send("wfProcessId", processtemplateId);
						this.submitEmBill(form,bus);
					}
				}
		     }
	        
		}else if("submit".equals(action)){//提交流程审批
			
			bus.send("wfProcessId", bus.getString("processCode"));
			
			this.submitEmBill(form,bus);
		}
	}
	
	
	//是否有报销单新增权限
	public boolean checkBillNewPower(){
		String emNewRescKey = "oa_fm_expenseAccountBill_edit_form.new";
		boolean emNewRe = SecurityService.rescCheck(emNewRescKey); 
		return emNewRe;
	}
	
	//是否oa预算控制
	public void checkByBudegetSet(AbstractForm form,DataMsgBus bus){
		
		boolean isBudEnabled = ExpenseAccountBillBudgetControlPlugin.isBudEnabled();
		if(isBudEnabled == false){
			/*
			 * 1、判断是否启用预算控制
			 * 2、判断控制类型：提示、不控制、控制
			 */
			ExpenseAccountBillBudgetControlPlugin.checkByBudegetSet4ExpenseLine(form, bus);
		}
	}
	
	/**
	 * 提交审核
	 * @param bus
	 */
	public void submitEmBill(AbstractForm form, DataMsgBus bus){
		String cguid = bus.getString("cguid");
		DbSvr db = DbSvr.getDbService(null);
		
		MS oaMs = new MS("AOS.IfUseOAModule");
		Object hasOA = oaMs.doService(null);
		if(Boolean.FALSE.equals(hasOA)){
			throw new BusinessException("工作协同未启用，无法创建工作流！");
		}
		
		Map billData = BillUtils.getBillInfo(bus.getString("cguid"), "OA_ExpenseAccountBill");
		if(CollectionUtil.isBlankMap(billData)){
			throw new BusinessException("数据被并发删除，请刷新后重新操作！");
		}
		ExpenseAccountBillCheckPlugin.verifyConcurrency(form, bus, db);

		//调用提交之前的接口
		//callBCI("beforeCommit",form, bus);
		
		//校验单据状态
		String curState = CollectionUtil.getStringFromMap(billData, "cStatusEnumGUID");
		if(BillCheckPlugin.State_Tsaved.equalsIgnoreCase(curState))
			throw new BusinessException("单据是暂存状态，不能审核，如需审核请先保存单据。");
		else if(BillCheckPlugin.State_Checking.equalsIgnoreCase(curState))
			throw new BusinessException("单据已经提交审批,不能重复提交！");
		else if(BillCheckPlugin.State_Checked.equalsIgnoreCase(curState))
			throw new BusinessException("单据已经审批结束，如需重新审批，请先执行反审批！");
		
		//处理单据的附件
		//getFileList(form,bus);
		//处理关联流程
		//getAssociateList(form,bus);
		/* 流程实例名称
		 * 取自单据配置参数WorkflowTitle
		 * ${wfProcessId}_${cCode},${cCode},发生金额:${iTotalAMT}
		 * 除了配置参数,参数取值来自于bus
		 */
		
		//启动工作流
		new BillCheckPlugin().startWorkflow(form, bus, curState);
		//调用提交之后的接口
		//callBCI("afterCommit",form, bus);
		BillCheckLogUtils.appendLog(form,bus,"commit");
		
		//返回提交送审成功
		Map rtn = new HashMap();
		rtn.put("msg", "提交送审成功!");
		form.setReturn(rtn);
	}
	
}