package com.aisino.app.a6.web.dotasklist.yiban.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.app.web.dotasklist.common.plugin.BillDesignPlugin;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;

public class YibanCAApvoucherService implements IService{
	

	@Override
	public Object doService(Object... param) {	
		String action = (String) param[0];
		String cguid=(String) param[1];
		DbSvr db=DbSvr.getDbService(null);	
		if("browse".equals(action)){
			String cBusinessCode ="105";   
			Map m= BillDesignPlugin.getBill(cBusinessCode,cguid);   //调用接口传该单据的formid
			List<Map> main=(List<Map>) m.get("maindata");
			List<Map> detail=(List<Map>) m.get("detaildata");  //根据单据设计，获取将要显示的字段及对应的值
			
			List<Map> mainTemp =db.queryIdForList("mobile_daiban_fibill.caapvouchermain", cguid);
			List<Map> detailTemp =db.queryIdForList("mobile_daiban_fibill.caapvoucherdetail",cguid);
			
			Map map = new HashMap();
			//主表
			if(main!=null&&!main.isEmpty()){
				main=BillDesignPlugin.mainProcess(main,mainTemp);//主表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("main", main);
			}
			//子表
			if(detail!=null&&!detail.isEmpty()){
				detail=BillDesignPlugin.detailProcess(detail,detailTemp);//子表（将1.0版本的特殊处理字段合并到单据设计结果中）			
				map.put("detail", detail);
			}
			
			List<Map> files = db.queryIdForList("mobile_common.getFiles", new Object[] {cguid});
			if(CollectionUtil.isEmpty(main))
				 throw new BusinessException("并发删除,请重新查询");
			if(files!=null&&!files.isEmpty())
		           map.put("file", files);
			return map;
		}else{
			return null;
		}
		
	}
	

}