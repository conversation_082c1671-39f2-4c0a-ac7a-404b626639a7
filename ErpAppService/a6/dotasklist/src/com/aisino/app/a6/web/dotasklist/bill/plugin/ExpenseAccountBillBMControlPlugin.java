package com.aisino.app.a6.web.dotasklist.bill.plugin;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;


import com.aisino.a6.finance.business.bm.BMUtil;
import com.aisino.aos.bill.common.BillUtils;
import com.aisino.aos.preference.common.Prefer;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;

/**
 * 财务预算控制
 * <AUTHOR>
 * @since 2017-08-29
 */
public class ExpenseAccountBillBMControlPlugin {
	
	public static void bmControl(AbstractForm form,DataMsgBus bus){
		String bmParam = checkBill(bus, "Status");
		String actionState = bus.getString("actionState");
		if(actionState.equalsIgnoreCase(bmParam)){//预算控制
			DbSvr db = DbSvr.getDbService(null);
			
			bus.send("dBillDataBMUsed", "cRefundDate");
			bus.send("currate.payAMT", "iTotalAMT");
			bus.send("currate.payAMT_F", "");
			bus.send("currate.payAMTCol", "iOpeningAMT");
			bus.send("listname", "list");
			bus.send("StatusField", "cStatusEnumGUID");
			
			BMUtil util = new BMUtil();
			
			Map result = new HashMap();
			
			result = checkSingleBill(db,form,util,bus);
				
			if(result != null && result.size() > 0){
				String errMsg = String.valueOf(result.get("error")==null?"":result.get("error"));
				if(!"".equals(errMsg) && StringUtils.isNotBlank(errMsg)){
					throw new BusinessException(errMsg);
				}
			}
		}
	}
	
	private static Map checkSingleBill(DbSvr db,AbstractForm form,BMUtil util, DataMsgBus bus){//
		String sysCode = bus.getString("cbuscode");
		Map result = new HashMap();
		if("gl".equals(sysCode)){
			result = util.checkBill(db,form, bus);
		}else{
			result = util.checkBill(db,form, bus);	//单据明细
		}
		return result;
	}
	
	public static String checkBill(DataMsgBus bus, String checkType){//是否預算控制
		String code = bus.getString("code");
		String cBusCode = bus.getString("cBusCode");
		String returnValue = null;
		BillUtils.getBillVoByCode(cBusCode);
		if("forCode".equals(checkType)){
			code = cBusCode;
			returnValue = DbSvr.getDbService(null).queryIdForString("finance_bm_ctrl.bm_isautoaudit", new Object[]{cBusCode});
		}else if("U3_GL_AutoCheck".equals(checkType)){
			code = checkType;
			returnValue = Prefer.get(code);
		}else{
			code = "A6_BM_"+checkType+"_"+cBusCode;
			returnValue = Prefer.get(code);
		}
		
		return returnValue;
	}
}
