<?xml version="1.0" ?>

<project default="build" basedir=".">
	<tstamp>
	   <format property="TODAY_UK_M" pattern="yyyyMMdd" locale="en"/>
	   <format property="TODAY_UK" pattern="yyyyMMdd.HHmm" locale="en"/> 
	</tstamp>

	<!-- Name of project and version -->
	<property name="com.name" value="Aisino" /> 	<!-- 公司名 -->
	<property name="proj.name" value="A3mobile" />		<!-- 项目名 -->
	<property name="module.version" value="3.3" />	<!-- 版本号 -->
	<property name="JOB_NAME" value="mobile" />	<!-- 打包工程名 -->
	<!-- Global properties for this build -->
	<property name="lib.dir" location="D:\dailybuild\A3\lib" />				                                                <!-- lib目录 -->
	<property name="tomcat.lib.dir" location="D:\dailybuild\apache-tomcat-6.0.10/lib"/>                                            <!-- Tomcat的lib目录 -->	
		
	<property name="build.dir" location="${basedir}\temp" />
	<property name="upload.dir" location="\\************\a3打包" />	
	<!--property name="toA3.dir" location="D:\AutoDeployA66.3ForA3" /-->	
	<property name="my.build.classes.dir" location="${build.dir}/classes" />
	<property name="remote.java.dir" location="${basedir}\BuildResult\${TODAY_UK}"/>

	<!-- Classpath declaration -->
	<path id="project.classpath">
		<fileset dir="${remote.java.dir}">
			<include name="**/*.jar" />
		</fileset>		
		<fileset dir="${lib.dir}">
			<include name="**/*.jar" />
			<include name="**/*.zip" />
		</fileset>
		<fileset dir="${tomcat.lib.dir}">
			<include name="**/*.jar" />
		</fileset>	
	</path>

	<!-- Useful shortcuts -->
	<patternset id="meta.files">
		<include name="**/*.xml" />
		<include name="**/*.dtd" />
		<include name="**/*.js" />
		<include name="**/*.css" />
		<include name="**/*.gif" />
		<include name="**/*.sql" />
		<include name="**/*.jpg" />
		<include name="**/*.png" />
		<include name="**/*.xls" />
		<include name="**/*.OCX" />		
		<include name="**/*.cll" />
		<include name="**/*.vbs" />
		<include name="**/*.html" />
		<include name="**/*.cab" />
		<include name="**/*.jpeg" />
		<include name="**/*.cpp" />
		<include name="**/*.ftl" />
		<include name="**/*.ocx" />	
		<include name="**/*.zip" />	
		<include name="**/*.ZIP" />		
	</patternset>
	
	<target name="copy">
	
     	<echo>复制构建结果</echo>
		
<!-- 		<copy todir="${toA3.dir}">
			<fileset dir="${remote.java.dir}">
			</fileset>			
		</copy>	 -->		
		<copy todir="${upload.dir}">
			<fileset dir="${remote.java.dir}">
			</fileset>			
		</copy>
		<copy todir="D:\hudson\BuildResult\${TODAY_UK}">
			<fileset dir="${remote.java.dir}">
			</fileset>			
		</copy>		
<!-- 	    拷贝打包结果到变更集目录	 -->
		<copy todir="D:\HudsonChangeSet\${JOB_NAME}\BuildJarResult">
			<fileset dir="${remote.java.dir}" excludes="**/*.zip">			   
			</fileset>			
		</copy>			
		<echo>-----------------BUILD SUCCESSFUL-------------------------------</echo>		
	</target>
	

	<target name="build">
		<echo>-----------------prepare to build------------------------</echo>
		<antcall target="clear" />
		<antcall target="create" />
		
		<echo>-----------------building mobile_common-------------------------------</echo>
		<antcall target="package">
		    <param name="module.name" value="common" />	
		    <param name="sub.name" value="notice" />		    
		</antcall>
		<antcall target="package">
		    <param name="module.name" value="common" />		
			<param name="sub.name" value="matstock" />
		</antcall>

		<antcall target="package">
		    <param name="module.name" value="common" />		
			<param name="sub.name" value="customer" />
		</antcall>

		<antcall target="package">
		    <param name="module.name" value="common" />		
			<param name="sub.name" value="dotasklist" />
		</antcall>	
		<antcall target="package">
		    <param name="module.name" value="common" />	
			<param name="sub.name" value="login" />	
		</antcall>	
		<antcall target="package">
		    <param name="module.name" value="common" />		
			<param name="sub.name" value="report" />
		</antcall>
		<antcall target="package">
		    <param name="module.name" value="common" />		
			<param name="sub.name" value="netschool" />
		</antcall>		
		
		<!--antcall target="package"/-->
		<echo>-----------------mobile_common  OK-------------------------------</echo>
		
		<echo>-----------------building a3-------------------------------</echo>
		<antcall target="package">
		    <param name="module.name" value="a3" />	
		    <param name="sub.name" value="login" />			    
		</antcall>
		<antcall target="package">
		    <param name="module.name" value="a3" />		
			<param name="sub.name" value="matstock" />
		</antcall>
		<antcall target="package">
		    <param name="module.name" value="a3" />		
			<param name="sub.name" value="customer" />
		</antcall>
		<antcall target="package">
		    <param name="module.name" value="a3" />		
			<param name="sub.name" value="dotasklist" />
		</antcall>
		<antcall target="package">
		    <param name="module.name" value="a3" />		
			<param name="sub.name" value="report" />
		</antcall>			
		<echo>-----------------a3  OK-------------------------------</echo>		

		<antcall target="copy"/>
		<echo>-----------------All Build Ok----------------------------</echo>		
	</target>
	
	<target name="package" >
		<mkdir dir="${my.build.classes.dir}\${module.name}\${sub.name}" /> 	
		<echo>编译类文件,来自: ${basedir}\${module.name}\${sub.name}</echo>
		<javac debug="true" srcdir="${basedir}\${module.name}\${sub.name}" executable="D:\Java\jdk1.7.0_67\bin\javac" listfiles="true" 
			encoding="utf8" nowarn="true"  includeantruntime="on" destdir="${my.build.classes.dir}\${module.name}\${sub.name}" 
			classpathref="project.classpath">		
		</javac>
		<echo>正在打包类文件B</echo>
		<mkdir dir="${remote.java.dir}" /> 
		<echo>${remote.java.dir}</echo>
		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${sub.name}-${module.version}.jar">	
			<fileset dir="${my.build.classes.dir}\${module.name}\${sub.name}" >
				<!-- 如果源码目录下有需要排除的类文件 -->
			</fileset>

		<!--echo>复制资源文件,来自: ${basedir}\${module.name}\${sub.name}\resource</echo>
		<echo>${my.build.classes.dir}</echo>
		<copy todir="${my.build.classes.dir}"-->
			<fileset dir="${basedir}\${module.name}\${sub.name}\resource">
				<patternset refid="meta.files" />
			</fileset>
		<!--/copy-->	
		</jar>		
		<echo>发布成功</echo>
	</target>

	
	<target name="jar">
		<delete dir="${build.dir}"></delete>
		<echo>发布成功</echo>
		
		<mkdir dir="${my.build.classes.dir}\${module.name}" /> 
		
		<echo>编译类文件,来自: ${basedir}\${module.name}\src\src</echo>
		<javac srcdir="${basedir}\${module.name}\src" executable="D:\Java\jdk1.6.0_27\bin\javac" listfiles="true" 
			encoding="utf8" nowarn="true" includeantruntime="on" debug="on" destdir="${my.build.classes.dir}\${module.name}" 
			classpathref="project.classpath">
			<classpath refid="project.classpath" />
		</javac>	
	
        <echo>正在打包类文件</echo>

		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.jar">
			<fileset dir="${my.build.classes.dir}\${module.name}\" >
				<!-- 如果源码目录下有需要排除的类文件 
				<exclude name="com/**" />
				-->
			</fileset>
		<!--复制资源文件,来自: ${basedir}\${proj.name}\code\${module.name}\resource-->			
			<fileset dir="${basedir}\${module.name}\resource">
 				<patternset refid="meta.files" />
			</fileset>
		</jar>
		
	</target>

	<target name="jar2">
	
		<mkdir dir="${my.build.classes.dir}\${module.name}" /> 
		
		<echo>编译类文件,来自: ${basedir}\${module.name}\src\src</echo>
		<javac srcdir="${basedir}\${module.name}\src" executable="D:\Java\jdk1.6.0_27\bin\javac" listfiles="true" 
			encoding="utf8" nowarn="true" includeantruntime="on" debug="on" destdir="${my.build.classes.dir}\${module.name}" 
			classpathref="project.classpath">
			<classpath refid="project.classpath" />
		</javac>	
	
        <echo>正在打包类文件</echo>

		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.jar">
			<fileset dir="${my.build.classes.dir}\${module.name}\" >
				<!-- 如果源码目录下有需要排除的类文件 
				<exclude name="com/**" />
				-->
			</fileset>
		<!--复制资源文件,来自: ${basedir}\${proj.name}\code\${module.name}\resource-->			
			<fileset dir="${basedir}\${module.name}\resource">
                      <patternset refid="meta.files" /> 
			</fileset>
		</jar>
		
		<echo>正在打包页面文件</echo>
		<zip destfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.zip" update="true" duplicate="preserve">
			<zipfileset dir="${basedir}\${module.name}\webapp" prefix="webapp"> 
			        <patternset refid="meta.files" />
			</zipfileset>
		</zip>
		
	</target>
	
	<target name="jar3">
	
		<mkdir dir="${my.build.classes.dir}\${module.name}\${sub.name}" /> 
		
		<echo>编译类文件,来自: ${basedir}\${module.name}\${sub.name}\src</echo>
		<javac srcdir="${basedir}\${module.name}\${sub.name}\src" executable="D:\Java\jdk1.6.0_27\bin\javac" listfiles="true" 
			encoding="utf8" nowarn="true" includeantruntime="on" debug="on" destdir="${my.build.classes.dir}\${module.name}\${sub.name}" 
            classpathref="project.classpath" excludes="com/aisino/a6/business/cancel/upgrade/**/*.java">
			<classpath refid="project.classpath" />
		</javac>	
	
        <echo>正在打包类文件</echo>

		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${sub.name}-${module.version}.jar">
			<fileset dir="${my.build.classes.dir}\${module.name}\${sub.name}" >
				<!-- 如果源码目录下有需要排除的类文件 -->
				
			</fileset>
		<!--复制资源文件,来自: ${basedir}\${proj.name}\code\${module.name}\resource-->			
			<fileset dir="${basedir}\${module.name}\${sub.name}\resource">
                  <patternset refid="meta.files" />
			</fileset>
		</jar>
					
	</target>

	<target name="jar4">
	
		<mkdir dir="${my.build.classes.dir}\${module.name}\${sub.name}" /> 
		
		<echo>编译类文件,来自: ${basedir}\${module.name}\src</echo>
		<javac srcdir="${basedir}\${module.name}\${sub.name}\src" executable="D:\Java\jdk1.6.0_27\bin\javac" listfiles="true" 
			encoding="utf8" nowarn="true" includeantruntime="on" debug="on" destdir="${my.build.classes.dir}\${module.name}\${sub.name}" 
            classpathref="project.classpath">
			<classpath refid="project.classpath" />
		</javac>	
	
        <echo>正在打包类文件</echo>

		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${sub.name}-${module.version}.jar">
			<fileset dir="${my.build.classes.dir}\${module.name}\${sub.name}" >
				<!-- 如果源码目录下有需要排除的类文件 -->
				
			</fileset>
		<!--复制资源文件,来自: ${basedir}\${proj.name}\code\${module.name}\resource>			
			<fileset dir="${basedir}\${module.name}\${sub.name}\resource">
                  <patternset refid="meta.files" />
			</fileset-->
		</jar>
					
	</target>	
	<target name="jar5">
	
		<mkdir dir="${my.build.classes.dir}\${module.name}\${sub.name}" /> 
		
		<echo>编译类文件,来自: ${basedir}\${module.name}\src</echo>
		<javac srcdir="${basedir}\${module.name}\${sub.name}\src" executable="D:\Java\jdk1.6.0_27\bin\javac" listfiles="true" 
			encoding="utf8" nowarn="true" includeantruntime="on" debug="on" destdir="${my.build.classes.dir}\${module.name}\${sub.name}" 
            classpathref="project.classpath">
			<classpath refid="project.classpath" />
		</javac>	
	
        <echo>正在打包类文件</echo>

		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${sub.name}-${module.version}.jar">
			<fileset dir="${my.build.classes.dir}\${module.name}\${sub.name}" >
			<!-- 如果源码目录下有需要排除的类文件 -->				
			</fileset>	
			<!--复制资源文件,来自: ${basedir}\${proj.name}\code\${module.name}\resource-->			
			<fileset dir="${basedir}\${module.name}\${sub.name}\resource">
                  <patternset refid="meta.files" />
			</fileset>
		</jar>
		
		<echo>正在打包页面文件</echo>
		<zip destfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${sub.name}-${module.version}.zip" update="true" duplicate="preserve">
			<zipfileset dir="${basedir}\${module.name}\${sub.name}\webapp" prefix="webapp"> 
			        <patternset refid="meta.files" />
			</zipfileset>
		</zip>
	</target>	
	
    <target name="jar6">
        <echo>正在打包${module.name}包页面文件</echo> 
        <zip destfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.zip" update="true" duplicate="preserve">
              <zipfileset dir="${basedir}\${module.name}\webapp" includes="**/*.*" prefix="webapp/" excludes="**/*.bak,**/*.jar,**/*.bat,**/*.scc,secdev.html" /> 
        </zip>
    </target>
	<target name="create">
		<mkdir dir="${remote.java.dir}" />
		<mkdir dir="${upload.dir}" />
		<mkdir dir="D:\hudson\BuildResult\${TODAY_UK}" />  
        <mkdir dir="D:\HudsonChangeSet\${JOB_NAME}\BuildJarResult" />		
	</target>

	<target name="jar7">
	<!--移动端专用，结合package使用-->
		<mkdir dir="${my.build.classes.dir}\${module.name}\${sub.name}" /> 
		
		<echo>编译类文件,来自: ${basedir}\${module.name}\${sub.name}\src</echo>
		<javac srcdir="${basedir}\${module.name}\${sub.name}\src" executable="D:\Java\jdk1.7.0_67\bin\javac" listfiles="true" 
			encoding="utf8" nowarn="true"  includeantruntime="on" destdir="${my.build.classes.dir}\${module.name}\${sub.name}" 
			classpathref="project.classpath">
			<classpath refid="project.classpath" />
		</javac>	
        <echo>正在打包类文件A</echo>
	</target>	
	
	<target name="clear">
		<delete dir="${build.dir}\temp" />		
	</target>
	
	<target name="lib_copy">
        <delete>
            <fileset dir="${lib.dir}" includes="Aisino-A6-${module.name}-*.*"/>
        </delete>
		<copy todir="${lib.dir}">
			<fileset dir="${remote.java.dir}">
			    <include name="Aisino-A6-${module.name}-*.jar"/>
			</fileset>			
		</copy>		
	</target>	
	

</project>
