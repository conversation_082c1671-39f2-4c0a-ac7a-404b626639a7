package com.aisino.app.web.notice;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.aisino.platform.core.ScheduleTask;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.view.DataMsgBus;

/**
 * 工作流消息推送定时任务，扫描时间间隔一小时。
 * 
 */
public class WorkFlowSendTask extends ScheduleTask {
	private static final String ID = "WorkFlowNotificationSendTask";
	private static final int TIME_INTERVAL = 60; // 消息推送时间间隔,单位分钟。
	private DbSvr accsys = DbSvr.getDefaultDbService();
	private DbSvr db = null;
	@Override
	public void doTask() {
		if(!NoticeSendUtil.hasAppClient())
			return;
		DataMsgBus bus = getTimeInterval();	
		List<Map<String, String>> notice = queryNotice(bus);
		if (notice != null) {
			NoticeSendUtil.send(notice);
		}
	}

	/**
	 * @Title: getTimeInterval
	 * @Description:获取发送信息的区间
	 * @return DataMsgBus
	 */

	private DataMsgBus getTimeInterval() {
		DataMsgBus bus = new DataMsgBus();
		Calendar calendar = Calendar.getInstance();
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		bus.put("endTime", format.format(calendar.getTime()));
		calendar.add(Calendar.MINUTE, -TIME_INTERVAL);
		bus.put("beginTime", format.format(calendar.getTime()));
		return bus;
	}

	/**
	 * @Description:获取要发送的通知列表
	 */

	@SuppressWarnings({ "rawtypes" })
	private List<Map<String, String>> queryNotice(DataMsgBus bus) {
		List<Map> dbListRecord = accsys
				.getListResult("select accid,accname from accinfo where ISHIDDEN <> 1");
		accsys.release();
		if (CollectionUtil.isEmpty(dbListRecord)) {
			return null;
		}
		Map<String, String> noticeMap = new HashMap<String, String>();
		List<Map<String, String>> noticeList = new ArrayList<Map<String, String>>();
		for (Map dbMap : dbListRecord) {
			try {
				String accid = CollectionUtil.getStringFromMap(dbMap, "accid");
				db = DbSvr.getDbService(accid);
				String dsName = CollectionUtil.getStringFromMap(dbMap,
						"accname");
				List<Map> msgList = db.queryIdForList(
						"app_workflow_notice.query_notice", bus);
				noticeMap.put("accId", accid);
				noticeMap.put("accName", dsName);
				if (msgList != null) {
					for (Map m : msgList) {
						if (m != null) {
							Map<String, String> map = new HashMap<String, String>();
							String userID = m.get("cGUID") == null ? "" : m
									.get("cGUID").toString();
							String title = m.get("title") == null ? "" : m.get(
									"title").toString();
							String content = m.get("content") == null ? "" : m
									.get("content").toString();
							String noticeType = m.get("noticeType") == null ? ""
									: m.get("noticeType").toString();
							map.put("userID", userID);
							map.put("title", "航天信息ERP");
							map.put("content", content);
							map.put("noticeType", noticeType);
							map.putAll(noticeMap);
							noticeList.add(map);
						}
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}finally {
				db.release();
			}
		}
		return noticeList;
	}

	@Override
	public String getID() {
		return ID;
	}

	@Override
	public int getScheduleType() {
		return ScheduleTask.EveryHour;
	}

	@Override
	public Date loadSchedule() {
		return new Date();
	}

}
