package com.aisino.app.web.notice;

import com.aisino.aos.system.SessionHelper;
import com.aisino.aos.system.utils.DateUtilExt;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.*;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
/**
 * 加载系统消息列表
 * <AUTHOR>
 *	noticeType=4
 *
 */
public class SystemNoticeListPlugin extends SqlPlugin implements
		FormCreateListener {

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		loadNotices(form, bus);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public Map loadNotices(AbstractForm form, DataMsgBus bus) {
		DbSvr db = DbSvr.getDbService(null);
		String noticeType = bus.getString("noticeType").toString();
		String userId = SessionHelper.getCurrentUserId();
		HttpServletRequest request = SessUtil.getRequest();
		String appName = request.getContextPath();
		String orgnId = !SessionHelper.isSingleOrgn() ? SessionHelper
				.getCurrentAdminOrgnId() : "1";
		String sql = "select  top 100 percent ROW_NUMBER() OVER (ORDER BY  pn.dcreatedate desc) AS rownum,"
				 +" pn.cGUID cguid,pn.iPriority priority,case when pn.cFromUserGUID='accsys' then '系统管理员' else u.cRealName end as fromUser,"
				 +" pn.cName name,pn.cDesc content , pn.dCreateDate createDate, pn.dDueDate duedate,pn.cUrl url, "
				 +" case when pn.dEndDate is null then 'font-weight:bold' else null end '_style' from pt_notice pn "
				 +" inner join (select creplyguid,Max(dCreateDate) dCreateDate from pt_notice rep where rep.cToUserGUID = ? group by rep.creplyguid ) pp" 
				 +" on pp.creplyguid = pn.creplyguid and pn.dCreateDate = pp.dCreateDate left join aos_rms_user u on pn.cFromUserGUID=u.cGUID "
				 +" where (pn.dDueDate is null or pn.dDueDate = '' or pn.dDueDate<=getDate())"
				 + " and pn.iNoticeType = ?     "
			 	 + "  and convert(VARCHAR(10),pn.dCreateDate, 23) = convert(VARCHAR(10), getdate(), 23)"
				 + "and 1=1 and "
				 +" (pn.corgnid = ? or pn.corgnid is null or pn.corgnid = '') and pn.cToUserGUID = ? and 1=1 and (1=1 or 1=1) and "
				 +" (pn.cDeleteFlag is null or pn.cDeleteFlag != '0') order by pn.dCreateDate desc";

		
		List list = db.getListResult(sql,new Object[]{
			userId,noticeType,orgnId,userId
		});
		int noticelength=0;
		if (CollectionUtil.isNotEmpty(list)) {
			SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy-MM-dd");
			SimpleDateFormat monthFormat = new SimpleDateFormat(
					"M\u6708d\u65E5");
			SimpleDateFormat hourFormat = new SimpleDateFormat("kk:mm");
			int i=0;
			for (;i<list.size();i++) {
				Map m=(Map) list.get(i);
				String name = (String) m.get("name");
				String content = (String) m.get("content");
				/*if (StringUtil.isBlank(content))
					m.put("content", name);
				else
					m.put("content",
							(new StringBuilder()).append(name).append("\uFF1A")
									.append(content).toString());*/
				String url = (String) m.get("url");
				if (StringUtil.isNotBlank(url)) {
					url = (new StringBuilder())
							.append(url.replace("pt/canvas?formid=", ""))
							.append("&cFromUser=").append(m.get("fromuser"))
							.toString();
					m.put("url", url);
				}
				Object object = m.get("createdate");
				String dateToStr = DateUtilExt.dateToStr(object,
						"yyyy-MM-dd kk:mm:ss");
				Date createDate = DateUtil.str2Time(dateToStr);
				if (yearFormat.format(createDate).equals(
						yearFormat.format(new Date())))
					m.put("createdate", hourFormat.format(createDate));
				else
					m.put("createdate", monthFormat.format(createDate));
				if("/A6".equals(appName)&&"1".equals(noticeType)){
					getA6YuJingNotice(m);
				}else if("/A3".equals(appName)&&"1".equals(noticeType)){
					getA3YuJingNotice(m);
				}
				
					if(!CollectionUtil.isBlankMap(m)){
						noticelength++;
					}else{
						list.remove(i);
						i=i-1;
					}
				}

		}
		if (CollectionUtil.isEmpty(list))
			list = new ArrayList();
		Map p = new HashMap();
		p.put("list", list);
		p.put("noticeType", noticeType);
		p.put("noticelength", noticelength);
		form.setReturn(p);
		return p;
	}
	
     /*A6APP预警消息显示  2017年2月23日 18:05:35  zrc*/
	public  void getA6YuJingNotice(Map m){
		
		String name = (String) m.get("name");
		String content = (String) m.get("content");
		DbSvr db = DbSvr.getDbService(null);
		List<Map> l;
		if(name.equals("负库存预警")){
			 l=db.queryIdForList("A6app_alarm.fukucun", new HashMap());
			if(CollectionUtil.isNotEmpty(l)){
				content="物品:"+l.get(0).get("cMatName")+"["+l.get(0).get("cMatCode")+"]";
				m.put("content", content);
			}
		}else if(name.equals("客户余额预警")){
			//l=db.queryIdForList("A6app_alarm.kehuyue", new HashMap());
			String sql=db.getStringResult("select csendtable from AOS_EW_SQL where  cguid='948441487158161868'", null);
			int a=sql.indexOf(',');
			String s33=sql.substring(0,a+1);
			s33=s33+"   convert(nvarchar, cast(round(iendamt ,2) as money),1) as iendamt,  ccustcode, ccustname,   ";
			String s44=sql.substring(a+1,sql.length());
			Map s=db.getOneRecorder(s33+s44, null);
			if(!CollectionUtil.isBlankMap(s)){
				content=s.get("ccustname")+"["+s.get("ccustcode")+"]"+"  应收余额:"+s.get("iendamt");
				m.put("content", content);
			}
		} else if(name.equals("业务员销售额")){
			l=db.queryIdForList("A6app_alarm.yewuyuanxiaoshoue", new HashMap());
			if(CollectionUtil.isNotEmpty(l)){
				content="销售额:"+l.get(0).get("dvalue");
				m.put("content", content);
			}
		}else if(name.equals("销售毛利")){
			l=db.queryIdForList("A6app_alarm.xiaoshoumaoli", new HashMap());
			if(CollectionUtil.isNotEmpty(l)){
				content="销售毛利:"+l.get(0).get("dvalue");
				m.put("content", content);
			}
		}else if(name.equals("物品有效期预警")){
			l=db.queryIdForList("A6app_alarm.baozhiqi", new HashMap());
			if(CollectionUtil.isNotEmpty(l)){
				content="物品:"+l.get(0).get("cMatName")+"["+l.get(0).get("cMatCode")+"]";
				m.put("content", content);
			}
		}else{
			m.clear();
			
		}
		}		
		/*a3app预警*/
		public  void getA3YuJingNotice(Map m){
			String name = (String) m.get("name");
			String content = (String) m.get("content");
			DbSvr db = DbSvr.getDbService(null);
			List<Map> l;
			if(name.equals("负库存预警")){
				l=db.queryIdForList("a3app_alarm.fukucunalarm");
				if(CollectionUtil.isNotEmpty(l)){
					content="物品:"+l.get(0).get("cmatname")+"["+l.get(0).get("cmatcode")+"]";
					m.put("content", content);
				}
			}else if(name.equals("毛利率预警")){
				l=db.queryIdForList("a3app_alarm.xiaoshoumaolilvalarm");
				if(CollectionUtil.isNotEmpty(l)){
					content="物品:"+l.get(0).get("cmatname")+"["+l.get(0).get("cmatcode")+"]"+",毛利率:"+l.get(0).get("reearn");
					m.put("content", content);
				}
			}else if(name.equals("物品保质期按天数预警")){
				l=db.queryIdForList("a3app_alarm.wpbzqatsyjalarm");
				if(CollectionUtil.isNotEmpty(l)){
					content="物品:"+l.get(0).get("cmatname")+"["+l.get(0).get("cmatcode")+"]";
					m.put("content", content);
				}
			}else{
				m.clear();
			}
		}
	}

