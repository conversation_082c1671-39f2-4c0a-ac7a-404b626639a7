package com.aisino.app.web.notice;


import java.util.HashMap;
import java.util.Map;


import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.core.Guid;
import com.aisino.platform.core.MS;


/**
 * 插入审核通知类
 * */
public class InsertCheckNoticePlugin {
	
	/**
	 * 非工作流单据审核失败的消息插入公用接口
	 * <AUTHOR>
	 * @date Dec 17, 2016
	 * @param 发给cToUserGuid（即单据的制单人） 、content（审核失败的原因）
	 * @return void
	 */
	public void InsertCheckNotice(String cToUserGuid,String content) {
		    Map m = new HashMap();

		    m.put("cToUserGuid", cToUserGuid);
		    m.put("cname", "个人消息");
		    m.put("iNoticeType", "4");
		    String busiguid = Guid.g();
		    m.put("cBusinessGUID", busiguid);
		    m.put("cUrl", "pt/canvas?formid=aos_send_pt_notice_form&cBusinessGuid=" + busiguid);

		    m.put("cFromUserGuid", SessionHelper.getCurrentUserId());
		    m.put("loginOrgnId", SessionHelper.isSingleOrgn() ? "1" : SessionHelper.getCurrentAdminOrgnId());
		    m.put("cDesc", content);
		    m.put("acctId", null);
		    m.put("cAttachGroupGuid", null);
		    m.put("creplyGuid", null);
		    m.put("sendGuid", null);
		    m.put("threadIndepentForBillMessage", true);
		    MS mst = new MS("aos.message_notify_send");
		    mst.doService(new Object[] { m });
	
	}
}
