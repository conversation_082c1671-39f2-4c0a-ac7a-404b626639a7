package com.aisino.app.web.notice;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.aisino.platform.core.ProductInfo;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.DateUtil;
import com.aisino.platform.view.DataMsgBus;

public class NotWorkFlowNoticeUtil {

	private static DbSvr accsys = null;
	private static DbSvr db = null;
	private static String LEVEL = "level"; // 非工作流查询sql表中sql等级列字段名称
	private static String EXECUTE_SQL = "sql"; // 查询sql表中sql语句字段名称
	private static String TABLE_NAME = "zsj_temp"; // 查询sql表名

	/**
	 * 根据预设的时间，返回定时器时间。
	 * 
	 * @param PRESENT_TIME
	 *            预设时间
	 * @return
	 */
	public static Date loadDate(int PRESENT_TIME) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.set(Calendar.HOUR_OF_DAY, PRESENT_TIME);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd kk:mm:ss");
		Date time = DateUtil.str2Time(format.format(calendar.getTime()));
		return time;
	}

	// 根据userid判断改用户是否有使用app客户端
	private static boolean userHasApp(String userid) {
		String sqlString = "select count(*) from acc_app_jpush_relation where cuserid = ?";
		try {
			int count = new Integer(accsys.getStringResult(sqlString, userid));
			return count > 0 ? true : false;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}

	}

	/**
	 * 直接去app系统中的代办事项，全部查询。。
	 * 
	 * @return 查询结果。
	 */
	@SuppressWarnings({ "rawtypes" })
	public static List<Map<String, String>> queryNotice2() {
		// 获取所有用户数据
		String getAllUser = "select distinct cUserId,cUserName,cUserPass from Acct_LinkUser where cUserName != ''";
		// 获取每个用户的所有账套信息
		String getUserAccount = "select cAcctID from Acct_LinkUser where cUserID = ? and cUserName = ? and cUserPass = ?";
		List<Map> userList = new ArrayList<Map>();
		List<Map> accountListGroupByUser = new ArrayList<Map>();

		// 最终返回的消息列表
		List<Map<String, String>> noticeList = new ArrayList<Map<String, String>>();
		try {
			accsys = DbSvr.getDefaultDbService();
			userList = accsys.getListResult(getAllUser);
			if (userList == null) {
				accsys.release();
				return null;
			}
			boolean isA3 = "A3".equalsIgnoreCase(ProductInfo.getProjectName());
			String[] modules = new String[] { "EM", "OA", "MA", "FI", "PU", "SA", "ST" };
			// 按用户进行循环操作
			for (Map map : userList) {
				Map<String, String> noticeMap = new HashMap<String, String>();
				String userID = CollectionUtil.getStringFromMap(map, "cUserId");
				String userName = CollectionUtil.getStringFromMap(map, "cUserName");
				String userPass = CollectionUtil.getStringFromMap(map, "cUserPass");
				// 循环用户账套前先检查用户是否有使用app端，如果没有则不进行循环
				if (!userHasApp(userID))
					continue;
				noticeMap.put("userID", userID);
				accountListGroupByUser = accsys.getListResult(getUserAccount, userID, userName, userPass);
				if (accountListGroupByUser == null)
					continue;				
				// 循环用户账套，查询信息，如果任一账套有数据，则直接返回，不在执行后续查询操作。
				for (Map map2 : accountListGroupByUser) {
					int allnum = 0;
					String cAcctID = CollectionUtil.getStringFromMap(map2, "cAcctID");
					String result = accsys.getStringResult(
							"select count(*) from acc_app_jpush_relation where cAcctID = ? and cUserID = ?", cAcctID,
							userID);
					if ("0".equals(result))
						continue;
					db = DbSvr.getDbService(cAcctID);
					Map<String, String> tempMap = new HashMap<String, String>();
					tempMap.put("accId", cAcctID);
					try {
						// 模拟登录
//						DataSourceUtil.setCurrDataSource(cAcctID);
//						SessionContain sc = new SessionContain();
//						sc.put("userID", userID);
//						sc.put("adminOrgn", "1");
//						SessUtil.buildSessionContainInThreadMustRemoveAfterUse("mrp", sc);
						List deptRoleList = null;
						if (!isA3)
							deptRoleList = getDeptRolesList(userID, cAcctID);
						for (int i = 0; i < modules.length; i++) {
							String module = modules[i];
							DataMsgBus bus = getBusParam(isA3, module, userID, cAcctID, deptRoleList);
							List<Map> list = db.queryIdForList(
									"mobile_common_daiban_noright_A" + (isA3 ? "3." : "6.") + module, bus);
							if (list != null && !list.isEmpty()) {
								allnum += Integer.parseInt(String.valueOf(list.get(0).get("num")));
							}
						}
					} catch (Exception e) {
						System.out.println("后台消息推送模拟Session/获取待办数目异常.");
						e.printStackTrace();
					} finally {
//						SessUtil.removeSessionContainInThread("mrp");
						db.release();
					}

					if (allnum != 0) {
						tempMap.put("title", "航天信息ERP");
						tempMap.put("content", "您有" + allnum + "条待处理单据");
						tempMap.put("noticeType", "待处理事项");
						tempMap.put("accName", DbSvr.getDefaultDbService()
								.getStringResult("select accName from accinfo where accid = ?", cAcctID));
						tempMap.putAll(noticeMap);
						noticeList.add(tempMap);
					}
				}
			}
			return noticeList;

		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}finally {
			accsys.release();
		}
	}

	@SuppressWarnings("rawtypes")
	private static DataMsgBus getBusParam(boolean isA3, String module, String userID, String acctID,
			List deptRoleList) {
		DataMsgBus bus = new DataMsgBus();
		bus.send("keyword", null);
		if (isA3)
			return bus;
		else {
			// 下面各种bus里面方式的数据全部直接从A6代码中拷贝出来的。
			bus.send("currUserGUID", userID);
			bus.send("deptRoleList", deptRoleList);
			if ("EM".equals(module)) {
				String elFormId = "oa_fm_loanBill_edit_form";
				String eaFormId = "oa_fm_expenseAccountBill_edit_form";
				// 借款单审批方式
				
				String elcheckWay = db.getStringResult("select cCheckWay from BILL_SETTING where cFormId = ?", elFormId);
				// 报销单审批方式
				String eacheckWay = db.getStringResult("select cCheckWay from BILL_SETTING where cFormId = ?", eaFormId);
				if (!"2".equals(elcheckWay)) {// 借款单手工审批
					bus.send("sgspqxLoan", "sg");
				}
				if (!"2".equals(eacheckWay)) {// 报销单手工审批
					bus.send("sgspqxEm", "sg");
				}
			} else if ("OA".equals(module)) {
				//OA全是工作流审批 不做处理
			} else if ("ST".equals(module)) {
				/* 库存调拨单获取审批方式 */
				String sttrFormId = "business_st_StTrans";
				String sttrcheckWay =db.getStringResult("select cCheckWay from BILL_SETTING where cFormId = ?", sttrFormId);
				/* 库存调拨单单手工审批 */
				if (!"2".equals(sttrcheckWay)) {
					bus.put("sgStTrans", "true");
				}
			} else if ("SA".equals(module)) {
				/* 销售订单获取审批方式 */
				String saFormId = "business_sa_saorder";
				String sacheckWay = db.getStringResult("select cCheckWay from BILL_SETTING where cFormId = ?", saFormId);
				/* 销售订单手工审批 */
				if (!"2".equals(sacheckWay)) {
					bus.put("sgSaOrder", "true");
				}
				/* 销售发票获取审批方式 20170629 ZRC APP2.0加入销售发票的审批 */
				String sainFormId = "business_sa_sainvoice";
				String saincheckWay = db.getStringResult("select cCheckWay from BILL_SETTING where cFormId = ?", sainFormId);
				/* 销售发票手工审批 */
				if (!"2".equals(saincheckWay)) {
					bus.put("sgSaInvoice", "true");

				}				
				/* 零售单获取审批方式 20180907 GUODX APP3.0加入零售单的审批 */
				String saRetailFormId = "business_sa_retail_form";
				String saRetailcheckWay = db.getStringResult("select cCheckWay from BILL_SETTING where cFormId = ?", saRetailFormId);
				/* 零售单手工审批 */
				if (!"2".equals(saincheckWay)) {
					bus.put("sgSaRetail", "true");

				}
			} else if ("PU".equals(module)) {
				/* 采购订单获取审批方式 */
				String poFormId = "business_pu_puorder_billform";
				String pocheckWay = db.getStringResult("select cCheckWay from BILL_SETTING where cFormId = ?", poFormId);
				/* 采购订单手工审批 */
				if (!"2".equals(pocheckWay)) {
					bus.put("sgPuOrder", "true");
				}				
				/* 采购计划获取审批方式 */
				String plFormId = "business_pu_purPlan_form";
				String plcheckWay = db.getStringResult("select cCheckWay from BILL_SETTING where cFormId = ?", plFormId);
				/* 采购计划手工审批 */
				if (!"2".equals(plcheckWay)) {
					bus.put("sgPuPlan", "true");
				}
			} else if ("FI".equals(module)) {
				// 付款单获取审批方式
				String apFormId = "bus_ap_bill_form";
				String apcheckWay = db.getStringResult("select cCheckWay from BILL_SETTING where cFormId = ?", apFormId);
				/* 付款单手工审批 */
				if (!"2".equals(apcheckWay)) {
					bus.put("sgAP", "true");
				}
				// 其它付款单获取审批方式
				String caFormId = "finance_ca_apvoucher_edit_form";
				String cacheckWay = db.getStringResult("select cCheckWay from BILL_SETTING where cFormId = ?", caFormId);
				/* 其它付款单手工审批 */
				if (!"2".equals(cacheckWay)) {// 其它付款单单手工审批
					bus.put("sgCA", "true");
				}
			}
			return bus;
		}

	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static List getDeptRolesList(String userID, String acctID) {
		DbSvr db = DbSvr.getDbService(acctID);
		List deptGroupRoleIds = new ArrayList();
		String currUserGUID = userID;
		String sql = "select distinct 'D_'+e.cDeptGUID deptGroupRoleId " + 
				"from AOS_RMS_EMPL e " + 
				"join aos_rms_user u on e.cGUID = u.cEMP " + 
				"where u.cGuid = ? " + 
				"union " + 
				"select distinct 'R_' + r.cGuid deptGroupRoleId " + 
				"from aos_rms_role r " + 
				"INNER JOIN AOS_RMS_USER_ORGN_ROLE_REL uorr ON uorr.cRoleID = r.cGuid " + 
				"INNER JOIN AOS_RMS_USER_ORGN_REL uor ON uor.cGUID = uorr.cUserOrgnID " + 
				"INNER JOIN aos_rms_user u ON uor.cUserID = u.cGuid " + 
				"INNER JOIN AOS_RMS_EMPL a ON a.cGUID = u.cEMP " + 
				"where u.cGuid = ? " + 
				"union " + 
				"select distinct 'G_'+g.cGroupGuid deptGroupRoleId " + 
				"from WF_Process_UserToGroup g " + 
				"where g.cUserGuid = ?";
		List<Map> userDeptRolesList = db.getListResult(sql, currUserGUID,currUserGUID,currUserGUID);
		if (userDeptRolesList != null && userDeptRolesList.size() > 0) {
			for (Map map : userDeptRolesList) {
				deptGroupRoleIds.add(map.get("deptGroupRoleId"));
			}
		}
		return deptGroupRoleIds;
	}

	/**
	 * 依次查询数据库表，如果某个表中有记录，则停止查询。
	 * 
	 * @return 查询结果。
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static List<Map<String, String>> queryNotice() {

		// 获取所有用户数据
		String getAllUser = "select distinct cUserId,cUserName,cUserPass from Acct_LinkUser where cUserName != ''";
		// 获取每个用户的所有账套信息
		String getUserAccount = "select cAcctID from Acct_LinkUser where cUserID = ? and cUserName = ? and cUserPass = ?";
		List<Map> userList = new ArrayList<Map>();
		List<Map> accountListGroupByUser = new ArrayList<Map>();

		// 最终返回的消息列表
		List<Map<String, String>> noticeList = new ArrayList<Map<String, String>>();
		try {
			userList = accsys.getListResult(getAllUser);
			if (userList == null)
				return null;
			// 按用户进行循环操作
			for (Map map : userList) {
				Map<String, String> noticeMap = new HashMap<String, String>();
				String userID = CollectionUtil.getStringFromMap(map, "cUserId");
				String userName = CollectionUtil.getStringFromMap(map, "cUserName");
				String userPass = CollectionUtil.getStringFromMap(map, "cUserPass");
				// 循环用户账套前先检查用户是否有使用app端，如果没有则不进行循环
				if (!userHasApp(userID))
					continue;
				noticeMap.put("userID", userID);
				accountListGroupByUser = accsys.getListResult(getUserAccount, userID, userName, userPass);
				if (accountListGroupByUser == null)
					continue;
				// 循环用户账套，查询信息，如果任一账套有数据，则直接返回，不在执行后续查询操作。
				for (Map map2 : accountListGroupByUser) {
					String cAcctID = CollectionUtil.getStringFromMap(map2, "cAcctID");
					Map<String, String> tempMap = new HashMap<String, String>();
					tempMap.put("accId", cAcctID);
					db = DbSvr.getDbService(cAcctID);
					// 获取执行的查询的sql
					List<Map> sqlList = getExecuteSqlList(accsys);
					if (sqlList == null)
						return null;
					int count = 0;// 标志是否有查询结果
					for (int i = 0; i < sqlList.size(); i++) {
						Map temp = sqlList.get(i);
						String sql = temp.get(EXECUTE_SQL).toString();
						// String sql = sqlList.get(i).get(EXECUTE_SQL)
						// .toString();
						try {
							String result = db.getStringResult(sql, userID);
							count += new Integer(result == null ? "0" : result);
							// 按照sql优先级查询，如果查询到结果则不在继续查询
							if (count > 0) {
								tempMap.put("title", "航天信息ERP");
								tempMap.put("content", "您有待处理单据");
								tempMap.put("noticeType", "非工作流消息");
								tempMap.put("accName", "");
								tempMap.putAll(noticeMap);
								noticeList.add(tempMap);
								break;
							}
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				}
			}
			return noticeList;

		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 获取查询的sql
	 * 
	 * @param dbSvr
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	private static List getExecuteSqlList(DbSvr dbSvr) {
		List<Map> sqlList = new ArrayList<Map>();
		try {
			String sql = "select " + LEVEL + ", " + EXECUTE_SQL + " from " + TABLE_NAME;
			sqlList = dbSvr.getListResult(sql);
			// 按照sql语句优先级排序。
			Collections.sort(sqlList, new Comparator<Map>() {
				@Override
				public int compare(Map o1, Map o2) {
					int o1Value = new Integer(o1.get(LEVEL).toString());
					int o2Vaule = new Integer(o2.get(LEVEL).toString());
					return o1Value - o2Vaule;
				}
			});
		} catch (Exception e) {
			e.printStackTrace();
		}
		return sqlList;
	}
}
