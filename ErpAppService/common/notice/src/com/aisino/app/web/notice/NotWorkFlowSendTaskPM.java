package com.aisino.app.web.notice;

import java.util.Date;
import java.util.List;
import java.util.Map;
import com.aisino.platform.core.ScheduleTask;

/**
 * 非工作流消息推送定时任务。
 * 
 * <AUTHOR>
 * 
 */
public class NotWorkFlowSendTaskPM extends ScheduleTask {
	private static final String ID = "NotWorkFlowNotificationSendTaskPM";
	// 预设的消息推送扫描时间，单位：时（24小时制度）。
	private static final int PRESENT_TIME = 16;

	@Override
	public void doTask() {
		if (!NoticeSendUtil.hasAppClient())
			return;
		List<Map<String, String>> notice = NotWorkFlowNoticeUtil.queryNotice2();
		if (notice != null)
			NoticeSendUtil.send(notice);
	}

	@Override
	public String getID() {
		return ID;
	}

	@Override
	public int getScheduleType() {
		return ScheduleTask.EverayDay;
	}

	@Override
	public Date loadSchedule() {
		return NotWorkFlowNoticeUtil.loadDate(PRESENT_TIME);
	}

}
