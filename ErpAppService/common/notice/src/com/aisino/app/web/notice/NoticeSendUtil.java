package com.aisino.app.web.notice;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.aisino.platform.db.DbSvr;
import cn.jiguang.common.resp.APIConnectionException;
import cn.jiguang.common.resp.APIRequestException;
import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Options;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.AndroidNotification;
import cn.jpush.api.push.model.notification.IosNotification;
import cn.jpush.api.push.model.notification.Notification;
import cn.jpush.api.push.model.notification.WinphoneNotification;

public class NoticeSendUtil {

	private static final String TABLE_NAME = "acc_app_jpush_relation";
	// App_key 和Master_secret 从极光推送官网后台获取，用于唯一标识一个极光账户。
	private static final String APP_KEY = "e7f75ff136ae59bff9eec475";
	private static final String MASTER_SECRET = "285e030159e6cf7a70c02c98";
	private static final JPushClient jPushClient = new JPushClient(
			MASTER_SECRET, APP_KEY);
	private static PushPayload payload = null; // 推送使用的PushPayload对象
	private static PushResult result = null; // 推送结果
	private static DbSvr accsys = DbSvr.getDefaultDbService();
	

	/**
	 * @Description 根据用户名和密码查询RegisterId和MobOs
	 * @param userName
	 *            用户名
	 * @param accId
	 *            账套ID
	 * @parma workFlowFlag 标志是不是工作流消息。
	 * @return List<Map>
	 */
	@SuppressWarnings("rawtypes")
	public static List<Map> getRegisterIdAndMobOS(String userID,
			String accId) {
		List<Map> result = new ArrayList<Map>();
		try {
			String sql = "select distinct cMobOS, cRegisterId from " + TABLE_NAME
					+ " where cuserID = ? and cAcctId = ?";
			result = accsys.getListResult(sql, userID, accId);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		return result;
	}

	/**
	 * 根据表acc_app_jpush_relation判断有无app用户，如果没有，就不进行消息查找推送。
	 * 
	 * @return
	 */
	public static boolean hasAppClient() {
		try {
			String sql = "select count(*) from " + TABLE_NAME;
			String result = accsys.getStringResult(sql);
			int count = new Integer(result);
			if (count == 0) {
				System.out.println("没有App用户，不进行消息推送扫描任务！");
				return false;
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	/**
	 * @Description 构造推送对象
	 * @param registerId
	 *            设备注册ID
	 * @param mobOS
	 *            设备类型
	 * @param tile
	 *            推送消息标题
	 * @param content
	 *            推送消息内容
	 * @param contentType
	 *            推送消息内容
	 * @param countMap 
	 * @return 构造的推送对象
	 */
	public static PushPayload buildPushObject(String registerId, String mobOS,
			String tile, String content, String contentType, String accName, Map<String, Integer> countMap) {
		Platform platform = null;
		tile = "航天信息ERP";
		content = "[ "+accName+" ] "+ content;
		if ("android".equalsIgnoreCase(mobOS)) {
			platform = Platform.android();	
			return PushPayload
					.newBuilder()
					.setPlatform(platform)
					.setAudience(Audience.registrationId(registerId))
					.setNotification(
							Notification
									.newBuilder()
									.setAlert(tile)
									.addPlatformNotification(
											AndroidNotification
													.newBuilder()
													.setAlert(content)
													.setTitle(tile).build())
									.build()).build();

		} else if ("iOS".equalsIgnoreCase(mobOS)) {
			if(!countMap.containsKey(registerId))
				countMap.put(registerId,1);
			else
				countMap.put(registerId, countMap.get(registerId) +1);
			int count = countMap.get(registerId);			
			platform = Platform.ios();
			return PushPayload
					.newBuilder()
					.setPlatform(platform)
					.setAudience(Audience.registrationId(registerId))
					.setNotification(
							Notification
									.newBuilder()
									.setAlert(tile)
									.addPlatformNotification(
											IosNotification
													.newBuilder().setBadge(count)
													.setAlert(content)
													.build()).build()).setOptions(Options.newBuilder().setApnsProduction(true).build()).build();
		} else if ("winphone".equalsIgnoreCase(mobOS)) {
			platform = Platform.winphone();
			return PushPayload
					.newBuilder()
					.setPlatform(platform)
					.setAudience(Audience.registrationId(registerId))
					.setNotification(
							Notification
									.newBuilder()
									.setAlert(tile)
									.addPlatformNotification(
											WinphoneNotification
													.newBuilder()
													.setAlert(content)
													.build()).build()).build();
		} else {
			System.out.println("未知设备类型。");
			return null;
		}

	}

	/**
	 * @Description:调用极光API进行消息推送
	 * 
	 * @param registerId
	 *            设备注册ID
	 * @param mobOS
	 *            设备操作系统（android/iOS/winPhone)
	 * @param title
	 *            发送消息标题
	 * @param content
	 *            发送消息内容
	 * @param noticeType
	 *            发送消息类型
	 * @return 调用消息发送API返回值
	 */

	public static void doSend(String registerId, String mobOS, String title,
			String content, String noticeType, String accName,Map<String, Integer> countMap) {
		try {
			System.out.println("推送消息" + registerId);
			payload = buildPushObject(registerId, mobOS, title, content,
					noticeType, accName, countMap);
			try {
				result = jPushClient.sendPush(payload);
				if (result.statusCode != 0) {
					System.out.println("推送失败(registerId:" + registerId
							+ ",MobOS:" + mobOS + ")");					
				}
				
			} catch (APIConnectionException e) {
				System.out.println(e.getMessage());

			} catch (APIRequestException e) {
				System.out.println(e.getMessage());
			} catch (Exception e) {
				e.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
			System.err.println("推送发生异常,RegisterId:" + registerId);
		}
	}

	/**
	 * 消息推送。
	 * 
	 * @param notice
	 *            参数中Map的key,value均不可为null。
	 * @param workFlowFlag
	 *            标志是不是工作流消息，工作流消息加账套信息
	 * @param notice
	 *            : key:{userName, accId, noticeType, title, content, accName}
	 */
	@SuppressWarnings("rawtypes")
	public static void send(List<Map<String, String>> notice) {
		String registerId = null;
		String mobOS = null;
		Map<String, Integer> countMap = new HashMap<String, Integer>();
		for (Map m : notice) {
			String userID = m.get("userID").toString();
			String accId = m.get("accId").toString();
			String noticeType = m.get("noticeType").toString();
			String title = m.get("title").toString();
			String content = m.get("content").toString();
			String accName = m.get("accName").toString();
			List<Map> list = getRegisterIdAndMobOS(userID, accId);
			if (list == null)
				continue;
			for (Map m1 : list) {
				registerId = m1.get("cRegisterId") == null ? null : m1.get(
						"cRegisterId").toString();
				mobOS = m1.get("cmobOS") == null ? null : m1.get("cmobOS")
						.toString();
				if (!"".equals(registerId) && registerId != null
						&& !"".equals(mobOS) && mobOS != null )
					doSend(registerId, mobOS, title, content, noticeType,
							accName,countMap);
				else {
					System.out.println("注册ID或者设备类型为空。");
				}
			}
		}
		countMap.clear();

	}
}
