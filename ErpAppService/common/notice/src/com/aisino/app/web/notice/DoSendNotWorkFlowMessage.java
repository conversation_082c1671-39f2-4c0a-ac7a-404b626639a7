package com.aisino.app.web.notice;

import java.util.List;
import java.util.Map;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class DoSendNotWorkFlowMessage implements FormCreateListener {

	private static final long serialVersionUID = 1L;

	@Override
	public void onFormCreate(AbstractForm from, DataMsgBus bus) {
		try {
			if (!NoticeSendUtil.hasAppClient()) {
				from.setReturn("没有使用App的用户，不进行消息推送");
				return;
			}
			List<Map<String, String>> notice = NotWorkFlowNoticeUtil.queryNotice2();
			if (notice != null)
				NoticeSendUtil.send(notice);
			from.setReturn(notice);
		} catch (Exception e) {
			e.printStackTrace();
			from.setReturn("非工作流消息推送失败，失败原因：" + e.getMessage());
		}
	}

}
