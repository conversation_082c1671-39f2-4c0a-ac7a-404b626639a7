package com.aisino.app.web.notice;

import java.util.HashMap;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

/**
 * 
 * 保存极光推送注册ID与当前用户、账套的对应信息。
 * 
 */
public class SaveRelationShip extends SqlPlugin implements FormCreateListener {

	private static final long serialVersionUID = 1L;
	private boolean isOK = false;
	private static final String TABLE_NAME = "acc_app_jpush_relation";

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		Map<String, String> map = new HashMap<String, String>();
		try {
			String userID = null;
			String accountID = bus.get("accountID").toString();
			String registerID = bus.get("registerID").toString();
			String OS = bus.get("os").toString();
			String mobile = "";// 手机号，暂时未添加。
			if("".equalsIgnoreCase(accountID) || "".equalsIgnoreCase(registerID) || "".equalsIgnoreCase(OS)){
				System.out.println("accId为空或 "+
			accountID+"---registerId:"+registerID+"---OS:"+OS);
				{
					form.setReturn(new HashMap(){{
						put("result",false);
					}});
				return;}
			}
			// 如果请求链接中包含operation并且是delte,执行删除操作
			DbSvr dbSvr = DbSvr.getDefaultDbService();
			if (bus.contain("operation")
					&& "delete".equalsIgnoreCase(bus.get("operation")
							.toString())) {
				// 用户退出登录时候，使用SessionHelper.getCurrentUserId()获取不到用户信息，后台进行查询
				String userName = bus.get("userName").toString();
				String getUserID = "select cUserID from acct_linkUser where cusername= ? and cacctid = ?";
				userID = dbSvr.getStringResult(getUserID, userName, accountID);
				isOK = Delete(userID, OS, dbSvr);
			} else {
				userID = SessionHelper.getCurrentUserId();
				isOK = SaveOrUpdate(userID, accountID, registerID, mobile, OS,
						dbSvr);
			}

		} catch (Exception e) {
			System.err.println("Error occured in SaveRelationShip.java");
		}
		map.put("result", String.valueOf(isOK));
		form.setReturn(map);
	}

	public boolean Delete(String userID, String OS, DbSvr dbsvr) {
		try {
			String deleteSql = "delete from " + TABLE_NAME
					+ " where cUserID = ? and  CMobOS = ?";
			int count = dbsvr.update(deleteSql, userID, OS);
			System.out.println("用户退出帐号，删除:" + count + "条记录");
			return true;
		} catch (Exception e) {
			System.err.println("Error occured when delete relationShip.");
			return false;
		}
	}

	public boolean SaveOrUpdate(String userID, String accountID,
			String registerID, String mobile, String OS, DbSvr dbSvr) {
		try {
			String querySql = "select count(*) from " + TABLE_NAME
					+ " where cUserID = ? and cAcctID = ? and cMobOS = ?";

			String countResult = dbSvr.getStringResult(querySql, userID,
					accountID, OS);
			int count = new Integer(countResult);
			// 如果不存在记录，则保存记录。
			if (count == 0) {
				String insertSql = "insert into "
						+ TABLE_NAME
						+ "(cuserID,cAcctID,cRegisterId,cMobil,cMobOS) values(?,?,?,?,?)";
				int count1 = dbSvr.update(insertSql, userID, accountID,
						registerID, mobile, OS);
				System.out.println("插入:" + count1 + "条记录");
			} else if (count == 1) {
				String quertRegisterIDSql = "select cRegisterId from "
						+ TABLE_NAME
						+ " where cuserID = ? and cAcctID = ? and cMobOS = ?";
				String quertRegisterIDResult = dbSvr.getStringResult(
						quertRegisterIDSql, userID, accountID, OS);
				if (!registerID.equalsIgnoreCase(quertRegisterIDResult)) {
					String updateSql = "update "
							+ TABLE_NAME
							+ " set cRegisterId = ? where cuserID = ? and cAcctID = ? and cMobOS = ?";
					int count2 = dbSvr.update(updateSql, registerID, userID,
							accountID, OS);
					System.out.println("更新:" + count2 + "条记录");
				} else {
					System.out.println("存在一条同样的设备信息，不用更新。");
				}
			} else {
				String deleteSql = "delete from " + TABLE_NAME
						+ " where cuserID = ? and cAcctID = ? and cMobOS = ?";
				int count3 = dbSvr.update(deleteSql, userID, accountID);
				System.out.println("删除:" + count3 + "条记录");
				String insertSql = "insert into "
						+ TABLE_NAME
						+ "(cuserID,cAcctID,cRegisterId,cMobil,cMobOS) values(?,?,?,?,?)";
				int count4 = dbSvr.update(insertSql, userID, accountID,
						registerID, mobile, OS);
				System.out.println("插入:" + count4 + "条记录");
			}
			return true;
		} catch (Exception e) {
			System.out.println(e);
			return false;
		}
	}

}
