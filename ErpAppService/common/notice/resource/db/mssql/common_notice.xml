<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls> 
	<sql group="app_workflow_notice">
	<!-- 获取工作流消息 -->
	<i id="query_notice">
		<![CDATA[
			select
			      case n.iNoticeType
			      when 1 then '预警消息'
			      when 2 then '协同消息'
			      when 3 then '业务消息'
			      when 4 then '个人消息'
			      when 5 then '系统通知'
			      else '未知类型消息' end as noticeType,
			      n.cName as title,
			      n.cDesc as content,
			      n.cToUserGUID cguid	
			from PT_Notice n
			left join AOS_RMS_USER u on n.corgnid = u.cOrgnID and n.cFromUserGUID = u.cGUID
			left join AOS_RMS_USER us on n.corgnid = us.cOrgnID and n.cToUserGUID = us.cGUID
			where n.dCreateDate >= {beginTime} 
				and n.dCreateDate < {endTime}
				and n.dEndDate is null
		]]>
	</i>
	
	</sql>
</sqls>
