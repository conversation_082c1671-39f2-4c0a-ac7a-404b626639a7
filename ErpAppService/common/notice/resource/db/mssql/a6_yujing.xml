<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls> 
	<sql group="A6app_alarm">
		<!-- 负库存预警 -->
	 <i id="fukucun">
			<![CDATA[
				SELECT top 1 cStoreName '仓库名称'
					,cMatCode 
					,cMatName 
					,iQty '库存量'
				FROM (
					SELECT cms.cName AS cStoreName
						,cm.cName AS cMatName
						,cm.cCode AS cMatCode
						,SUM(st.iCurQty) AS iQty
					FROM ST_CurrentStock st
					LEFT JOIN cm_StoreHouse cms ON cms.cGUID = st.cStoreGUID
					LEFT JOIN CM_Material cm ON cm.cGUID = st.cMatGUID
					GROUP BY cms.cCode
						,cms.cName
						,cm.cName
						,cm.cCode
					HAVING sum(st.iCurQty) < 0
					) kkkk
				
			]]>
		</i>
		<i id="kehuyue">
		<!-- 客户余额预警 -->
			<![CDATA[
								SELECT top 1  cacctcode '科目编码'
					,cacctname '科目名称'
					,ccustcode 
					,ccustname,
					 convert(nvarchar, cast(round(iendamt ,2) as money),1) as iendamt
				FROM (
					SELECT acc.ccode cacctcode
						,acc.cname cacctname
						,cus.ccode ccustcode
						,cus.cname ccustname
						,ISNULL(ini.iinitamt, 0) + (isNull(hap.happenamt, 0) * acc.ibalancedir) iendamt
					FROM (
						SELECT ab.cacctguid
							,ab.ccustguid
							,ab.iinitamt
						FROM gl_assistbook ab
						WHERE ab.iyear = (
								SELECT iyear
								FROM co_sysinit
								WHERE cSubSysCode = 'GL'
								)
							AND ab.imonth = (
								SELECT imonth
								FROM co_sysinit
								WHERE cSubSysCode = 'GL'
								)
						) ini
					FULL JOIN (
						SELECT vl.cacctguid
							,vl.ccustguid
							,SUM(idebitamt - icreditamt) happenamt
						FROM gl_voucherline vl
						LEFT JOIN gl_voucher v ON vl.cvouguid = v.cguid
						WHERE v.iyear > (
								SELECT iyear
								FROM co_sysinit
								WHERE cSubSysCode = 'GL'
								)
							OR (
								v.iyear = (
									SELECT iyear
									FROM co_sysinit
									WHERE cSubSysCode = 'GL'
									)
								OR v.imonth >= (
									SELECT imonth
									FROM co_sysinit
									WHERE cSubSysCode = 'GL'
									)
								)
						GROUP BY vl.cacctguid
							,vl.ccustguid
						) hap ON ini.cacctguid = hap.cacctguid
						AND ini.ccustguid = hap.ccustguid
					LEFT JOIN gl_account acc ON ISNULL(ini.cacctguid, hap.cacctguid) = acc.cguid
					LEFT JOIN CM_Customer cus ON ISNULL(ini.ccustguid, hap.ccustguid) = cus.cguid
					WHERE acc.cname = '输入科目名称'
						AND cus.cname = '输入客户名称'
						AND ISNULL(ini.iinitamt, 0) + (isNull(hap.happenamt, 0) * acc.ibalancedir) > 30000
					) OUT

			]]>
		</i>
		
	 <i id="yewuyuanxiaoshoue">  <!-- 业务员销售额预警 -->
			<![CDATA[
				SELECT  
				 convert(nvarchar, cast(round(dvalue ,2) as money),1) as dvalue
					FROM (
						SELECT 
							sum(isnull(saline.itotal, 0)) AS dvalue
						FROM sa_invoiceline saline
							,sa_invoice sa
							,cm_employee emp
							,cm_department dept
						WHERE sa.cguid = saline.cheadguid
							AND sa.cempguid = emp.cguid
							AND emp.cdeptguid = dept.cguid
							AND sa.iauditstatus = 'checked'
							AND sa.cinvalider IS NULL
							AND emp.cname IS NOT NULL
							AND convert(VARCHAR(10), sa.ddate, 23) = convert(VARCHAR(10), getdate(), 23)
						HAVING sum(isnull(saline.iamt, 0)) != 0
						) e
			]]>
		</i>
		
		
		 <i id="xiaoshoumaoli">  <!-- 销售毛利预警 -->
			<![CDATA[
				select 
				      convert(nvarchar, cast(round(dvalue ,2) as money),1) as dvalue
					  from (select 
					               sum(sal.iamt - isnull(m.irefprice, 0) * sal.iunitqty) as dvalue
					          from sa_invoiceline sal
					          left join sa_invoice sa
					            on sal.cheadguid = sa.cguid
					          left join cm_material m
					            on m.cguid = sal.cmatguid
					          left join cm_customer d
					            on d.cguid = sa.ccustguid
					         where cinvalider is null
					           and convert(varchar(10), sa.ddate, 23) =convert(varchar(10), getdate(), 23)
					      ) e

			]]>
		</i>
		
		
		
		 <i id="baozhiqi">  <!-- 保质期预警 -->
			<![CDATA[
				SELECT  top 1  cMatGUID
							,cStorName '仓库'
							,cMatCode 
							,cMatName 
							,cBatchGUID '批次'
							,cStorGUID
							,iUnitQTY '库存量'
							,dOverDate '到数'
							,GQDAY '到期日期'
							,SYDAY '剩余天数'
						FROM (
							SELECT CM_Material.cGUID cMatGUID
								,CM_Material.cMatName
								,CM_Material.iPrecision
								,CM_Material.cCode cMatCode
								,ST_stkRecordLine.cBatchGUID
								,CM_Storehouse.cGUID cStorGUID
								,CM_Storehouse.cCode
								,CM_Storehouse.cName cStorName
								,sum(CASE 
										WHEN ST_StkRecord.iRSFlag = 1
											THEN isnull(ST_stkRecordLine.iUnitQTY, 0)
										WHEN ST_StkRecord.iRSFlag = 0
											THEN - 1 * isnull(ST_stkRecordLine.iUnitQTY, 0)
										END) iUnitQTY
								,max(convert(CHAR(10), ST_stkRecordLine.dOverDate, 120)) dOverDate
								,CASE 
									WHEN (DATEDIFF(d, GETDATE(), max(ST_stkRecordLine.dOverDate))) > 0
										THEN (DATEDIFF(d, GETDATE(), max(ST_stkRecordLine.dOverDate)))
									ELSE NULL
									END SYDAY
								,CASE 
									WHEN (DATEDIFF(d, GETDATE(), max(ST_stkRecordLine.dOverDate))) <= 0
										THEN cast((DATEDIFF(d, max(ST_stkRecordLine.dOverDate), GETDATE())) AS CHAR)
									ELSE NULL
									END GQDAY
							FROM ST_StkRecord
							INNER JOIN ST_StkRecordLine ON ST_StkRecordLine.cHeadGUID = ST_StkRecord.cGUID
							INNER JOIN CM_Material ON ST_stkRecordLine.cMatGUID = CM_Material.cGUID
							INNER JOIN CM_Storehouse ON ST_StkRecordLine.cStoreGUID = CM_Storehouse.cGUID
							WHERE CM_Material.iGuaranteeFlag = '1'
								AND (
									ST_StkRecord.iInitFlag = '0'
									AND ST_StkRecord.cBillType != '091'
									OR ST_StkRecord.iInitFlag = '1'
									AND ST_StkRecord.cBillType = '030'
									)
							GROUP BY CM_Material.cGUID
								,CM_Material.cMatName
								,CM_Material.cCode
								,CM_Material.iPrecision
								,ST_stkRecordLine.cBatchGUID
								,CM_Storehouse.cGUID
								,CM_Storehouse.cCode
								,CM_Storehouse.cName
							HAVING (
									(DATEDIFF(d, GETDATE(), max(ST_stkRecordLine.dOverDate))) <= (select iNumber_1_value from AOS_EW_Ordered where cSqlGUID='121229214577485853')
									AND (DATEDIFF(d, GETDATE(), max(ST_stkRecordLine.dOverDate))) >= 0
									)
							)
						
						kkkk

			]]>
		</i>
	</sql>
</sqls>
