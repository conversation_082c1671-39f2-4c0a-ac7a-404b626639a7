<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	
	<sql group="mobile_common_daiban_noright_A3">
		<i id="FI" desp="A3财务列表查询">
			<![CDATA[
				select count (t.cvoucode) as num
			    from ( select pay.cvoucode
				       from AP_Payment pay
					   left join CM_Employee emp on pay.cEmpGUID = emp.cGUID
				       left join CM_Department depart on emp.cDeptGUID = depart.cGUID
				       left join CM_Supplier sup on pay.cSupGUID=sup.cguid
				       left join pl_user creator ON pay.cCreatorGUID = creator.cPKID
				       where pay.iInitFlag = 0 
				       and pay.iAuditStatus = 'saved'
				       and pay.cInvGUID is null
				       and (('*' = {keyword:'*'} or convert(varchar(20), pay.dCreatorTime, 112) like '%{keyword}%') or $like(creator.crealname,keyword) or ('*' = {keyword:'*'} or '付款单' like '%{keyword}%'))
				       and pay.cFlag = 'AP'
						) t
			]]>
		</i>
		
		<i id="PU" desp="A3采购列表查询">
			<![CDATA[
				select count(t.cbillcode) as num
				from ( select st.cbillcode
				       from ST_StkRecord st
				       inner join ( select stline.cheadguid,sum(stline.iTotal) iTotal from ST_StkRecordLine stline group by stline.cheadguid ) iStTotal on iStTotal.cHeadGUID=st.cguid
                       left join CM_Employee emp on st.cEmpGUID = emp.cGUID
				       left join CM_Department depart on emp.cDeptGUID = depart.cGUID
				       left join pl_user creator ON st.cCreatorGUID = creator.cPKID	
				       where st.iAuditStatus = 'saved'
                       and (('*' = {keyword:'*'} or convert(varchar(20), st.dCreatorTime, 112) like '%{keyword}%') or $like(creator.crealname,keyword) or ('*' = {keyword:'*'} or ('进货单' like '%{keyword}%')))
                       and st.cBillType = '010'
                ) t
			]]>
		</i>
		
		<i id="SA" desp="A3采购列表查询">
			<![CDATA[
				select count(t.cbillcode) as num from 
				(select st.cbillcode
				from ST_StkRecord st
				inner join ( select stline.cheadguid,sum(stline.iTotal) iTotal from ST_StkRecordLine stline group by stline.cheadguid ) iStTotal on iStTotal.cHeadGUID=st.cguid
                left join CM_Employee emp on st.cEmpGUID = emp.cGUID
				left join CM_Department depart on emp.cDeptGUID = depart.cGUID
				left join pl_user creator ON st.cCreatorGUID = creator.cPKID	
				left join CM_Customer cust ON st.cCustGUID = cust.cGUID	
				where st.iAuditStatus = 'saved'
                and (('*' = {keyword:'*'} or convert(varchar(20), st.dCreatorTime, 112) like '%{keyword}%') or $like(creator.crealname,keyword) or ('*' = {keyword:'*'} or ('销货单' like '%{keyword}%')))
                and st.cBillType = '020'
                union all
				select st.cbillcode
				from ST_StkRecord st
				inner join ( select stline.cheadguid,sum(stline.iTotal) iTotal from ST_StkRecordLine stline group by stline.cheadguid ) iStTotal on iStTotal.cHeadGUID=st.cguid
                left join CM_Employee emp on st.cEmpGUID = emp.cGUID
				left join CM_Department depart on emp.cDeptGUID = depart.cGUID
				left join pl_user creator ON st.cCreatorGUID = creator.cPKID	
				left join CM_Customer cust ON st.cCustGUID = cust.cGUID	
				where st.iAuditStatus = 'saved'
                and (('*' = {keyword:'*'} or convert(varchar(20), st.dCreatorTime, 112) like '%{keyword}%') or $like(creator.crealname,keyword) or ('*' = {keyword:'*'} or ('销售退货单' like '%{keyword}%')))
                and st.cBillType = '022'
                ) t
			]]>
		</i>
				
		<i id="ST" desp="A3库存列表查询">
			<![CDATA[
				select count(t.cbillcode) as num from 
				(select trans.cBillCode
				from ST_StkTrans trans
				inner join ( select transline.cheadguid,sum(transline.iQTY) iQTY,sum(transline.iOutAMT) iOutAMT,sum(transline.iInAMT) iInAMT from ST_StkTransLine transline group by transline.cheadguid ) iTransTotal on iTransTotal.cHeadGUID=trans.cguid
				left join CM_storehouse outstore on trans.cOutStoreGUID=outstore.cGUID
			    left join CM_storehouse instore on trans.cInStoreGUID=instore.cGUID
			    left join pl_user creator ON trans.cCreatorGUID = creator.cPKID
			    where trans.iAuditStatus = 'saved'
                and (('*' = {keyword:'*'} or convert(varchar(20), trans.dCreatorTime, 112) like '%{keyword}%') or $like(creator.crealname,keyword) or ('*' = {keyword:'*'} or ('调拨单' like '%{keyword}%')))	
			    ) t
			]]>
		</i>
		
		<i id="EM" desp="A3EM">
				<![CDATA[
				select 0 as num
				]]>
			</i>
			
		<i id="OA" desp="A3OA">
			<![CDATA[
			select 0 as num
			]]>
		</i>
			
		<i id="MA" desp="A3MA">
				<![CDATA[
				select 0 as num
				]]>
			</i>		
	</sql>
	
	
	<sql group="mobile_common_daiban_noright_A6">
	
		<i id="EM" desp="A6待办报销单列表查询">
			<![CDATA[
			
				select sum(em.num) as num
				from
				(
					select count(1) num
					from OA_LoanBill loan			
					where 'sg'={sgspqxLoan:''}
					and loan.cStatusEnumGUID = 'saved'			
					
					union all
					select count(1) num
					from OA_ExpenseAccountBill eab
					where 'sg'={sgspqxEm:''}
					and eab.cStatusEnumGUID = 'saved'									
				)em
			
			
			]]>
		</i>
		
		<i id="OA" desp="A6协同办公列表查询">
			<![CDATA[				
				select 0 as num
			]]>
		</i>
		
		<i id="ST" desp="A6待办库存调拨单列表查询">
			<![CDATA[
			
			  select sum(sao.num) as num  from 
  	            (
  	              select 
	            	  count(1) as num
			          from ST_StkTrans st	
			          where st.iAuditStatus = 'saved' 
			          and ({sgStTrans:'false'}='true')	     					   
		        )sao
			
			
			]]>
		</i>
		
		<i id="SA" desp="A6待办销售列表查询">
			<![CDATA[
			
			select sum(sao.num) as num from 
  	            (select 
            	  count(1) as num
		         from SA_Order st		
		         where st.iAuditStatus = 'saved' 
		         and ({sgSaOrder:'false'}='true')	 	
				union all				
					select count(1) as num
			         from SA_Invoice st    	               
			         where st.iAuditStatus = 'saved' 			         
			         and ({sgSaInvoice:'false'}='true')
			         and st.csystype='SA'
			         and st.iInitFlag='0'	
			    union all				
					select count(1) as num
			         from ST_StkRecord st    	               
			         where st.iAuditStatus = 'saved' 
			               and st.cBillType = '212'
					       and st.iInitFlag = '0'
					       and st.iRSFlag = '0'			         
			               and ({sgSaRetail:'false'}='true')			                 
		        )sao			
			]]>
		</i>
		
		<i id="PU" desp="A6待办采购列表查询">
			<![CDATA[			
			  select sum(pu.num) as num from 
  	            (select count(1)  num
  	               from PU_Order po  				
  	            where po.iAuditStatus='saved'
  	           and ({sgPuOrder:'false'}='true') 
  	           union all
  	           select count(1)  num
  	               from PU_Planhead po  				
  	            where po.iAuditStatus='saved'
  	           and ({sgPuPlan:'false'}='true') 	           
			)pu
			
			]]>
		</i>
		
		<i id="FI" desp="A6待办付款单列表查询(应付)">
			<![CDATA[		
			  SELECT sum(num) as num 
			  FROM(
				select count(1)  num
			        from AP_Payment st
	                where st.iAuditStatus = 'saved' 
					and ({sgAP:'false'}='true')
					and st.cflag='AP'				
				union all
           			select count(1) num
				        from CA_OtherPayment st		               
		                where st.iAuditStatus = 'saved' 
				        and ({sgCA:'false'}='true')	
				        and  st.cflag='P'
		       )m			
			]]>
		</i>
		
		<i id="MA" desp="A6MA">
			<![CDATA[
				select 0 as num
			]]>
		</i>
	</sql>
	
</sqls>