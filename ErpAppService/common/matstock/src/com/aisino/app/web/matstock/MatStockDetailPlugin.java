package com.aisino.app.web.matstock;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.SessUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class MatStockDetailPlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		DbSvr dbSvr = DbSvr.getDbService(null);
		String cguid=bus.getString("cguid"); 
		List<Map> list = dbSvr.queryIdForList("mobile_matstock_detail.getmatdetail",bus);
		Map map = new HashMap();
		map.put("matstockdetail", list);
		form.setReturn(map);

	}
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		
	}
}
