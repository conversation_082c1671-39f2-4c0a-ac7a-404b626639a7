package com.aisino.app.web.dotasklist.common.reference;

import java.io.File;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.aisino.platform.core.ProductInfo;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.SessUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetApPaymentPeriodPlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		DbSvr db = DbSvr.getDbService(null);
		Map map = new HashMap();
		String busyear = bus.getString("year");
		String year = db.getStringResult("select iInitYear from co_sysinit where csubsyscode='AR'");
		String iperiod = db.getStringResult("select iInitMonth from co_sysinit where csubsyscode='AR'");
		if(year==null){
			map.put("error", "未启用应付管理模块");
			form.setReturn(map);
		}else{
			String monthinitarr[] = {"1","2","3","4","5","6","7","8","9","10","11","12"};
			if(year.equals(busyear)){
				String montharr[] = Arrays.copyOfRange(monthinitarr,Integer.parseInt(iperiod)-1,12);				
				map.put("rtn", montharr);
			}else{
				map.put("rtn", monthinitarr);
			}
			form.setReturn(map);			
		}
	}
	
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		
	}
	
}
