package com.aisino.app.web.dotasklist.common.util;

import java.io.File;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.aisino.platform.core.ProductInfo;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.SessUtil;

public class CommonUtil {	
	public List<Map> getImgPath(List<Map> list) {
		HttpServletRequest request = SessUtil.getRequest();
		String requestUrl = request.getScheme() //当前链接使用的协议
			    +"://" + request.getServerName()//服务器地址 
			    + ":" + request.getServerPort() //端口号 
			    + request.getContextPath(); //应用名称
		String path = SessUtil.getSession().getServletContext().getRealPath("/");
		String imgPath = null;
		if(!CollectionUtil.isEmpty(list)){
			for(int i=0;i<list.size();i++){
				imgPath = getClass().getResource("/").getFile().toString();  
				File f = new File(ProductInfo.getWebRealPath()+"/imgUploads/"+list.get(i).get("cmatguid")+".jpg");
				if(f.exists()){
					list.get(i).put("imgPath", requestUrl+"/imgUploads/"+list.get(i).get("cmatguid")+".jpg");
				}else{
					list.get(i).put("imgPath", "../../../../img/matstock.png");
				}

			}
		}
		return list;
	}

}
