package com.aisino.app.web.dotasklist.common.util;

public class PrecDealUtil {	
	/**
	 * 精度处理
	 * <AUTHOR>
	 * @date sep 03.2018
	 */
	public static String precDeal(int prec,String tempprice ,String origiprice) {
		String showprice;
		if(tempprice.contains(".")&&!"0E-9".equals(origiprice)){
			String be=tempprice.split("\\.")[0];
			String af=tempprice.split("\\.")[1];
			String mk=origiprice.split("\\.")[1];
			if(prec!=af.length()){
				showprice=be+"."+mk.substring(0,prec);
				return showprice;
			}else{
				return tempprice;
			}
		}else {
			return "0";
		}
	}


}
