package com.aisino.app.web.dotasklist.common.reference;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetReferEmployeePlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {		
		DbSvr dbSvr = DbSvr.getDbService(null);
		Map map = new HashMap();
		String cdeptguid=bus.getString("cdeptguid");
		if("".equals(cdeptguid)){ //为空特殊处理一下部门
			bus.put("cdeptguid", null);
		}
		List<Map> list = dbSvr.queryIdForList("mobile_common_reference_sql.referEmpList", bus);
		map.put("rtn", list);
	    form.setReturn(map);
	}
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		
	}
}
