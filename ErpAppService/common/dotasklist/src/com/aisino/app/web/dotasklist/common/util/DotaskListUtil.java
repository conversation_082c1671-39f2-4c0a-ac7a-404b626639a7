package com.aisino.app.web.dotasklist.common.util;


import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DotaskListUtil {	
	/**
	 * <AUTHOR>
	 * @date sep 08.2018
	 */
	//定义单据列表每页显示条数
	public static final int pagenum = 100;
	public static String getPastDate(int past) {
		//获取过去第几天的日期
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - past);
		Date today = calendar.getTime();
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		String result = format.format(today);
		return result;
	}
}
