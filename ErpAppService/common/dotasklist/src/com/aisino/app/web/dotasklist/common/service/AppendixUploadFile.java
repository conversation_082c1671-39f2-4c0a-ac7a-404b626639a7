package com.aisino.app.web.dotasklist.common.service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang.StringUtils;

import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.core.Guid;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.veng.json.dauglas.JSONException;
import com.aisino.platform.veng.json.dauglas.JSONObject;

/**
 * 单据附件上传的servlet
 */
public class AppendixUploadFile extends HttpServlet {

	@SuppressWarnings({ "unchecked", "rawtypes" })
	protected void service(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		
		String cGroupGuid = null;//上传的文件对应的单据id
		String userGuid = null;
		String accountId = null;
		String classPath = URLDecoder.decode(this.getClass().getResource("/").getPath()+"upload","utf-8");// 上传路径
		File dirFile = new File(classPath);
		// 给上传的文件设一个最大值，这里是不得超过25MB
		int maxSize = 25 * 1024 * 1024;
		// 创建工厂对象和文件上传对象
		DiskFileItemFactory factory = new DiskFileItemFactory();
		ServletFileUpload upload = new ServletFileUpload(factory);
		upload.setFileSizeMax(maxSize);// 单个文件大小
		upload.setSizeMax(maxSize);// 最多上传25mb的文件
		// 如果文件夹不存在则创建
		if (!dirFile.exists()) {
			dirFile.mkdirs();
		}
		// 解析上传请求
		List<FileItem> formItems = null;
		//使用map接收字段
		Map<String,String> map = new HashMap<String,String>(); 
		//用来接收aos_file_files表中插入数据的list
		List<Map> list = new ArrayList<Map>();
		String date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
		try {
			byte[] buffer = new byte[1024];
			formItems = upload.parseRequest(request);
			/* 	请求示例
			 * _parts:
				    [ [ 'cGroupGuid', '' ],
				      [ 'userGuid', '1' ],
				      [ 'accountId', '001' ],
				      [ 'file',
				        { uri: 'file:///storage/emulated/0/Pictures:Screenshot_2018-09-25-14-23-37.png',
				          type: 'multipart/form-data',
				          name: 'Screenshot_2018-09-25-14-23-37.png' } ],
				      [ 'file',
				        { uri: 'file:///storage/emulated/0/Pictures:Screenshot_2018-09-18-13-02-22.png',
				          type: 'multipart/form-data',
				          name: 'Screenshot_2018-09-18-13-02-22.png' } ],
			*/				          
			if (formItems != null && formItems.size() > 0) {
				// 迭代表单数据
				for (FileItem item : formItems) {					
					if (!item.isFormField()) {// 表单是文件类型
						String cguid = Guid.g();
						String fileName = new File(item.getName()).getName();
						//String filePath = path + File.separator + fileName;
						String cFileName = cguid+"_"+fileName;
						String cFileType = fileName.substring(fileName.lastIndexOf(".")+1);
						InputStream in = item.getInputStream();
						int len = -1;
						//创建FileWriter对象，用来写入字符流
			            File f = new File(classPath+File.separator+cFileName);
			            // 将缓冲对文件的输出
			            OutputStream bw = new FileOutputStream(f); 
						// 读入需要下载的文件的内容，打包到zip文件
						while ((len = in.read(buffer)) != -1) {
							bw.write(buffer, 0, len);
						}
						Map fileData = new HashMap();
						fileData.put("cGuid", cguid);
						fileData.put("cFileName", cFileName);
						fileData.put("cFileUrl", "upload");
						fileData.put("cFileType", cFileType);
						fileData.put("cUploadDay", date);
						fileData.put("cTempFlag", "0");//新增的附件，都作为临时文件存储，单据保存成功后会统一更新为永久文件。
						fileData.put("cGroupGuid", cGroupGuid);
						fileData.put("cTimestamp", Guid.g());
						fileData.put("cFileRealName", fileName);
						fileData.put("cUserID", userGuid);
						list.add(fileData);
			            // 刷新该流的缓冲
			            bw.flush(); 
			            bw.close();
			            in.close();
					}else{
						map.put(item.getFieldName(), item.getString("utf-8"));
						if("cgroupguid".equals(item.getFieldName())){
							cGroupGuid = item.getString("utf-8");
							if(StringUtils.isBlank(cGroupGuid)){
								cGroupGuid=Guid.g();
							}
						}else if("userguid".equals(item.getFieldName())){
							userGuid = item.getString("utf-8");
						}else if("accountid".equals(item.getFieldName())){
							accountId = item.getString("utf-8");
						}
					}
				}
			}
		} catch (FileUploadException e2) {
			e2.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println(map.toString());
		DbSvr db = DbSvr.getDbService(accountId);
		db.insertAll("aos_file_files", list);
		db.testCommit();
		PrintWriter pw = response.getWriter();
		JSONObject json = new JSONObject();
		try {
			json.put("msg", "success");
			json.put("cgroupguid",cGroupGuid);
		} catch (JSONException e) {
			e.printStackTrace();
		}
		pw.write(json.toString());
		pw.close();
	
	}

}