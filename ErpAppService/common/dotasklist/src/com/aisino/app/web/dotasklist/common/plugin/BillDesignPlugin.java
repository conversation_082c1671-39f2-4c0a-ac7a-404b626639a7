package com.aisino.app.web.dotasklist.common.plugin;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.aisino.aos.bill.common.BillUtils;
import com.aisino.aos.bill.vo.BillVo;
import com.aisino.aos.mobile.vo.ApproveBillField;
import com.aisino.aos.preference.common.Prefer;
import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.Annotations;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.db.SqlInfo;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.DateUtil;
import com.aisino.platform.util.StringUtil;


/**
 * 移动端单据页面展示
 * 
 * <AUTHOR>
 * @version 2.0
 */

public class BillDesignPlugin extends SqlPlugin {

	public static Map getBill(String cBusinessCode,String cguid) {

		Map m = new HashMap();	   
		
		BillVo billVo = BillUtils.getBillVoByCode(cBusinessCode);
		String cFormId = billVo.getFormId();
		String cMainEntity = billVo.getMainEntity();
		String cLineEntity = billVo.getLineEntity();
		String billName = billVo.getName();
		List<ApproveBillField> mainFields = ApproveBillAppUtilPlugin.getApproveBillFieldsByEntity(cMainEntity,cFormId);
		List<ApproveBillField> lineFields = ApproveBillAppUtilPlugin.getApproveBillFieldsByEntity(cLineEntity,cFormId);
	   
		mainFields=ProcessFieldsQx(cMainEntity,mainFields,cBusinessCode);  //过滤字段权限，单据设计中的字段若没有字段权限，则将其remove掉
		lineFields=ProcessFieldsQx(cLineEntity,lineFields,cBusinessCode);		

		List<Map> maindata = null;
		List<Map> detaildata = null;
		if(StringUtil.isNotBlank(cMainEntity))
			maindata = getMainData(cMainEntity, cguid, mainFields);
		if(StringUtil.isNotBlank(cLineEntity))
			detaildata = getDetailData(cLineEntity, cguid, lineFields);
		m.put("billname", billName);
		m.put("maindata", maindata);
		m.put("detaildata", detaildata);
/*		m.put("mainFields", mainFields);
		m.put("lineFields", lineFields);*/
	    return m;
	}
	
	
	//主表（1.0中特殊处理的字段覆盖单据设计中的字段）
	public static List<Map> mainProcess(List<Map> main,List<Map> mainTemp) {
		if(CollectionUtil.isEmpty(main))
			return null;
		if(CollectionUtil.isEmpty(mainTemp))
			return main;
		Map m =mainTemp.get(0);
		Set<String> tempkeys =m.keySet();
		for(String tempkey : tempkeys){		  			
			for(Map md : main){
				if(tempkey.equalsIgnoreCase(md.get("code").toString())){
					md.put("value",m.get(tempkey));
				    break;
				} 
			}
		}			
		return main;
	}
	
	
	//子表（1.0中特殊处理的字段覆盖单据设计中的字段）
	public static List<Map> detailProcess(List<Map> detail,List<Map> detailTemp) {
		if(CollectionUtil.isEmpty(detail))
			return null;
		if(CollectionUtil.isEmpty(detailTemp))
			return detail;
		for(Map mapdesign:detail){
			String cguid1= (String) mapdesign.get("linecguid");
			List<Map> line1=(List<Map>) mapdesign.get("line");
			
			for(Map maptemp: detailTemp){
				String cguid2= (String) maptemp.get("cguid");
				if(cguid1.equalsIgnoreCase(cguid2)){
					maptemp.remove("cguid"); //使查出的sql只包含特殊处理的字段
	                Set<String> tempkeys =maptemp.keySet();
					for(String tempkey : tempkeys){
						for(Map md : line1){
							if(tempkey.equalsIgnoreCase(md.get("code").toString())){
								md.put("value",maptemp.get(tempkey));
							    break;
							} 
						}
					}						
					break;
				}
			}
		}
		return detail;
	}
	
	//主子表数据格式化(该方法被弃用，因为返回值的顺序又乱了)
	@SuppressWarnings("unchecked")
	public static List<Map> billFormat(String part,List<Map> list) {
		List<Map> data = new ArrayList<Map>();
		
       if("main".equalsIgnoreCase(part)){
    	   Map m = new HashMap();	
           for(Map map:list){   		   
    		   m.put(map.get("name"), map.get("value"));
    	   }
    	   data.add(m);
       }

       if("detail".equalsIgnoreCase(part)){
    	   for(Map map:list){
    		   List<Map> line=(List<Map>) map.get("line");
    		   Map m = new HashMap();	
    		   for(Map p:line){   		   
        		   m.put(p.get("name"), p.get("value"));
        	   }
        	   data.add(m);
    	   }
    	   
       }
		return data;
	}

private static List<Map> getMainData(String cEntity, String cguid, List<ApproveBillField> fields) {
	DbSvr db = DbSvr.getDbService(null);
	Map bus = new HashMap();
	bus.put("billId", cguid);
	StringBuilder billHtml = new StringBuilder();
	StringBuilder sql = new StringBuilder();
	sql.append("select s.cguid @field[s.cguid] ").append("from ").append(cEntity).append(" s @table[").append(cEntity).append(" s] where #equal(s.cguid,billId)").append(" @where[and]");
	bus.put("approveBillFields", fields);
	Annotations.set(bus, new ApproveBillProcessorApp());
	List<Map> list = db.executeQuery(new SqlInfo(sql.toString()).getEso(bus));
	if(CollectionUtil.isEmpty(list))
		return null;
	List<Map> result = new ArrayList<Map>();
	for (Map m : list) {
		for (ApproveBillField field : fields) {		
			String fieldValue = getFieldValue(field, m);
     		Map map = new HashMap();
			map.put("code",field.getCcode());
			map.put("name", field.getCfieldname());
			map.put("value", fieldValue);		
			result.add(map);
			}
		}
	return result;
}

private static String getFieldValue(ApproveBillField field, Map m) {
	String cCode = field.getCcode();
	String fieldValue=null;
	String[] cCodes = cCode.split("\\.");
	if(cCodes.length == 2 || cCodes.length == 3){
		fieldValue = CollectionUtil.getStringFromMap(m, cCodes[0] + cCodes[1]);
	}
	if(cCodes.length == 4){
		fieldValue = CollectionUtil.getStringFromMap(m, cCodes[0] + cCodes[1]+cCodes[2] + cCodes[3]);
	}
	if (StringUtil.isBlank(fieldValue))
		return null;
	String cDataType = field.getCdatatype();
	if (StringUtil.isBlank(cDataType) || StringUtil.equals("string", cDataType))
		return fieldValue.toString();

	// 处理数字类型数据的精度
	if (StringUtil.equals("number", cDataType)) {
		fieldValue = dealNumberDataPre(field, fieldValue);
	}

	// 处理日期类型的数据
	if (StringUtil.equals("date", cDataType) && isDateFormat(fieldValue))
		fieldValue = DateUtil.date2Str(DateUtil.str2Time(fieldValue), "yyyy-MM-dd");

	return fieldValue;
}

private static boolean isDateFormat(String fieldValue) {
	return fieldValue.length() >= 10 && DateUtil.isRegular(fieldValue.substring(0, 10));
}

private static String dealNumberDataPre(ApproveBillField field, String fieldValue) {
	String cPrecision = field.getCprecision();
	if (StringUtil.isBlank(cPrecision)){
		if("0E-9".equalsIgnoreCase(fieldValue)){
			return "0.0";
		}else{
			return fieldValue;
		}
		
	}		
	// 如果fieldValue不是数字则不进行精度处理
	if (!fieldValue.matches("-?\\d+\\.?\\d*"))
		return fieldValue;
	int pre = -1;
	if (cPrecision.matches("[0-9]+"))
		pre = Integer.parseInt(cPrecision);
	else if (StringUtil.isNotBlank(Prefer.get(cPrecision)) && Prefer.get(cPrecision).matches("[0-9]+"))
		pre = Integer.parseInt(Prefer.get(cPrecision));
	if (pre == -1)
		return fieldValue;
	return new BigDecimal(fieldValue).setScale(pre, BigDecimal.ROUND_HALF_UP).toString();
}

private static List<Map> getDetailData(String cLineEntity, String cguid, List<ApproveBillField> fields) {
	DbSvr db = DbSvr.getDbService(null);
	List<Map> list =new ArrayList<Map>();
	Map bus = new HashMap();
	bus.put("key", cguid);
	StringBuilder lineBillHtml = new StringBuilder();

	try{
		String sql = "select cguid from " + cLineEntity + " where cHeadGuid = ?";
		list= db.getListResult(sql, bus.get("key"));
	}catch(Exception e){	
		if(e.toString().contains("列名 'cHeadGuid' 无效")){  //付款单主子表关联关系不是cheadguid			
			String sqlpayment = "select cguid from " + cLineEntity + " where cPaymentGUID = ?";
			list= db.getListResult(sqlpayment, bus.get("key"));
		}
	}
	
	List<Map> result = new ArrayList<Map>();
	if(list!=null){
		for (Map m : list) {
			Map map = new HashMap();
			map.put("line", getMainData(cLineEntity, m.get("cguid").toString(), fields));
			map.put("linecguid", m.get("cguid").toString());
			result.add(map);
		}
	}
	return result;
}


private static List<ApproveBillField> ProcessFieldsQx(String cEntity, List<ApproveBillField> fields,String cBusinessCode) {
	DbSvr db = DbSvr.getDbService(null);
	String userid = SessionHelper.getCurrentUserId();
	List<Map> qxFields=db.queryIdForList("mobile_common.checkfieldqx", new Object[] {userid,cEntity,cBusinessCode});
	List<ApproveBillField> fieldsls =new ArrayList<ApproveBillField>();
	if(qxFields!=null){
		for (ApproveBillField field : fields) {
			String code = field.getCcode();
			for(Map qx: qxFields){
				String qxfield =qx.get("cfieldguid")!=null? qx.get("cfieldguid").toString(): "";
				if(code.equalsIgnoreCase(cEntity+"."+qxfield)){
					fieldsls.add(field);
					break;
				}
			}
		    
		}
		fields.removeAll(fieldsls);
	}
	return fields;
}

}
