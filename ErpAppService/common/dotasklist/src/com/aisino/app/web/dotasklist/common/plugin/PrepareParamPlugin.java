package com.aisino.app.web.dotasklist.common.plugin;


import java.util.ArrayList;
import java.util.List;

import com.aisino.platform.view.DataMsgBus;

public class PrepareParamPlugin {	
	/**
	 * <AUTHOR>
	 * @date sep 08.2018
	 */
	public void prepareReferParam(DataMsgBus bus,String billname) {
		if("saorder".equals(billname)){
			//销售CO_BusinessProcess的cSystemFlowGUID为00001
			bus.put("cSystemFlowGUID", "00001");
			bus.put("cBillType", "066");
			
			List bustypelist = new ArrayList();
			bustypelist.add("02001");
			bustypelist.add("s0002");
			bustypelist.add("s0003");
			bustypelist.add("s0004");
			bus.put("bustypelist", bustypelist);
		}
	}
}
