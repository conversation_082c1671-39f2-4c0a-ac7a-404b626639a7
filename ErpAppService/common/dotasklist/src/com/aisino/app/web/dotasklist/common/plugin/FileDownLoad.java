package com.aisino.app.web.dotasklist.common.plugin;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.PrintWriter;


import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


import com.aisino.platform.core.ProductInfo;

/**
 * 移动端下载文件
 * 
 * <AUTHOR>
 * @version 1.0
 */

public class FileDownLoad extends HttpServlet {

	@Override
	protected void service(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {

		String cFileUrl = request.getParameter("cFileUrl");

		String cFileName = new String(request.getParameter("cFileName")
				.getBytes("ISO8859-1"), "UTF-8");
		// 处理请求
		// 读取要下载的文件
		String dir = "";
		if (cFileUrl == null || "".equals(cFileUrl)) {
			error(response);
			return;
		} else {
			dir = cFileUrl.equals("upload") ? "\\WEB-INF\\classes\\" : "\\";
		}
		// 文件夹路径
		String path = ProductInfo.getWebRealPath() + dir + cFileUrl;
		File f = new File(path + "\\" + cFileName);
		try {
			if (f.exists()) {
				downLoad(f, request, response);
			} else if (cFileName != null && !"".equals(cFileName)) {
				// 若乱码则根据 文件名前缀查找文件
				String fileName = getFileRealName(cFileName, path);
				File f1 = new File(path + "\\" + fileName);
				if (f1.exists()) {
					downLoad(f1, request, response);
				} else {
					error(response);
				}
			} else {
				error(response);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 若乱码则根据前18位id获取文件名称
	 * 
	 * @param name
	 * @param path
	 * @return
	 */
	public String getFileRealName(String name, String path) {
		File fileDir = new File(path);
		File[] files = fileDir.listFiles();
		String fileName = "";
		for (int i = 0; i < files.length; i++) {
			if (files[i].getName().startsWith(name.substring(0, 19))) {
				fileName = files[i].getName();
				break;
			}
		}

		return fileName;
	}

	/**
	 * 下载文件
	 * 
	 * @param f
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	public void downLoad(File f, HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		FileInputStream fis = new FileInputStream(f);
		String realname = f.getName();
	
		// 解决中文文件名下载后乱码的问题
		String filename = new String(realname.substring(19).getBytes("UTF-8"),"ISO-8859-1");
		//URLEncoder.encode(realname.substring(19), "utf-8");
		byte[] b = new byte[fis.available()];
		fis.read(b);
		response.setCharacterEncoding("utf-8");
		//默认为下载   为图片 直接打开
		String openWay = "attachment";
		if(realname.toLowerCase().endsWith(".jpg")||
				realname.toLowerCase().endsWith(".gif")||
				realname.toLowerCase().endsWith(".png")||
				realname.toLowerCase().endsWith(".jpeg")||
				realname.toLowerCase().endsWith(".bmp")){
			openWay = "inline";
		}
			 response.setHeader("Content-Disposition",openWay+";filename="
			+ filename + "");
		// 获取响应报文输出流对象
		ServletOutputStream out = response.getOutputStream();
		// 输出
		out.write(b);
		out.flush();
		out.close();

	}

	/**
	 * 未找到文件 提示
	 * 
	 * @param response
	 * @throws IOException
	 */
	public void error(HttpServletResponse response) throws IOException {
		PrintWriter out = response.getWriter();
		out.print("<h5>下载失败，未找到该文件！</h5>");
		out.close();
	}

}
