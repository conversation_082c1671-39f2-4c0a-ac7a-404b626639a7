package com.aisino.app.web.dotasklist.common.reference;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.app.web.dotasklist.common.plugin.PrepareParamPlugin;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetReferCbustypePlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {	
		DbSvr dbSvr = DbSvr.getDbService(null);
		Map map = new HashMap();
		//传一些CO_BusinessProcess、cbilltype等参数
		PrepareParamPlugin param = new PrepareParamPlugin();
		param.prepareReferParam(bus, bus.getString("billname"));		
		List<Map> list = new ArrayList<Map>();
		if(bus.contain("canblank") && "y".equalsIgnoreCase(bus.getString("canblank"))){
			Map blank = new  HashMap();
			blank.put("code", "");
			blank.put("name", "选择业务类型");
			list.add(blank);
		}
		
		List<Map> l = dbSvr.queryIdForList("mobile_common_reference_sql.referCbustype", bus);	
		if(l != null && !l.isEmpty())
			list.addAll(l);
		map.put("rtn", list);
	    form.setReturn(map);
	}
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		
	}
	
	
}
