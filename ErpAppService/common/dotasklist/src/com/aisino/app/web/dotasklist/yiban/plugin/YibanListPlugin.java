package com.aisino.app.web.dotasklist.yiban.plugin;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import com.aisino.app.web.dotasklist.yiban.plugin.Com;
import com.aisino.platform.core.MS;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;


/**
 * <AUTHOR>
 * @desp 各模块的已办列表（各模块已办列表展现不同）
 * @date 20161208
 */

public class YibanListPlugin extends SqlPlugin implements FormCreateListener {

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		DbSvr dbSvr = DbSvr.getDbService(null);
		List<Map> list =new ArrayList<Map>();	//模块注册的服务，都将已办事项加到list中	          
		String module=bus.getString("module");	//module传值为"EM","OA","MA","FI","PU","SA","ST"	
		String today=getDate(bus.getString("keyword"), bus);
		String keyword = bus.getString("keyword"); //搜索关键字
		/* 2017-02-28 qcj 增加参数当前页数和每页条数 */
		String curpage=bus.getString("curpage");
		String pagenum=bus.getString("pagenum");
		
		MS m = new MS("App.YibanList."+module);  
	    m.doService("getlist",list,null,keyword,today,curpage,pagenum);	//doService第一个参数为getnum/getlist，第二个为list，第三个为listnum,第四个为keyword 
	    /* 2017-02-28 qcj 无需再排序
	    Com com =new Com();
		Collections.sort(list, com); 
		*/
	    Map map = new HashMap();		
		map.put("list", list);
		form.setReturn(map);
	}
	
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {

	}
	
	public static String getDate(String keyword, DataMsgBus bus){
      
    	String format= "[0-9]{4}[0-9]{2}[0-9]{2}";
    	if(!StringUtil.isBlank(keyword)){
    		boolean datea= Pattern.compile(format).matcher(keyword).matches();
	    	if(datea){  
	    		bus.put("keyword",null);
	        	return keyword.substring(0, 4)+"-"+keyword.substring(4, 6)+"-"+keyword.substring(6, 8);
	        }else{
	        	return bus.getString("ddate"); 
	        }}
    	 else
    		return bus.getString("ddate");
        
	 }
	

}
