package com.aisino.app.web.dotasklist.common.reference;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.aisino.platform.core.ProductInfo;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.SessUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetApPaymentYearPlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		DbSvr db = DbSvr.getDbService(null);
		Map map = new HashMap();
		List<Map> yearlist = db.getListResult("select iInitYear,iYear from co_sysinit where csubsyscode='AR'");
//		String  year = db.getStringResult("select iYear from co_sysinit where csubsyscode='AR'");
		if(yearlist == null){
			map.put("error", "未启用应收管理模块");
			form.setReturn(map);
		}else{
			int inityear = Integer.parseInt(CollectionUtil.getStringFromMap(yearlist.get(0), "iInitYear"));
			int year = Integer.parseInt(CollectionUtil.getStringFromMap(yearlist.get(0), "iYear"));
			List yearls = new ArrayList();
			for(int i=0; i< year-inityear+3; i++){
				yearls.add(i, inityear+i+"");
			}
			map.put("rtn", yearls);
		    form.setReturn(map);
		}
		
	}
	
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		
	}
	
}
