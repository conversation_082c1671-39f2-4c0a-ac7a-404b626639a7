package com.aisino.app.web.dotasklist.common.plugin;

import com.aisino.aos.mobile.vo.ApproveBillField;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import java.util.*;

/**
 * 移动端单据页面展示
 * 
 * <AUTHOR>
 * @version 2.0
 */

public class ApproveBillAppUtilPlugin{

	  public ApproveBillAppUtilPlugin()
	    {
	    }

	    public static List getApproveBillFieldsByEntity(String cEntity,String cFormID)
	    {
	        DbSvr db = DbSvr.getDbService(null);
	        List list = db.queryIdForList("mobile_appbill_setting_sql.getBillFieldsByEntity", new Object[] {
	            cEntity,cFormID
	        });
	        List approveBillFields = new ArrayList();
	        if(CollectionUtil.isEmpty(list))
	            return approveBillFields;
	        Map m;
	        for(Iterator i$ = list.iterator(); i$.hasNext(); approveBillFields.add(new ApproveBillField(m)))
	            m = (Map)i$.next();

	        return approveBillFields;
	    }



}
