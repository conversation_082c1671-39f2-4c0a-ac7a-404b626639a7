package com.aisino.app.web.dotasklist.daiban.service;

import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.db.DbSvr;


public class CommonGetUserAndDB {

	public static String getUserID(Object... param) {
		return SessionHelper.getCurrentUserId();
	}


	public static String getUserEmpID(Object... param) {
		return SessionHelper.getCurrentEmpId();
	}

	public static DbSvr getDbSvr(Object... param) {
		return DbSvr.getDbService(null);
	}


}
