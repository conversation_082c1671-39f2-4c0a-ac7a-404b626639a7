package com.aisino.app.web.dotasklist.common.plugin;

import java.util.List;
import java.util.Map;

import com.aisino.app.a6.web.dotasklist.common.service.SecurityServiceRescCheck;
import com.aisino.platform.core.MS;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class ListSearchCheckPlugin {
	/**
	 * 根据审批方式设置查询条件
	 * <AUTHOR>
	 * @date Aug 10, 2018
	 */
	public static void dealcheck(DataMsgBus bus,DbSvr db,Map map) {
		/*获取审批方式 0 直接审批，1 自动审批，2 工作流审批*/
		String formid = (String) map.get("formid");
		String table = (String) map.get("table");
		String checkWay = db.queryIdForString("mobile_common_daiban.getCheckway", new Object[]{formid});	
		/*手工审核需要先校验当前用户是否有审核权限*/
		boolean re =SecurityServiceRescCheck.RescCheck((String) map.get("rescKey"));  //判断当前登录人是否具有审核按钮的权限
		boolean bill= SecurityServiceRescCheck.RescCheck((String) map.get("billKey")); 
		boolean list = SecurityServiceRescCheck.RescCheck((String) map.get("listKey")); 
	    /*手工审批*/
		if(!"2".equals(checkWay)){
			   if(re&&bill||re&&list){  
				   bus.put("sg"+table, "true");
			}
		}		
		/*查看该用户是否有协同-待办事项的菜单权限。*/
		boolean hasworkflow = SecurityServiceRescCheck.RescCheck("pt_workflow_worklist");		
		if(hasworkflow){
			 bus.put("wf"+table, "true");
	    }else{
		    if(re&&bill) 
		    	bus.put("wffilter"+table,"true");
		    if(map.containsKey("initRescKey")&&map.containsKey("initBillKey")){  //期初审核权限及单据权限
		    	boolean initre = SecurityServiceRescCheck.RescCheck((String) map.get("initRescKey")); 
		    	boolean initbill = SecurityServiceRescCheck.RescCheck((String) map.get("initBillKey")); 
		    	if(initre&&initbill)
			    	bus.put("qcwffilter"+table,"true");
		    }		    	
	    }	
	}
}
