package com.aisino.app.web.dotasklist.common.plugin;

import com.aisino.aos.mobile.vo.ApproveBillField;
import com.aisino.platform.db.AnnotationProcessor;
import com.aisino.platform.db.Annotations;
import com.aisino.platform.util.StringUtil;
import java.util.*;

public class ApproveBillProcessorApp
    implements AnnotationProcessor
{

    public ApproveBillProcessorApp()
    {
    }

    public void process(Annotations an, Map map)
    {
        List approveBillFields = (List)map.get("approveBillFields");
        int alias_num = 0;
        Iterator i$ = approveBillFields.iterator();
        do
        {
            if(!i$.hasNext())
                break;
            ApproveBillField field = (ApproveBillField)i$.next();
            String cCode = field.getCcode();
            String cField = field.getCfield();
            if(!StringUtil.isBlank(cField))
            {
                String cCodes[] = cCode.split("\\.");
                String cFields[] = cField.split("\\.");
                if(cFields.length == 2 || cFields.length == 3)
                    an.addSelectFieldWithAlais(cFields[0], cFields[1], (new StringBuilder()).append(cCodes[0]).append(cCodes[1]).toString());
                else
                if(cFields.length == 4)
                {
                    an.leftJoinTableByAlais(cCodes[0], (new StringBuilder()).append("alias_").append(alias_num).toString(), cFields[3], cFields[0], cFields[1]);
                    an.addSelectFieldWithAlaisByTableAlais((new StringBuilder()).append("alias_").append(alias_num).toString(), cCodes[1], (new StringBuilder()).append(cCodes[0]).append(cCodes[1]).append(cCodes[2]).append(cCodes[3]).toString());
                    alias_num++;
                }
            }
        } while(true);
    }
}
