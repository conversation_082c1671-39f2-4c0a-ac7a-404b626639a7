package com.aisino.app.web.dotasklist.common.util;

import java.util.ArrayList;
import java.util.List;

import org.hsqldb.lib.StringUtil;

import com.aisino.platform.db.DbSvr;

public class AppendixUtil {

	/**
	 * 将指定cguid的单据号的附件更新为永久附件
	 * */
	public static void updateToPermanent(String cguid, DbSvr dbSvr){
		if(StringUtil.isEmpty(cguid)) return;
		List<Object[]> paramList = new ArrayList();
		paramList.add(new String[]{cguid});
		String sql = "update aos_file_files set ctempflag='1' where cguid=?";
		dbSvr.batchUpdate(sql, paramList);
	}
}
