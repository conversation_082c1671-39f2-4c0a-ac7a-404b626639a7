package com.aisino.app.web.dotasklist.common.reference;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetReferCbusprocessPlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		DbSvr dbSvr = DbSvr.getDbService(null);
		Map map = new HashMap();
		String billname=bus.getString("billname");
		if("saorder".equals(billname)){
			//销售CO_BusinessProcess的cSystemFlowGUID为00001
			bus.put("cSystemFlowGUID", "00001");
			bus.put("cBillType", "066");
		}
		
		List<Map> list = new ArrayList<Map>();
		if(bus.contain("canblank") && "y".equalsIgnoreCase(bus.getString("canblank"))){
			Map blank = new  HashMap();
			blank.put("code", "");
			blank.put("name", "选择业务流程");
			list.add(blank);
		}
		
		if("".equals(bus.getString("cbustype")))
			bus.put("cbustype", null);
		
		List<Map> l = dbSvr.queryIdForList("mobile_common_reference_sql.referCbusprocess", bus);
		if(l != null && !l.isEmpty())
			list.addAll(l);
		map.put("rtn", list);
	    form.setReturn(map);
	}
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		
	}
}
