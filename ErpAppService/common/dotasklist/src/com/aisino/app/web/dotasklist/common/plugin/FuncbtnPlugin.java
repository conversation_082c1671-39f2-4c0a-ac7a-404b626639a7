package com.aisino.app.web.dotasklist.common.plugin;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.platform.core.MS;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class FuncbtnPlugin extends SqlPlugin implements FormCreateListener {

	@Override
	/**
	 * 根据action获取功能按钮
	 * <AUTHOR>
	 * @date Dec 26, 2016
	 */
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		DbSvr db = DbSvr.getDbService(null);
	    String cguid=bus.getString("cguid");	//单据cguid  
	    String action = bus.getString("action");
	    String cbilltype = bus.getString("cbilltype");
	    if("flowlog".equals(action)){  //查看流程日志
			MS m = new MS("App.Dotask.flowlog");
			/* 2016-12-26 qcj 修正流程日志返回list */
			List<Map> list= (List)m.doService(cguid,cbilltype);		    	
			form.setReturn(list);
	    }else if("appendix".equals(action)){  //查看附件
			MS m = new MS("App.Dotask.appendix");  
			String cFileUrl=bus.getString("cFileUrl");
			String cFileName=bus.getString("cFileName");
			Object s= (Object) m.doService(cFileUrl,cFileName);		    	
			form.setReturn(s);
	    }else if("getcurqty".equals(action)){  //计算物品的库存单位现存量
	    	if(!bus.contain("cbatchguid")) bus.put("cbatchguid", null);
	    	if(!bus.contain("cpositionguid")) bus.put("cpositionguid", null);
	    	if(!bus.contain("doverdate")) bus.put("doverdate", null);
	    	if(!bus.contain("dproduction")) bus.put("dproduction", null);
	    	if(!bus.contain("iguaranteedays")) bus.put("iguaranteedays", null);
	    	String cstoreguid = bus.getString("cstoreguid")==null?"":bus.get("cstoreguid").toString();;
			if("".equals(bus.getString("cstoreguid")))
				bus.put("cstoreguid", null);
			Map map = db.queryIdFirstRow("mobile_common_func_sql.getcurqty", bus);
			form.setReturn(map);
	    }else if("getempdept".equals(action)){  //客户获得职员部门
	    	if(!bus.contain("ccustguid")) bus.put("ccustguid", null);
			Map map = db.queryIdFirstRow("mobile_common.getempdeptbycustomer", bus);
			form.setReturn(map);
	    }
	    

	}
	
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {

	}

}
