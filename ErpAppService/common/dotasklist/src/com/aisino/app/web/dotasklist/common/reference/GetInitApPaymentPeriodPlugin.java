package com.aisino.app.web.dotasklist.common.reference;

import java.util.HashMap;
import java.util.Map;

import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetInitApPaymentPeriodPlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		DbSvr db = DbSvr.getDbService(null);
		Map map = new HashMap();
		String year = db.getStringResult("select iYear from co_sysinit where csubsyscode='AR' ");
		String iperiod = db.getStringResult("select iMonth from co_sysinit where csubsyscode='AR'");
		if(year==null){
			map.put("error", "未启用应付管理模块");
			form.setReturn(map);
		}else{
			map.put("year", year);
			map.put("imonth", iperiod);
			form.setReturn(map);			
		}
	}
	
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		
	}
	
}
