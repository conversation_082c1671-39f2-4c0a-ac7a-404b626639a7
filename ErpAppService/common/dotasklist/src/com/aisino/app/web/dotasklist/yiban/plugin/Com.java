package com.aisino.app.web.dotasklist.yiban.plugin;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.Map;

public class Com implements Comparator {

	@SuppressWarnings("rawtypes")
	@Override
	public int compare(Object o1, Object o2) {
		Map<String, String> m1 = (Map<String, String>) o1;
		Map<String, String> m2 = (Map<String, String>) o2;
		SimpleDateFormat sdf1 = null;
		SimpleDateFormat sdf2 = null;
		String time1 = m1.get("办理时间").toString();
		String time2 = m2.get("办理时间").toString();
		if(time1.indexOf(":")!=-1)
		sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		else
			sdf1 = new SimpleDateFormat("yyyy-MM-dd");
		if(time2.indexOf(":")!=-1)
			sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		else
			sdf2 = new SimpleDateFormat("yyyy-MM-dd");
		Date d1 = null;
		Date d2 = null;
		try {
			d1 = sdf1.parse(time1.toString());
			d2 = sdf2.parse(time2.toString());
		} catch (ParseException e1) {
			e1.printStackTrace();
		}
		if (d1.getTime() > d2.getTime()) {
			return -1;
		} else if (d1.getTime() == d2.getTime()) {
			return 0;
		} else {
			return 1;
		}
	}

}
