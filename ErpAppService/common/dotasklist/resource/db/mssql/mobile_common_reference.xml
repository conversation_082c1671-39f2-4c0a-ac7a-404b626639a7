<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_common_reference">
		<i id="referCustomerlist" desp="客户参照">
			<![CDATA[
			select a.cGUID as code,
				   a.cname as name
			from CM_Customer a	
		    where a.iStatus=1
		    order by a.cCode
		    and ($like(a.cname,keyword) or $like(a.ccode,keyword) or $like(a.csname,keyword)) 
				@filter[CM_Customer,CM_CustomerClass,CM_Customer.cClassGUID=CM_CustomerClass.cguid]
			]]>
		</i>
		
		<i id="referStorelist"  desp="仓库参照">
		    <![CDATA[
			select a.cGUID as code,
				   a.cname as name
			from CM_Storehouse a	
		    where a.iStatus=1
		    order by a.cCode
		    and ($like(a.cname,keyword) or $like(a.ccode,keyword)) 
			@filter[CM_Storehouse]
			]]>
		</i>
		
		
		<i id="referEmpList"  desp="业务员参照">
		    <![CDATA[
		    select a.cGUID as code,
				   a.cname+'('+b.cname+')' as name,
				   b.cGUID as dcode,
				   b.cname as dname
			from CM_Employee a
			inner join CM_Department b on b.cGUID=a.cDeptGUID  
		    where a.iStatus=1 and b.iStatus=1
		    	  and $equal(a.cDeptGUID,cDeptGUID)
		    	  and ($like(a.cname,keyword) or $like(a.ccode,keyword)) 
		    order by a.cCode
		    @filter[CM_Employee,CM_Department]
			]]>
		</i>
		
		<i id="referDepList"  desp="部门参照">
		    <![CDATA[
		     select a.cGUID as code,
				    a.cname as name
		    from CM_Department a
		    where a.iStatus=1 
		          and a.iLeaf = 1
		    	  and ($like(a.cname,keyword) or $like(a.ccode,keyword)or $like(a.cSName,keyword)) 
		    order by a.cCode
		    @filter[CM_Department]
			]]>
		</i>
		
		<i id="referCbustype"  desp="业务类型参照">
		    <![CDATA[
		    select cguid as code,
				   cName as name
			from CO_BusinessType
							where cguid=?
			]]>
		</i>
		
		<i id="referCbusprocess"  desp="业务流程参照">
		    <![CDATA[
		   select distinct a.cguid as code,
				  a.cName as name							  
		   from CO_BusinessProcess a
				left join CO_ProcessDefine b on a.cSystemFlowGUID = b.cGUID
				left join CO_Process c on b.cGUID = c.cProcessID
		   where a.cbustype = '01001' and a.iStatus=1
				 and (c.cDBillType = {cBillType} or c.cSBillType = {cBillType} or b.cGUID='01001')
				 order by a.ccode
		   @filter[CO_BusinessProcess] 
			]]>
		</i>
		
		<i id="referCurrency"  desp="币种参照">
		    <![CDATA[
		    select t.cGUID code,
		    	   t.cname name
            from GL_Currency t 
            where istatus=1 
            order by iNative desc,t.cname
			]]>
		</i>	
		
				<i id="referSettleTypelist"  desp="结算方式参照">
		    <![CDATA[
		    select t.cGUID code,
		    	   t.cname name
            from CA_SettleMode t 
            where istatus=1 and ileaf=1
            order by t.ccode
			]]>
		</i>
		
		
		
	</sql>

</sqls>