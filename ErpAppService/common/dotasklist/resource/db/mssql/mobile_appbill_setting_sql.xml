<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd">
	<sqls>
	   <sql group="mobile_appbill_setting_sql">
	      <i id="getEntityByBillTypeGuid"><![CDATA[
	         select me.cName name,
			        me.cTable code
			 from META_ENTITY me
				where me.cGuid in (select bb.cMainEntity from BILL_BILLS bb where #equal(bb.cGuid,cBillType)
				 union select bb.cLineEntity from BILL_BILLS bb where #equal(bb.cGuid,cBillType)) 
	      ]]></i>
	      <i id="loadBillField"><![CDATA[
	         select m.cGuid,
	                m.cCode,
	                m.cField,
			        m.cFieldName,
			        m.cDataType,
			        m.cprecision,
			        m.cformid
			from MOB_WF_BILL_FIELD m
			where #equal(m.cEntity, cEntity)
			and $equal(m.cFormId,cFormId)
			order by m.iOrder
	      ]]></i>
	      
	       <i id="getBillFieldsByEntity"><![CDATA[
			select mw.*
			from MOB_WF_BILL_FIELD mw
			where mw.cEntity = ? and mw.cFormId=? order by mw.iorder
         ]]></i>
	   </sql>
	</sqls>
	
	