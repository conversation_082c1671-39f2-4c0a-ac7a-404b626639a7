<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
	<sql group="mobile_common">
		<i id="getFiles" desp="获取附件信息">
			<![CDATA[
				select cFileName,cFileUrl,cFileRealName
				from AOS_FILE_FILES
				where cGroupGuid = ?
			]]>
		</i>
		
		<i id="checkIamtSAorder"  desp="销售订单校验金额权限">
		    <![CDATA[
		    SELECT  m.cFieldGuid
			FROM AOS_DR_COL_RULE ad
			left join AOS_DR_COL_ITEM m on m.cguid=ad.cItemcGuid
			WHERE cAssignGuid =?
			and m.cFieldGuid='iTotal'
			and m.cEntityGuid='sa_orderline'
			]]>
		</i>
		
		
		<i id="checkIamtSAinvoice"  desp="销售发票校验金额权限">
		    <![CDATA[
		    SELECT  m.cFieldGuid
			FROM AOS_DR_COL_RULE ad
			left join AOS_DR_COL_ITEM m on m.cguid=ad.cItemcGuid
			WHERE cAssignGuid =?
			and m.cFieldGuid='iTotal'
			and m.cEntityGuid='SA_InvoiceLine'
			]]>
		</i>
		
		<i id="checkIamtFK"  desp="付款单校验金额权限">
		    <![CDATA[
		    SELECT  m.cFieldGuid
			FROM AOS_DR_COL_RULE ad
			left join AOS_DR_COL_ITEM m on m.cguid=ad.cItemcGuid
			WHERE cAssignGuid =?
			and m.cFieldGuid='iamt'
			and m.cEntityGuid='ap_paymentdetail'
			]]>
		</i>
		
		<i id="checkIamtPUorder"  desp="采购订单校验金额权限">
		    <![CDATA[
		    SELECT  m.cFieldGuid
			FROM AOS_DR_COL_RULE ad
			left join AOS_DR_COL_ITEM m on m.cguid=ad.cItemcGuid
			WHERE cAssignGuid =?
			and m.cFieldGuid='iTotal'
			and m.cEntityGuid='pu_orderline'
			]]>
		</i>
		
		<i id="checkfieldqx"  desp="字段权限校验">
		    <![CDATA[
		    SELECT  m.cFieldGuid
			FROM AOS_DR_COL_RULE ad
			left join AOS_DR_COL_ITEM m on m.cguid=ad.cItemcGuid
			WHERE cAssignGuid =?
			and m.cEntityGuid=?
			and m.cBuscode=?
			]]>
		</i>
		
		<i id="getempdeptbycustomer" desp="根据客户获得业务员及所属部门">
			<![CDATA[
			select cust.cEmpGUID,
				   emp.cname+'('+dept.cname+')' as cEmpName,
				   emp.cDeptGUID,
				   dept.cname as cDeptName,
				   cust.cguid as cCustGUID,
				   cust.cname as CCustName		
			from CM_Customer cust
            left join CM_Employee emp on cust.cEmpGUID = emp.cGUID
            left join CM_Department dept on emp.cDeptGUID = dept.cGUID
		    where $equal(cust.cguid,ccustguid)
			]]>
		</i>	
		
	</sql>
	<sql group="mobile_common_daiban">
		<i id="getCheckway" desp="获取审核方式">
			<![CDATA[
				select cCheckWay
				from BILL_SETTING
				where cFormId = ?
			]]>
		</i>
	</sql>
	
	<sql group="mobile_common_liuChengRizhi"  desp="非工作流审批流程日志">
	     <i id="sttrans"  desp="库存调拨单">
		    <![CDATA[
		      select 
					'新增' as  op,
					 s.dCreatorTime as	 endtime,
				    aup.crealname operatorname
				    from  ST_StkTrans s 
				    left join AOS_RMS_USER aup on aup.cguid=s.cCreatorGUID
				    where s.cguid=?  and s.cCreatorGUID is not null
		      union all
		      select 
		            '修改' as  op,
		            s.dModifyTime as  endtime,
					s.cModifyName   as	 operatorname
					 from  ST_StkTrans s 
					  where s.cguid=?  and s.dModifyTime is not null
		     union all
		       select 
		            '审核' as  op,
					 s.dCheckTime as	 endtime,
				     aup.crealname operatorname	 
				     from  ST_StkTrans s 
				     left join AOS_RMS_USER aup on aup.cguid=s.cAuditorGUID
				     where s.cguid=?  and s.cAuditorGUID is not null
			]]>
		</i>
	    <i id="sainvoice"  desp="销售发票">
		    <![CDATA[
		      select 
					'新增' as  op,
					 s.dCreatorTime as	 endtime,
				    aup.crealname operatorname
				    from  SA_Invoice s 
				    left join AOS_RMS_USER aup on aup.cguid=s.cCreatorGUID
				    where s.cguid=?  and s.cCreatorGUID is not null
		      union all
		      select 
		            '修改' as  op,
		            s.dModifyTime as  endtime,
					s.cModifyName   as	 operatorname
					 from  SA_Invoice s 
					  where s.cguid=?  and s.dModifyTime is not null
		     union all
		       select 
		            '审核' as  op,
					 s.dCheckTime as	 endtime,
				     aup.crealname operatorname	 
				     from  SA_Invoice s 
				     left join AOS_RMS_USER aup on aup.cguid=s.cAuditorGUID
				     where s.cguid=?  and s.cAuditorGUID is not null
			]]>
		</i>
		<i id="saorder"  desp="销售订单">
		    <![CDATA[
		      select 
					'新增' as  op,
					 s.dCreatorTime as	 endtime,
				    aup.crealname operatorname
				    from  sa_order s 
				    left join AOS_RMS_USER aup on aup.cguid=s.cCreatorGUID
				    where s.cguid=?  and s.cCreatorGUID is not null
		      union all
		      select 
		            '修改' as  op,
		            s.dModifyTime as  endtime,
					s.cModifyName   as	 operatorname
					 from  sa_order s 
					  where s.cguid=?  and s.dModifyTime is not null
		     union all
		       select 
		            '审核' as  op,
					 s.dCheckTime as	 endtime,
				     aup.crealname operatorname	 
				     from  sa_order s 
				     left join AOS_RMS_USER aup on aup.cguid=s.cAuditorGUID
				     where s.cguid=?  and s.cAuditorGUID is not null
			]]>
		</i>
		
		
			<i id="saretail"  desp="零售单">
		    <![CDATA[
		      select 
					'新增' as  op,
					 s.dCreatorTime as	 endtime,
				    aup.crealname operatorname
				    from  ST_StkRecord s 
				    left join AOS_RMS_USER aup on aup.cguid=s.cCreatorGUID
				    where s.cguid=?  and s.cCreatorGUID is not null
		      union all
		      select 
		            '修改' as  op,
		            s.dModifyTime as  endtime,
					s.cModifyName   as	 operatorname
					 from  ST_StkRecord s 
					  where s.cguid=?  and s.dModifyTime is not null
		     union all
		       select 
		            '审核' as  op,
					 s.dCheckTime as	 endtime,
				     aup.crealname operatorname	 
				     from  ST_StkRecord s 
				     left join AOS_RMS_USER aup on aup.cguid=s.cAuditorGUID
				     where s.cguid=?  and s.cAuditorGUID is not null
			]]>
		</i>
		
			<i id="puplan"  desp="采购计划">
		    <![CDATA[
		        select 
					'新增' as  op,
					 s.dCreatorTime as	 endtime,
				    aup.crealname operatorname
				    from  PU_Planhead s 
				    left join AOS_RMS_USER aup on aup.cguid=s.cCreatorGUID
				    where s.cguid=?  and s.cCreatorGUID is not null
		      union all
		      select 
		            '修改' as  op,
		            s.dModifyTime as  endtime,
					s.cModifyName   as	 operatorname
					 from  PU_Planhead s 
					  where s.cguid=?  and s.dModifyTime is not null
		     union all
		       select 
		            '审核' as  op,
					 s.dCheckTime as	 endtime,
				     aup.crealname operatorname	 
				     from  PU_Planhead s 
				     left join AOS_RMS_USER aup on aup.cguid=s.cAuditorGUID
				     where s.cguid=?  and s.cAuditorGUID is not null
			]]>
		</i>
		
		<i id="puorder"  desp="采购订单">
		    <![CDATA[
		        select 
					'新增' as  op,
					 s.dCreatorTime as	 endtime,
				    aup.crealname operatorname
				    from  PU_ORDER s 
				    left join AOS_RMS_USER aup on aup.cguid=s.cCreatorGUID
				    where s.cguid=?  and s.cCreatorGUID is not null
		      union all
		      select 
		            '修改' as  op,
		            s.dModifyTime as  endtime,
					s.cModifyName   as	 operatorname
					 from  PU_ORDER s 
					  where s.cguid=?  and s.dModifyTime is not null
		     union all
		       select 
		            '审核' as  op,
					 s.dCheckTime as	 endtime,
				     aup.crealname operatorname	 
				     from  PU_ORDER s 
				     left join AOS_RMS_USER aup on aup.cguid=s.cAuditorGUID
				     where s.cguid=?  and s.cAuditorGUID is not null
			]]>
		</i>
		<i id="AP_Payment"  desp="付款单">
		    <![CDATA[
		            select 
					'新增' as  op,
					s.cCreatorDate as	 endtime,
				    aup.crealname operatorname
				    from  AP_Payment s 
				    left join AOS_RMS_USER aup on aup.cguid=s.cCreatorGUID
				    where s.cguid=?  and s.cCreatorGUID is not null
		      union all
                    select 
		            '修改' as  op,
		             s.dModifyDate as  endtime,
					 s.cModifyName   as	 operatorname
					 from  AP_Payment s 
					 where s.cguid=?  and s.cModifyName is not null
		     union all
                    select 
		            '审核' as  op,
					 s.dAuditDate as	 endtime,
				     aup.crealname operatorname	 
				     from  AP_Payment s 
				    left join AOS_RMS_USER aup on aup.cguid=s.cAuditGUID
				    where s.cguid=?  and s.cAuditGUID is not null
			]]>
		</i>
		<i id="CA_OtherPayment"  desp="其它付款单">
		    <![CDATA[
		            select 
					'新增' as  op,
					s.dVouDate as	 endtime,
				    aup.crealname operatorname
				     from  CA_OtherPayment s 
				     left join AOS_RMS_USER aup on aup.cguid=s.cCreatorGUID
				     where s.cguid=?  
				     and  s.cCreatorGUID  is not null
		     union all
                   select 
		            '审核' as  op,
					 s.dAuditDate asendtime,
				     aup.crealname operatorname	 
				     from  CA_OtherPayment s 
				     left join AOS_RMS_USER aup on aup.cguid=s.cAuditGUID
				     where s.cguid=?  
                     and  s.cAuditGUID is not null
			]]>
		</i>
		
		<!-- 2017-03-08 qcj 增加借款报销非工作流流程日志查询sql -->
		<i id="Loan" desp="借款单">
			<![CDATA[
				select '新增' as op,CONVERT(varchar(100),loan.dCreateDate,20) as endtime,_user.crealname as operatorname
				from OA_LoanBill loan
				left join AOS_RMS_USER _user on _user.cguid=loan.cCreatorGUID
				where loan.cguid=? and loan.cCreatorGUID is not null
				union all
				select '修改' as op,CONVERT(varchar(100),loan.dModifyTime,20) as endtime,_user.crealname as operatorname
				from OA_LoanBill loan
				left join AOS_RMS_USER _user on _user.cguid=loan.cModifierGUID
				where loan.cguid=? and loan.cModifierGUID is not null
				union all
				select '审核' as op,CONVERT(varchar(100),loan.dCheckTime,20) as endtime,_user.crealname as operatorname
				from OA_LoanBill loan
				left join AOS_RMS_USER _user on _user.cguid=loan.cAuditorGuid
				where loan.cguid=? and loan.cAuditorGuid is not null
			]]>
		</i>
		
		<i id="ExpenseAccount" desp="报销单">
			<![CDATA[
				select '新增' as op,CONVERT(varchar(100),eab.dCreateDate,20) as endtime,_user.crealname as operatorname
				from OA_ExpenseAccountBill eab
				left join AOS_RMS_USER _user on _user.cguid=eab.cCreatorGUID
				where eab.cguid=? and eab.cCreatorGUID is not null
				union all
				select '修改' as op,CONVERT(varchar(100),eab.dModifyTime,20) as endtime,_user.crealname as operatorname
				from OA_ExpenseAccountBill eab
				left join AOS_RMS_USER _user on _user.cguid=eab.cModifierGUID
				where eab.cguid=? and eab.cModifierGUID is not null
				union all
				select '审核' as op,CONVERT(varchar(100),eab.dCheckTime,20) as endtime,_user.crealname as operatorname
				from OA_ExpenseAccountBill eab
				left join AOS_RMS_USER _user on _user.cguid=eab.cAuditorGuid
				where eab.cguid=? and eab.cAuditorGuid is not null
			]]>
		</i>
		
		<i id="stkreronds"  desp="材料出库单，销售出库单"><![CDATA[
			select 
				'新增' as  op,
				s.dCreatorTime as	 endtime,
				aup.crealname operatorname
			from  ST_StkRecord s 
			left join AOS_RMS_USER aup on aup.cguid=s.cCreatorGUID
			where s.cguid=?  and s.cCreatorGUID is not null
			
		union all
		    
			select 
				'修改' as  op,
				s.dModifyTime as  endtime,
				s.cModifyName   as	 operatorname
			from  ST_StkRecord s 
			where s.cguid=?  and s.dModifyTime is not null
		    
		union all
		
			select 
				'审核' as  op,
				s.dCheckTime as	 endtime,
				aup.crealname operatorname	 
			from  ST_StkRecord s 
			left join AOS_RMS_USER aup on aup.cguid=s.cAuditorGUID
			where s.cguid=?  and s.cAuditorGUID is not null
		]]></i>
		
	</sql>	
</sqls>