<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE Forms SYSTEM "form_definition.dtd">
<Forms>
	<form id="mobile_common_refer_customer"  desp="客户参照选择">
        <widgets>          
        </widgets>
		<extendPoint addglobe="false">
			<plugin type="com.aisino.app.web.dotasklist.common.reference.GetReferCustomerPlugin" ></plugin>
		</extendPoint>	
	</form>	
	
	<form id="mobile_common_refer_storehouse"  desp="仓库参照选择">
        <widgets>          
        </widgets>
		<extendPoint addglobe="false">
			<plugin type="com.aisino.app.web.dotasklist.common.reference.GetReferStorehousePlugin" ></plugin>
		</extendPoint>	
	</form>	
	
	<form id="mobile_common_refer_employee"  desp="业务员参照选择">
        <widgets>          
        </widgets>       
		<extendPoint addglobe="false">
			<plugin type="com.aisino.app.web.dotasklist.common.reference.GetReferEmployeePlugin" ></plugin>
		</extendPoint>	
	</form>	
	
		
	<form id="mobile_common_refer_department"  desp="部门参照选择">
        <widgets>          
        </widgets>
		<extendPoint addglobe="false">
			<plugin type="com.aisino.app.web.dotasklist.common.reference.GetReferDepartmentPlugin" ></plugin>
		</extendPoint>	
	</form>	
	
			
	<form id="mobile_common_refer_cbustype"  desp="业务类型参照选择">
        <widgets>          
        </widgets>
		<extendPoint addglobe="false">
			<plugin type="com.aisino.app.web.dotasklist.common.reference.GetReferCbustypePlugin" ></plugin>
		</extendPoint>	
	</form>	
	
	<form id="mobile_common_refer_cbusprocess"  desp="业务流程参照选择">
        <widgets>          
        </widgets>
		<extendPoint addglobe="false">
			<plugin type="com.aisino.app.web.dotasklist.common.reference.GetReferCbusprocessPlugin" ></plugin>
		</extendPoint>	
	</form>	
	
	<form id="mobile_common_refer_currency"  desp="币种参照选择">
        <widgets>          
        </widgets>
		<extendPoint addglobe="false">
			<plugin type="com.aisino.app.web.dotasklist.common.reference.GetReferCurrencyPlugin" ></plugin>
		</extendPoint>	
	</form>	
	
		
	<form id="mobile_common_refer_settletype"  desp="结算方式参照选择">
        <widgets>          
        </widgets>
		<extendPoint addglobe="false">
			<plugin type="com.aisino.app.web.dotasklist.common.reference.GetReferSettleTypePlugin" ></plugin>
		</extendPoint>	
	</form>	
	
	
	<form id="mobile_common_refer_matclass"  desp="物品分类参照选择">
        <widgets>          
        </widgets>
		<extendPoint addglobe="false">
			<plugin type="com.aisino.app.web.dotasklist.common.reference.GetReferMatClassPlugin" ></plugin>
		</extendPoint>	
	</form>	
	
	
	<form id="mobile_common_refer_material"  desp="物品参照选择">
        <widgets>          
        </widgets>
		<extendPoint addglobe="false">
			<plugin type="com.aisino.app.web.dotasklist.common.reference.GetReferMaterialPlugin" ></plugin>
		</extendPoint>	
	</form>	
	
	<form id="mobile_common_refer_appaymentyear"  desp="应收列表会计期间年份选择">
        <widgets>          
        </widgets>
		<extendPoint addglobe="false">
			<plugin type="com.aisino.app.web.dotasklist.common.reference.GetApPaymentYearPlugin" ></plugin>
		</extendPoint>	
	</form>	
	
	<form id="mobile_common_refer_appaymentperiod"  desp="应收列表会计期间期间选择">
        <widgets>          
        </widgets>
		<extendPoint addglobe="false">
			<plugin type="com.aisino.app.web.dotasklist.common.reference.GetApPaymentPeriodPlugin" ></plugin>
		</extendPoint>	
	</form>
	
	<form id="mobile_common_refer_appaymentinitperiod"  desp="应收列表会计期间初始值">
        <widgets>          
        </widgets>
		<extendPoint addglobe="false">
			<plugin type="com.aisino.app.web.dotasklist.common.reference.GetInitApPaymentPeriodPlugin"></plugin>
		</extendPoint>	
	</form>	
	
	<form id="mobile_common_refer_appmatbatch"  desp="物品批次参照">
        <widgets>          
        </widgets>
		<extendPoint addglobe="false">
			<plugin type="com.aisino.app.web.dotasklist.common.reference.GetMatBatchPlugin"></plugin>
		</extendPoint>	
	</form>
	
</Forms>