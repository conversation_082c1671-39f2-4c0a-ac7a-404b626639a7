<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE Forms SYSTEM "form_definition.dtd">
<!-- 作者:guodx  日期: 2017年08月17日 描述:复写移动应用单据配置页面  -->
	<Forms>
		<form id="mobile_service_bill_setting" extend="TreeListTemplate" desp="移动应用单据配置页面">
			<toolbar buttons="btnSave,,btnFilter,,btnFirst,btnPrev,btnNext,btnLast,,btnRefresh,,btnHelp,btnExit">
				<button id="btnSave" text="保存" img="img/Save.gif" width="50px" hint="保存"/>
	 			<button id="btnFirst" text="最上"  width="50px" img="img/vfirst.gif"/>
				<button id="btnPrev" text="上移" width="50px" img="img/vprev.gif"/>
				<button id="btnNext" text="下移" width="50px" img="img/vnext.gif"/>
				<button id="btnLast" text="最下" width="50px" img="img/vlast.gif"/>
				<button id="btnFilter" text="页内查询" width="70px" hint="页内查询" img="img/SearchList.gif"></button>
		    </toolbar>
			<widgets>
	            <basic name="tree" widget="XTree" attr="foldclick:true;" width="100%" height="100%">
	                <valueFetcher type="com.aisino.platform.view.basicWidget.fetcher.XTreeFetcher">
						<value name="object">com.aisino.aos.bill.fetcher.BillSettingTreeFetcher</value>
					</valueFetcher>
	            </basic>
	            <layout name="list_layout" type="TableLayout" width="100%" height="100%" attr="groupSize:1;heights:30px 0;">
	                <layout type="VerticalLayout" width="100%" height="100%" attr="widths:250px 100px 100px 0" topSpace="3px">
		                <basic name="cBillType" widget="Hidden"></basic>
		                <basic name="cFormId" widget="Hidden"></basic>
		                <basic name="cEntity" label="实体" widget="Combox" referWidgets="cBillType" leftSpace="-20px" attr="syn:true;">
		                    <valueFetcher type="com.aisino.platform.view.basicWidget.fetcher.SqlValueGetter">
		                       <value name="sqlid">mobile_appbill_setting_sql.getEntityByBillTypeGuid</value>
		                    </valueFetcher>
		                </basic>
		                <basic name="cEntityFieldBtn" label="实体段" widget="button" width="85px" leftSpace="20px"></basic>
		                <basic name="del" label="删行" width="85px" leftSpace="20px" widget="Button"></basic>
						<basic name="delAll" label="清空" width="85px" leftSpace="20px" widget="Button"></basic>
					</layout>
		            <basic name="list" widget="XEditGrid" attr="filter:true;page:false;initRowNum:0;enableDragRow:true;" width="100%" height="100%" >
		                <col label="cguid" id="cguid" hidden="true"/>
		                <col label="cField" id="cField" hidden="true"/>
		                <col label="段代码" id="cCode" filter="str" width="150px"/>
		                <col label="段名称" id="cFieldName" editType="text" filter="str"/>
		                <col label="数据类型" id="cDataType" editType="select" sort="false">
							<valueFetcher type="com.aisino.platform.view.basicWidget.fetcher.StaticFetcher">
								<value name="string">字符</value>
								<value name="number">数字</value>
								<value name="date">日期</value>
							</valueFetcher>
				        </col>	
		                <col label="精度" id="cPrecision" editType="text"/>
		                <col label="顺序" id="iOrder" hidden="true"></col>
		                <col label="单据formid" id="cFormId"></col>
		            </basic>
		        </layout>
			</widgets>
			<extendPoint>
			   <plugin type="com.aisino.platform.template.db.RowSetBindPlugin" desp="保存插件">
			       <value name="table">MOB_WF_BILL_FIELD</value>
			       <value name="pk">cGuid</value>
			       <value name="list">list</value>
			       <value name="referWidgets">cEntity</value>
			       <value name="loadSqlid">mobile_appbill_setting_sql.loadBillField</value>
			   </plugin>
			</extendPoint>
			
			<bind element="this" event="onCreate"><![CDATA[
			    window.tree = w('tree');
			    tree.expandLevel(2);
			    window.filter_state = false;
			]]></bind>
			
			<bind element="tree" event="doclick" param="id,data,level,isLeaf,parentId"><![CDATA[
			    ws('cBillType',data.code);
			    ws('cFormId',data.cformid);
			    w('cEntity').reload();
			    PT.ns('load','','cEntity='+wg('cEntity')+';cFormId='+wg('cFormId'));
			]]></bind>
			<bind element="cEntity" event="dochange"><![CDATA[
			    PT.ns('load','','cEntity='+wg('cEntity')+';cFormId='+wg('cFormId'));
			]]></bind>
			
			<bind element="cEntityFieldBtn" event="click"><![CDATA[
			    var centity = wg('cEntity');
			    if(!centity||centity==null){
			        PT.alert('请选择实体！');
			        return;
			    }
			    F.getEntityField(centity);
			]]></bind>
			
			<bind element="this" event="getEntityField" param="centity"><![CDATA[
			    var selected = w('list').getColumnData('ccode');
				PT.putBusValue('selected',selected);
			    var rtn = PT.showModalForm('mobile_service_bill_field_choose','entities='+centity);
			    var selectedData = PT.f().getFieldListByTree(rtn);
			    if(selectedData==null)
			        return;
			    var oldData = w('list').get();
			    var newData = new Array();
			    var num = 0;
			    
			    for(var j=0;j<selectedData.length;j++){
			   		 selectedData[j].cformid=wg('cformid');			   
			    }
			    
			    if(oldData==null||oldData.length<1)
			        w('list').addRows(selectedData);
			    for(var i=0;i<oldData.length;i++){
			        var oldRow = oldData[i];
			        for(var j=0;j<selectedData.length;j++){
			            var newRow = selectedData[j];
			            if(newRow['ccode']==oldRow['ccode']){
			                newRow['cfieldname'] = oldRow['cfieldname'];
			                newRow['cprecision'] = oldRow['cprecision'];
			                newRow['cdatatype'] = oldRow['cdatatype'];
			                newRow['cformid'] = oldRow['cformid'];	
			                newData[num++] = newRow;
			                selectedData.remove(newRow);
			            }
			        }
			    }
			    for(var i=0;i<selectedData.length;i++){
			        newData[num++] = selectedData[i];
			    }
			    w('list').clear();
			    w('list').addRows(newData);
			]]></bind>
			
			<bind element="this" event="getFieldListByTree" param="treelist"><![CDATA[
			    if(treelist==null||treelist.length<1)
			       return null;
			    var rtn = new Array();
			    for(var i=0;i<treelist.length;i++){
			       var m = treelist[i];
			       var cdatatype = '';
				   if(m.cdatatype=='Int'||m.cdatatype=='Float')
				       cdatatype = 'number';
				   else if(m.cdatatype=='Date')
				       cdatatype = 'date';
				   else
				       cdatatype = 'string';
  
				   var showName = m.name;
				   if(m['parentName'] && m['_level']>2){
					   showName = m.parentName+showName;
				   }
				   
				   var p = {
					   ccode:m.code,
					   cfield:m.ownfield?m.ownfield:m.code,
					   cfieldname:showName,
					   centity:wg('centity'),
					   cdatatype:cdatatype
				   };
				   rtn.push(p);
			    }
			    return rtn;
			]]></bind>
			
			<bind element="btnSave" event="click"><![CDATA[
			    if(filter_state){
					w('btnFilter').style.backgroundColor = '';
					w('list').clearFilter();
					w('list').hideFilter();
					filter_state = !filter_state;
				}
				if(wg('centity')==null ||wg('centity')==''){
					PT.alert('请选择实体！');
					return;
				}
			    var list = w('list');
				var size = list.size();
				if(size>0){
					for(var index = 0; index < size; index++){
						var row = list.getRowByIndex(index);
						list.cv('iorder',index,row);
					}					
					w('list').clearEditFlag();
			    	PT.ns('save');
			    	PT.t('保存成功！');
				}else{
				    PT.alert('请至少显示一个字段！');
				    return;
				}
			]]></bind>
			<bind element="this" event="moveRecord" param="moveKind,index"><![CDATA[
				var s =w('list').getSelected();
				if(!s) {
					PT.alert("请选择要移动的行！");
					return;
				}
				if(moveKind=='move')
					w('list').moveRow(s,index);
				else if(moveKind=='moveTo')
					w('list').moveRowTo(s,index);
				if(filter_state){
					PT.wid('btnFilter').style.backgroundColor = '';
					w('list').clearFilter();
					w('list').hideFilter();
					filter_state = !filter_state;
				}
			]]></bind>
			
			<bind element="btnFirst" event="click"><![CDATA[
				F.moveRecord('moveTo',0);
			]]></bind>
			
			<bind element="btnPrev" event="click"><![CDATA[
				F.moveRecord('move',-1);
			]]></bind>
			
			<bind element="btnNext" event="click"><![CDATA[
				F.moveRecord('move',1);
			]]></bind>
			
			<bind element="btnLast" event="click"><![CDATA[
				F.moveRecord('moveTo',w('list').size());
			]]></bind>
			
			<bind element="btnRefresh" event="click"><![CDATA[
				var tree = w('tree');
				var centity = wg('centity');
				var selectedNode = tree.getSelectedNodeID();
				var parent = tree.getParentNodeID(selectedNode);
				tree.reloadRoot(function(){
		 		    tree.clickNode(selectedNode);
					tree.expandLevel(3,function(){
					   tree.selectNode(selectedNode);
			 	       PT.ns('load','','cEntity='+wg('cEntity')+';cFormId='+wg('cFormId'));
					});
			 	});
		    ]]></bind>
			
			<bind element="btnFilter" event="click"><![CDATA[
				if(!filter_state){
					PT.wid('btnFilter').style.backgroundColor = '#9dc8f5';
					w('list').showFilter();
				}else{
					PT.wid('btnFilter').style.backgroundColor = '';
					w('list').clearFilter();
					w('list').hideFilter();
				}
				filter_state = !filter_state;
			]]></bind>
			
			<bind element="list" event="onSelect" param="row,flag,isAll"><![CDATA[
				var l = w('list');
				if('number'!=row['cdatatype']){
				    l.disableCellEdit(row,['cprecision'],true);		
			    }
		    ]]></bind>	
		    
		    <bind element='list.cDataType' event='afterEdit' param='r,c,g,v,ov'><![CDATA[
			    if('string'==v||'date'==v){
			       g.cv('cPrecision','');
			       g.disableCellEdit(r,['cprecision'],true);
			    }
			    if('number'==v){
			       g.disableCellEdit(r,['cprecision'],false);
			    }
			]]></bind>
			
			<bind element="del" event="click"><![CDATA[
				if(w('list').getSelected())
					PT.confirm('确认删除该行数据？',function(){F.deleteListRow();});
				else
					PT.alert('请选择要删除的数据！');
			]]></bind>
			
			<bind element="delAll" event="click"><![CDATA[
				PT.confirm('确认清空数据？',function(){F.deleteListRow(true);});
			]]></bind>
			
			<bind element="this" event="deleteListRow" param="isAll"><![CDATA[
				var l = w('list');
				if(isAll){
					l.clear();
					return;
				}
				var sr=l.getSelected();
				if(l.length<1 || !sr)
					return;
				var A=sr._id,idx=l.getRowIndex(A);
		    	l.deleteSelect();
		    	l.select(idx);
			]]></bind>
		</form>
	</Forms>
	