<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE Forms SYSTEM "form_definition.dtd">
<!--作者:guodx 日期: 2017-8-17 复写移动端选择  -->
	<Forms>
		<form id="mobile_service_bill_field_choose" extend="pt_metadata_field_refer" desp="移动应用单据字段选择页面">
			<toolbar buttons="save,exit"> 
				<button id="save" text="确定" img="img/Save.gif" hint="确定"></button>
				<button id="exit" text="退出" img="img/Exit.gif" hint="退出" onclick="PT.closeModal();"></button>
			</toolbar>
			<widgets>
				<basic name="selected"  layout="content" widget="Hidden"></basic>
				<basic name="tree" widget="XTree" attr="check:true;syn:true;"></basic>
			</widgets>
			<extendPoint>
				<plugin type=""></plugin>
			</extendPoint>
			<bind element="this" event="onCreate" extendway="after"><![CDATA[
				var selected = PT.getParentBusValue('selected');
				tree.expandLevel(3,function(selected){
					if(selected){
						for(var i=0;i<selected.length;i++){
							tree.doCheck(selected[i],2);
						}
					}
				},selected);
			]]></bind>
			<bind element="save" event="click"><![CDATA[
				var l = tree.getCheckData(false);
				if(l==null || l.length<1){
					PT.alert('请选择一条记录！');
					return;
				}
				for(var i =0;i<l.length;i++){
					var ti = l[i];
					if(ti['parentId'])
						ti['parentName'] = tree.getNode(ti['parentId'])['name'];
				}
				PT.closeModal(l);
		]]></bind>
		<bind element="tree" event="dodblclick" extendway="override"><![CDATA[
		]]></bind>
		</form>
	</Forms>
	
