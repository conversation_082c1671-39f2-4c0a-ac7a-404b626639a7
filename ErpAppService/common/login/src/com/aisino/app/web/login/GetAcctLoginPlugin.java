package com.aisino.app.web.login;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.aisino.platform.core.MS;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetAcctLoginPlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		
		DbSvr dbSvr = DbSvr.getDefaultDbService();
		List acctList = new ArrayList();
		List list = null;
		String user = bus.getString("userName");
		String pwd = bus.getString("passWord");
		com.aisino.platform.jdbc.obj.TableObj table = dbSvr
				.getTableInfomation("Acct_LinkUser");
		// 得接收参数判断是A3、还是A6登陆（此处写公共还是拆开、需考虑）
		MS ms = new MS("App.login");  
		list = (List)ms.doService("getAcct",dbSvr, user);
		MS m = new MS("ACS.PwdSvc");
		if (CollectionUtil.isNotEmpty(list)) {
			Iterator i$ = list.iterator();
			do {
				if (!i$.hasNext())
					break;
				Map map = (Map) i$.next();
				String cguid = CollectionUtil.getStringFromMap(map, "cguid");
				String oldEncodePwd = CollectionUtil.getStringFromMap(map,
						"password");
				Object re = m.doService(new Object[] { "valid", pwd,
						oldEncodePwd, cguid });
				if (re != null) {
					boolean flag = ((Boolean) re).booleanValue();
					if (flag)
						acctList.add(map);
				}
			} while (true);
		}

		Map map = new HashMap();
		map.put("list", acctList);
		form.setReturn(map);
	}

	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {

	}

}
