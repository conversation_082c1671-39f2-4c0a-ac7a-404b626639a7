package com.aisino.app.web.login;

import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetPwdConfigPlugin extends SqlPlugin implements FormCreateListener {

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		String cGuid = SessionHelper.getCurrentUserId();
		String sql = "select t.iMinLength,t.iValidateDays,t.iValidateForever,t.iIncludeCharNum,t.cDefaultPwd,t.iAllowChange from AOS_RMS_PWDTYPE t, AOS_RMS_USER u where t.cGuid = u.cPwdType and u.cGuid = ?";
		DbSvr db = DbSvr.getDbService(null);
		List<Map> list = db.getListResult(sql, cGuid);
		form.setReturn(list);
	}

}
