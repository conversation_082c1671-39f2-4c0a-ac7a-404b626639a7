package com.aisino.app.web.login;


import java.util.HashMap;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.core.MS;
import com.aisino.platform.util.SessUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class LogoutPlugin implements  FormCreateListener
{



    public void onFormCreate(AbstractForm form, DataMsgBus bus)
    {
    	MS m = new MS("System.Logout");
    	String userId = SessionHelper.getCurrentUserId();
        m.doService(userId);
        SessUtil.inValidSession();
        Map p = new HashMap();
        p.put("msg", "success");
        form.setReturn(p);
    }
}


