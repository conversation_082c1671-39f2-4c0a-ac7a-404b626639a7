package com.aisino.app.web.service;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.util.AES;
import com.aisino.app.web.util.IPV4;
import com.aisino.app.web.util.JWT;
import com.aisino.app.web.util.Qr;
import com.aisino.platform.core.Guid;
import com.aisino.platform.core.ProductInfo;
import com.aisino.platform.core.serviceInf.IFService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.veng.json.dauglas.JSONException;
import com.aisino.platform.veng.json.dauglas.JSONObject;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;

public class AppLoginService implements IFService {
	//演示账套扫码下载地址
	private static final String YSZTQRCODE = "http://cmgl.aisino.com:8896/A3/qrcode/qrcode.html";
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public Object doService(AbstractForm form, DataMsgBus bus) {
		DbSvr acc = DbSvr.getDefaultDbService();
		String url = ProductInfo.getWebRoot();
		url = url.substring(0, url.length() - 1);
		String id = SessionHelper.getCurrentDataSource();
		String user = SessionHelper.getCurrentUserName();

		String cguid = Guid.g();
		String subject = id + "," + user;
		String token = JWT.create(cguid, "Aisino Corp.", subject);
		// int index = url.indexOf("127.0.0.1");
		if (url.indexOf("127.0.0.1") != -1) {// 如果包含127.0.0.1
			String ip = IPV4.getIpAddress();
			url = url.replaceAll("127.0.0.1", ip);
		}
		JSONObject json = new JSONObject();
		try {
			json.put("url", url);
			json.put("username", user);
			json.put("accid", id);
		} catch (JSONException e) {
			e.printStackTrace();
		}
		String code = null;
		try {
			code = AES.aesEncrypt(json.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}
		String content = YSZTQRCODE+"?code="+code;
		String fileName = Qr.bar(content);
		String searchSql = "select * from acc_app_token where username = ? and accid = ?";
		Map p  = acc.getOneRecorder(searchSql, new Object[]{user, id});
		if(p!=null&&!p.isEmpty()){
			String oldFile = (String) p.get("filename");
			File file = new File(ProductInfo.getWebRealPath()+"appToken/"+oldFile);
			if(file.exists()){
				file.delete();
			}
			String sql = "update acc_app_token set filename = ? where username = ? and accid = ?";
			acc.update(sql, new Object[]{
				fileName,user,id
			});
		}else{
			Map param = new HashMap();
			param.put("cguid", cguid);
			param.put("token", token);
			param.put("username", user);
			param.put("accid", id);
			param.put("filename", fileName);
			acc.insertRow("acc_app_token", param);
		}

		return null;
	}

}
