package com.aisino.app.web.service;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.aisino.platform.acs.model.User;
import com.aisino.platform.config.DataBaseConfig;
import com.aisino.platform.config.xmlObject.Ds;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.CodeException;
import com.aisino.platform.jdbc.datasource.Dm;
import com.aisino.platform.util.SessUtil;
import com.aisino.platform.view.DataMsgBus;

/**
 * <p>Title: PT5-ACS</p>
 * <p>Copyright: Copyright  2010-7-20</p>
 * @author： leo
 * @version 1.0
 * com.aisino.platform.acs.authentication.UserPwdAuthentication
 */
public class UserTokenAuthentication implements IService {

	/**
	 * @created ：2010-7-20
	 * @auther ： leo
	 * @see com.aisino.platform.core.serviceInf.IService#doService(java.lang.Object[])
	 */
	public Object doService(Object... arg) {

		String action = (String) arg[0];//操作类型	
		DataMsgBus bus = (DataMsgBus) arg[1];
        String dbid = obtainDbid(bus);
		if ("fetchUser".equals(action)) {//提取用户
			String username = obtainUsername(bus);
			if (username == null)
				username = "";
			username = username.toLowerCase();
			User user = loadUserByUsername(username,dbid);
			return user;
		}
		return null;
	}
	public static User loadUserByUsername(String username,String dbid) {
		Map usermap = null;
		try {
			DbSvr svr = DbSvr.getDbService(dbid);
			usermap = svr.queryIdFirstRow("ACS.loadUserByUsername", username);		
		} catch (Exception e) {
			throw new CodeException("security.DataBaseError");//访问数据库失败。可能由以下原因导致：1、访问的帐套已超出授权允许的最大帐套数；2、数据库连接不上
		}
		return User.MapToUser(usermap);
	}
	/**
	 * @方法功能:从请求中获取用户名信息。
	 * @调用参数:DataMsgBus bus
	 * @返回类型:String类型的用户名
	 */
	public static String obtainUsername(DataMsgBus bus) {
		String username = bus.getString("user");
		if (username == null) {
			username = "";
		}
		return username.toLowerCase();
	}

	/**
	 * @方法功能:从请求中获取帐套信息。
	 * @调用参数:DataMsgBus bus
	 * @返回类型:String类型的帐套
	 */
	public static String obtainDbid(DataMsgBus bus) {
		String dbid = bus.getString("account");
		if (dbid == null||"".equals(dbid)) {
			dbid = DataBaseConfig.getDefaultDsId();
		}
		return dbid;
	}

	/**
	 * @方法功能:从请求中获取帐套信息。
	 * @调用参数:DataMsgBus bus
	 * @返回类型:String类型的帐套
	 */
	public static String obtainDbname(DataMsgBus bus) {
		String dbid = bus.getString("account");
		if (dbid == null||"".equals(dbid)) {
			dbid = DataBaseConfig.getDefaultDsId();
		}
		String dbname = "";
		Ds ds = Dm.searchDs(dbid);//DataBaseConfig.getDs(dbid);
		if(ds!=null)dbname = ds.getName();
		return dbname;
	}

	/**
	 * @方法功能:从请求中获取日期信息
	 * @调用参数:DataMsgBus bus
	 * @返回类型:Date类型
	 */
	public static Date obtainLoginDate(DataMsgBus bus) {
		String str = bus.getString("date");
		Date loginDate = null;

		if (str == null||"".equals(str)) {
			loginDate = new Date();
		} else {
			loginDate = dateParse(str);
		}
		return loginDate;
	}
	public static Date dateParse(String str) throws CodeException{
		if(str==null||"".equals(str))return null;
		Date d;
		try {
			d = new SimpleDateFormat("yyyy-MM-dd").parse(str);
		} catch (ParseException e) {
			System.out.println("date parse erro");
			throw new CodeException("date parse erro");
		}
		return d;
	}
	public static String obtainIp() {
		HttpServletRequest request = SessUtil.getRequest();
		String ip = request.getHeader("x-forwarded-for");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		if ("127.0.0.1".equals(ip)||"0:0:0:0:0:0:0:1".equals(ip))
			try {
				ip = InetAddress.getLocalHost().getHostAddress();
			} catch (UnknownHostException e) {
			}
		return ip;
	}
}
