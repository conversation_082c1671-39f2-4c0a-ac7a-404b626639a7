package com.aisino.app.web.login;

import java.util.HashMap;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.core.MS;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.Crud;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;
/**
 * 修改密码的plugin
 * <AUTHOR>
 *
 */
public class ChangePwdPlugin extends SqlPlugin implements FormCreateListener {

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		String cGuid = SessionHelper.getCurrentUserId();
		String oldPwd = bus.getString("oldcPwd");
		String pwd = bus.getString("cPWD");
		if (StringUtil.isBlank(cGuid))
			throw new BusinessException(
					"\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u51FA\u9519\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55\uFF01");
		String sql = "select cPWD from AOS_RMS_USER where cGuid = ?";
		String dbPWD = DbSvr.getDbService(null).getStringResult(sql,
				new Object[] { cGuid });
		if (!checkOldPwd(oldPwd, cGuid, dbPWD)) {
			throw new BusinessException(
					"\u5BF9\u4E0D\u8D77\uFF0C\u539F\u5BC6\u7801\u4E0D\u6B63\u786E\uFF0C\u8BF7\u91CD\u8BD5\u3002");
		} else {
			String newEncryptionPWD = newPwd(pwd, cGuid);
			Crud c = new Crud("AOS_RMS_USER");
			c.define("cPWD", new Object[] { newEncryptionPWD });
			c.defineCondition("cGuid", new Object[] { cGuid });
			DbSvr.getDbService(null).execute(c.getUpdateEso());
			MS m = new MS("AOS.PwdChangeAfter");
			m.publishMessage(new Object[] { cGuid, newEncryptionPWD });
		}
		Map<String,String> p = new HashMap<String,String>();
		p.put("errmsg", "success");
		form.setReturn(p);

	}

	private String newPwd(String pwd, String cGuid) {
		MS m = new MS("ACS.PwdSvc");
		Object re = m.doService(new Object[] { "encode", pwd, cGuid });
		pwd = re != null ? re.toString() : "";
		return pwd;
	}

	private boolean checkOldPwd(String pwd, String cguid, String encodePwd) {
		MS m = new MS("ACS.PwdSvc");
		Object re = m
				.doService(new Object[] { "valid", pwd, encodePwd, cguid });
		if (re == null)
			return false;
		else
			return ((Boolean) re).booleanValue();
	}

}
