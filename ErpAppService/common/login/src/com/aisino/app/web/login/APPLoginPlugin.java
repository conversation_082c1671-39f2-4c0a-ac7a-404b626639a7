package com.aisino.app.web.login;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.service.AppLoginCheckService;
import com.aisino.app.web.service.MicroTaxService;
import com.aisino.app.web.util.JWT;
import com.aisino.platform.acs.service.LoginService;
import com.aisino.platform.core.Guid;
import com.aisino.platform.core.MS;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.jdbc.datasource.Dm;
import com.aisino.platform.system.log.LogService;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.DateUtil;
import com.aisino.platform.util.NoCaseMap;
import com.aisino.platform.util.SessUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;
import com.aisino.platform.view.login.AccessControl;

public class APPLoginPlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		SessUtil.inValidSession();
		Map<String, Object> rtn = new NoCaseMap<Object>();
		String tokenId = bus.getString("tokenid");
		boolean checkLogin = AppLoginCheckService.LoginBeforeCheck(bus);//登录前校验
		if(!checkLogin){
			rtn.put("errmsg", "第一次登录请在PC端修改密码");
			form.setReturn(rtn);
			return;
		}
		String username;
		String accountId;
		String token;
		
		if (StringUtil.isNotBlank(tokenId)) {
			// token信息要存在accsys库中,才好确定登录的是哪个帐套,用tokenid登录
			DbSvr acc = DbSvr.getDefaultDbService();
			token = acc.getStringResult(
					"select token from acc_app_token where cguid=?", tokenId);
			// info里有帐套号和用户名,使用info做登录
			Map<String, String> info = JWT.parse(token);
			username = info.get("user");
			accountId = info.get("act");
			bus.send("account", accountId);
			bus.send("user", username);
			bus.send("date", DateUtil.date2Str(new Date()));
			// bus.send("AuthService", "ACS.UserTokenAuth");
			AccessControl login = new LoginService();
			login.canAccess(form, bus);
		} else {
			username = bus.getString("username");
			if (StringUtil.isBlank(username)) {
				rtn.put("errorMsg", "用户名不能为空！");
				form.setReturn(rtn);
				return;
			}
			accountId = bus.getString("accountId");
			if (StringUtil.isBlank(accountId)) {
				rtn.put("errorMsg", "账套不能为空！");
				form.setReturn(rtn);
				return;
			}
			try {
				SessionHelper.getCurrentUserId();
			} catch (Exception e1) {
				DataMsgBus newBus = new DataMsgBus();
				newBus.copy(bus);
				newBus.put("user", bus.getString("username"));
				newBus.put("pwd", bus.getString("password"));
				newBus.put("account", bus.getString("accountId"));
				newBus.put("date", DateUtil.date2Str(new Date()));
				newBus.setNewAction("login");
				MS m = new MS("App.login");
				m.doService("doFormSubmit", newBus);
			}
		}
		DbSvr db = DbSvr.getDbService(accountId);
		String sql = "select cGuid, cPWD, cUserType,cRealName from aos_rms_user where cName = ?";
		Map map = db.getOneRecorder(sql, username);
		String userId = CollectionUtil.getStringFromMap(map, "cguid");
		rtn.put("userGuid", userId);
		rtn.put("username", username);
		rtn.put("errorMsg", "success");
		rtn.put("userRealName",
				CollectionUtil.getStringFromMap(map, "cRealName"));
		// A3,A6登录差异的方法 AppLogin.getUserPerm
		MS m = new MS("App.login");
		List<Map> list = (List<Map>) m.doService("getUserPerm", db, userId,
				CollectionUtil.getStringFromMap(map, "cUserType"));
		rtn.put("funlist", list);
		// AppLogin.getUserPerm(db, userId,CollectionUtil.getStringFromMap(map,
		// "cUserType"))
		SessUtil.putInContext(SessionHelper.ADMIN_ORGN_KEY_IN_SESSION, "1");
		SessUtil.putInContext(SessionHelper.CURRENT_USER_KEY_IN_SESSION, userId);
		SessUtil.putInContext(SessionHelper.LOGIN_ORGN_KEY_IN_SESSION, "1");
		isBindMobile(bus, rtn);
		form.setReturn(rtn);
		if("true".equals(rtn.get("isBind"))){//已绑定，向微税平台发送数据
			String mobile = (String) rtn.get("phoneNumber");
			MicroTaxService.sendMsg(accountId,mobile);
		}
		//LogService.log("航天信息ERP App登录","login","用户APP登录["+username+"]","0");
	}
	

	/**
	 * 查询用户是否进行了手机绑定
	 */
	@SuppressWarnings("rawtypes")
	public void isBindMobile(DataMsgBus bus, Map<String, Object> rtn) {
		String tax = getTax();
		String acc = bus.getString("accountId");
		DbSvr dbSvr = DbSvr.getDefaultDbService();
		String bindSql = "select mo.cusername,mo.caccname,acc.accid,mo.cisbind,mo.cacctid,mo.ctax,mo.cguid,mo.cmobile from acc_app_bind_mobile mo "
				+ "inner join accinfo acc on acc.accid = mo.cacctid where mo.cUsername = ?";
		List<Map> dataList = dbSvr.getListResult(bindSql,
				new Object[] { bus.getString("username")});
		if (dataList == null) {//未找到数据，表示未绑定手机号
			rtn.put("isBind", "false");
			rtn.put("phoneNumber", null);
			return;
		}else{
			rtn.put("isBind", "true");
		}
		boolean hasAcc = false;
		for(Map p : dataList){
			String accid = (String) p.get("cacctid");
			if(accid.equals(acc)){//表中存在的信息，获取mobile,之前已经通过username过滤，这里仅根据accid过滤即可得到唯一手机号
				rtn.put("phoneNumber", p.get("cmobile"));
				String saveTax = "update acc_app_bind_mobile set cTax=? where cacctid = ?;";//存入纳税人识别号到acc_app_bind_mobile表中
				dbSvr.update(saveTax, new Object[]{tax,(String) p.get("cacctid")});
				hasAcc = true;
				return;
			}
		}
		if(!hasAcc){//如果acc库的acc_app_bind_mobile表中没有这个这个数据，存在新建账套的可能
			String mobile = null;
			List<Map> acctList = getAcc(bus.getString("username"), bus.getString("password"));
			for( Map moMap :dataList){
				for(Map accMap : acctList){
					if(moMap.get("cacctid").equals(accMap.get("code"))){
						mobile = (String) moMap.get("cmobile");
						break;
					}
				}
			}
			String dbName = Dm.searchDs(SessionHelper.getCurrentDataSource()).getName();
			String saveMobile = "INSERT INTO acc_app_bind_mobile "
				+ "(cGuid,cUsername,cTax,cAccName,cAcctid,cIsbind,cMobile) VALUES (?,?,?,?,?,?,?)";
			dbSvr.update(saveMobile, new Object[]{
				Guid.g(), bus.getString("username"),
				tax,dbName,bus.getString("accountId"),"1",mobile
			});
			rtn.put("phoneNumber",mobile);
		}
	}
	/**
	 * 获取登录账套的纳税人识别号
	 * @return
	 */
	public String getTax(){
		DbSvr db = DbSvr.getDbService(null);
		//获取纳税人识别号
		String taxSql = "select ctaxno from CO_CorporationInfo";
		Map taxMap = db.getOneRecorder(taxSql);
		String tax =null;
		if(taxMap.get("ctaxno")==null||"".equals(taxMap.get("ctaxno"))){
			tax="";
		}else{
			tax = (String) taxMap.get("ctaxno");
		}
		return tax;
	}
	/**
	 * 获取用户名，密码对应的所有账套
	 * 
	 * @param user
	 * @param pwd
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public List<Map> getAcc(String user, String pwd) {
		DbSvr db = DbSvr.getDefaultDbService();
		List<Map> acctList = new ArrayList<Map>();
		List list = null;
		// 得接收参数判断是A3、还是A6登陆（此处写公共还是拆开、需考虑）
		MS ms = new MS("App.login");
		list = (List) ms.doService("getAcct", db, user);
		MS m = new MS("ACS.PwdSvc");
		if (CollectionUtil.isNotEmpty(list)) {
			Iterator i$ = list.iterator();
			do {
				if (!i$.hasNext())
					break;
				Map map = (Map) i$.next();
				String cguid = CollectionUtil.getStringFromMap(map, "cguid");
				String oldEncodePwd = CollectionUtil.getStringFromMap(map,
						"password");
				Object re = m.doService(new Object[] { "valid", pwd,
						oldEncodePwd, cguid });
				if (re != null) {
					boolean flag = ((Boolean) re).booleanValue();
					if (flag)
						acctList.add(map);
				}
			} while (true);
		}
		return acctList;
	}
}
