package com.aisino.app.web.login;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.input.SAXBuilder;
import com.aisino.platform.core.ProductInfo;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class ReleaseVersionPlugin extends SqlPlugin implements
		FormCreateListener {

	private static final long serialVersionUID = 1L;
	private static final String SERVER = "ftp.aisino.com"; // FTP服务器地址
	private static final String USER = "glrjyw"; // 用户名
	private static final String PASSWORD = "service"; // 密码
	private static final int PORT = 21; // 端口号
	private static final String PATH = "/软件补丁程序下载/App_ftp/"; // 访问文件路径
	private static final String FILE_NAME = "version.xml";
	private static final String LOCAL_FILE = ProductInfo.getWebRealPath() +"app"+ File.separatorChar + 
			 FILE_NAME;
	private static final String DEFAULT_VERSION = "1.0.000000";
	@SuppressWarnings("unchecked")
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		Map<String,String > result = new HashMap<String, String>();
		result.put("android_version",DEFAULT_VERSION);
		result.put("ios_version",DEFAULT_VERSION);
		try {
			File file = new File(LOCAL_FILE);		
			boolean downLoadFlag = false; // 是否需要下载文件
			if (file.exists()) {
				System.out.println("文件" + file.getAbsolutePath() + "存在");
				SimpleDateFormat dFormat = new SimpleDateFormat("yyyy-MM-dd");
				long lastModified = file.lastModified(); // 获取文件最后修改时间
				Calendar calendar = Calendar.getInstance();
				Calendar calendar2 = Calendar.getInstance();
				calendar.setTimeInMillis(lastModified);
				String fileDay = dFormat.format(calendar.getTime());
				String currentDay = dFormat.format(calendar2.getTime());
				downLoadFlag = currentDay.equalsIgnoreCase(fileDay) ? false
						: true;
				System.out.println("是否需要下载文件:" + (downLoadFlag ? "是" : "否"));
			} else {
				downLoadFlag = true;
			}
			if (downLoadFlag) {
				FTPClient ftp = new FTPClient();
				OutputStream out = null;
				try {
					int reply;
					ftp.connect(SERVER, PORT);
					ftp.login(USER, PASSWORD);
					reply = ftp.getReplyCode();
					if (!FTPReply.isPositiveCompletion(reply)) { 
						ftp.disconnect();
					}
					ftp.changeWorkingDirectory("/");
					ftp.changeWorkingDirectory(new String(PATH.getBytes("GBK"),"iso-8859-1"));					
					File local = new File(LOCAL_FILE);
					if(!local.getParentFile().exists())
						local.getParentFile().mkdirs();
					out = new FileOutputStream(local);
					FTPFile[] ff = ftp.listFiles();
					for (FTPFile f : ff) {
						if (FILE_NAME.equalsIgnoreCase(f.getName())) {
							ftp.retrieveFile(FILE_NAME, out);						
						}
					}
					// 设置文件的最后修改时间为当前时间。
					file.setLastModified(Calendar.getInstance()
							.getTimeInMillis());
				} catch (Exception e) {
					e.printStackTrace();
				} finally {
					try {
						out.close();
					} catch (Exception e2) {
					}
					
					if (ftp.isConnected()) {		
						try {
							ftp.disconnect();
						} catch (IOException e) {
							e.printStackTrace();
						}
					}
				}
			}
			// 解析文件，获取版本号信息
			SAXBuilder saxBuilder = new SAXBuilder();
			InputStream in = null;
			try {
				in = new FileInputStream(file);
				Document document = saxBuilder.build(in,"UTF-8");
				Element root = document.getRootElement();
				List<Element> list = root.getChildren();
				for(Element e:list){
					if("android_version".equalsIgnoreCase(e.getName())){
						result.put("android_version", e.getValue());
					}	
					if("ios_version".equalsIgnoreCase(e.getName())){
						result.put("ios_version", e.getValue());
					}
				}
			}catch (Exception e) {
				e.printStackTrace();
			}finally {
				try {
					in.close();
				}catch (Exception e) {
					in = null;						
				}			
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		form.setReturn(result);
	}
}
