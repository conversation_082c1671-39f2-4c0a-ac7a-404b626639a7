package com.aisino.app.web.service;

import java.util.Map;

import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.DataMsgBus;

public class AppLoginCheckService {
	/**
	 * 校验，是否第一次登录
	 * @param bus
	 * @return
	 */
	public static boolean LoginBeforeCheck(DataMsgBus bus){
		String user = bus.getString("username");
		String acc = bus.getString("accountId");
		String sql = "SELECT a.ifirstlogin,p.iFirstLogMustChange FROM aos_rms_user a"
				+ " left join AOS_RMS_PWDTYPE p on p.cguid= a.cpwdtype"
				+ "  where a.cName = ?";
		DbSvr db = DbSvr.getDbService(acc);
		Map p= db.getOneRecorder(sql, user);
		int firstLogin = (Integer)p.get("ifirstlogin");
		int firstLoginNeedChange = (Integer)p.get("ifirstlogmustchange");
		if(firstLogin==1&&firstLoginNeedChange==1){
			return false;
		}
		return true;
	}

}
