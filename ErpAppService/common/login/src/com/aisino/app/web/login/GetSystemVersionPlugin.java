package com.aisino.app.web.login;

import java.util.List;
import java.util.Map;

import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetSystemVersionPlugin  extends SqlPlugin implements FormCreateListener {

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		DbSvr db = DbSvr.getDbService(null);
		String sql = "select cdataversion from Co_versionInfo";
		List<Map> list = db.getListResult(sql);
		form.setReturn(list);
	}

}
