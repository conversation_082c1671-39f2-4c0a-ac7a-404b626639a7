package com.aisino.app.web.login;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.notice.SystemNoticeListPlugin;
import com.aisino.platform.core.MS;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.jdbc.datasource.Dm;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetLoginInfoPlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		// TODO Auto-generated method stub
		DbSvr dbSvr = DbSvr.getDbService(null);	
		Map rtn = new HashMap();
		
	   Map daibanMap = this.getAllModuleNum(form,bus,dbSvr);

	   //预警消息
	   bus.put("noticeType", 1);
	   Map yujingMap = this.getAllNoticeNum(form,bus,dbSvr);

	   //系统通知
	   bus.put("noticeType", 4);
	   Map systemMap = this.getAllNoticeNum(form,bus,dbSvr);

	   //后台版本号
		String sql = "select cdataversion from Co_versionInfo";
		String cdataversion = dbSvr.getStringResult(sql);
		
	   rtn.putAll(daibanMap);
	   rtn.put("yujingnum", yujingMap.get("noticelength"));
	   rtn.put("systemnum", systemMap.get("noticelength"));
	   rtn.put("cdataversion", cdataversion);
	   rtn.put("accountname", Dm.searchDs(SessionHelper.getCurrentDataSource()).getName());
	   form.setReturn(rtn);  
	}
	

	/**
	 * 查询总待办消息数目
	 */
	@SuppressWarnings("rawtypes")
	public Map getAllModuleNum(AbstractForm form,DataMsgBus bus,DbSvr dbSvr) {
		List<Map> modulelist = new ArrayList<Map>();		
		int allnum=0;
		String[] modules=new String[]{"EM","OA","MA","FI","PU","SA","ST"}; 
		for(int i=0;i<modules.length;i++){
			String module =modules[i];
			int listnum = 0;  //该分类的待办数目
			Map np = new HashMap();
			np.put("listnum", listnum);
			MS m = new MS("App.DaibanList."+module);  
		    m.doService("getnum",null,np,null);	//doService第一个参数为getnum/getlist，第二个为list，第三个为listnum,第四个为搜索关键字	    		    
		    allnum += Integer.parseInt(String.valueOf(np.get("listnum")));		    
		}
		Map map = new HashMap();
		map.put("daibannum",allnum); 
        return map;
	}
	
	
	/**
	 * 查询总预警消息数目noticeType=1
	 */
	@SuppressWarnings("rawtypes")
	public Map getAllNoticeNum(AbstractForm form,DataMsgBus bus,DbSvr dbSvr) {
		SystemNoticeListPlugin notice = new SystemNoticeListPlugin();
		Map loadNotices = notice.loadNotices(form, bus);	
        return loadNotices;
	}
}
