package com.aisino.app.web.service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.util.DES;
import com.aisino.app.web.util.IPV4;
import com.aisino.platform.core.ProductInfo;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.veng.json.dauglas.JSONException;
import com.aisino.platform.veng.json.dauglas.JSONObject;

/**
 * 发送到微税服务接口
 * 
 * <AUTHOR>
 * 
 */
public class MicroTaxService {
	private final static String ENCODE = "utf-8";
	/**
	 * 发送信息
	 * @param accId
	 * @param mobile
	 * @return
	 */
	public static String sendMsg(String accId, String mobile) {
		String data = getUserMsg(accId,mobile);//未加密
		//System.out.println("用户注册信息==================================================================》");
		//System.out.println(data);
		String result = null;
		//String resultString =null; 
		try {
			byte[] desData = DES.encrypt(data.getBytes(ENCODE));//DES加密
			String base64Data = DES.encode(desData);//base64编码
			//System.out.println(base64Data);
			String urlData = URLEncoder.encode(base64Data, ENCODE);
			//http://*************:8007/测试环境
			String url = "http://microtax.aisino.com:8080/microtax.service/erp/signin.html";
			//http://microtax.aisino.com:8080/    正式环境
			String base64Result = sendGet(url,urlData);//向微税发送get请求并获取请求结果
			result = new String(base64Result.getBytes(), "UTF-8");
			//byte[] desResult = DES.decode(base64Result.getBytes(ENCODE));//base64解码
			//result = DES.decrypt(desResult);//DES解密
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		};
		return result;
	}

	/**
	 * 税号+用户名+账套 校验 { "innerIp": "", 内网ip "outIp": "", 外网ip "userSource": "",
	 * 用户来源 A3/A6 "companyName": "", 企业名称 "taxcode": "", 税号 "userName": "", 用户名
	 * "roleName": "", 用户角色 "accountName": "", 账套名 "mobile": "", 手机号
	 * "signInAttr": "" 访问属性 PC/App }
	 * 
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public static String getUserMsg(String accId, String mobile) {
		DbSvr db = DbSvr.getDbService(null);
		String sql = "select aos.cRealName,co.cAccName,co.cTAXNo "
				+ "from AOS_RMS_USER aos,CO_CorporationInfo co "
				+ "where aos.cGUID = ?";
		String roleSql = "SELECT DISTINCT u.cGUID userId,u.cNAME userName,uor.cRoleID roleId,r.cNAME roleName "
				+ "FROM AOS_RMS_USER u "
				+ "LEFT JOIN AOS_RMS_USER_ORGN_REL uo ON uo.cUserID = u.cGUID "
				+ "LEFT JOIN AOS_RMS_USER_ORGN_ROLE_REL uor ON uor.cUserOrgnID = uo.cGUID "
				+ "LEFT JOIN AOS_RMS_ROLE r ON r.cGUID = uor.cRoleID "
				+ "WHERE uor.cRoleID IS NOT NULL AND u.cGUID = ?";
		Map p = db.getOneRecorder(sql, SessionHelper.getCurrentUserId());
		List<Map> roleList = db.getListResult(roleSql,SessionHelper.getCurrentUserId());
		String userSource = ProductInfo.getProjectName();
		DbSvr defDb = DbSvr.getDefaultDbService();
		String accSql = "select accname from accinfo where accid = ?";
		Map acc = defDb.getOneRecorder(accSql, accId);
		JSONObject data = new JSONObject();
		String roles = "";
		if(roleList!=null){//得到所有角色信息
			for(Map role:roleList){
				roles+=role.get("roleName")+";";
			}
		}
		try {
			data.put("innerIp", IPV4.getIpAddress());
			data.put("outIp", IPV4.getIPV4());
			data.put("userSource", userSource);
			data.put("companyName", p.get("caccname"));
			data.put("taxcode", p.get("ctaxno"));
			data.put("userName", p.get("cRealName"));
			data.put("roleName", roles);
			data.put("accountName", acc.get("accname"));
			data.put("mobile", mobile);
			data.put("signInAttr", "App");
		} catch (JSONException e) {
			e.printStackTrace();
		}
		return data.toString();
	}
	
	
    /**
     * 向指定URL发送GET方法的请求
     * 
     * @param url
     *            发送请求的URL
     * @param param
     *            请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return URL 所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param) {
        String result = "";
        BufferedReader in = null;
        try {
            String urlNameString = url + "?data=" + param;
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 建立实际的连接
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(
                    connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            System.out.println("发送GET请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        return result;
    }
}
