package com.aisino.app.web.util;


import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import org.apache.commons.codec.binary.Base64;

/**
 * 微税接口加解密
 * 
 * <AUTHOR>
 * 
 */
public class DES {
    // 算法名称  
    public static final String KEY_ALGORITHM = "DES";  
    // 算法名称/加密模式/填充方式   
    public static final String CIPHER_ALGORITHM_CBC = "DES/CBC/PKCS5Padding";  
	// 加密秘钥
	private final static byte[] KEYArray = new byte[] { 85, 20, 35, 90, 59, 80,
			12, 96 };
	// 解密秘钥
	private final static byte[] IVArray = new byte[] { 53, 28, 32, 112, 99,
			124, 42, 105 };

	public static void main(String[] args) throws Exception {
		byte[] byt = encrypt("123".getBytes());
		System.out.println(encode(byt));
		System.out.println(new String(decrypt(byt)));
	}
	
	/**
	 * 加密
	 * 
	 * @param datasource
	 *            byte[]
	 * @return byte[]
	 */
	public static byte[] encrypt(byte[] datasource) {
		try {
			//SecureRandom random = new SecureRandom();
			IvParameterSpec iv = new IvParameterSpec(IVArray); 
			DESKeySpec desKey = new DESKeySpec(KEYArray);
			// 创建一个密匙工厂，然后用它把DESKeySpec转换成
			SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(KEY_ALGORITHM);
			SecretKey securekey = keyFactory.generateSecret(desKey);
			// Cipher对象实际完成加密操作
			Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM_CBC);
			// 用密匙初始化Cipher对象
			cipher.init(Cipher.ENCRYPT_MODE, securekey, iv);
			// 现在，获取数据并加密
			// 正式执行加密操作
			return cipher.doFinal(datasource);
		} catch (Throwable e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 解密
	 * 
	 * @param src
	 *            byte[]
	 * @return byte[]
	 * @throws Exception
	 */
	public static byte[] decrypt(byte[] src) throws Exception {
		IvParameterSpec iv = new IvParameterSpec(IVArray); 
		DESKeySpec desKey = new DESKeySpec(KEYArray);
		// 创建一个密匙工厂
		SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(KEY_ALGORITHM);
		// 将DESKeySpec对象转换成SecretKey对象
		SecretKey securekey = keyFactory.generateSecret(desKey);
		// Cipher对象实际完成解密操作
		Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM_CBC);
		// 用密匙初始化Cipher对象
		cipher.init(Cipher.DECRYPT_MODE, securekey, iv);
		// 真正开始解密操作
		return cipher.doFinal(src);
	}
	/** 
     * @param bytes 
     * @return 
     */  
    public static byte[] decode(byte[] bytes) { 
        return Base64.decodeBase64(bytes);  
    }  
  
    /** 
     * 二进制数据编码为BASE64字符串 
     * 
     * @param bytes 
     * @return 
     * @throws Exception 
     */  
    public static String encode(final byte[] bytes) {  
        return new String(Base64.encodeBase64(bytes));  
    }  
}
