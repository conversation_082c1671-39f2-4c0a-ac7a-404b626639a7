package com.aisino.app.web.login;

import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class AppQrImgPlugin implements FormCreateListener {

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		String fileName = DbSvr.getDefaultDbService().getStringResult(
				"select filename from acc_app_token where username=? and accid=?", 
				SessionHelper.getCurrentUserName(),
				SessionHelper.getCurrentDataSource());
		form.executeJs("document.getElementById('appqr').src='../appToken/"+fileName+"'");
	}

}
