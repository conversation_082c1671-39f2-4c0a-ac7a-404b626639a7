package com.aisino.app.web.login;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.core.Globe;
import com.aisino.platform.core.MS;
import com.aisino.platform.core.ProductInfo;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.jdbc.datasource.Dm;
import com.aisino.platform.util.SessUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetUserMsg extends SqlPlugin implements FormCreateListener {

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		//select * from Co_versionInfo 版本信息
		String webversionInfo = (String) Globe.get("webversionInfo");
		String versioninfo = (new StringBuilder())
				.append(StringUtil.isNotBlank(webversionInfo) ? webversionInfo
						: "")
				.append(" ")
				.append(version())
				.toString();
		SessUtil.setAttribute("app_token","qwer" );
		String userId = SessionHelper.getCurrentUserId();
		String sqlUserName = "select cRealName from AOS_RMS_USER where cGUID =?";
		String sqlCompany = " select cAccName from CO_CorporationInfo";
		DbSvr db = DbSvr.getDbService(null);
		List<Map> userList = db.getListResult(sqlUserName, userId);
		List<Map> companyList = db.getListResult(sqlCompany);
		Map<String, String> p = new HashMap<String, String>();
		p.put("userRealName", userList.get(0).get("crealname").toString());
		p.put("orgnName", SessionHelper.getCurrentMainOrgnName());
		p.put("companyName", companyList.get(0).get("caccname").toString());
		p.put("loginedDs", Dm.searchDs(SessionHelper.getCurrentDataSource())
				.getName());
		p.put("versioninfo",versioninfo);
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String dateStr = sdf.format(date);
		p.put("date", dateStr);
		DbSvr defDb = DbSvr.getDefaultDbService();
		String mobileSql = "select cmobile from acc_app_bind_mobile where cusername = ? and cacctid = ?";
		String userName = SessionHelper.getCurrentUserName();
		Map map = defDb.getOneRecorder(mobileSql, new Object[]{
			userName,SessionHelper.getCurrentDataSource()
		});
		if(map!=null){
			p.put("mobile", (String)map.get("cmobile"));
		}
		form.setReturn(p);
	}

	private String version() {
		MS service = new MS("ACS.DOG");
		if (service == null
				|| !((Boolean) service.doService(new Object[] { "isDogOn" }))
						.booleanValue())
			return "\u8BD5\u7528\u7248";
		String product = ProductInfo.getProjectName();
		if (((Boolean) service.doService(new Object[] { "hasDogProduct",
				product })).booleanValue())
			return "\u6B63\u5F0F\u7248";
		else
			return "\u8BD5\u7528\u7248";
	}

}
