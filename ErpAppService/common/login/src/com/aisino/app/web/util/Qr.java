package com.aisino.app.web.util;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.File;

import javax.imageio.ImageIO;

import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.core.Guid;
import com.aisino.platform.core.ProductInfo;
import com.swetake.util.Qrcode;

public class Qr {
	
	public static String bar(String content) {
		//content act,user
		try {
			// 图片大小
			int width = 306;
			// 方块大小
			int q = 6;
			Qrcode qrcodeHandler = new Qrcode();
			qrcodeHandler.setQrcodeErrorCorrect('L');
			qrcodeHandler.setQrcodeEncodeMode('B');
			qrcodeHandler.setQrcodeVersion(8);
			//System.out.println(content);
			byte[] contentBytes = content.getBytes();
			BufferedImage bufImg = new BufferedImage(width, width,
					BufferedImage.TYPE_INT_RGB);
			Graphics2D gs = bufImg.createGraphics();
			gs.setBackground(Color.WHITE);
			gs.clearRect(0, 0, width, width);
			gs.setColor(Color.BLACK);
			// 设置偏移量 不设置可能导致解析出错
			int pixoff = 2;
			// 输出内容> 二维码
			boolean[][] codeOut = qrcodeHandler.calQrcode(contentBytes);
			for (int i = 0; i < codeOut.length; i++) {
				for (int j = 0; j < codeOut.length; j++) {
					if (codeOut[j][i]) {
						gs.fillRect(j * q + pixoff, i * q + pixoff, q, q);
					}
				}
			}
			int width_4 = width / 4;
			int width_8 = width_4 / 2;
			int height_4 = width / 4;
			int height_8 = height_4 / 2;
			// logo
			String filePath = ProductInfo.getWebRealPath()+"appToken/";
			Image img = ImageIO.read(new File(filePath+"logo.png"));
			gs.drawImage(img, width_4 + width_8, height_4 + height_8, width_4, height_4, null);
			gs.dispose();
			bufImg.flush();
			String fileName = SessionHelper.getCurrentUserName()+"_"+Guid.g()+".png";
			File imgFile = new File(filePath+fileName);
			ImageIO.write(bufImg, "png", imgFile);
			return fileName;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

	}
}
