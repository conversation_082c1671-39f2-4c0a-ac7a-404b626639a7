package com.aisino.app.web.base;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.SessUtil;
import com.aisino.platform.veng.json.dauglas.JSONException;
import com.aisino.platform.veng.json.dauglas.JSONObject;

public class AppBaseServlet extends HttpServlet {
	
	protected void service(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		String mark = request.getParameter("mark");
		if("downloadAppForAndroid".equals(mark)){//android端下载apk
			downloadAppForAndroid(request,response);
		}else{
			//response.sendRedirect("http://172.26.8.85:8896/A3/qrcode/qrcode.html");
			//response.sendRedirect("http://cmgl.aisino.com:8896/A3/qrcode/qrcode.html");
			response.sendRedirect("http://cmgl.aisino.com:8896/A3/qrcode/qrcode.html");
		}
	}
	
	/**
	 * 从演示账套下载apk
	 * @param request
	 * @param response
	 * @throws IOException
	 */
	public static void downloadAppForAndroid(HttpServletRequest request, HttpServletResponse response) throws IOException{
		String filename = "aisinoerp.apk";
		String enc = "UTF-8";
		String fileName = null;
		String apkPath = "D:/Aisino/app/aisinoerp.apk";
		File downloadFile = new File(apkPath);
		
		if (downloadFile.exists()) {
	        //设置文件MIME类型  
	        response.setContentType(request.getSession().getServletContext().getMimeType(filename));  
			Long length = downloadFile.length();
			response.setContentLength(length.intValue());
			fileName = URLEncoder.encode(downloadFile.getName(), enc);
			response.addHeader("Content-Disposition", "attachment; filename="
					+ fileName);
			ServletOutputStream servletOutputStream = response
					.getOutputStream();
			FileInputStream fileInputStream = new FileInputStream(downloadFile);
			BufferedInputStream bufferedInputStream = new BufferedInputStream(
					fileInputStream);
			int size = 0;
			byte[] b = new byte[4096];
			while ((size = bufferedInputStream.read(b)) != -1) {
				servletOutputStream.write(b, 0, size);
			}
			servletOutputStream.flush();
			servletOutputStream.close();
			bufferedInputStream.close();
		}
	}
}
