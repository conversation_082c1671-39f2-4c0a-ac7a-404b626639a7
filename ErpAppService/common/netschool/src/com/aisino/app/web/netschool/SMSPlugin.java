package com.aisino.app.web.netschool;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import com.aisino.platform.core.Plugin;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class SMSPlugin extends Plugin implements FormCreateListener {

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
        Map<String,Object> resultMap =new HashMap<String,Object>();//返回结果的map
		String phone = bus.getString("phoneNumber");
		String message = null;
		String code = getFourRandom();
		String time = Double.toString(System.currentTimeMillis());
		try {
			message = URLEncoder.encode("【航天信息】 验证码:"+code+",验证码5分钟内有效。", "UTF-8");
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
		}
		String url = "http://sdk999in.eucp.b2m.cn:8080/sdkproxy/sendsms.action?"
				+ "cdkey=9SDK-EMY-0999-JFUOP&password=mutation&phone="
				+ phone + "&message="
				+ message+ "&addserial=888&seqid="
				+ time;
        String result = "";
        BufferedReader in = null;
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 建立实际的连接
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段
//            for (String key : map.keySet()) {
//                System.out.println(key + "--->" + map.get(key));
//            }
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(
                    connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            System.out.println("发送GET请求出现异常！" + e);
            e.printStackTrace();
            resultMap.put("msg", "短信请求失败，服务器繁忙");
            resultMap.put("err", "99");
            form.setReturn(resultMap);
            return;
        }
        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        int index = result.indexOf("<error>");
        if(index==-1){
        	resultMap.put("msg", "服务器繁忙");
        	resultMap.put("err", "98");
            form.setReturn(resultMap);
            return;
        }
        String err = result.substring(index+7, index+8);
        resultMap.put("code", code);
        resultMap.put("err", err);
        form.setReturn(resultMap);

	}

	@Override
	public void setValue(String arg0, String arg1) {
		
	}
    public static String getFourRandom(){
        Random random = new Random();
        String fourRandom = ""+ random.nextInt(9999);
        int randLength = fourRandom.length();
        if(randLength<4){
          for(int i=1; i<=4-randLength; i++)
              fourRandom = "0" + fourRandom;
        }
        return fourRandom;
    }
}
