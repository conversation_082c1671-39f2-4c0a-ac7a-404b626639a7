package com.aisino.app.web.netschool;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.aisino.app.web.service.MicroTaxService;
import com.aisino.platform.core.Guid;
import com.aisino.platform.core.MS;
import com.aisino.platform.core.Plugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.system.log.LogService;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;
/**
 * 注册手机号调用接口
 * <AUTHOR>
 *
 */
public class PhoneDataPlugin extends Plugin implements FormCreateListener {

	@SuppressWarnings("rawtypes")
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		//LogService.log("注册手机号","close","","");
		String user = bus.getString("userName");
		String pwd = bus.getString("passWord");
		String accId = bus.getString("accountId");
		String cmobile = bus.getString("phoneNumber");
		List<Map> acctList = getAcc(user, pwd);
		DbSvr db = DbSvr.getDefaultDbService();
		String sql = "INSERT INTO acc_app_bind_mobile (cGuid,cUsername,cTax,cAccName,cAcctid,cIsbind,cMobile) VALUES (?,?,?,?,?,?,?)";
		for (int i = 0; i < acctList.size(); i++) {
			db.update(sql, new Object[] {// 其他账套，纳税人识别号暂存为空
					Guid.g(), user, "", acctList.get(i).get("name"),
							acctList.get(i).get("code"), "1", cmobile });
		}
		
		Map<String, String> p = new HashMap<String, String>();
		p.put("msg", "success");
		p.put("phoneNumber", cmobile);
		form.setReturn(p);
		MicroTaxService.sendMsg(accId, cmobile);//发送信息到微税平台
	}

	@Override
	public void setValue(String arg0, String arg1) {

	}

	/**
	 * 获取用户名，密码对应的所有账套
	 * 
	 * @param user
	 * @param pwd
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public List<Map> getAcc(String user, String pwd) {
		DbSvr db = DbSvr.getDefaultDbService();
		List<Map> acctList = new ArrayList<Map>();
		List list = null;
		// 得接收参数判断是A3、还是A6登陆（此处写公共还是拆开、需考虑）
		MS ms = new MS("App.login");
		list = (List) ms.doService("getAcct", db, user);
		MS m = new MS("ACS.PwdSvc");
		if (CollectionUtil.isNotEmpty(list)) {
			Iterator i$ = list.iterator();
			do {
				if (!i$.hasNext())
					break;
				Map map = (Map) i$.next();
				String cguid = CollectionUtil.getStringFromMap(map, "cguid");
				String oldEncodePwd = CollectionUtil.getStringFromMap(map,
						"password");
				Object re = m.doService(new Object[] { "valid", pwd,
						oldEncodePwd, cguid });
				if (re != null) {
					boolean flag = ((Boolean) re).booleanValue();
					if (flag)
						acctList.add(map);
				}
			} while (true);
		}
		return acctList;
	}

}
