package com.aisino.app.web.netschool;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.Reader;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.aisino.platform.veng.json.dauglas.JSONException;
import com.aisino.platform.veng.json.dauglas.JSONObject;


/**
 * Servlet implementation class NetSchoolServlet
 */
public class NetSchoolServlet extends HttpServlet {
	private static final long serialVersionUID = 1L;

	/**
	 * @see HttpServlet#service(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void service(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		
		String phone = request.getParameter("phoneNumber");
		Map<String, Object> paramMap = new HashMap<String, Object>();
		String url = "http://www.htxxpx.com/interfaces/getToken";
		paramMap.put("accessKey", "heZ8m3P4izwmOaJSIe0i2VUyQvMbwkEj");
		paramMap.put("mobile",phone);
		paramMap.put("password","123456");
		paramMap.put("type", "login");
		String data = null;
		try {
			data = post(url,paramMap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println(data);
		JSONObject json = null;
		String responseUrl = null;
		try {
			json = new JSONObject(data);
			responseUrl ="http://www.htxxpx.com/interfaces/login"
					+ "?token="+json.getString("token")
					+ "&mobile="+phone
					+ "&timestamp="+json.getString("timestamp")
					+ "&schoolId="+json.getString("schoolId")
					+ "&companyId="+json.getString("companyId");
		} catch (JSONException e) {
			e.printStackTrace();
		}
		response.sendRedirect(responseUrl);
	}
	public static String post(String urlStr, Map<String, Object> paramMap)
			throws Exception {
		HttpURLConnection conn = null;
		PrintWriter writer = null;
		try {
			// 创建 URL 对象
			URL url = new URL(urlStr);
			// 获取请求参数
			String param = getParamString(paramMap);
			// 获取 URL 连接
			conn = (HttpURLConnection) url.openConnection();
			// 设置通用请求属性
			setHttpUrlConnection(conn, "POST");
			// 建立实际的连接
			conn.connect();
			// 将请求参数写入请求字符流中
			writer = new PrintWriter(conn.getOutputStream());
			writer.print(param);
			writer.flush();
			// 读取响应的内容
			return readResponseContent(conn.getInputStream());
		} finally {
			if (null != conn)
				conn.disconnect();
			if (null != writer)
				writer.close();
		}
	}

	private static void setHttpUrlConnection(HttpURLConnection conn,
			String requestMethod) throws ProtocolException {
		conn.setRequestMethod(requestMethod);
		conn.setRequestProperty("accept", "*/*");
		conn.setRequestProperty("Accept-Language", "zh-CN");
		conn.setRequestProperty("User-Agent",
				"Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0)");
		conn.setRequestProperty("Proxy-Connection", "Keep-Alive");
		if (null != requestMethod && "POST".equals(requestMethod)) {
			conn.setDoOutput(true);
		}
	}

	private static String getParamString(Map<String, Object> paramMap) {
		if (null == paramMap || paramMap.isEmpty()) {
			return "";
		}
		StringBuilder builder = new StringBuilder();
		for (String key : paramMap.keySet()) {
			builder.append("&").append(key).append("=")
					.append(paramMap.get(key));
		}
		return builder.deleteCharAt(0).toString();
	}

	private static String readResponseContent(InputStream in)
			throws IOException {
		Reader reader = null;
		StringBuilder content = new StringBuilder();
		try {
			reader = new InputStreamReader(in);
			char[] buffer = new char[1024];
			int head = 0;
			while ((head = reader.read(buffer)) > 0) {
				content.append(new String(buffer, 0, head));
			}
			return content.toString();
		} finally {
			if (null != in)
				in.close();
			if (null != reader)
				reader.close();
		}
	}
}
