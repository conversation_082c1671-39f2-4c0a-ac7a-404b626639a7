<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE Forms SYSTEM "form_definition.dtd">
<Forms>
	<form id="common_mobile_zjm" extend="Widzard" desp="助记码检索">
        <widgets>   
        	<basic name="title" label="助记码检索"></basic>
			<basic name="path" default="001,002"></basic>   
			<layout type="TableLayout" name="001" attr="groupSize:1" layout="panel" desp="执行">
				<basic name="a" widget="Title"  default="本功能用于航天信息ERP移动端，执行本操作将对移动端客户列表生成检索助记码。" ></basic>
				<basic name="a1" widget="Title"  default="客户列表数据量大时，执行时间稍长。确认执行请点击下一步。" inline="single" ></basic>
		
			</layout>  
			<layout type="TableLayout" name="002" attr="groupSize:1" layout="panel" desp="结果">	
				<basic name="bar" widget="ProgressBar" ishide="true"  height="10px" width="500px" label="正在执行脚本文件,请稍候..."/>
			</layout>  
        </widgets>
        <extendPoint>
        	<plugin type="com.aisino.app.web.customer.MobileUpdteChotcodePlugin" onEvent="updateDb"></plugin>
        </extendPoint>
        
        <bind element="this" event="onCreate" extendway="after"><![CDATA[		   
		     w('last').hiding();	        
		]]></bind>	
        
        
		<bind element="next" event="click" extendway="before"><![CDATA[
			var bar = PT.wid('bar');
	    	bar.showing();
			bar.start();
			PT.as('updateDb',null,null,
				function(result){
					bar.finish('脚本执行完成'); 
					PT.alert('脚本执行完成'); 
					bar.hiding();
				}
			);
		]]></bind>
		
		
	</form>	
</Forms>