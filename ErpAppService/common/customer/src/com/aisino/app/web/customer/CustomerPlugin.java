package com.aisino.app.web.customer;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.SessUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class CustomerPlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		DbSvr dbSvr = DbSvr.getDbService(null);
	//	String cguid=bus.getString("cguid");
		Date date = new Date();
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String ddate = df.format(date);
		bus.put("ddate", ddate);
		List<Map> list = dbSvr.queryIdForList("mobile_customer_crm.customer",bus);
		Map map = new HashMap();
		map.put("customer", list);
		form.setReturn(map);

	}
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		
	}
}
