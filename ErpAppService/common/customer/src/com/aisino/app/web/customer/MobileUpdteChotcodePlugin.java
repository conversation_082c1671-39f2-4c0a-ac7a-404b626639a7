package com.aisino.app.web.customer;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.SubmitListener;
import net.sourceforge.pinyin4j.PinyinHelper;

/**
 * 
 * 用于UDP用户维护移动端使用的CmobileCode值。
 * 
 */
public class MobileUpdteChotcodePlugin implements SubmitListener {
	private static final long serialVersionUID = -5579897525368176711L;
	private int BATCH_NUMBER = 10000; //批量更新数目

	/**
	 * 查询数据由于一次性全部取出，当数据量较大时，可能对服务器造成较大的压力， 及时调用System.gc()主动提醒JVM进行一次资源回收。
	 */
	@SuppressWarnings("rawtypes")
	@Override
	public void doSubmitAction(AbstractForm arg0, DataMsgBus arg1) {
		try {
			System.gc();
			String getListSql = "select cguid, cname from cm_customer";
			String update = "update cm_customer set cmobilecode = ? where cguid = ?";
			DbSvr dbSvr = DbSvr.getDbService(null);
			List<Map> result = dbSvr.getListResult(getListSql);
			List<Object[]> nlist = new ArrayList<Object[]>();

			for (Map m : result) {
				Object[] temp = new Object[2];
				temp[0] = getPinyin((String) m.get("cname"));
				temp[1] = m.get("cguid");
				nlist.add(temp);
			}
			
			result = null;
			int size = nlist.size();
			
			for (int i = 0; i < size; i = i + BATCH_NUMBER) {
				if ((i + BATCH_NUMBER) < size)
					dbSvr.batchUpdate(update, nlist.subList(i, i + BATCH_NUMBER));
				else
					dbSvr.batchUpdate(update, nlist.subList(i, size));
			}

			nlist.clear();
			System.gc();

		} catch (Exception e) {
			System.gc();
		}

	}

	/**
	 * 获取字符串首字母拼音
	 * 
	 * @param name
	 * @return
	 */
	private String getPinyin(String name) {
		char[] chars = name.toCharArray();
		StringBuffer str = new StringBuffer();
		for (char ch : chars) {
			char[] chs = getFirstByChar(ch);
			// 多音字时默认只取第一拼音
			if (chs.length > 0) {
				str.append(chs[0]);
			}
		}
		// 如果长度超过50，则只截取前50位
		if (str.length() > 50)
			return str.substring(0, 50);
		return str.toString();
	}

	/**
	 * 获取字符拼音首字母
	 * 
	 * @param src
	 * @return
	 */
	private char[] getFirstByChar(char src) {
		// 如果不是汉字直接返回
		if (src <= 128) {
			return new char[] { src };
		}
		if ("·".equals(src + "")) {
			return new char[] { src };
		}

		// 获取所有的拼音
		String[] pinyingStr = PinyinHelper.toHanyuPinyinStringArray(src);
		if (pinyingStr == null) {
			return new char[] {};
		}
		// 创建返回对象
		char[] headChars = new char[pinyingStr.length];
		int i = 0;
		// 截取首字符
		for (String s : pinyingStr) {
			headChars[i++] = Character.toLowerCase(s.charAt(0));
		}
		return headChars;
	}

}
