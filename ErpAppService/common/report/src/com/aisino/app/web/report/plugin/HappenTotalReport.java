package com.aisino.app.web.report.plugin;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.app.web.report.util.ReportUtil;
import com.aisino.platform.core.Plugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class HappenTotalReport extends Plugin implements FormCreateListener {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@SuppressWarnings("rawtypes")
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		Map<String,Object> returnMap = new HashMap<String, Object>();
		DbSvr db = DbSvr.getDbService(null);
		Map preiod = db.getOneRecorder("select * from co_sysinit where csubsyscode='GL'");
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		if(preiod==null){
			//未启用总账管理模块时需要提示
			returnMap.put("error", "未启用总账管理模块");
			form.setReturn(returnMap);
		}else{
			List<String> legend = new ArrayList<String>();
			legend.add("支出");
			legend.add("收入");
			//legend.add("利润");
			returnMap.put("legend", legend);
			List<String> x = new ArrayList<String>();
			Map<String,Object> series = new HashMap<String, Object>();
			List<Object> debit = new ArrayList<Object>();
			List<Object> credit = new ArrayList<Object>();
			Map<String,Object> debitmap = new HashMap<String, Object>();
			Map<String,Object> creditmap = new HashMap<String, Object>();
			//List<String> data = new ArrayList<String>();
			double iInitYear = CollectionUtil.getNumber(preiod, "iinityear");
			double iInitMonth = CollectionUtil.getNumber(preiod, "iinitmonth");
			//查询时钟
			Calendar clock = Calendar.getInstance();
			clock.setTime(new Date());
			Calendar endClock = Calendar.getInstance();
			endClock.setTime(new Date());
			
			//启用时间
			Calendar init = Calendar.getInstance();
			init.set(Calendar.YEAR, new Double(iInitYear).intValue());
			init.set(Calendar.MONTH, new Double(iInitMonth).intValue()-1);
			init.set(Calendar.DAY_OF_MONTH, 1);
			//查询维度day.week.month.quarter.year
			//查询范围当前期 向前取10个刻度
			String type = bus.getString("type");
			ReportUtil.setQueryClock(type, clock, endClock);
			System.out.println("endClock========>"+df.format(endClock.getTime()));
			Map<String,String> paramstype = new HashMap<String,String>();
			paramstype.put("typetime", df.format(clock.getTime()));
			paramstype.put("endtime", df.format(endClock.getTime()));
			List<Map> list = db.queryIdForList("mobile_report_happen.total_"+type, paramstype);
			Map<String, Map> dataMap = new HashMap<String, Map>();
			if(list!=null){
				for (Map map : list) {
					String t = CollectionUtil.getStringFromMap(map, "t");
					dataMap.put(t, map);
				}
			}
			Calendar tempclock = Calendar.getInstance();
			for (int i = 0; i < ReportUtil.clock; i++) {
				tempclock.setTime(clock.getTime());
				String key = ReportUtil.getClockKey(type, clock);
				String showname  = "";
				if ("week".equals(type)) {
					x.add(ReportUtil.getXWeekKey(tempclock));
					showname = ReportUtil.getWeekFullKey(tempclock);
				} else {
					x.add(ReportUtil.getXKey(type, key));
					showname = ReportUtil.getFullKey(type, key);
				}
				debitmap = new HashMap<String, Object>();
				creditmap = new HashMap<String, Object>();
				debitmap.put("showname", showname);
				creditmap.put("showname", showname);
				
				if(!clock.getTime().before(init.getTime())){
					Map info = dataMap.get(key);
					if(info!=null){
						BigDecimal d = CollectionUtil.getBigDecimal(info, "debit");
						BigDecimal c = CollectionUtil.getBigDecimal(info, "credit");

						debitmap.put("value", d.setScale(2).toString());
						creditmap.put("value", c.multiply(new BigDecimal("-1")).setScale(2).toString());
						debitmap.put("showvalue", ReportUtil.formatBigDecimal(d));
						creditmap.put("showvalue", ReportUtil.minusFormatBigDecimal(c.multiply(new BigDecimal("-1"))));
						//data.add(d.subtract(c).setScale(2).toString());
					}else{

						debitmap.put("value", "0.00");
						creditmap.put("value", "0.00");
						debitmap.put("showvalue", "0.00");
						creditmap.put("showvalue", "0.00");
						//data.add("0.00");
					}
				}else{

					debitmap.put("value", "0.00");
					creditmap.put("value", "0.00");
					//data.add("0.00");
				}
				debit.add(debitmap);
				credit.add(creditmap);
			}
			series.put("data_收入", debit);
			series.put("data_支出", credit);
			//series.put("data_利润", data);
			returnMap.put("x", x);
			returnMap.put("series", series);
			returnMap.put("cur", ReportUtil.getCurKey(type));
			form.setReturn(returnMap);
		}
		
	}
	

	
	@Override
	public void setValue(String name, String value) {

	}

}
