package com.aisino.app.web.report.plugin;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.dotasklist.common.util.DotaskListUtil;
import com.aisino.app.web.report.util.ReportUtil;
import com.aisino.platform.acs.service.SecurityService;
import com.aisino.platform.core.Plugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.SessUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetApPaymentListPlugin extends Plugin implements FormCreateListener {
	/**
	 * 应付列表
	 * 2018-10-16
	 */
	private static final long serialVersionUID = 1L;
	@SuppressWarnings("rawtypes")
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		Map<String,Object> returnMap = new HashMap<String, Object>();
		DbSvr db = DbSvr.getDbService(null);
		//判断A6还是A3
		String appName = SessUtil.getRequest().getContextPath();
		Map preiod = null;
		if("/A6".equals(appName)){
			preiod = db.getOneRecorder("select * from co_sysinit where csubsyscode='AR'");
		}else if("/A3".equals(appName)){
			preiod = db.getOneRecorder("select * from co_sysinit where csubsyscode = 'SC'");
		}
		
		if(preiod==null){
			//未启用应收模块时需要提示
			returnMap.put("error", "未启用应付管理模块");
			form.setReturn(returnMap);
		}else{
			//查询条件
			String clockStart = null;
			int past = 0;
			String saleType=bus.getString("saleType")==null?"":bus.get("saleType").toString();;
			//如果saleType为空，即使用查询条件
			if("".equals(saleType)){
				if("0".equals(bus.getString("searchtype"))){
					if("".equals(bus.getString("yearstart"))){
						throw new BusinessException("请输入会计期间开始年。");
					}
					if("".equals(bus.getString("periodstart"))){
						throw new BusinessException("请输入会计期间开始期。");
					}
					if("".equals(bus.getString("yearend"))){
						throw new BusinessException("请输入会计期间结束年。");
					}
					if("".equals(bus.getString("periodend"))){
						throw new BusinessException("请输入会计期间期。");
					}
				}else if("1".equals(bus.getString("searchtype"))){
					if("".equals(bus.getString("startdate")))
						throw new BusinessException("请输入开始日期。");
					if("".equals(bus.getString("enddate")))
						throw new BusinessException("请输入结束日期。。");
				}else{
					throw new BusinessException("查询方案不对，请从新传值。");
				}
					
				if("".equals(bus.getString("ccustguid")))
					bus.put("ccustguid", null);
				if("".equals(bus.getString("cdeptguid")))
					bus.put("cdeptguid", null);
				if("".equals(bus.getString("cempguid")))
					bus.put("cempguid", null);
			}else{
				if("today".equalsIgnoreCase(saleType)){
					past =0;
				}else if("week".equalsIgnoreCase(saleType)){
					past=7;
				}else if("month".equalsIgnoreCase(saleType)){
					past=30; //近30天
				}
				clockStart = DotaskListUtil.getPastDate(past);
				bus.put("startdate", clockStart);
				bus.put("enddate", null);
			}	
			//查询角度
			String searchangle = bus.getString("searchangle")==null?"":bus.getString("searchangle");
			//客户==供应商
			bus.setControlInfo("pt_control_currentpage",bus.getString("curpage"));
			bus.setControlInfo("pt_control_pagesize","100");
			//分页查列表
			List<Map> list = null;
			List<Map> listxj = null;
			if("1".equals(searchangle)){//按部门
				list = db.queryIdForListByPage("mobile_list_ap_payment_sql.ApPaymentListbyDepartment",bus);
				listxj = db.queryIdForList("mobile_list_ap_payment_sql.ApPaymentdecrsumbyDepartment", bus);
			}else if("2".equals(searchangle)){//按职员
				list = db.queryIdForListByPage("mobile_list_ap_payment_sql.ApPaymentListbyEmployee",bus);
				listxj = db.queryIdForList("mobile_list_ap_payment_sql.ApPaymentdecrsumbyEmployee", bus);
			}else if("3".equals(searchangle)){//按项目
				list = db.queryIdForListByPage("mobile_list_ap_payment_sql.ApPaymentListbyProject",bus);
				listxj = db.queryIdForList("mobile_list_ap_payment_sql.ApPaymentdecrsumbyProject", bus);
			}else{//（默认）按客户				
				list = db.queryIdForListByPage("mobile_list_ap_payment_sql.ApPaymentList",bus);
				listxj = db.queryIdForList("mobile_list_ap_payment_sql.ApPaymentdecrsumbyCustomer", bus);
			}
			if(list!=null){
				boolean addflag = this.bottomAddsum(list,listxj,searchangle);
				String initbalance = "0.00";
				String rowbalance = "0.00";
				for(int i=0;i<list.size();i++){
					Map m = list.get(i);
					if("0".equals(m.get("iperiod").toString())){
						if("/A6".equals(appName)){//累加计算期初balance，可能是多行
							BigDecimal initbala = new BigDecimal(initbalance.replace(",", ""));
							BigDecimal debitamt = new BigDecimal(m.get("debitamt")==null?"0.00":m.get("debitamt").toString().replace(",", ""));
							initbala = initbala.add(debitamt);
							initbalance = fmtMicrometer(initbala.toString());
							if(i!=0){
								list.remove(m);
								i--;
							}
						}else if("/A3".equals(appName)){//直接取查出来的balance
							initbalance = m.get("balabce").toString();
							//判断期初balance是否为0
						}
						if(i<list.size()-1 && !"0".equals(list.get(i+1).get("iperiod").toString())){
							//期初余额为0的不显示期初行
							if("0.00".equals(initbalance)){
								list.remove(m);
								i--;
							}else{
								Map initmap = list.get(i);
								initmap.put("invoicetype", "期初余额");
								initmap.put("balabce", initbalance);
								initmap.put("cbillcode","");
								initmap.put("ddate","");
								initmap.put("iperiod","");
								initmap.put("debitamt","");
								initmap.put("creditamt","");
								initmap.put("closeflag", "1");
								rowbalance = initbalance;
							}
							initbalance = "0.00";
						}else{
							if("0.00".equals(initbalance)){
								list.remove(m);
								i--;
							}
						}
						continue;
					}
					
					// 余额计算需要用公式计算
					if(!"0".equals(m.get("iperiod").toString())){
						String rbal = countrowbalance(rowbalance,m.get("debitamt")==null?"0.00":m.get("debitamt").toString(),m.get("creditamt")==null?"0.00":m.get("creditamt").toString());
						rowbalance = rbal;
						m.put("balabce", rbal);						
					}
					
					//返给前台明细是否能展开（为0的期初与合计不能展开）
					m.put("closeflag", "0");
					//分角度查询判断客户，部门，职员，项目是否一致
					String angstr = "";
					if("1".equals(searchangle)){//部门deptcode
						angstr = "deptcode";
					}else if("2".equals(searchangle)){//职员
						angstr = "empcode";
					}else if("3".equals(searchangle)){//项目
						angstr = "procode";
					}else{
						angstr = "custcode";
					}
					//判断下一行的客户/部门/职员/项目 是否和上一行的一致
					String angcode = m.get(angstr)==null?"":m.get(angstr).toString();
					if(i<list.size()-1){
						String nextangcode = list.get(i+1).get(angstr)==null?"":list.get(i+1).get(angstr).toString();
						if(!angcode.equals(nextangcode)){
							//客户/部门等改变时，添加对应客户/部门的小计行
							list.add(i+1, this.addxjh(listxj, searchangle, angcode));
							i=i+1;
							rowbalance = "0.00";
						}
					}else{//list最后一行时，判断是否在本行后添加合计行
						if(addflag){
							list.add(i+1, this.addxjh(listxj, searchangle, angcode));
							i=i+1;//不能删，退出循环							
						}
					}
					
				}
			}
			
			/* 应收总金额的查询 */
			String debitamtsum = db.queryIdForString("mobile_list_ap_payment_sql.ApPaymentdebitamtsum", bus);
			if(debitamtsum==null){
				debitamtsum = "0.00";
			}
			/* 实收总金额的查询 */
			String creditamtsum = db.queryIdForString("mobile_list_ap_payment_sql.ApPaymentcreditamtsum", bus);
			if(creditamtsum==null){
				creditamtsum = "0.00";
			}
			//组装前台返回map
			Map map = new HashMap();
			map.put("list", list);
			map.put("debitamtsum", debitamtsum);
			map.put("creditamtsum", creditamtsum);
			map.put("balabcesum", countbalabce(debitamtsum,creditamtsum));
			form.setReturn(map);
		}
		
	}
	
	//公式计算余额
	public String countrowbalance(String initbal, String rdebit, String rcredit){
		BigDecimal biginitys = new BigDecimal(initbal.replace(",", ""));
		BigDecimal bigrowys = new BigDecimal(rdebit.replace(",", ""));
		BigDecimal bigrowss = new BigDecimal(rcredit.replace(",", ""));
		BigDecimal bigys = biginitys.add(bigrowys);
		BigDecimal bigrbal = bigys.subtract(bigrowss);
		return fmtMicrometer(bigrbal.toString());
	}
	
	//计算差
	public String countbalabce(String ys,String ss){
		BigDecimal bigys = new BigDecimal(ys.replace(",", ""));
		BigDecimal bigss = new BigDecimal(ss.replace(",", ""));
		BigDecimal bigye = bigys.subtract(bigss);
		return fmtMicrometer(bigye.toString());
	}
	
	//添加小计行
	public Map addxjh(List<Map> listxj,String searchangle,String custcode){
		Map xjmap = new HashMap();
		for(Map xjm : listxj){
			if(custcode.equals(xjm.get("custcode")==null?"":xjm.get("custcode").toString())){
				xjmap.put("custcode", xjm.get("custcode".toString()));
				xjmap.put("custname", xjm.get("custname".toString()));
				if("1".equals(searchangle)){
					xjmap.put("invoicetype", "部门["+xjm.get("custname")==null?"":xjm.get("custname".toString())+"]小计");
				}else if("2".equals(searchangle)){
					xjmap.put("invoicetype", "职员["+xjm.get("custname")==null?"":xjm.get("custname".toString())+"]小计");
				}else if("3".equals(searchangle)){
					xjmap.put("invoicetype", "项目p"+xjm.get("custname")==null?"":xjm.get("custname".toString())+"]小计");
				}else{
					xjmap.put("invoicetype", "客户["+xjm.get("custname".toString())+"]小计");					
				}
				xjmap.put("closeflag", "1");
				xjmap.put("debitamt", xjm.get("debitamtsum").toString());
				xjmap.put("creditamt", xjm.get("creditamtsum").toString());
				xjmap.put("balabce", countbalabce(xjm.get("debitamtsum").toString(),xjm.get("creditamtsum").toString()));
			}
		}
		return xjmap;
	}
	
	//计算同一客户/部门/职员/项目 当页应收，实收合计，判断是否在最后一行后添加合计
	public boolean bottomAddsum(List<Map> list, List<Map> listxj, String searchangle){
		boolean addflag = false;
		String codestr = "";
		BigDecimal yssum = new BigDecimal("0.00");
		BigDecimal sssum = new BigDecimal("0.00");
		if("1".equals(searchangle)){
			codestr = "deptcode";
		}else if("2".equals(searchangle)){
			codestr = "empcode";
		}else if("3".equals(searchangle)){
			codestr = "procode";
		}else{
			codestr = "custcode";
		}
		if(list.size()<100){
			addflag=true;
			return addflag;
		}
		String anglestr = list.get(list.size()-1).get(codestr)==null?"":list.get(list.size()-1).get(codestr).toString();
		if(!"0".equals(list.get(list.size()-1).get("iperiod").toString())){
			for(int i=list.size();i>0;i--){
				Map listm = list.get(i-1);
				if(anglestr.equals(listm.get(codestr)==null?"":listm.get(codestr).toString())){
					BigDecimal rowys = new BigDecimal(listm.get("debitamt")==null?"0.00":listm.get("debitamt").toString().replace(",", ""));
					BigDecimal rowss = new BigDecimal(listm.get("creditamt")==null?"0.00":listm.get("creditamt").toString().replace(",", ""));
					yssum = yssum.add(rowys);
					sssum = sssum.add(rowss);
				}else{//当前页不止一个客户/部门...
					for(Map xjm : listxj){
						if(anglestr.equals(xjm.get(codestr)==null?"":xjm.get(codestr).toString())){
							BigDecimal xjys = new BigDecimal(xjm.get("debitamtsum").toString().replace(",", ""));
							BigDecimal xjss = new BigDecimal(xjm.get("creditamtsum").toString().replace(",", ""));
							if(yssum.compareTo(xjys)==0 && sssum.compareTo(xjss)==0){
								addflag = true;
							}
							break;
						}
					}
					break;
				}
			}
			//当前页只有一个客户/部门...
			for(Map xjm : listxj){
				if(anglestr.equals(xjm.get(codestr)==null?"":xjm.get(codestr).toString())){
					BigDecimal xjys = new BigDecimal(xjm.get("debitamtsum").toString().replace(",", ""));
					BigDecimal xjss = new BigDecimal(xjm.get("creditamtsum").toString().replace(",", ""));
					if(yssum.compareTo(xjys)==0 && sssum.compareTo(xjss)==0){
						addflag = true;
						break;
					}else{
						break;						
					}
				}
			}
		}
		return addflag;
	}
	
	
	//格式化数字为千分位显示 
	public static String fmtMicrometer(String text){  
	    DecimalFormat df = null;  
	    if(text.indexOf(".") > 0){
	        if(text.length() - text.indexOf(".")-1 == 0){  
	            df = new DecimalFormat("###,##0.");  
	        }else if(text.length() - text.indexOf(".")-1 == 1) {  
	            df = new DecimalFormat("###,##0.0");  
	        }else{  
	            df = new DecimalFormat("###,##0.00");  
	        }  
	    }else{
	        df = new DecimalFormat("###,##0");  
	    }  
	    double number = 0.0;  
	    try {  
	         number = Double.parseDouble(text);  
	    }catch (Exception e) {  
	        number = 0.0;  
	    }  
	    return df.format(number);  
	} 
	
	@Override
	public void setValue(String name, String value) {
		
	}

}
