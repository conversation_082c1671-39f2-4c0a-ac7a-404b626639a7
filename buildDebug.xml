<?xml version="1.0" ?>

<project default="build" basedir=".">
	<tstamp>
	   <format property="TODAY_UK_M" pattern="yyyyMMdd" locale="en"/>
	   <format property="TODAY_UK" pattern="yyyyMMdd.HHmm" locale="en"/> 
	</tstamp>

	<!-- Name of project and version -->
	<property name="com.name" value="Aisino" /> 	<!-- 公司名 -->
	<property name="proj.name" value="A6" />		<!-- 项目名 -->
	<property name="module.version" value="6.0" />	<!-- 版本号 -->

	<!-- Global properties for this build -->
	<property name="lib.dir" location="D:\dailybuild\A6\6.0" />				                                                <!-- lib目录 -->
	<property name="tomcat.lib.dir" location="D:\dailybuild\apache-tomcat-6.0.10/lib"/>                                            <!-- <PERSON><PERSON>的lib目录 -->	
		
	<property name="build.dir" location="${basedir}\temp" />
	<property name="upload.dir" location="\\**************\A6_v6.0Debug" />	
	<property name="my.build.classes.dir" location="${build.dir}/classes" />
	<property name="remote.java.dir" location="${basedir}\BuildResult\${TODAY_UK}\DEBUG"/>

	<!-- Classpath declaration -->
	<path id="project.classpath">
		<fileset dir="${remote.java.dir}">
			<include name="**/*.jar" />
		</fileset>	
		<fileset dir="${lib.dir}">
			<include name="**/*.jar" />
			<include name="**/*.zip" />
		</fileset>
		<fileset dir="${tomcat.lib.dir}">
			<include name="**/*.jar" />
		</fileset>		
	</path>

	<!-- Useful shortcuts -->
	<patternset id="meta.files">
		<include name="**/*.xml" />
		<include name="**/*.dtd" />
		<include name="**/*.js" />
		<include name="**/*.css" />
		<include name="**/*.gif" />
		<include name="**/*.sql" />
		<include name="**/*.jpg" />
		<include name="**/*.png" />
		<include name="**/*.xls" />
		<include name="**/*.cll" />
		<include name="**/*.vbs" />
		<include name="**/*.html" />
		<include name="**/*.cab" />
		<include name="**/*.jpeg" />		
	</patternset>
	
	<target name="copy">
	
     	<echo>复制构建结果</echo>
		
		<copy todir="${upload.dir}">
			<fileset dir="${remote.java.dir}">
			</fileset>			
		</copy>
		<copy todir="D:\hudson\BuildResult\${TODAY_UK}\DEBUG">
			<fileset dir="${remote.java.dir}">
			</fileset>			
		</copy>		
		
		<echo>-----------------BUILD SUCCESSFUL-------------------------------</echo>		
	</target>
	

	<target name="build">
		<echo>-----------------prepare to build Debug------------------------</echo>
		<antcall target="clear" />
		<antcall target="create" />

		<echo>-----------------building A6COMMON-------------------------------</echo>
		<antcall target="jar">
		    <param name="module.name" value="A6COMMON" />	
		</antcall>
		<echo>-----------------A6COMMON build ok----------------------------</echo>
	    <echo>-----------------building A6Reference-------------------------------</echo>
		<antcall target="jar5">
		    <param name="module.name" value="A6Reference" />	
		</antcall>
		<echo>-----------------A6Reference build ok----------------------------</echo>
		<echo>-----------------building UTIL-------------------------------</echo>
		<antcall target="jar3">
		    <param name="module.name" value="UTIL" />
		    <param name="sub.name" value="import" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="UTIL" />
		    <param name="sub.name" value="sysinit" />	
		</antcall>
		<echo>-----------------UTIL build ok----------------------------</echo>	
		<echo>-----------------building A6BUSINESSTEM-------------------------------</echo>
		<antcall target="jar">
		    <param name="module.name" value="A6BUSINESSTEM" />	
		</antcall>
		<echo>-----------------A6BUSINESSTEM build ok----------------------------</echo>	
		
		<echo>-----------------building BUSBASE-------------------------------</echo>
		<antcall target="jar3">
		    <param name="module.name" value="BUSBASE" />
		    <param name="sub.name" value="common" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="BUSBASE" />
		    <param name="sub.name" value="scm" />	
		</antcall>		
		<echo>-----------------BUSBASE build ok----------------------------</echo>

		<echo>-----------------building ARP-------------------------------</echo>
		<antcall target="jar3">
		    <param name="module.name" value="ARP" />
		    <param name="sub.name" value="util" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="ARP" />
		    <param name="sub.name" value="init" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="ARP" />
		    <param name="sub.name" value="cancel" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="ARP" />
		    <param name="sub.name" value="cashbill" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="ARP" />
		    <param name="sub.name" value="close" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="ARP" />
		    <param name="sub.name" value="receivablebill" />	
		</antcall>	
		<antcall target="jar3">
		    <param name="module.name" value="ARP" />
		    <param name="sub.name" value="statistic" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="ARP" />
		    <param name="sub.name" value="exch" />	
		</antcall>	
		<echo>-----------------ARP build ok----------------------------</echo>
		
		<echo>-----------------building ST-------------------------------</echo>
		<antcall target="jar3">
		    <param name="module.name" value="ST" />
		    <param name="sub.name" value="util" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="ST" />
		    <param name="sub.name" value="storagebill" />	
		</antcall>		
		<antcall target="jar3">
		    <param name="module.name" value="ST" />
		    <param name="sub.name" value="init" />	
		</antcall>		
		<antcall target="jar3">
		    <param name="module.name" value="ST" />
		    <param name="sub.name" value="adjustmentbill" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="ST" />
		    <param name="sub.name" value="assembly" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="ST" />
		    <param name="sub.name" value="statistic" />	
		</antcall>	
				<antcall target="jar3">
		    <param name="module.name" value="ST" />
		    <param name="sub.name" value="account" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="ST" />
		    <param name="sub.name" value="shapechange" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="ST" />
		    <param name="sub.name" value="barcode" />	
		</antcall>
		<echo>-----------------ST build ok----------------------------</echo>		
		
		<echo>-----------------building OM-------------------------------</echo>
		<antcall target="jar3">
		    <param name="module.name" value="OM" />
		    <param name="sub.name" value="common" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="OM" />
		    <param name="sub.name" value="invoice" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="OM" />
		    <param name="sub.name" value="order" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="OM" />
		    <param name="sub.name" value="settle" />	
		</antcall>
		<antcall target="jar4">
		    <param name="module.name" value="OM" />
		    <param name="sub.name" value="statistic" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="OM" />
		    <param name="sub.name" value="stin" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="OM" />
		    <param name="sub.name" value="price" />	
		</antcall>			
		<echo>-----------------OM build ok----------------------------</echo>
		
		<echo>-----------------building PU-------------------------------</echo>
		<antcall target="jar3">
		    <param name="module.name" value="PU" />
		    <param name="sub.name" value="util" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="PU" />
		    <param name="sub.name" value="plan" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="PU" />
		    <param name="sub.name" value="invoice" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="PU" />
		    <param name="sub.name" value="order" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="PU" />
		    <param name="sub.name" value="statistic" />	
		</antcall>	
		<antcall target="jar3">
		    <param name="module.name" value="PU" />
		    <param name="sub.name" value="stkin" />	
		</antcall>
				<antcall target="jar3">
		    <param name="module.name" value="PU" />
		    <param name="sub.name" value="init" />	
		</antcall>
				<antcall target="jar3">
		    <param name="module.name" value="PU" />
		    <param name="sub.name" value="settle" />	
		</antcall>
						<antcall target="jar3">
		    <param name="module.name" value="PU" />
		    <param name="sub.name" value="rebate" />	
		</antcall>
						<antcall target="jar3">
		    <param name="module.name" value="PU" />
		    <param name="sub.name" value="price" />	
		</antcall>
								<antcall target="jar3">
		    <param name="module.name" value="PU" />
		    <param name="sub.name" value="carriage" />	
		</antcall>
				<antcall target="jar3">
		    <param name="module.name" value="PU" />
		    <param name="sub.name" value="account" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="PU" />
		    <param name="sub.name" value="receive" />	
		</antcall>		
		<echo>-----------------PU build ok----------------------------</echo>		

		<echo>-----------------building SA-------------------------------</echo>
		<antcall target="jar3">
		    <param name="module.name" value="SA" />
		    <param name="sub.name" value="util" />	
		</antcall>	
					<antcall target="jar3">
		    <param name="module.name" value="SA" />
		    <param name="sub.name" value="saprice" />	
		</antcall>	
				<antcall target="jar3">
		    <param name="module.name" value="SA" />
		    <param name="sub.name" value="carriage" />	
		</antcall>	
						<antcall target="jar3">
		    <param name="module.name" value="SA" />
		    <param name="sub.name" value="consignsettle" />	
		</antcall>	
				<antcall target="jar3">
		    <param name="module.name" value="SA" />
		    <param name="sub.name" value="dispatch" />	
		</antcall>	
				<antcall target="jar3">
		    <param name="module.name" value="SA" />
		    <param name="sub.name" value="gx" />	
		</antcall>	
				<antcall target="jar3">
		    <param name="module.name" value="SA" />
		    <param name="sub.name" value="invoice" />	
		</antcall>	
				<antcall target="jar3">
		    <param name="module.name" value="SA" />
		    <param name="sub.name" value="order" />	
		</antcall>	
				<antcall target="jar3">
		    <param name="module.name" value="SA" />
		    <param name="sub.name" value="rebate" />	
		</antcall>	
				<antcall target="jar3">
		    <param name="module.name" value="SA" />
		    <param name="sub.name" value="retail" />	
		</antcall>	
				<antcall target="jar3">
		    <param name="module.name" value="SA" />
		    <param name="sub.name" value="statistic" />	
		</antcall>	
				<antcall target="jar3">
		    <param name="module.name" value="SA" />
		    <param name="sub.name" value="stkout" />	
		</antcall>	
				<antcall target="jar3">
		    <param name="module.name" value="SA" />
		    <param name="sub.name" value="znj" />	
		</antcall>	
				<antcall target="jar3">
		    <param name="module.name" value="SA" />
		    <param name="sub.name" value="account" />	
		</antcall>					
		<echo>-----------------SA build ok----------------------------</echo>
			
		<echo>-----------------building IA-------------------------------</echo>
		<antcall target="jar3">
		    <param name="module.name" value="IA" />
		    <param name="sub.name" value="util" />	
		</antcall>			
		<antcall target="jar3">
		    <param name="module.name" value="IA" />
		    <param name="sub.name" value="account" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="IA" />
		    <param name="sub.name" value="initfinal" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="IA" />
		    <param name="sub.name" value="accountbill" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="IA" />
		    <param name="sub.name" value="statistic" />	
		</antcall>			
		<echo>-----------------IA build ok----------------------------</echo>		
        
		<echo>-----------------building QC-------------------------------</echo>
		<antcall target="jar3">
		    <param name="module.name" value="QC" />
		    <param name="sub.name" value="defective" />	
		</antcall>			
		<antcall target="jar3">
		    <param name="module.name" value="QC" />
		    <param name="sub.name" value="macheck" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="QC" />
		    <param name="sub.name" value="pucheck" />	
		</antcall>
		<antcall target="jar3">
		    <param name="module.name" value="QC" />
		    <param name="sub.name" value="statistic" />	
		</antcall>	
		<echo>-----------------QC build ok----------------------------</echo>
	    <echo>-----------------building BI-------------------------------</echo>
		<antcall target="jar">
		    <param name="module.name" value="BI" />	
		</antcall>
		<echo>-----------------BI build ok----------------------------</echo>
		
		<echo>-----------------All DEBUG Build Ok----------------------------</echo>		
		<antcall target="copy" />		
		
	</target>

	
	<target name="jar">
	
		<mkdir dir="${my.build.classes.dir}\${module.name}" /> 
		
		<echo>编译类文件,来自: ${basedir}\${module.name}\src\src</echo>
		<javac srcdir="${basedir}\${module.name}\src" executable="D:\Java\jdk1.6.0_27\bin\javac" listfiles="true" 
			encoding="utf8" nowarn="true" debug="on" includeantruntime="on" destdir="${my.build.classes.dir}\${module.name}" 
			classpathref="project.classpath">
			<classpath refid="project.classpath" />
		</javac>	
	
        <echo>正在打包类文件</echo>

		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.jar">
			<fileset dir="${my.build.classes.dir}\${module.name}\" >
				<!-- 如果源码目录下有需要排除的类文件 
				<exclude name="com/**" />
				-->
			</fileset>
		<!--复制资源文件,来自: ${basedir}\${proj.name}\code\${module.name}\resource-->			
			<fileset dir="${basedir}\${module.name}\resource">
 				<patternset refid="meta.files" />
			</fileset>
		</jar>
		
	</target>

	<target name="jar2">
	
		<mkdir dir="${my.build.classes.dir}\${module.name}" /> 
		
		<echo>编译类文件,来自: ${basedir}\${module.name}\src\src</echo>
		<javac srcdir="${basedir}\${module.name}\src" executable="D:\Java\jdk1.6.0_27\bin\javac" listfiles="true" 
			encoding="utf8" nowarn="true"  debug="on" includeantruntime="on" destdir="${my.build.classes.dir}\${module.name}" 
			classpathref="project.classpath">
			<classpath refid="project.classpath" />
		</javac>	
	
        <echo>正在打包类文件</echo>

		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.jar">
			<fileset dir="${my.build.classes.dir}\${module.name}\" >
				<!-- 如果源码目录下有需要排除的类文件 
				<exclude name="com/**" />
				-->
			</fileset>
		<!--复制资源文件,来自: ${basedir}\${proj.name}\code\${module.name}\resource-->			
			<fileset dir="${basedir}\${module.name}\resource">
                      <patternset refid="meta.files" /> 
			</fileset>
		</jar>
		
		<echo>正在打包页面文件</echo>
		<zip destfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.zip" update="true" duplicate="preserve">
			<zipfileset dir="${basedir}\${module.name}\webapp" prefix="webapp"> 
			        <patternset refid="meta.files" />
			</zipfileset>
		</zip>
		
	</target>
	
	<target name="jar3">
	
		<mkdir dir="${my.build.classes.dir}\${module.name}\${sub.name}" /> 
		
		<echo>编译类文件,来自: ${basedir}\${module.name}\src\src</echo>
		<javac srcdir="${basedir}\${module.name}\${sub.name}\src" executable="D:\Java\jdk1.6.0_27\bin\javac" listfiles="true" 
			encoding="utf8" nowarn="true" debug="on" includeantruntime="on" destdir="${my.build.classes.dir}\${module.name}\${sub.name}" 
            classpathref="project.classpath" excludes="com/aisino/a6/business/cancel/upgrade/**/*.java">
			<classpath refid="project.classpath" />
		</javac>	
	
        <echo>正在打包类文件</echo>

		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${sub.name}-${module.version}.jar">
			<fileset dir="${my.build.classes.dir}\${module.name}\${sub.name}" >
				<!-- 如果源码目录下有需要排除的类文件 -->
				
			</fileset>
		<!--复制资源文件,来自: ${basedir}\${proj.name}\code\${module.name}\resource-->			
			<fileset dir="${basedir}\${module.name}\${sub.name}\resource">
                  <patternset refid="meta.files" />
			</fileset>
		</jar>
					
	</target>

	<target name="jar4">
	
		<mkdir dir="${my.build.classes.dir}\${module.name}\${sub.name}" /> 
	
        <echo>正在打包类文件</echo>

		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${sub.name}-${module.version}.jar">

		<!--复制资源文件,来自: ${basedir}\${proj.name}\code\${module.name}\resource-->			
			<fileset dir="${basedir}\${module.name}\${sub.name}\resource">
                 <patternset refid="meta.files" />
			</fileset>
		</jar>		
					
	</target>		
      <target name="jar5">	
		<mkdir dir="${my.build.classes.dir}\${module.name}" /> 
        <echo>正在打包类文件</echo>
		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.jar">
			<fileset dir="${my.build.classes.dir}\${module.name}\" >
				<!-- 如果源码目录下有需要排除的类文件 
				<exclude name="com/**" />
				-->
			</fileset>
		<!--复制资源文件,来自: ${basedir}\${proj.name}\code\${module.name}\resource-->			
			<fileset dir="${basedir}\${module.name}\resource">
 				<patternset refid="meta.files" />
			</fileset>
		</jar>	
	</target>
	<target name="create">
		<mkdir dir="${remote.java.dir}" />
		<mkdir dir="${upload.dir}" />
		<mkdir dir="D:\hudson\BuildResult\${TODAY_UK}\DEBUG" />  		
	</target>

	<target name="clear">
		<delete dir="${build.dir}\temp" />		
	</target>
	
	<target name="lib_copy">
        <delete>
            <fileset dir="${lib.dir}" includes="Aisino-A6-${module.name}-*.*"/>
        </delete>
		<copy todir="${lib.dir}">
			<fileset dir="${remote.java.dir}">
			    <include name="Aisino-A6-${module.name}-*.jar"/>
			</fileset>			
		</copy>		
	</target>	
	

</project>
