<?xml version="1.0"?>
<!-- AOS 6.0 访问控制包 -->
<project default="package" basedir=".">

	<tstamp prefix="build.">
		<format property="TimeSign" pattern="yyMMdd" />
	</tstamp>
	<!-- Name of project and version -->
	<property name="com.name" value="aisino" />
	<!-- 公司名 -->
	<property name="proj.name" value="Aisino-A6" />
	<!-- 项目名 -->
	<property name="module.name" value="update-all" />
	<!-- 模块名 -->
	<property name="time" value="" />
	<!-- 上午a下午b -->
	<property name="module.version" value="${build.TimeSign}" />
	<!-- 版本号 -->


	<!-- Global properties for this build -->
	<!-- 源码目录 -->
	<property name="my.src.java.dir" location="update/src" />
	<!-- 配置文件目录 -->
	<property name="my.src.res.dir" location="update/resource" />
	<property name="lib.dir" location="webapp/WEB-INF/lib" />
	<!-- lib目录 -->
	<property name="tomcat.lib.dir" location="C:\server\tomcat-6.0.29\lib" />
	<!-- Tomcat的lib目录 -->



	<property name="build.dir" location="bin/temp" />
	<property name="my.build.classes.dir" location="${build.dir}/classes" />
	<property name="jar.dir" location="." />
	<property name="remote.java.dir" location="C:\workspace\javaws\libs\update" />
	<property name="jar" value="${remote.java.dir}/${proj.name}-${module.name}-${module.version}-dev.jar" />
	<property name="obfjar" value="${remote.java.dir}/${proj.name}-${module.name}-${module.version}.jar" />

	<property name="renamelog" value="${remote.java.dir}/renamelog.xml" />
	<property name="shrinklog" value="${remote.java.dir}/shrinklog.xml" />



	<!-- Classpath declaration -->
	<path id="project.classpath">
		<fileset dir="${lib.dir}">
			<include name="**/*.jar" />
			<include name="**/*.zip" />
		</fileset>
		<fileset dir="${tomcat.lib.dir}">
			<include name="**/*.jar" />
		</fileset>

	</path>
	<!-- Useful shortcuts -->
	<patternset id="meta.files">
		<include name="**/*.xml" />
		<include name="**/*.dll" />
		<include name="**/*.sql" />
	</patternset>


	<!-- Clean up -->
	<target name="clean" description="Clean the build directory">
		<delete dir="${build.dir}" />
		<mkdir dir="${build.dir}" />
	</target>

	<!-- Compile Java source -->
	<target name="compile" depends="clean">
		<mkdir dir="${my.build.classes.dir}" />

		<echo>编译类文件,来自: ${my.src.java.dir}</echo>
		<!--
		jdk1.6.0_27  jdk1.7.0_21
		-->
		<javac source="1.7" fork="true" executable="C:\Program Files (x86)\Java\jdk1.7.0_21\bin\javac" 
			srcdir="${my.src.java.dir}" listfiles="true" encoding="utf8" nowarn="true" 
			destdir="${my.build.classes.dir}" classpathref="project.classpath" includeAntRuntime="false">
			<!-- 如果源码目录下有需要排除的类文件 
			<exclude name="com/aisino/platform/**" />
			-->
		</javac>

		<echo>复制资源文件,来自: ${my.src.res.dir}</echo>
		<copy todir="${my.build.classes.dir}">
			<fileset dir="${my.src.res.dir}">
				<patternset refid="meta.files" />
			</fileset>
		</copy>
		<copy todir="${my.build.classes.dir}">
			<fileset dir="${my.src.java.dir}/">
				<include name="**/*.rsa" />
			</fileset>
		</copy>
	</target>

	<target name="package" depends="compile">
		<echo>正在打包类文件</echo>
		<mkdir dir="${remote.java.dir}" />
		<jar jarfile="${jar}">
			<fileset dir="${my.build.classes.dir}">
			</fileset>
		</jar>

		<delete dir="${build.dir}">
		</delete>
		<echo>发布成功</echo>
	</target>


</project>
