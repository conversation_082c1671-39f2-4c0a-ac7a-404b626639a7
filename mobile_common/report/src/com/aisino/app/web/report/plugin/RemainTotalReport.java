package com.aisino.app.web.report.plugin;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.ObjectUtils;

import com.aisino.app.web.report.util.ReportUtil;
import com.aisino.platform.core.Plugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class RemainTotalReport extends Plugin implements FormCreateListener {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@SuppressWarnings("rawtypes")
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		//map包含的key  legend:list<String>; x:list<String>,series:map
		Map<String,Object> returnMap = new HashMap<String, Object>();
		DbSvr db = DbSvr.getDbService(null);
		Map preiod = db.getOneRecorder("select * from co_sysinit where csubsyscode='GL'");
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		if(preiod==null){
			//未启用总账管理模块时需要提示
			returnMap.put("error", "未启用总账管理模块");
			form.setReturn(returnMap);
		}else{
			List<String> legend = new ArrayList<String>();
			legend.add("资金余额");
			returnMap.put("legend", legend);
			List<String> x = new ArrayList<String>();
			Map<String,Object> series = new HashMap<String, Object>();
			List<Object> data = new ArrayList<Object>();
			Map<String,Object> datatemp = new HashMap<String, Object>();
			double iInitYear = CollectionUtil.getNumber(preiod, "iinityear");
			double iInitMonth = CollectionUtil.getNumber(preiod, "iinitmonth");
			//查询时钟
			Calendar clock = Calendar.getInstance();
			clock.setTime(new Date());
			Calendar endClock = Calendar.getInstance();
			endClock.setTime(new Date());
			//启用时间
			Calendar init = Calendar.getInstance();
			init.set(Calendar.YEAR, new Double(iInitYear).intValue());
			init.set(Calendar.MONTH, new Double(iInitMonth).intValue()-1);//月份是从0开始计算的
			init.set(Calendar.DAY_OF_MONTH, 1);
			//查询维度day.week.month.quarter.year
			//查询范围当前期 向前取10个刻度
			String type = bus.getString("type");
			//查期初余额
			Map<String,Number> paramsinit = new HashMap<String,Number>();
			paramsinit.put("iyear", iInitYear);
			paramsinit.put("imonth", iInitMonth);
			Number iInitAmt = db.queryIdForNumber("mobile_report_remain.total_init", paramsinit);
			ReportUtil.setQueryClock(type, clock, endClock);
			
			//查询凭证中的期初
			Map<String, String> paramstotalvou = new HashMap<String, String>();
			paramstotalvou.put("paramstime",df.format(clock.getTime()));
			Number iVouInit = db.queryIdForNumber("mobile_report_remain.total_vou", paramstotalvou);
			//凭证发生
			paramstotalvou.put("endTime", df.format(endClock.getTime()));
			List<Map> list = db.queryIdForList("mobile_report_remain.total_"+type, paramstotalvou);
			Map<String, BigDecimal> dataMap = new HashMap<String, BigDecimal>();
			if(list!=null){
				for (Map map : list) {
					String t = CollectionUtil.getStringFromMap(map, "t");
					BigDecimal vou = CollectionUtil.getBigDecimal(map, "remain");
					dataMap.put(t, vou);
				}
			}
			BigDecimal countAmt = new BigDecimal(ObjectUtils.toString(iInitAmt, "0"))
				.add(new BigDecimal(ObjectUtils.toString(iVouInit, "0")));
			Calendar clocktemp = Calendar.getInstance();
			Calendar clockweek = Calendar.getInstance();
			for (int i = 0; i < ReportUtil.clock; i++) {
				clockweek.setTime(clock.getTime());
				clocktemp = ReportUtil.getLastDay(type, clock);
				String key = ReportUtil.getClockKey(type, clock);
				String showname  = "";
				if ("week".equals(type)) {
					x.add(ReportUtil.getXWeekKey(clockweek));
					showname = ReportUtil.getWeekFullKey(clockweek);
				} else {
					x.add(ReportUtil.getXKey(type, key));
					showname = ReportUtil.getFullKey(type, key);
				}
				datatemp = new HashMap<String, Object>();
				datatemp.put("showname", showname);
				System.out.println("init============>"+init.getTime());
				System.out.println("clocktemp============>"+clocktemp.getTime());
				if(!clocktemp.getTime().before(init.getTime())){
					BigDecimal value = CollectionUtil.getBigDecimal(dataMap, key);
					countAmt = countAmt.add(value);
					datatemp.put("value", countAmt.setScale(2).toString());
					datatemp.put("showvalue", ReportUtil.formatBigDecimal(countAmt));
				}else{
					datatemp.put("value", "0.00");
					datatemp.put("showvalue", "0.00");
				}
				data.add(datatemp);
			}
			series.put("data_资金余额", data);
			returnMap.put("x", x);
			returnMap.put("series", series);
			returnMap.put("cur", ReportUtil.getCurKey(type));
			form.setReturn(returnMap);
		}
		
	}

	@Override
	public void setValue(String name, String value) {}
}
