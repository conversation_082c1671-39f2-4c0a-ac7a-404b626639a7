package com.aisino.app.web.report.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.aisino.platform.util.StringUtil;

public class ReportUtil {

	//查询刻度数
	public static int clock = 5;
	
	public static Map<String,String> getQueryCondition(String type, String queryKey, String action) {
		//act=pre/next
		Map<String,String> m = new HashMap<String,String>();
		try {
			if("day".equals(type)){
				//格式:yyyy-MM-dd
				SimpleDateFormat s = new SimpleDateFormat("yyyy-MM-dd");
				Calendar c = Calendar.getInstance();
				if(StringUtil.isBlank(queryKey)){
					c.setTime(new Date());
				}else{
					c.setTime(s.parse(queryKey));
				}
				if("pre".equals(action)){
					c.set(Calendar.DAY_OF_YEAR, c.get(Calendar.DAY_OF_YEAR)-1);
				}else if("next".equals(action)){
					c.set(Calendar.DAY_OF_YEAR, c.get(Calendar.DAY_OF_YEAR)+1);
				}
				String key = s.format(c.getTime());
				m.put("s", key);
				m.put("e", key);
				m.put("k", key);
			}else if("week".equals(type)){
				//格式yyyy-MM-dd~yyyy-MM-dd
				Calendar c1 = Calendar.getInstance();
				Calendar c2 = Calendar.getInstance();
				SimpleDateFormat s = new SimpleDateFormat("yyyy-MM-dd");
				if(StringUtil.isBlank(queryKey)){
					c1.setTime(new Date());
					c1.setFirstDayOfWeek(Calendar.MONDAY);
					c1.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
					c2.setTime(new Date());
					c2.setFirstDayOfWeek(Calendar.MONDAY);
					c2.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
				}else{
					String[] k = queryKey.split("~");
					c1.setTime(s.parse(k[0]));
					c2.setTime(s.parse(k[1]));
				}
					
				if("pre".equals(action)){
					
					c1.set(Calendar.DAY_OF_YEAR, c1.get(Calendar.DAY_OF_YEAR)-7);
					if(c1.get(Calendar.YEAR) != c2.get(Calendar.YEAR)){
						c2.set(Calendar.YEAR, c1.get(Calendar.YEAR));
					}
					c2.set(Calendar.DAY_OF_YEAR, c1.get(Calendar.DAY_OF_YEAR)+6);
				}else if("next".equals(action)){
					c1.set(Calendar.DAY_OF_YEAR, c1.get(Calendar.DAY_OF_YEAR)+7);
					if(c1.get(Calendar.YEAR) != c2.get(Calendar.YEAR)){
						c2.set(Calendar.YEAR, c1.get(Calendar.YEAR));
					}
					c2.set(Calendar.DAY_OF_YEAR, c1.get(Calendar.DAY_OF_YEAR)+6);
					
				}
				
				m.put("s", s.format(c1.getTime()));
				m.put("e", s.format(c2.getTime()));
				m.put("k", s.format(c1.getTime())+"~"+s.format(c2.getTime()));
			}else if("month".equals(type)){
				//格式yyyy-MM
				SimpleDateFormat s = new SimpleDateFormat("yyyy-MM");
				Calendar c = Calendar.getInstance();
				if(StringUtil.isBlank(queryKey)){
					c.setTime(new Date());
				}else{
					c.setTime(s.parse(queryKey));
				}
				if("pre".equals(action)){
					c.set(Calendar.MONTH, c.get(Calendar.MONTH)-1);
				}else if("next".equals(action)){
					c.set(Calendar.MONTH, c.get(Calendar.MONTH)+1);
				}
				queryKey = s.format(c.getTime());
				m.put("s", queryKey+"-01");
				m.put("e", queryKey+"-31");
				m.put("k", queryKey);
			}else if("quarter".equals(type)){
				//格式yyyy第x季度
				if(StringUtil.isBlank(queryKey)){
					Calendar c = Calendar.getInstance();
					c.setTime(new Date());
					SimpleDateFormat df = new SimpleDateFormat("yyyy");
					String year = df.format(c.getTime());
					int mm = c.get(Calendar.MONTH);
					if(mm<3){
						m.put("s", year+"-01-01");
						m.put("e", year+"-03-31");
						m.put("k", year+"第1季度");
					}else if(mm<6){
						m.put("s", year+"-04-01");
						m.put("e", year+"-06-30");
						m.put("k", year+"第2季度");
					}else if(mm<9){
						m.put("s", year+"-07-01");
						m.put("e", year+"-09-30");
						m.put("k", year+"第3季度");
					}else{
						m.put("s", year+"-10-01");
						m.put("e", year+"-12-31");
						m.put("k", year+"第4季度");
					}
				}else{
					int year = Integer.valueOf(queryKey.substring(0, 4));
					if(queryKey.indexOf("第1季度")>0){
						if("pre".equals(action)){
							year=year-1;
							m.put("s", year+"-10-01");
							m.put("e", year+"-12-31");
							m.put("k", year+"第4季度");
						}else if("next".equals(action)){
							m.put("s", year+"-04-01");
							m.put("e", year+"-06-30");
							m.put("k", year+"第2季度");
						}else{
							m.put("s", year+"-01-01");
							m.put("e", year+"-03-31");
							m.put("k", year+"第1季度");
						}
					}else if(queryKey.indexOf("第2季度")>0){
						if("pre".equals(action)){
							m.put("s", year+"-01-01");
							m.put("e", year+"-03-31");
							m.put("k", year+"第1季度");
						}else if("next".equals(action)){
							m.put("s", year+"-07-01");
							m.put("e", year+"-09-30");
							m.put("k", year+"第3季度");
						}else{
							m.put("s", year+"-04-01");
							m.put("e", year+"-06-30");
							m.put("k", year+"第2季度");
						}
					}else if(queryKey.indexOf("第3季度")>0){
						if("pre".equals(action)){
							m.put("s", year+"-04-01");
							m.put("e", year+"-06-30");
							m.put("k", year+"第2季度");
						}else if("next".equals(action)){
							m.put("s", year+"-10-01");
							m.put("e", year+"-12-31");
							m.put("k", year+"第4季度");
						}else{
							m.put("s", year+"-07-01");
							m.put("e", year+"-09-30");
							m.put("k", year+"第3季度");
						}
					}else{
						if("pre".equals(action)){
							m.put("s", year+"-07-01");
							m.put("e", year+"-09-30");
							m.put("k", year+"第3季度");
						}else if("next".equals(action)){
							year=year+1;
							m.put("s", year+"-01-01");
							m.put("e", year+"-03-31");
							m.put("k", year+"第1季度");
						}else{
							m.put("s", year+"-10-01");
							m.put("e", year+"-12-31");
							m.put("k", year+"第4季度");
						}
					}
				}
			}else if("year".equals(type)){
				//格式yyyy
				if(StringUtil.isBlank(queryKey)){
					SimpleDateFormat df = new SimpleDateFormat("yyyy");
					queryKey = df.format(new Date());
				}
				int year = Integer.valueOf(queryKey);
				if("pre".equals(action)){
					year = year-1;
				}else if("next".equals(action)){
					year = year+1;
				}
				m.put("s", year+"-01-01");
				m.put("e", year+"-12-31");
				m.put("k", String.valueOf(year));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return m;
	}
	public static String getClockKey(String type, Calendar clock) {
		//根据时间算出刻度数据
		String key = null;
		if("day".equals(type)){
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			key = df.format(clock.getTime());
			clock.set(Calendar.DAY_OF_MONTH, clock.get(Calendar.DAY_OF_MONTH)+1);
		}else if("week".equals(type)){
			SimpleDateFormat df = new SimpleDateFormat("yyyy年第w周");
			clock.setFirstDayOfWeek(Calendar.MONDAY);
			clock.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
			int week = clock.get(Calendar.WEEK_OF_YEAR);
			int year = clock.get(Calendar.YEAR);
			key = year+"年第"+week+"周";
			clock.set(Calendar.WEEK_OF_YEAR, clock.get(Calendar.WEEK_OF_YEAR)+1);
		}else if("month".equals(type)){
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM");
			key = df.format(clock.getTime());
			clock.set(Calendar.MONTH, clock.get(Calendar.MONTH)+1);
			clock.set(Calendar.DAY_OF_MONTH, 1);
			clock.add(Calendar.MONTH, 1);
			clock.add(Calendar.DATE, -1);
		}else if("quarter".equals(type)){
			SimpleDateFormat df = new SimpleDateFormat("yyyy");
			key = df.format(clock.getTime());
			int m = clock.get(Calendar.MONTH);
			if(m<3){
				key+="第1季度";
			}else if(m<6){
				key+="第2季度";
			}else if(m<9){
				key+="第3季度";
			}else{
				key+="第4季度";
			}
			clock.set(Calendar.MONTH, clock.get(Calendar.MONTH)+3);
			clock.set(Calendar.DAY_OF_MONTH, 1);
			clock.add(Calendar.MONTH, 1);
			clock.add(Calendar.DATE, -1);
			
		}else if("year".equals(type)){
			SimpleDateFormat df = new SimpleDateFormat("yyyy");
			key = df.format(clock.getTime());
			clock.set(Calendar.YEAR, clock.get(Calendar.YEAR)+1);
			clock.set(Calendar.DAY_OF_YEAR, 1);
			clock.roll(Calendar.DAY_OF_YEAR, -1);
		}
		return key;
	}
	public static Calendar getLastDay(String type , Calendar clock){
		Calendar clocktemp = Calendar.getInstance();
		if("day".equals(type)){
			clocktemp.setTime(clock.getTime());
		}else if("week".equals(type)){
			clocktemp.setTime(clock.getTime());
			clocktemp.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
			clocktemp.set(Calendar.WEEK_OF_YEAR, clocktemp.get(Calendar.WEEK_OF_YEAR)+1);
		}else if("month".equals(type)){
			clocktemp.setTime(clock.getTime());
			clocktemp.set(Calendar.MONTH, clock.get(Calendar.MONTH));
			clocktemp.set(Calendar.DAY_OF_MONTH, 1);
			clocktemp.add(Calendar.MONTH, 1);
			clocktemp.add(Calendar.DATE, -1);
		}else if("quarter".equals(type)){
			clocktemp.setTime(clock.getTime());
			clocktemp.set(Calendar.MONTH, clock.get(Calendar.MONTH));
			clocktemp.set(Calendar.DAY_OF_MONTH, 1);
			clocktemp.add(Calendar.MONTH, 1);
			clocktemp.add(Calendar.DATE, -1);
		}else if("year".equals(type)){
			clocktemp.setTime(clock.getTime());
			clocktemp.set(Calendar.YEAR, clock.get(Calendar.YEAR));
			clocktemp.set(Calendar.DAY_OF_YEAR, 1);
			clocktemp.roll(Calendar.DAY_OF_YEAR, -1);
		}
		return clocktemp;
	}
	
	public static String getXKey(String type, String key) {
		if("day".equals(type)){
			String k = key.substring(8, 10);
			if(k.startsWith("0")){
				k=k.replaceFirst("0", "");
			}
			return k;
		}else if("month".equals(type)){
			String k = key.substring(5, 7);
			if(k.startsWith("0")){
				k=k.replaceFirst("0", "");
			}
			return k;
		}else if("quarter".equals(type)){
			return key.substring(5, 6);
		}else if("year".equals(type)){
			return key;
		}
		return "";
	}
	
	public static String getFullKey(String type, String key) {
		if("day".equals(type)){
			String k = key.substring(8, 10);
			if(k.startsWith("0")){
				k=k.replaceFirst("0", "");
			}
			return k;
		}else if("month".equals(type)){
			String k = key.substring(5, 7);
			if(k.startsWith("0")){
				k=k.replaceFirst("0", "");
			}
			return k;
		}else if("quarter".equals(type)){
			return key.substring(5, 6);
		}else if("year".equals(type)){
			return key;
		}
		return "";
	}
	
	public static String getWeekKey(String key) {
		// key = yyyy年第w周
		
		String r;
		SimpleDateFormat show = new SimpleDateFormat("dd");
		
		Calendar c = Calendar.getInstance();
		Date date = new Date();
		System.out.println(c.getTime());
		r=show.format(c.getTime());
		if(r.startsWith("0")){
			r=r.replaceFirst("0", "");
		}
		return r;
	}
	
	public static String getXWeekKey(Calendar key) {
		String r;
		SimpleDateFormat show = new SimpleDateFormat("dd");
		r=show.format(key.getTime());
		if(r.startsWith("0")){
			r=r.replaceFirst("0", "");
		}
		return r;
	}
	
	public static String getWeekFullKey(Calendar key){
		String r = "";
		SimpleDateFormat show = new SimpleDateFormat("MM-dd");
		Calendar c1 = Calendar.getInstance();
		Calendar c2 = Calendar.getInstance();
		c1.setTime(key.getTime());
		c1.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
		c2.setTime(key.getTime());
		c2.set(Calendar.WEEK_OF_YEAR, c2.get(Calendar.WEEK_OF_YEAR)+1);
		c2.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
		r= show.format(c1.getTime()) + "~" + show.format(c2.getTime());
		return r;
	}
	
	public static void setQueryClock(String type, Calendar clock, Calendar endClock){
		if("day".equals(type)){
			clock.set(Calendar.DAY_OF_MONTH, clock.get(Calendar.DAY_OF_MONTH)-(ReportUtil.clock-1));
		}else if("week".equals(type)){
			clock.set(Calendar.WEEK_OF_YEAR, clock.get(Calendar.WEEK_OF_YEAR)-(ReportUtil.clock-1));
			//周的第一天在这里设置
			clock.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
			endClock.set(Calendar.WEEK_OF_YEAR, endClock.get(Calendar.WEEK_OF_YEAR)+1);
			endClock.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
			
			
		}else if("month".equals(type)){
			clock.set(Calendar.MONTH, clock.get(Calendar.MONTH)-(ReportUtil.clock-1));
			clock.set(Calendar.DAY_OF_MONTH, 1);
			endClock.set(Calendar.DAY_OF_MONTH, 1);
			endClock.add(Calendar.MONTH, 1);
			endClock.add(Calendar.DATE, -1);
		}else if("quarter".equals(type)){
			int mm = clock.get(Calendar.MONTH);
			clock.set(Calendar.MONTH, clock.get(Calendar.MONTH)-(ReportUtil.clock+mm/3-1)*3);
			clock.set(Calendar.DAY_OF_MONTH, 1);
			
			endClock.set(Calendar.MONTH, (mm/3+1)*3-1);
			endClock.set(Calendar.DAY_OF_MONTH, 1);
			endClock.add(Calendar.MONTH, 1);
			endClock.add(Calendar.DATE, -1);
			
		}else if("year".equals(type)){
			clock.set(Calendar.YEAR, clock.get(Calendar.YEAR)-(ReportUtil.clock-1));
			clock.set(Calendar.MONTH, 0);
			clock.set(Calendar.DAY_OF_MONTH, 1);
			endClock.set(Calendar.MONTH, 11);
			endClock.set(Calendar.DAY_OF_MONTH, 31);
		}
	}
	
	public static String formatBigDecimal(BigDecimal value){
		
		if(value == null){
			return "0.00";
		}else{
			DecimalFormat df = new DecimalFormat("###,##0.00");
			BigDecimal wan = new BigDecimal("999999.99");
			BigDecimal yi = new BigDecimal("9999999999.99");
			if(df.format(value).equals("-0.00")){
				return "0.00";
			}else if(value.compareTo(wan)<0){
				return df.format(value);
			}else if(value.compareTo(yi)<0){
				return df.format(value.divide(new BigDecimal("10000"), 2, BigDecimal.ROUND_HALF_UP))+"万";
			}else if(value.compareTo(yi)>=0){
				return df.format(value.divide(new BigDecimal("100000000"), 2, BigDecimal.ROUND_HALF_UP))+"亿";
			}
			
			return "0.00";
		}
	}
	
	public static String minusFormatBigDecimal(BigDecimal value){
		
		if(value == null){
			return "0.00";
		}else{
			DecimalFormat df = new DecimalFormat("-###,##0.00");
			BigDecimal wan = new BigDecimal("-999999.99");
			BigDecimal yi = new BigDecimal("-9999999999.99");
			if(df.format(value).equals("-0.00")){
				return "0.00";
			}else if(value.compareTo(wan)>0){
				return df.format(value);
			}else if(value.compareTo(yi)>0){
				return df.format(value.divide(new BigDecimal("10000"), 2, BigDecimal.ROUND_HALF_UP))+"万";
			}else if(value.compareTo(yi)<=0){
				return df.format(value.divide(new BigDecimal("100000000"), 2, BigDecimal.ROUND_HALF_UP))+"亿";
			}
			
			return "0.00";
		}
	}
	public static String getCurKey(String type){
		String key = null;
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		if("day".equals(type)){
			key = df.format(new Date());
		}else if("week".equals(type)){
			Calendar c = Calendar.getInstance();
			c.setTime(new Date());
			key = "~"+df.format(c.getTime());
			c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
			key = df.format(c.getTime()) + key;
		}else if("month".equals(type)){
			df = new SimpleDateFormat("yyyy-MM");
			key = df.format(new Date());
		}else if("quarter".equals(type)){
			Calendar c = Calendar.getInstance();
			c.setTime(new Date());
			df = new SimpleDateFormat("yyyy");
			key = df.format(c.getTime());
			int m = c.get(Calendar.MONTH);
			if(m<3){
				key+="第1季度";
			}else if(m<6){
				key+="第2季度";
			}else if(m<9){
				key+="第3季度";
			}else{
				key+="第4季度";
			}
		}else if("year".equals(type)){
			df = new SimpleDateFormat("yyyy");
			key = df.format(new Date());
		}
		return key;
	}
}
