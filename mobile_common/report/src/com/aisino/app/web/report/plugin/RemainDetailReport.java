package com.aisino.app.web.report.plugin;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.app.web.report.util.ReportUtil;
import com.aisino.platform.core.Plugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class RemainDetailReport extends Plugin implements FormCreateListener {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@SuppressWarnings({"rawtypes"})
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		Map<String,Object> returnMap = new HashMap<String, Object>();
		DbSvr db = DbSvr.getDbService(null);
		Map preiod = db.getOneRecorder("select * from co_sysinit where csubsyscode='GL'");
		if(preiod==null){
			//未启用总账管理模块时需要提示
			returnMap.put("error", "未启用总账管理模块");
			//form.setReturn(returnMap);
		}else{
			double iInitYear = CollectionUtil.getNumber(preiod, "iinityear");
			double iInitMonth = CollectionUtil.getNumber(preiod, "iinitmonth");
			//查询时钟
			Calendar clock = Calendar.getInstance();
			clock.setTime(new Date());
			//启用时间
			Calendar init = Calendar.getInstance();
			init.set(Calendar.YEAR, new Double(iInitYear).intValue());
			init.set(Calendar.MONTH, new Double(iInitMonth).intValue()-1);
			init.set(Calendar.DAY_OF_MONTH, 1);
			//查询维度day.week.month.quarter.year
			//查询范围当前期 向前取10个刻度,或者取到启用期间
			String type = bus.getString("type");
			String queryKey = bus.getString("queryKey");
			String action = bus.getString("act");
			Map<String,String> queryPeriod = ReportUtil.getQueryCondition(type, queryKey, action);
			//查期初余额
			Map<String,Number> paramsinit = new HashMap<String,Number>();
			paramsinit.put("iyear", iInitYear);
			paramsinit.put("imonth", iInitMonth);
			List<Map> iInitAmt = db.queryIdForList("mobile_report_remain.detail_init", paramsinit);
			//查询凭证中的期初
			Map paramsvou = new HashMap();
			paramsvou.put("s", queryPeriod.get("s"));
			List<Map> iVouInit = db.queryIdForList("mobile_report_remain.detail_vou",paramsvou );
			Map<String, BigDecimal> iVouMap = new HashMap<String, BigDecimal>();
			iVouMap = convertList(iVouMap, iVouInit);
			//凭证发生
			Map paramsdetail = new HashMap();
			paramsdetail.put("s", queryPeriod.get("s"));
			paramsdetail.put("e", queryPeriod.get("e"));
			List<Map> list = db.queryIdForList("mobile_report_remain.detail", paramsdetail);
			Map<String, BigDecimal> vou = new HashMap<String, BigDecimal>();
			vou = convertList(vou, list);
			List<Map> data = new ArrayList<Map>();
			System.out.println("queryPeriod==============>"+queryPeriod.toString());
			Calendar clocktemp = Calendar.getInstance(); 
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			Date endDate;
			try {
				endDate = df.parse(queryPeriod.get("e"));
				clocktemp.setTime(endDate);
			} catch (ParseException e) {
				e.printStackTrace();
			}
			
			if(iInitAmt!=null){
				for (Map map : iInitAmt) {
					String cFullName = CollectionUtil.getStringFromMap(map, "cfullname");
					String cAcctGUID = CollectionUtil.getStringFromMap(map, "cacctguid");
					Double j = (new BigDecimal(CollectionUtil.getStringFromMap(map, "init"))).doubleValue();
					BigDecimal i = new BigDecimal(j);
					
					Map<String, String> d = new HashMap<String, String>();
					d.put("acct", cFullName);
					BigDecimal a = new BigDecimal(0);
					if(iVouMap.get(cAcctGUID)!= null){
						a = new BigDecimal(String.valueOf(iVouMap.get(cAcctGUID)));
					}
					BigDecimal b = new BigDecimal(0);
					if(vou.size() >0){
						if(vou.get(cAcctGUID) != null){
							b = new BigDecimal(String.valueOf(vou.get(cAcctGUID)));
						}
					}
					if(!clocktemp.getTime().before(init.getTime())){
						d.put("remain", ReportUtil.formatBigDecimal(i.add(a).add(b)));
					}else{
						d.put("remain", "0.00");
					}
					
					data.add(d);
				}
			}
			List<Map> cols = new ArrayList<Map>();
			Map<String, String> col1 = new HashMap<String, String>();
			col1.put("code", "acct");
			col1.put("name", "资金账户");
			col1.put("width", "60");
			cols.add(col1);
			Map<String, String> col2 = new HashMap<String, String>();
			col2.put("code", "remain");
			col2.put("name", "余额");
			col2.put("width", "40");
			cols.add(col2);
			returnMap.put("data", data);
			returnMap.put("date", queryPeriod.get("k"));
			returnMap.put("cols", cols);
			form.setReturn(returnMap);
		}
	}

	@SuppressWarnings("rawtypes")
	private Map<String, BigDecimal> convertList(Map<String, BigDecimal> initMap, List<Map> iInitAmt) {
		if(iInitAmt!=null){
			for (Map map : iInitAmt) {
				String acct = CollectionUtil.getStringFromMap(map, "cacctguid");
				BigDecimal i = CollectionUtil.getBigDecimal(map, "init");
				initMap.put(acct, i);
			}
		}
		return initMap;
	}


	@Override
	public void setValue(String name, String value) {

	}

}
