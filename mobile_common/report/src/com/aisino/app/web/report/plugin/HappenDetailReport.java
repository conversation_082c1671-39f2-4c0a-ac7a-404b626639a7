package com.aisino.app.web.report.plugin;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.app.web.report.util.ReportUtil;
import com.aisino.platform.core.Plugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class HappenDetailReport extends Plugin implements FormCreateListener {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@SuppressWarnings("rawtypes")
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		Map<String,Object> returnMap = new HashMap<String, Object>();
		DbSvr db = DbSvr.getDbService(null);
		Map preiod = db.getOneRecorder("select * from co_sysinit where csubsyscode='GL'");
		if(preiod==null){
			//未启用总账管理模块时需要提示
			returnMap.put("error", "未启用总账管理模块");
			//form.setReturn(returnMap);
		}else{
			double iInitYear = CollectionUtil.getNumber(preiod, "iinityear");
			double iInitMonth = CollectionUtil.getNumber(preiod, "iinitmonth");
			//查询时钟
			Calendar clock = Calendar.getInstance();
			clock.setTime(new Date());
			//启用时间
			Calendar init = Calendar.getInstance();
			init.set(Calendar.YEAR, new Double(iInitYear).intValue());
			init.set(Calendar.MONTH, new Double(iInitMonth).intValue()-1);
			init.set(Calendar.DAY_OF_MONTH, 1);
			//查询维度day.week.month.quarter.year
			//查询范围当前期 向前取10个刻度,或者取到启用期间
			String type = bus.getString("type");
			String queryKey = bus.getString("queryKey");
			String action = bus.getString("act");
			Map<String,String> queryPeriod = ReportUtil.getQueryCondition(type, queryKey, action);
			Map<String,String> paramsdetail = new HashMap<String,String>();
			paramsdetail.put("s", queryPeriod.get("s"));
			paramsdetail.put("e", queryPeriod.get("e"));
			List<Map> list = db.queryIdForList("mobile_report_happen.detail",paramsdetail);
			List<Map> data = new ArrayList<Map>();
			if(list!=null){
				for (Map map : list) {
					String cFullName = CollectionUtil.getStringFromMap(map, "cfullname");
					Map<String, String> d = new HashMap<String, String>();
					d.put("acct", cFullName);
					BigDecimal debit = CollectionUtil.getBigDecimal(map, "debit");
					BigDecimal credit = CollectionUtil.getBigDecimal(map, "credit");
					d.put("debit", ReportUtil.formatBigDecimal(debit));
					d.put("credit", ReportUtil.formatBigDecimal(credit));
					data.add(d);
				}
			}
			List<Map> cols = new ArrayList<Map>();
			Map<String, String> col1 = new HashMap<String, String>();
			col1.put("code", "acct");
			col1.put("name", "资金账户");
			col1.put("width", "40");
			cols.add(col1);
			Map<String, String> col2 = new HashMap<String, String>();
			col2.put("code", "debit");
			col2.put("name", "收入");
			col2.put("width", "30");
			cols.add(col2);
			Map<String, String> col3 = new HashMap<String, String>();
			col3.put("code", "credit");
			col3.put("name", "支出");
			col3.put("width", "30");
			cols.add(col3);
			returnMap.put("cols", cols);
			returnMap.put("data", data);
			returnMap.put("date", queryPeriod.get("k"));
			form.setReturn(returnMap);
		}
	}
	
	@Override
	public void setValue(String name, String value) {

	}

}
