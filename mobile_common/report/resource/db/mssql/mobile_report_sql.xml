<?xml version="1.0" encoding="UTF-8"?>

<sqls>
	<sql group="mobile_report_remain">
		<!-- 图表查询数据sql -->
		<i id="total_init">
			<![CDATA[
			select sum(ab.iInitAMT*a.iBalanceDir) init 
			from gl_accountbook ab 
			inner join gl_account a on a.cguid=ab.cacctguid 
			where (a.icash=1 or a.ibank=1 ) and a.istatus=1 and a.ileaf=1 and ab.iyear={iyear} and ab.imonth={imonth} 
			@filter[gl_account]
			]]>
		</i>
		<i id="total_vou">
			<![CDATA[
			select sum(vl.iDebitAMT-vl.iCreditAMT) init 
			from gl_voucherline vl 
			inner join gl_voucher v on v.cguid=vl.cvouguid 
			inner join gl_account a on a.cguid=vl.cacctguid 
			where (a.icash=1 or a.ibank=1) and v.iStatus=1 and a.istatus=1 and convert(varchar(100),v.dVouDate,23) < {paramstime} 
			@filter[gl_account]
			]]>
		</i>
		<i id="total_day">
			<![CDATA[
			select sum(vl.iDebitAMT-vl.iCreditAMT) remain ,CONVERT(varchar(10),v.dvoudate,120) t from gl_voucherline vl 
			inner join gl_voucher v on v.cguid=vl.cvouguid 
			inner join gl_account a on a.cguid=vl.cacctguid 
			where (a.icash=1 or a.ibank=1) and v.iStatus=1 and a.istatus=1 and v.dvoudate<={endTime}
			group by v.dvoudate 
			@filter[gl_account]
			]]>
		</i>
		<i id="total_week">
			<![CDATA[
			SET DATEFIRST 1
			select sum(vl.iDebitAMT-vl.iCreditAMT) remain ,cast(v.iyear as varchar)+'年第'+ cast(datepart(week,v.dvoudate) as varchar)+'周' t from gl_voucherline vl 
			inner join gl_voucher v on v.cguid=vl.cvouguid 
			inner join gl_account a on a.cguid=vl.cacctguid 
			where (a.icash=1 or a.ibank=1) and v.iStatus=1 and a.istatus=1  and v.dvoudate<={endTime}
			group by v.iyear,datepart(week,v.dvoudate) 
			@filter[gl_account]
			]]>
		</i>
		<i id="total_month">
			<![CDATA[
			select sum(vl.iDebitAMT-vl.iCreditAMT) remain ,cast(v.iyear as varchar)+'-'+  CASE v.iMonth 
				WHEN 1 THEN '01'  
				WHEN 2 THEN '02'  
				WHEN 3 THEN '03'  
				WHEN 4 THEN '04'  
				WHEN 5 THEN '05'  
				WHEN 6 THEN '06'  
				WHEN 7 THEN '07'  
				WHEN 8 THEN '08'  
				WHEN 9 THEN '09'  
				ELSE convert(varchar(2), v.imonth) END t from gl_voucherline vl 
			inner join gl_voucher v on v.cguid=vl.cvouguid 
			inner join gl_account a on a.cguid=vl.cacctguid 
			where (a.icash=1 or a.ibank=1) and v.iStatus=1 and a.istatus=1  and v.dvoudate<={endTime}
			group by v.iyear,v.imonth 
			@filter[gl_account]
			]]>
		</i>
		<i id="total_quarter">
			<![CDATA[
			select sum(vl.iDebitAMT-vl.iCreditAMT) remain , cast(v.iyear as varchar)+'第'+cast(datepart(quarter,v.dvoudate) as VARCHAR)+'季度' t from gl_voucherline vl 
			inner join gl_voucher v on v.cguid=vl.cvouguid 
			inner join gl_account a on a.cguid=vl.cacctguid 
			where (a.icash=1 or a.ibank=1) and v.iStatus=1 and a.istatus=1  and v.dvoudate<={endTime}
			group by v.iyear,datepart(quarter,v.dvoudate) 
			@filter[gl_account]
			]]>
		</i>
		<i id="total_year">
			<![CDATA[
			select sum(vl.iDebitAMT-vl.iCreditAMT) remain ,iyear t from gl_voucherline vl 
			inner join gl_voucher v on v.cguid=vl.cvouguid 
			inner join gl_account a on a.cguid=vl.cacctguid 
			where (a.icash=1 or a.ibank=1) and v.iStatus=1 and a.istatus=1  and v.dvoudate<={endTime}
			group by v.iyear 
			@filter[gl_account]
			]]>
		</i>
		<!-- 列表查询sql -->
		<i id="detail_init">
			<![CDATA[
			select ab.iInitAMT*a.iBalanceDir init, cacctguid, a.cfullname from gl_accountbook ab 
			inner join gl_account a on a.cguid=ab.cacctguid 
			where (a.icash=1 or a.ibank=1 ) and a.istatus=1 and a.ileaf=1 and ab.iyear={iyear} and ab.imonth={imonth} 
			order by a.ccode 
			@filter[gl_account]
			]]>
		</i>
		<i id="detail_vou">
			<![CDATA[
			select sum(vl.iDebitAMT-vl.iCreditAMT) init,vl.cacctguid from gl_voucherline vl 
			inner join gl_voucher v on v.cguid=vl.cvouguid 
			inner join gl_account a on a.cguid=vl.cacctguid 
			where (a.icash=1 or a.ibank=1) and v.iStatus=1 and a.istatus=1 and convert(varchar(100),v.dVouDate,23) < {s}
			group by vl.cacctguid 
			@filter[gl_account]
			]]>
		</i>
		<i id="detail">
			<![CDATA[
			select sum(vl.iDebitAMT-vl.iCreditAMT) init ,vl.cacctguid
			from gl_voucherline vl 
			inner join gl_voucher v on v.cguid=vl.cvouguid 
			inner join gl_account a on a.cguid=vl.cacctguid 
			where (a.icash=1 or a.ibank=1) and v.iStatus=1 and a.istatus=1  and convert(varchar(100),v.dVouDate,23) >= {s} and convert(varchar(100),v.dVouDate,23)<={e}
			group by vl.cacctguid,a.cfullname 
			@filter[gl_account]
			]]>
		</i>
	</sql>
	<sql group="mobile_report_happen">
		<i id="total_day">
			<![CDATA[
			select sum(vl.iDebitAMT) debit,sum(vl.iCreditAMT) credit,CONVERT(varchar(10),v.dvoudate,120) t 
			from gl_voucherline vl 
			inner join gl_voucher v on v.cguid=vl.cvouguid 
			inner join gl_account a on a.cguid=vl.cacctguid 
			where (a.icash=1 or a.ibank=1) and v.iStatus=1 and a.istatus=1 and convert(varchar(100),v.dVouDate,23) >= {typetime} and convert(varchar(100),v.dVouDate,23)<=getdate()
			group by v.dvoudate 
			@filter[gl_account]
			]]>
		</i>
		<i id="total_week">
			<![CDATA[
			SET DATEFIRST 1
			select sum(vl.iDebitAMT) debit,sum(vl.iCreditAMT) credit,cast(v.iyear as varchar)+'年第'+ cast(datepart(week,v.dvoudate) as varchar)+'周' t  
			from gl_voucherline vl 
			inner join gl_voucher v on v.cguid=vl.cvouguid 
			inner join gl_account a on a.cguid=vl.cacctguid 
			where (a.icash=1 or a.ibank=1) and v.iStatus=1 and a.istatus=1 and convert(varchar(100),v.dVouDate,23) >= {typetime} and convert(varchar(100),v.dVouDate,23)<={endtime}
			group by v.iyear,datepart(week,v.dvoudate) 
			@filter[gl_account]
			]]>
		</i>
		<i id="total_month">
			<![CDATA[
			select sum(vl.iDebitAMT) debit,sum(vl.iCreditAMT) credit,cast(v.iyear as varchar)+'-'+  CASE v.iMonth 
				WHEN 1 THEN '01'  
				WHEN 2 THEN '02'   
				WHEN 3 THEN '03'   
				WHEN 4 THEN '04'   
				WHEN 5 THEN '05'   
				WHEN 6 THEN '06'   
				WHEN 7 THEN '07'   
				WHEN 8 THEN '08'   
				WHEN 9 THEN '09'   
				ELSE convert(varchar(2), v.imonth) END t 
			from gl_voucherline vl 
			inner join gl_voucher v on v.cguid=vl.cvouguid 
			inner join gl_account a on a.cguid=vl.cacctguid 
			where (a.icash=1 or a.ibank=1) and v.iStatus=1 and a.istatus=1 and convert(varchar(100),v.dVouDate,23) >= {typetime} and convert(varchar(100),v.dVouDate,23)<={endtime}
			group by v.iyear,v.imonth 
			@filter[gl_account]
			]]>
		</i>
		<i id="total_quarter">
			<![CDATA[
			select sum(vl.iDebitAMT) debit,sum(vl.iCreditAMT) credit, cast(v.iyear as varchar)+'第'+cast(datepart(quarter,v.dvoudate) as VARCHAR)+'季度' t
			from gl_voucherline vl 
			inner join gl_voucher v on v.cguid=vl.cvouguid 
			inner join gl_account a on a.cguid=vl.cacctguid 
			where (a.icash=1 or a.ibank=1) and v.iStatus=1 and a.istatus=1 and convert(varchar(100),v.dVouDate,23) >= {typetime} and convert(varchar(100),v.dVouDate,23)<={endtime}
			group by v.iyear,datepart(quarter,v.dvoudate) 
			@filter[gl_account]
			]]>
		</i>
		<i id="total_year">
			<![CDATA[
			select sum(vl.iDebitAMT) debit,sum(vl.iCreditAMT) credit, v.iyear t
			from gl_voucherline vl 
			inner join gl_voucher v on v.cguid=vl.cvouguid 
			inner join gl_account a on a.cguid=vl.cacctguid 
			where (a.icash=1 or a.ibank=1) and v.iStatus=1 and a.istatus=1  and convert(varchar(100),v.dVouDate,23) >= {typetime} and convert(varchar(100),v.dVouDate,23)<={endtime}
			group by v.iyear 
			@filter[gl_account]
			]]>
		</i>
		<i id="detail">
			<![CDATA[
			select max(s.debit) as debit, max(s.credit) as credit , s.cfullname as cfullname from (
				select sum(vl.iDebitAMT) debit,sum(vl.iCreditAMT) credit,a.cfullname, a.ccode 
				from gl_voucherline vl 
				inner join gl_voucher v on v.cguid=vl.cvouguid 
				inner join gl_account a on a.cguid=vl.cacctguid 
				where (a.icash=1 or a.ibank=1)  and v.iStatus=1 and a.istatus=1 and convert(varchar(100),v.dVouDate,23) >= {s} and convert(varchar(100),v.dVouDate,23)<={e}
				group by vl.cacctguid,a.cfullname , a.ccode 
				@filter[gl_account]
				union
				select null debit,null credit,a.cfullname , a.ccode 
				from gl_voucherline vl 
				right join gl_account a on a.cguid=vl.cacctguid 
				where (a.icash=1 or a.ibank=1) and a.istatus=1 and a.iLeaf = 1 
				group by vl.cacctguid,a.cfullname , a.ccode 
				@filter[gl_account]
			) s GROUP BY s.cfullname, s.ccode ORDER BY s.ccode	
			]]>
		</i>
	</sql>
</sqls>