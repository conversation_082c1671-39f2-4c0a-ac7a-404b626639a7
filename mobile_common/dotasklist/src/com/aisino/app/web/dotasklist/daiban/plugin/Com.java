package com.aisino.app.web.dotasklist.daiban.plugin;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.Map;

public class Com implements Comparator {

	@SuppressWarnings("rawtypes")
	@Override
	public int compare(Object o1, Object o2) {
		Map m1 = (Map) o1;
		Map m2 = (Map) o2;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date d1 = null;
		Date d2 = null;
		try {
			d1 = sdf.parse(m1.get("申请时间").toString());
			d2 = sdf.parse(m2.get("申请时间").toString());
		} catch (ParseException e1) {
			e1.printStackTrace();
		}
		if (d1.getTime() > d2.getTime()) {
			return -1;
		} else if (d1.getTime() == d2.getTime()) {
			return 0;
		} else {
			return 1;
		}
	}

}
