package com.aisino.app.web.dotasklist.yiban.plugin;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.aisino.platform.core.MS;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.veng.json.dauglas.JSONException;
import com.aisino.platform.veng.json.dauglas.JSONObject;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class YibanDetailPlugin extends SqlPlugin implements FormCreateListener {

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
	    String cbilltype=bus.getString("cbilltype");
	    String cguid=bus.getString("cguid");	//单据cguid    		
		// 已办事项详情
		MS m = new MS("App.Yiban"+cbilltype);  
		Map map= (Map) m.doService("browse",cguid);		    	
		form.setReturn(map);
	}
	
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {

	}

}
