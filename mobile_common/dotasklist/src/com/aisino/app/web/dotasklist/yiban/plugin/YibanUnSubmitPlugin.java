package com.aisino.app.web.dotasklist.yiban.plugin;

import com.aisino.platform.core.MS;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class YibanUnSubmitPlugin extends SqlPlugin implements FormCreateListener {

	@Override
	/**
	 * 已办事项反审核
	 * <AUTHOR>
	 * @date Dec 16, 2016
	 */
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
	    String cbilltype=bus.getString("cbilltype");
	    String cguid=bus.getString("cguid");	//单据cguid  
	    //处理方式，值为 unsubmit:非工作流直接反审核 ；  后续可能会加审核日志、下载附件。
	    String action = bus.getString("action");
	    if("unsubmit".equals(action)){
		    // 已办事项详情
			MS m = new MS("App.Yiban"+cbilltype);  
			String s= (String) m.doService("unsubmit",cguid);		    	
			form.setReturn(s);
	    }else{
	    
	    }

	}
	
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {

	}

}
