package com.aisino.app.web.dotasklist.common.service;


import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;

import com.aisino.platform.core.ProductInfo;
import com.aisino.platform.core.serviceInf.IService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;


public class AppendixService  implements IService{
	@Override
	/*查看附件服务*/
	public Object doService(Object... param) {	
		String cFileUrl=(String) param[0]; 
		String cFileName=(String) param[1];
		DbSvr db=DbSvr.getDbService(null);	
		String path =  ProductInfo.getWebRealPath() + "\\WEB-INF\\classes\\" + cFileUrl + "\\";
		File file = new File(path + cFileName);
		if(!file.exists()){
			throw new BusinessException("没有找到此文件,可能已经被删除!");
		}
		return(Object) file;
		
		/*else{	
			try {
				//读取要下载的文件，保存到文件输入流
				InputStream in = new FileInputStream(file);
				//创建输出流
				OutputStream out = response.getOutputStream();
				//创建缓冲区
				byte buffer[] = new byte[1024];
				int len = 0;
				//循环将输入流中的内容读取到缓冲区当中
				while((len=in.read(buffer))>0){
				//输出缓冲区的内容到浏览器，实现文件下载
				out.write(buffer, 0, len);
				}
				//关闭文件输入流
				in.close();
				//关闭输出流
				out.close();

			} catch (FileNotFoundException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			return null;
		}*/
		
		
	}
	
	

}
