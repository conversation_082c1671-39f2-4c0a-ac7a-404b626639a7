package com.aisino.app.web.dotasklist.daiban.plugin;

import com.aisino.platform.core.MS;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class DaibanSubmitPlugin extends SqlPlugin implements FormCreateListener {

	@Override
	/**
	 * 根据action获取审核方式
	 * <AUTHOR>
	 * @date Dec 16, 2016
	 */
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
	    String cbilltype=bus.getString("cbilltype");
	    String cguid=bus.getString("cguid");	//单据cguid  
	    //处理方式，值为 submit:非工作流直接审核 ；agree：同意；backprev：返回上一步；backorigin：返回发起人;terminate:终止
	    String action = bus.getString("action");
	    String firstsubmit=bus.getString("firstsubmit");
	    if("submit".equals(action)){
		    // 待办事项详情
			MS m = new MS("App.Daiban"+cbilltype);  
			String s= (String) m.doService("submit",cguid,firstsubmit);		    	
			form.setReturn(s);
	    }else{
	    	/* 2016-12-23 qcj 修正工作流审批参数传递为bus */
	    	MS m = new MS("App.Daiban.WorkflowApprove");
	    	String s= (String) m.doService(bus);	
	    	form.setReturn(s);
	    }

	}
	
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {

	}

}
