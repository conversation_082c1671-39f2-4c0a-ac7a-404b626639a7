package com.aisino.app.web.dotasklist.yiban.plugin;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.platform.core.MS;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;

import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;


/**
 * <AUTHOR>  已办模块（借款报销、协同办公、生产审批、财务审批、采购审批、销售审批、库存审批）
 * @date 20161208
 */

public class YibanModulesPlugin extends SqlPlugin implements FormCreateListener {

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		DbSvr dbSvr = DbSvr.getDbService(null);	
		List<Map> modulelist = new ArrayList<Map>();
		
		String[] modules=new String[]{"EM","OA","MA","FI","PU","SA","ST"}; 
		for(int i=0;i<modules.length;i++){
			String module =modules[i];
			int listnum = 0;  //该分类的已办数目
			Map np = new HashMap();
			np.put("listnum", listnum);
			MS m = new MS("App.YibanList."+module);  
		    m.doService("getnum",null,np,null,null);	//doService第一个参数为getnum/getlist，第二个为list，第三个为listnum,第四个为keyword,第五个为today	    
		    Map p = new HashMap();
			p.put(module, np.get("listnum")); 
			modulelist.add(p);
		}
		Map map = new HashMap();
		map.put("modulelist",modulelist); 
		form.setReturn(map);  //返回格式为：{"modulelist":[{"EM":0},{"OA":0},{"MA":0},{"FI":0},{"PU":30},{"SA":15},{"ST":0}]}
		
	}
	
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {

	}

}
