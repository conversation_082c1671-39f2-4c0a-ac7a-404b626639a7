package com.aisino.app.web.dotasklist.daiban.plugin;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.platform.core.MS;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;


/**
 * <AUTHOR>
 * @desp 各模块的待办列表（各模块待办列表展现不同）
 * @date 20161208
 */

public class DaibanListPlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		DbSvr dbSvr = DbSvr.getDbService(null);
		List<Map> list =new ArrayList<Map>();	//模块注册的服务，都将待办事项加到list中	          
		String module=bus.getString("module");	//module传值为"EM","OA","MA","FI","PU","SA","ST"	
		String keyword=bus.getString("keyword"); //搜索关键字
		/* 2017-02-28 qcj 增加参数当前页数和每页条数 */
		String curpage=bus.getString("curpage");
		String pagenum=bus.getString("pagenum");
		
		MS m = new MS("App.DaibanList."+module);  
	    m.doService("getlist",list,null,keyword,curpage,pagenum);	//doService第一个参数为getnum/getlist，第二个为list，第三个为listnum,第四个为搜索关键字 
		// 2017-02-28 qcj 无需再排序
	    //Com com =new Com();
		//Collections.sort(list, com); 
	    //String tableName = "Temp"+SimpleKey.getInstance().genKey();
	    //创建临时表
	    //TempTableUtil.createTable(tableName, dbSvr);
	    //TempTableUtil.insertData(tableName, list, dbSvr);	
	    //if(StringUtil.isNotBlank(tableName)) TempTableUtil.dropTable(tableName, dbSvr); 

	    Map map = new HashMap();		
		map.put("list", list);
		form.setReturn(map);
	}
	
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {

	}
	
	

}
