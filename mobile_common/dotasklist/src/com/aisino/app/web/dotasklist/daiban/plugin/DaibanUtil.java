package com.aisino.app.web.dotasklist.daiban.plugin;

import com.aisino.platform.db.DbSvr;

public class DaibanUtil {
	
	
	/***已办事项不查取自动审批生成的单据。但是自动审批的单据在web端口反审批以后
	 * 进入代办事项列表，此时在进行审批，就变成手工审批了，但是由于数据库表里
	 * cCheckWay的值得还是之前的"1"(自动审批)，需要更新为0才能在已办列表中查出来。
	 * zrc  2017年2月22日 16:15:30
	 * ***/
	public static  void  updateCheckWay(String tablename,String cguid){
		
		DbSvr db=DbSvr.getDbService(null);	
        //单据表里的实际的值
		String checkwaynow=db.getStringResult("select cCheckWay  from  "+tablename+"  where cguid=?", cguid);
		if("1".equals(checkwaynow)){
				db.execute("update  "+tablename+"  set cCheckWay='0' where cguid='"+cguid+"'");
		}
	}

}

