package com.aisino.app.web.dotasklist.daiban.plugin;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.platform.core.MS;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

/**
 * 返回待办事项总数
 * <AUTHOR>
 * @date 2017年2月23日
 *
 */
public class DaibanModulesNumPlugin implements FormCreateListener{

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		// TODO Auto-generated method stub
		DbSvr dbSvr = DbSvr.getDbService(null);	
		List<Map> modulelist = new ArrayList<Map>();
		
		int allnum=0;
		String[] modules=new String[]{"EM","OA","MA","FI","PU","SA","ST"}; 
		for(int i=0;i<modules.length;i++){
			String module =modules[i];
			int listnum = 0;  //该分类的待办数目
			Map np = new HashMap();
			np.put("listnum", listnum);
			MS m = new MS("App.DaibanList."+module);  
		    m.doService("getnum",null,np,null);	//doService第一个参数为getnum/getlist，第二个为list，第三个为listnum,第四个为搜索关键字	    
		    
		    allnum += Integer.parseInt(String.valueOf(np.get("listnum")));
		    
		}
		Map map = new HashMap();
		map.put("allnum",allnum); 
		form.setReturn(allnum);  //返回格式为：{"allnum":[{"allnum":0}]}
		
	}

}
