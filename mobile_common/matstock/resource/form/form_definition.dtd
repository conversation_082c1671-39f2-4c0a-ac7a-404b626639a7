<?xml version="1.0" encoding="UTF-8"?>
<!ELEMENT Forms ((js|form|plugin|valueFetcher)*)>
<!ELEMENT form (import*,value*,info*,toolbar?,widgets?, extendPoint?,bind*)>
<!ELEMENT toolbar (button*)>
<!ELEMENT button (item*,basic?)>
<!ELEMENT item EMPTY>
<!ELEMENT widgets ((basic|datagrid|layout)*)>
<!ELEMENT layout ((basic|datagrid|layout)*)>
<!ELEMENT basic (value*,valueFetcher?,col*)>
<!ELEMENT valueFetcher (plugin*,value*)>
<!ELEMENT datagrid (valueFetcher?,col+,evnt*)>
<!ELEMENT col (valueFetcher?,col*)>
<!ELEMENT evnt (#PCDATA)>
<!ELEMENT extendPoint (plugin*)>
<!ELEMENT plugin (plugin*,value*)>
<!ELEMENT value (#PCDATA)>
<!ELEMENT import EMPTY>
<!ELEMENT info EMPTY>
<!ELEMENT js (#PCDATA)>
<!ELEMENT bind (#PCDATA)>


<!ATTLIST form
	id CDATA #REQUIRED
	formType CDATA #IMPLIED
	attr CDATA #IMPLIED
	busCode CDATA #IMPLIED 
	extend CDATA #IMPLIED 
	implement CDATA #IMPLIED
	override (true | false) "false"
	host CDATA #IMPLIED 
	canvasClass CDATA #IMPLIED
	bodyClass CDATA #IMPLIED
	backgroundColor CDATA #IMPLIED 
	valign CDATA #IMPLIED 
	js CDATA #IMPLIED
	desp CDATA #IMPLIED
	states CDATA #IMPLIED
	version CDATA #IMPLIED
	width CDATA #IMPLIED
	height CDATA #IMPLIED
	abstract (true | false) "false"
>
<!ATTLIST import
	class CDATA #IMPLIED	
	file CDATA #IMPLIED	
	language (javascript | vbscript | css) "javascript"
>
<!ATTLIST info
	var CDATA #IMPLIED
	default CDATA #IMPLIED
>
<!ATTLIST widgets>
<!ATTLIST toolbar
	template CDATA #IMPLIED	
	buttons  CDATA #IMPLIED
	ban CDATA #IMPLIED 
    attr CDATA #IMPLIED
    ishide (true | false) "false"
>
<!ATTLIST button
	id CDATA #IMPLIED
	text CDATA #IMPLIED
	img CDATA #IMPLIED	
	rightImg CDATA #IMPLIED	
	heigth CDATA #IMPLIED	
	width CDATA #IMPLIED
	hint CDATA #IMPLIED
	linkToState CDATA #IMPLIED
	gotoState CDATA #IMPLIED
	onclick CDATA #IMPLIED
	attr CDATA #IMPLIED
	itemextway ( before | after | override ) "after"
>
<!ATTLIST item
	id CDATA #IMPLIED
    text CDATA #REQUIRED
	img CDATA #IMPLIED
	linkToState CDATA #IMPLIED
	gotoState CDATA #IMPLIED
	onclick CDATA #IMPLIED	
	attr CDATA #IMPLIED
>
<!ATTLIST layout
	type CDATA #REQUIRED
    name CDATA #IMPLIED	
    layout CDATA #IMPLIED
	leftSpace CDATA #IMPLIED
	topSpace CDATA #IMPLIED
	rightSpace CDATA #IMPLIED
	bottomSpace CDATA #IMPLIED	
	linkToState CDATA #IMPLIED
	align (left | right | center) "left"
	width CDATA #IMPLIED
	height CDATA #IMPLIED 
	isHidden (true | false) "false"
	ignore (true | false) "false"
	attr CDATA #IMPLIED
	desp CDATA #IMPLIED
	backgroundColor CDATA #IMPLIED
	backgroundImg CDATA #IMPLIED
	scrollx (true | false) "false"
	scrolly (true | false) "false"
	colspan CDATA #IMPLIED
	rowspan CDATA #IMPLIED 
	itemextway ( before | after | override ) "after"
>
<!ATTLIST basic
	name CDATA #REQUIRED
	label CDATA #IMPLIED
	labelWidth CDATA #IMPLIED 
	layout CDATA #IMPLIED
	widget CDATA #IMPLIED
	default CDATA #IMPLIED
	linkToState CDATA #IMPLIED
	attr CDATA #IMPLIED
	referWidgets CDATA #IMPLIED
	leftSpace CDATA #IMPLIED
	topSpace CDATA #IMPLIED
	rightSpace CDATA #IMPLIED
	bottomSpace CDATA #IMPLIED	
	width CDATA #IMPLIED
	height CDATA #IMPLIED	
	hint CDATA #IMPLIED
	readonly CDATA #IMPLIED
	disabled (true | false) "false"
	maxlength CDATA #IMPLIED
	inline (first | last | single | middle | inner) "middle"
	ishide (true | false) "false"
	ignore (true | false) "false"
	require (true | false) "false"
	align (left | right) "left"
	onblur CDATA #IMPLIED
	onfocus CDATA #IMPLIED
	onchange CDATA #IMPLIED
	onclick CDATA #IMPLIED
	ondbclick CDATA #IMPLIED
	onkeydown CDATA #IMPLIED
	onkeyup CDATA #IMPLIED
	checkattr CDATA #IMPLIED
	checks CDATA #IMPLIED
	canBeReset (true | false) "true"
	autoSubmit (true | false) "true" 	
	extendcol (true | false) "true" 	
	itemextway ( before | after) "after" 
	
	backgroundColor CDATA #IMPLIED
	backgroundImg CDATA #IMPLIED
	colspan CDATA #IMPLIED
	rowspan CDATA #IMPLIED 
>
<!ATTLIST valueFetcher
    id CDATA #IMPLIED
    ctg CDATA #IMPLIED
	type CDATA #IMPLIED
>
<!ATTLIST datagrid
    name CDATA #REQUIRED
    label CDATA #IMPLIED
    linkToState CDATA #IMPLIED
    layout CDATA #IMPLIED	
	hasPage (true | false) "false"
	canBeReset (true | false) "false"
	pageAction CDATA #IMPLIED 
	pagebar CDATA #IMPLIED  	
	pageHj (true | false) "true"	
	template CDATA #IMPLIED
	leftSpace CDATA #IMPLIED
	topSpace CDATA #IMPLIED
	rightSpace CDATA #IMPLIED
	bottomSpace CDATA #IMPLIED		
	width CDATA #IMPLIED
	height CDATA #IMPLIED
	attr CDATA #IMPLIED
>
<!ATTLIST col
    id CDATA #REQUIRED
	label CDATA #REQUIRED
	cacu CDATA #IMPLIED
	hidden (true | false) "false"
	formula CDATA #IMPLIED
	pageCacuExp CDATA #IMPLIED
	default CDATA #IMPLIED	
	img CDATA #IMPLIED  
	attr CDATA #IMPLIED
	referWidgets CDATA #IMPLIED
	width CDATA #IMPLIED
	minWidth CDATA #IMPLIED
	require (true | false) "false"
	sort CDATA #IMPLIED
	filter CDATA #IMPLIED
	align CDATA #IMPLIED
	href CDATA #IMPLIED
	color CDATA #IMPLIED
	maxlength CDATA #IMPLIED
	editType (int | text | textarea | num |btn| check | radio | select | multiSelect | date | refer | editRefer | txt | ed | co | coro | ch | ro | edn | nro | dyco) "txt"
	dType (str | num) "str"
	inputFormator CDATA #IMPLIED
	checkattr CDATA #IMPLIED
	checks CDATA #IMPLIED
	style CDATA #IMPLIED 
	headAlign CDATA #IMPLIED  
	renderer CDATA #IMPLIED
	title CDATA #IMPLIED 
	resizable (true | false) "true" 
	lock (true | false) "false" 
	ignore (true | false) "false"
	sum (true | false) "false"
	prec CDATA #IMPLIED 
	disabled (true | false) "false"
>
<!ATTLIST evnt
	type CDATA #REQUIRED
>
<!ATTLIST js
	functionname CDATA #REQUIRED
	desp CDATA #IMPLIED
>
<!ATTLIST extendPoint
	type CDATA #IMPLIED
	addglobe (true | false) "true"
>
<!ATTLIST plugin
	type CDATA #IMPLIED
	attr CDATA #IMPLIED
	desp CDATA #IMPLIED
	idx CDATA #IMPLIED
	onEvent CDATA #IMPLIED
	id  CDATA #IMPLIED
	parent CDATA #IMPLIED
>
<!ATTLIST value
	name CDATA #REQUIRED
>
<!ATTLIST bind
	element CDATA #REQUIRED
	event CDATA #REQUIRED
	param CDATA #IMPLIED
	extendway ( before | after | override | all | base) "base"
>
