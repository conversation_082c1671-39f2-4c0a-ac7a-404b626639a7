package com.aisino.app.web.matstock;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.aisino.platform.core.ProductInfo;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.SessUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetMatStockPlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		HttpServletRequest request = SessUtil.getRequest();
		String requestUrl = request.getScheme() //当前链接使用的协议
			    +"://" + request.getServerName()//服务器地址 
			    + ":" + request.getServerPort() //端口号 
			    + request.getContextPath(); //应用名称
		String path = SessUtil.getSession().getServletContext().getRealPath("/");
		DbSvr dbSvr = DbSvr.getDbService(null);
		//bus中含keyword搜索关键字
		/* 增加查询当前页数和每页条数 */
		bus.setControlInfo("pt_control_currentpage",bus.getString("curpage"));
		bus.setControlInfo("pt_control_pagesize",bus.getString("pagenum"));
		List<Map> list = dbSvr.queryIdForListByPage("mobile_matstock_list.getmatlist",bus);
	/*	List<Map> list = dbSvr.queryIdForList("mobile_matstock_list.getmatlist",bus);*/
		String imgPath = null;
		if(!CollectionUtil.isEmpty(list)){
			for(int i=0;i<list.size();i++){
//				File fPicture = new File(path+"imgUploads/"+list.get(i).get("cguid")+".jpg");
//				if(fPicture.exists()){
//					list.get(i).put("imgPath", requestUrl+ "/imgUploads/"+list.get(i).get("cguid")+".jpg");
//				}else{
//					list.get(i).put("imgPath", requestUrl + "gl/GLrdBase/imgs/upload.png");
//				}
				imgPath = getClass().getResource("/").getFile().toString();  
				File f = new File(ProductInfo.getWebRealPath()+"/imgUploads/"+list.get(i).get("hidden_cguid")+".jpg");
				if(f.exists()){
					list.get(i).put("imgPath", requestUrl+"/imgUploads/"+list.get(i).get("hidden_cguid")+".jpg");
				}else{
					list.get(i).put("imgPath", "../../../../img/matstock.png");
				}

			}
		}
		Map map = new HashMap();
		map.put("matstock", list);
		form.setReturn(map);

	}
	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {
		
	}
}
