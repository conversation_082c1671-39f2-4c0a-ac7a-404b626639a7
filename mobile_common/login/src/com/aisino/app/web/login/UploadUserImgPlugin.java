package com.aisino.app.web.login;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.core.ProductInfo;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class UploadUserImgPlugin  extends SqlPlugin implements FormCreateListener {

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		
		System.out.println(1);
		Map<String, File> map = (Map) bus.getValue("fileName");
		if (map == null || map.isEmpty()) {
			return;
		}
		for (String fileName : map.keySet()) {
			File file = (File) map.get(fileName);//得到上传的文件
			String imgPath = ProductInfo.getWebRealPath()+ "headImg";
			File f = new File(imgPath); 
		    if(!f.exists()) { 
		        f.mkdirs(); //创建头像文件夹
		    }
		    String userId = SessionHelper.getCurrentUserId();
		    String dir =imgPath+File.separator +userId;
		    f = new File(dir);
			try {
				FileInputStream fReader = new FileInputStream(file);
				BufferedInputStream bReader =new BufferedInputStream(fReader);  
		          
		        FileOutputStream newFWrite=new FileOutputStream(f);  
		        BufferedOutputStream bWriter=new BufferedOutputStream(newFWrite);  
		        byte[] buf = new byte[2048];
		        int i = -1;
		        while((i=bReader.read(buf))!=-1){
		        	bWriter.write(buf,0,i);
		        }
		        if (bWriter!=null) {  
		            bWriter.close();
		        }  
		        if (bReader!=null) {  
		            bReader.close();
		        }
			} catch (Exception e) {
				e.printStackTrace();
			}  
	        
		}
	}

}
