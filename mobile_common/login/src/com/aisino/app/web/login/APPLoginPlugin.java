package com.aisino.app.web.login;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.util.JWT;
import com.aisino.platform.acs.service.LoginService;
import com.aisino.platform.core.MS;
import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.DateUtil;
import com.aisino.platform.util.NoCaseMap;
import com.aisino.platform.util.SessUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;
import com.aisino.platform.view.login.AccessControl;

public class APPLoginPlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		SessUtil.inValidSession();
		String tokenId = bus.getString("tokenid");
		String username;
		String accountId;
		Map<String, Object> rtn = new NoCaseMap<Object>();
		if(StringUtil.isNotBlank(tokenId)){
			//token信息要存在accsys库中,才好确定登录的是哪个帐套,用tokenid登录
			DbSvr acc = DbSvr.getDefaultDbService();
			String token = acc.getStringResult("select token from acc_app_token where cguid=?", tokenId);
			//info里有帐套号和用户名,使用info做登录
			Map<String, String> info = JWT.parse(token);
			username = info.get("user");
			accountId = info.get("act");
			bus.send("account", accountId);
			bus.send("user", username);
			bus.send("date", DateUtil.date2Str(new Date()));
			bus.send("AuthService", "ACS.UserTokenAuth");
			AccessControl login = new LoginService();
			login.canAccess(form, bus);
		}else{
			username = bus.getString("username");
			if (StringUtil.isBlank(username)) {
				rtn.put("errorMsg", "用户名不能为空！");
				form.setReturn(rtn);
				return;
			}
			accountId = bus.getString("accountId");
			if (StringUtil.isBlank(accountId)) {
				rtn.put("errorMsg", "账套不能为空！");
				form.setReturn(rtn);
				return;
			}
			try {
				SessionHelper.getCurrentUserId();
			} catch (Exception e1) {
				DataMsgBus newBus = new DataMsgBus();
				newBus.copy(bus);
				newBus.put("user", bus.getString("username"));
				newBus.put("pwd", bus.getString("password"));
				newBus.put("account", bus.getString("accountId"));
				newBus.put("date", DateUtil.date2Str(new Date()));
				newBus.setNewAction("login");
				MS m = new MS("App.login");  
				m.doService("doFormSubmit",newBus);	
			}
		}

		DbSvr db = DbSvr.getDbService(accountId);
		String sql = "select cGuid, cPWD, cUserType,cRealName from aos_rms_user where cName = ?";
		Map map = db.getOneRecorder(sql, username);
		String userId = CollectionUtil.getStringFromMap(map, "cguid");
		rtn.put("userGuid", userId);
		rtn.put("username", username);
		rtn.put("errorMsg", "success");
		rtn.put("userRealName", CollectionUtil.getStringFromMap(map, "cRealName"));
		//A3,A6登录差异的方法      AppLogin.getUserPerm
		MS m = new MS("App.login");  
		List<Map> list = (List<Map>)m.doService("getUserPerm", db, userId,CollectionUtil.getStringFromMap(map, "cUserType"));	
		rtn.put("funlist",list);
		//AppLogin.getUserPerm(db, userId,CollectionUtil.getStringFromMap(map, "cUserType"))
		SessUtil.putInContext(SessionHelper.ADMIN_ORGN_KEY_IN_SESSION, "1");
		SessUtil.putInContext(SessionHelper.CURRENT_USER_KEY_IN_SESSION, userId);
		SessUtil.putInContext(SessionHelper.LOGIN_ORGN_KEY_IN_SESSION, "1");
		form.setReturn(rtn);
		
	}
}
