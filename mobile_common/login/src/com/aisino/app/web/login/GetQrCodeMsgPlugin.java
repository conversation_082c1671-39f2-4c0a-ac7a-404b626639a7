package com.aisino.app.web.login;

import java.util.List;
import java.util.Map;

import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.SessUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class GetQrCodeMsgPlugin extends SqlPlugin implements FormCreateListener{

	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		SessUtil.inValidSession();
		DbSvr db = DbSvr.getDbService(null);
		String sql = "select username,accid from acc_app_token where cguid = ?";
		List<Map> list = db.getListResult(sql, bus.getString("cguid"));
		form.setReturn(list);
	}

}
