package com.aisino.app.web.service;

import java.util.HashMap;
import java.util.Map;

import com.aisino.aos.system.SessionHelper;
import com.aisino.app.web.util.JWT;
import com.aisino.app.web.util.Qr;
import com.aisino.platform.core.Guid;
import com.aisino.platform.core.ProductInfo;
import com.aisino.platform.core.serviceInf.IFService;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;

public class AppLoginService implements IFService {

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public Object doService(AbstractForm form, DataMsgBus bus) {
		DbSvr acc = DbSvr.getDefaultDbService();
		String url = ProductInfo.getWebRoot();
		url = url.substring(0, url.length()-1);
		String id = SessionHelper.getCurrentDataSource();
		String user = SessionHelper.getCurrentUserName();
		if(!acc.checkExists("acc_app_token", "username,accid", user, id)){
			String cguid = Guid.g();
			String subject = id+","+user;
			String token = JWT.create(cguid, "Aisino Corp.", subject);
			String content = url+","+cguid;
			String fileName = Qr.bar(content);
			Map param = new HashMap();
			param.put("cguid", cguid);
			param.put("token", token);
			param.put("username", user);
			param.put("accid", id);
			param.put("filename", fileName);
			acc.insertRow("acc_app_token", param);
		}
		return null;
	}

}
