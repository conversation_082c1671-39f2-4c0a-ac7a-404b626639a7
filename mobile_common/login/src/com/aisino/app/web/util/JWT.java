package com.aisino.app.web.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import java.security.Key;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.crypto.spec.SecretKeySpec;

public class JWT {
	public static Map<String, String> parse(String jwt) {
		Map<String, String> info = new HashMap<String, String>();
		Claims claims = Jwts.parser().setSigningKey("AisinoErpApp".getBytes()).parseClaimsJws(jwt).getBody();
		String[] values = claims.getSubject().split(",");
		info.put("act", values[0]);
		info.put("user", values[1]);
		return info;
	}
	
	public synchronized static String create(String id, String issuer, String subject) {
		SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;
		Calendar now = Calendar.getInstance();
		now.setTime(new Date());
		byte[] apiKeySecretBytes = "AisinoErpApp".getBytes();
		Key signingKey = new SecretKeySpec(apiKeySecretBytes,
				signatureAlgorithm.getJcaName());
		// jwt加密
		JwtBuilder builder = Jwts.builder().setId(id)
				.setIssuedAt(now.getTime()).setSubject(subject)
				.setIssuer(issuer).signWith(signatureAlgorithm, signingKey);
		// 有效期 一个月
		now.set(Calendar.MONTH, now.get(Calendar.MONTH) + 1);
		long expMillis = now.getTimeInMillis();
		Date exp = new Date(expMillis);
		builder.setExpiration(exp);
		return builder.compact();
	}
}
