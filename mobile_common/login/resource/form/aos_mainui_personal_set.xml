<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE Forms SYSTEM "form_definition.dtd">
<!-- 平台研发部 作者:YJG  日期: 2013-5-6 下午4:10:07 描述:个人设置与软件信息-->
<Forms>
	<form id="aos_mainui_personal_set" desp="个人设置与软件信息" width="420px" height="260px" version='2' >
		<widgets>
			<layout type="TabLayout" name="personalSet" width="100%" height="100%" desp="个人设置与软件信息">
				<layout type="TableLayout" name="personalInfo" desp="登录信息" topSpace="10px" width="99%" attr="groupSize:2">
					<basic name="name_desp" widget="Label" label="姓名：" align="right" width="100px" topSpace="10px"></basic>
					<basic name="name" widget="Label" label="" align="left" width="280px" topSpace="10px"></basic>
					<basic name="companyName_desp" widget="Label" label="单位：" align="right" width="100px"  topSpace="10px"></basic>
					<basic name="companyName" widget="Label" label="" align="left" width="280px"  topSpace="10px"></basic>
					<basic name="loginedOrgn_desp" widget="Label" label="管理组织：" align="right" width="100px" topSpace="10px"></basic>
					<basic name="loginedOrgn" widget="Label" label="" align="left" width="280px" topSpace="10px"></basic>
					<basic name="versionInfo_desp" widget="Label" label="版本：" align="right" width="100px" topSpace="10px" ></basic>
					<basic name="versionInfo" widget="Label" label="" align="left" width="280px" topSpace="10px" ></basic>
					<basic name="loginedDs_desp" widget="Label" label="登录账套：" align="right" width="100px" topSpace="10px"></basic>
					<basic name="loginedDs" widget="Label" label="" align="left" width="280px" topSpace="10px"></basic>
					<basic name="loginedTime_desp" widget="Label" label="登录日期：" align="right" width="100px" topSpace="10px"></basic>
					<basic name="loginedTime" widget="Label" label="" align="left" width="280px" topSpace="10px"></basic>
				</layout>
				<layout type="BandLayout" name="personalInfo" desp="个人选项" topSpace="10px" width="99%">
					<layout type="BandLayout" name="MaxTabNumLayout" width="380px">
						<basic name="maxTabNum" label="最大页签数：" leftSpace="20px" width="50px" topSpace="10px" widget="Int" maxlength="2"></basic>
						<basic name="maxTabNumCheck" label="（5 到 20之间）" widget="Label" width="100px"></basic>
					</layout>
					<basic name="themeSet_desp" label="风格设置 ：" widget="Label" leftSpace="25px" bottomSpace="20px" width="72px"></basic>
					<basic name="themeSet" widget="RadioGroup" width="200px" attr="groupSize:4" topSpace="20px">
						<valueFetcher type="com.aisino.platform.view.basicWidget.fetcher.StaticFetcher">
              				<value name="blue">蓝色</value>
              				<value name="deepblue">深蓝</value>
              				<value name="gray">灰色</value>
              				<value name="metal">金属</value>
              				<value name="green">春天</value>
              				<value name="summer">夏天</value>
              				<value name="autumn">秋天</value>
              				<value name="winter">冬天</value>
						</valueFetcher>
					</basic>
					<basic name="personalInfoDesp" widget="Label" label="说明：个人选项的设置会保存到本机，仅对当前机器有效。" inline="single" width="360px" leftSpace="15px" topSpace="30px"></basic>
				</layout>
				<layout type="BandLayout" name="personalInfo" desp="密码设置" width="99%" height="100%">
					<basic name="cGuid" label="用户cGuid" widget="Hidden"></basic>
					<basic name="oldcPwd" widget="Password" label="原密码：" checks="Sn" width="180px" inline="single" topSpace="10px"></basic>
				    <basic name="cPwd" widget="Password" label="新密码：" checks="Sn"  width="180px" inline="single"></basic>	
				    <basic name="cPwdConfig" widget="Password" label="密码确认：" checks="Sn"  width="180px" inline="single"></basic>
				    <basic name="save" widget="Button" label="保存" attr="img:img/Save.gif" width="50px" leftSpace="110px" topSpace="10px" ></basic>
				    <basic name="reset" widget="Button" label="重置" attr="img:img/Cancel.gif" width="50px" leftSpace="10px" topSpace="10px"></basic>	
				 
				    <basic name="iMinLength" widget="Hidden" label="密码的最小长度"></basic>
				    <basic name="iValidateDays" widget="Hidden" label="密码有效天数"></basic>
				    <basic name="cDefaultPwd" widget="Hidden" label="默认密码"></basic>
				    <basic name="iValidateForever" widget="Hidden" label="是否永久有效"></basic> 
				    <basic name="iIncludeCharNum" widget="Hidden" label="是否必须包含字母和数字"></basic>
				    <basic name="rootorgn" widget="Hidden" label="登陆用户所在的组织"></basic>
				    <basic name="adminTypeGuid" widget="Hidden" label="管理员类型的ID"></basic>
				    <basic name="iAllowChange" widget="Hidden" label="是否允许修改密码"></basic>
				</layout>
				<layout type="TableLayout" name="appInfo" desp="移动应用" width="99%" topSpace="10px" attr="groupSize:2" >
					<basic name="app_desp" widget="Label" label="扫码登录：" align="right" width="100px"></basic>
					<basic name="appQr" widget="Img" align="left" width="200px" height="200px" attr="zoom:true;img:/apptoken/logo.png"></basic>
				</layout>
				<layout type="TableLayout" name="productInfo" desp="关于" width="99%" topSpace="10px" attr="groupSize:2" >
					<basic name="homepage_desp" widget="Label" label="主页：" align="right" width="100px"></basic>
					<basic name="homepage" widget="Link" label="http://soft.aisino.com" align="left" width="100px"></basic>
					<basic name="weibo_desp" widget="Label" label="微博：" align="right" width="100px"></basic>
					<basic name="weibo" widget="Link" label="http://e.weibo.com/aisinoerp" align="left" width="100px"></basic>
					<basic name="weixin_desp" widget="Label" label="微信：" align="right" width="100px"></basic>
					<basic name="weixin" widget="Img" align="left" width="100px" height="100px" attr="img:res/mainui/images/softInfoAndPerSet/weixin_qr_code.png"></basic>
					<basic name="copyright_desp" widget="Label" label="版权：" align="right" width="100px"></basic>
					<basic name="copyright" widget="Label" label="" align="left" width="280px" 
						default="版权所有 ©2013 航天信息软件技术有限公司" ></basic>
					<basic name="copyright_en_desp" widget="Label" label="Copyright：" align="right" width="100px"></basic>
					<basic name="copyright_en" widget="Label" label="" align="left" width="280px"
						default="Copyright 2013 Aisino Software Technology" ></basic>
					<basic name="copyright_en_desp1" widget="Label" label="" align="right" width="100px"></basic>
					<basic name="copyright_en1" widget="Label" label="" align="left" width="280px" 
						default="Co.,Ltd. All rights reserved." ></basic>
				</layout>
			</layout>
		</widgets>
		<extendPoint>
			<plugin type="com.aisino.aos.mainui.plugin.PersonalSetPlugin"></plugin>
			<plugin type="com.aisino.app.web.login.AppQrImgPlugin"></plugin>
		</extendPoint>
		<bind element="this" event="onCreate"><![CDATA[
			window.onFormCreate = true;
			var teme = getCookie('aos_theme');
			if(!theme)
				theme = 'blue';
			w('themeSet').set(theme);
			var maxTabNum = getCookie('maxTabNum');
			if(!maxTabNum)
				maxTabNum = 6;
			ws('maxTabNum',maxTabNum);
			onFormCreate = false;
		]]></bind>
		
		<bind element="themeSet" event="dochange"><![CDATA[
			if(onFormCreate)
				return;
			var theme = w('themeSet').get();
			if(!theme)
				return;
			PT.f().switchTheme(theme);
			w('themeSet').setDisabled(true);
			setTimeout("w('themeSet').setDisabled(false);",500);
		]]></bind>
		
		<bind element="this" event="switchTheme" param ="theme"><![CDATA[
			try{
				setCookie('aos_theme',theme); 
				window.dialogArguments.mainUI.setTheme(theme);
				PT.t('风格设置成功！');
			}catch(e){
				PT.debug('onclick error.'+e);
				throw e;
			}
		]]></bind>
	
		<bind element="maxTabNum" event="blur"><![CDATA[
			var maxTabNum = wg('maxTabNum');
			if(!maxTabNum){
				PT.alert('最大页签数不能为空！');
				return;
			}
			maxTabNum = parseInt(maxTabNum);
			if(maxTabNum<5 || maxTabNum>20){
				PT.alert('最大页签数只能设置在5到20之间！');
				return;
			}
			setCookie('maxTabNum',maxTabNum);
			window.dialogArguments.mainUI.maxNum = maxTabNum;
			PT.t('最大页签数设置成功！');
		]]></bind>
	
		<bind element="save" event="click"><![CDATA[
			var cPwd = wg('cPwd');
			var cPwdConfig = wg('cPwdConfig');
				
			if(wg('iAllowChange') == 0){
				PT.alert('密码策略中不允许修改密码！');
				return;
			}
			var iMinLength = wg('iMinLength');
			if(cPwd!=null&&cPwd!=''){
				if(cPwdConfig==null||cPwdConfig==''){
					PT.alert('确认密码不能为空！');
					return;
				}
				if(cPwdConfig!=cPwd){
					PT.alert('两次输入的密码不一致！');
					return;
				}

				if(cPwd.length<iMinLength) {
					PT.alert('用户密码不符合密码策略的要求，必须不低于'+iMinLength+'位，请重新设置！');
					return;
				}
				
				var iIncludeCharNum = wg('iIncludeCharNum');
				if(iIncludeCharNum==1){
					var i = /[a-zA-Z]/.test(cPwd);
					var j = /\d/.test(cPwd);
					if(!i||!j){
						PT.alert('用户密码不符合密码策略的要求，必须包含字母和数字的组合，请重新设置！');
						return;
					}
				}				
			} else {
				if(cPwdConfig != null&&cPwdConfig != ''){
					PT.alert('两次输入的密码不一致！');
					return;
				}
				if(iMinLength >0){
					PT.alert('密码策略不允许输入空密码！');
					return;
				}
			}
			
			var re = PT.ns('save');	
			if(re == '1')
				PT.t('保存成功！', 'save');
		]]></bind>
		<bind element="reset" event="click"><![CDATA[
			ws('oldcPwd','');
			ws('cPwd','');
			ws('cPwdConfig','');
		]]></bind>
		<bind element="homepage" event="click"><![CDATA[
			window.open('http://soft.aisino.com');
		]]></bind>
		<bind element="weibo" event="click"><![CDATA[
			window.open('http://e.weibo.com/aisinoerp');
		]]></bind>
	</form>
</Forms>
	