package com.aisino.app.web.customer;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;

public class CustomerListPlugin extends SqlPlugin implements FormCreateListener {

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		DbSvr dbSvr = DbSvr.getDbService(null);
		//暂不处理修改
		dbSvr.update("UPDATE a   SET   a.cmobilecode =  dbo.f_GetPY(b.cName)  FROM CM_Customer a,CM_Customer b    WHERE a.cguid = b.cguid AND (a.cmobilecode IS NULL)");
		dbSvr.commit();
		
		bus.setControlInfo("pt_control_currentpage",bus.getString("curpage"));
		bus.setControlInfo("pt_control_pagesize",bus.getString("pagenum"));
		// bus中包含keyword搜索关键字
	//	List<Map> list = dbSvr.queryIdForList("mobile_customer_crm.customerlist", bus);
		List<Map> list = dbSvr.queryIdForListByPage("mobile_customer_crm.customerlist", bus);
		Map map = new HashMap();
		map.put("customerlist", list);
		form.setReturn(map);
//		form.setReturn(groupAddressList(list));
	}

	/**
	 * 通讯录按字母分组
	 * 
	 * @param 查询结果
	 *            list
	 * @return 分组结果 Map
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Map groupAddressList(List<Map> list) {

		if (list == null || list.size() == 0)
			return null;
		//最终结果
		List result = new ArrayList();
		//临时list
		List tempLsit = new ArrayList();
		//分组名称  字母
		String title = "";
		//循环时  下一个字母
		String nextTitle ="";
		
		for (int i=0;i<list.size();i++) {
			
			//取得分组名称
			title = (String) list.get(i).get("firstLetter");
			//获取下一次循环时对应分组名称
			if(i<list.size()-1){
				nextTitle = (String) list.get(i+1).get("firstLetter");
			}else{
				nextTitle = "";
			}
			//当前循环结果  放入临时list中
			tempLsit.add(list.get(i));
			//若当前分组名称与下一次分组名称不等  则该类分组已完成
			if(!nextTitle.equals(title)){
				//临时map
				Map temp = new HashMap();
				//分组名称
				temp.put("title", title);
				//改分组中的数据
				temp.put("titleDate", tempLsit);
				//添加到最终结果中
				result.add(temp);
				//下一组的list
				if(StringUtil.isNotBlank(nextTitle))
					tempLsit  = new ArrayList();
			}
			
		}
		Map map = new HashMap();
		map.put("customerlist", result);
		return map;
	}

	public void doSubmitAction(AbstractForm form, DataMsgBus bus) {

	}
}
