package com.aisino.app.web.notice;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.platform.core.SqlPlugin;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;
/**
 * 查询个人消息详细
 * <AUTHOR>
 *
 */
public class GetNoticeDetailPlugin extends SqlPlugin implements
		FormCreateListener {

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		String url =bus.getString("url");
		String sql = null;
		List<Map> files = null;
		String fileSql = "select aos_file.cFileName,aos_file.cFileUrl,aos_file.cGroupGuid from AOS_FILE_FILES aos_file"+
						 " left join pt_notice p on aos_file.cGroupGuid = p.cattachgroupguid"+
						 " where p.creplyGuid = (select creplyGuid from pt_notice where cBusinessGuid = ?)"+
						 " order by p.dCreateDate asc";
		DbSvr db = DbSvr.getDbService(null);
		List<Map> list = null;
		Map p = new HashMap();
		if("oa_pi_docMgmt_edit_form".equals(url)){
			//o.cguid,
			sql = "select o.ccode as '编码',o.cname as '名称',d.cname as '文档类型',o.cremark as '备注',s.name as '状态',u.crealname as '创建人',o.dcreatedate as '创建时间',v.cRealName as '修改人',o.dModifyTime as '修改时间',x.cRealName as '审核人',o.dCheckTime as '审核时间'"
					+ " from OA_DocumentMgmt o left join OA_DocType d on d.cguid = o.cdoctypeguid"
					+ " left join (select top 999999999 cGUID code,cCode,cName name "
					+ " from OA_StaticEnum "
					+ " where cScope in ('oa_status','oa_fm_status','oa_od_status','oa_od_borrowStatus') and cStatus='1'"
					+ " ) s on s.code = o.cStatusEnumGUID"
					+ " left join AOS_RMS_USER u on u.cguid = o.cCreaterGUID"
					+ " left join AOS_RMS_USER v on v.cguid = o.cModifierGUID"
					+ " left join AOS_RMS_USER x on x.cguid = o.cAuditorGuid"
					+ " where o.cguid=?";
		
			list = db.getListResult(sql, bus.getString("cguid"));
			files = db.getListResult(fileSql, bus.getString("cguid"));
		}else if("aos_send_pt_notice_form".equals(url)){
			//p.cguid,p.cAttachGroupGuid,p.cFromUserGUID,p.creplyGuid,p.iNoticeType,p.cSendGuid,
			sql="select  p.cname as '消息名称',p.cDesc as '消息内容',p.dCreateDate as '创建时间',u.crealname as '发送人',p.cattachgroupguid"
					+" from pt_notice p left join AOS_RMS_USER u on u.cGUID=p.cFromUserGUID where"
					+ " p.creplyGuid = (select creplyGuid from pt_notice where cBusinessGuid = ?) order by p.dCreateDate asc";
			list = db.getListResult(sql, bus.getString("cbusinessguid"));
			//files = db.getListResult(fileSql, db.getStringResult("select p.cattachgroupguid from  pt_notice p where p.cBusinessGuid=?", bus.getString("cbusinessguid")));
			files = db.getListResult(fileSql, bus.getString("cbusinessguid"));
		}
		p.put("list", list);
		p.put("file", files);
		form.setReturn(p);
	}

}
