<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls> 
	<sql group="a3app_alarm">
	<!-- 负库存预警 -->
	<i id="fukucunalarm">
		<![CDATA[
			select top 1 cstorename '仓库名称',
				   cmatcode, 
				   cmatName,  
				   cbatchcode '批次', 
				   iCurQuan '现存量'   
			from (SELECT cs.cName cStoreName,
						 cm.cCode AS cMatCode,  
						 cm.cName cMatName,  
						 isnull(sa.cBatchGUID,'-') cBatchCode,  
					     SUM(sa.iCurQuan) iCurQuan  
				  FROM ST_CurAccount sa  
				  INNER JOIN CM_Material cm ON cm.cGUID = sa.cMatID  
				  LEFT JOIN CM_MatBatch cb ON cb.cBatchCode = sa.cBatchGUID  
				  LEFT JOIN CM_Storehouse cs ON cs.cGUID = sa.cStoreGUID  
				  WHERE sa.iCurQuan < '0'  
				  GROUP BY cs.cName,cm.cCode,cm.cName,sa.cBatchGUID) s
		]]>
	</i>
	<!-- 销售毛利率预警 -->
	<i id="xiaoshoumaolilvalarm">
		<![CDATA[
			select top 1 cmatcode, 
			       cmatname, 
			       reearn, 
			       ranking '毛利率排名（由低到高）'
			from (SELECT cMatGUID,
			             cMatCode, 
			             cMatName, 
			             ltrim(reEarn) + '%' reEarn, 
			             Rank()OVER (ORDER BY reEarn ASC) AS ranking 
                   FROM (select TOP 100000 cm.cGUID as
                                cMatGUID, 
                                cm.cCode as cMatCode, 
                                cm.cMatName cMatName, 
                                CASE isnull(sum(t.iAMT), 0) 
                                WHEN 0 THEN '0'
                                ELSE cast ((isnull(sum(t.iAMT), 0) - isnull(sum(t.iCost),0)) / sum(t.iAMT) * 100 as dec (30, 2))
                                end AS reEarn 
                         from v_business_a3scm_sa_billlistInfo t 
                         LEFT JOIN CM_Material cm ON cm.cGUID = t.cMatGUID LEFT JOIN CM_MatClass cmc ON cmc.cGUID = cm.cMatCGUID 
                         WHERE Datediff(d, t.dDate, Getdate()) <= (select iNumber_1_value from AOS_EW_Ordered where cSqlGUID='248289868326573284')
                         group by cm.cGUID, cm.cCode, cm.cMatName
                         order by reEarn ASC) kk) tt		
		]]>
	</i>
	<!-- 物品保质期按天数预警 -->
	<i id="wpbzqatsyjalarm">
		<![CDATA[
			select top 1 cmatguid,
			       cstorname,
			       cmatcode, 
			       cmatname,
			       cbatchguid,
			       cstorguid,
			       iunitqty,
			       doverdate,
			       syday,
			       gqday from (select cMatGUID, 
			                          cMatName,       
			                          iPrecision,
			                          cMatCode,
			                          cBatchGUID, 
			                          cStorGUID,
			                          cStorCode,       
			                          cStorName,
			                          max(dOverDate) as dOverDate, 
			                          sum(iUnitQTY) as iUnitQTY,
			                          CASE WHEN (DATEDIFF(d,GETDATE(),max(dOverDate))) > 0 THEN (DATEDIFF(d, GETDATE(), max(dOverDate)))   ELSE '0' END SYDAY,
			                          CASE WHEN (DATEDIFF(d, GETDATE(),max(dOverDate))) <= 0 THEN cast((DATEDIFF(d, max(dOverDate), GETDATE())) AS char) ELSE '0' END GQDAY 
			                    from v_business_scmst_getdOverDateLine 
			                    group by cMatGUID, cMatName, iPrecision, cMatCode,cBatchGUID,cStorGUID,cStorCode,cStorName 
			                    having ((DATEDIFF(d, GETDATE(),max(dOverDate)))<=(select iNumber_1_value from AOS_EW_Ordered where cSqlGUID='302311200392266677') and (DATEDIFF(d, GETDATE(),max(dOverDate))) >=0)) kkkk
		]]>
	</i>
	<!-- 其他预警 -->
	<i id="qitaalarm">
		<![CDATA[
		
		]]>
	</i>
	</sql>
</sqls>
